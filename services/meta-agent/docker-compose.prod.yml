version: '3.8'

# Production Docker Compose Configuration
# Usage: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up

services:
  postgres:
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
    networks:
      - ai-agent-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
      restart_policy:
        condition: any
        delay: 5s
        max_attempts: 3

  redis:
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD} --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data_prod:/data
    networks:
      - ai-agent-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  kafka:
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
      KAFKA_DELETE_TOPIC_ENABLE: false
      KAFKA_LOG_RETENTION_HOURS: 168
      KAFKA_LOG_SEGMENT_BYTES: 1073741824
      KAFKA_LOG_RETENTION_CHECK_INTERVAL_MS: 300000
    volumes:
      - kafka_data_prod:/var/lib/kafka/data
    networks:
      - ai-agent-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=production
      - DEBUG=false
      - CORS_ORIGINS=${CORS_ORIGINS}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
      - LOG_LEVEL=info
    volumes:
      - backend_logs:/app/logs
    networks:
      - ai-agent-network
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
      restart_policy:
        condition: any
        delay: 5s
        max_attempts: 3
      update_config:
        parallelism: 1
        delay: 10s
        order: start-first
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    command: >
      sh -c "
        echo 'Starting production backend...' &&
        alembic upgrade head &&
        gunicorn src.main:app 
        -w 4 
        -k uvicorn.workers.UvicornWorker 
        --bind 0.0.0.0:8000 
        --access-logfile /app/logs/access.log 
        --error-logfile /app/logs/error.log 
        --log-level info
        --timeout 120
        --keep-alive 2
        --max-requests 1000
        --max-requests-jitter 100
        --preload
      "

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
      - NEXT_PUBLIC_WS_URL=${NEXT_PUBLIC_WS_URL}
    networks:
      - ai-agent-network
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
      restart_policy:
        condition: any
        delay: 5s
        max_attempts: 3

  nginx:
    profiles:
      - ""  # Enable nginx by default in production
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M
      restart_policy:
        condition: any
        delay: 5s
        max_attempts: 3

  prometheus:
    volumes:
      - ./monitoring/prometheus.prod.yml:/etc/prometheus/prometheus.yml
      - prometheus_data_prod:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=720h'
      - '--web.enable-lifecycle'
      - '--web.external-url=http://localhost:9090'
    networks:
      - ai-agent-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  grafana:
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_ADMIN_PASSWORD}
      GF_USERS_ALLOW_SIGN_UP: false
      GF_SERVER_ROOT_URL: ${GRAFANA_ROOT_URL}
      GF_SECURITY_SECRET_KEY: ${GRAFANA_SECRET_KEY}
      GF_DATABASE_TYPE: postgres
      GF_DATABASE_HOST: postgres:5432
      GF_DATABASE_NAME: ${POSTGRES_DB}
      GF_DATABASE_USER: ${POSTGRES_USER}
      GF_DATABASE_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - grafana_data_prod:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - ai-agent-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # Production-only services
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.8.0
    container_name: ai-agent-filebeat
    user: root
    environment:
      - ELASTICSEARCH_HOST=${ELASTICSEARCH_HOST}
      - ELASTICSEARCH_USERNAME=${ELASTICSEARCH_USERNAME}
      - ELASTICSEARCH_PASSWORD=${ELASTICSEARCH_PASSWORD}
    volumes:
      - ./monitoring/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - backend_logs:/app/logs:ro
      - nginx_logs:/var/log/nginx:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - ai-agent-network
    depends_on:
      - backend
    profiles:
      - monitoring

  backup:
    image: postgres:15-alpine
    container_name: ai-agent-backup
    depends_on:
      - postgres
    environment:
      PGPASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh
    command: >
      sh -c "
        chmod +x /backup.sh &&
        crond -f
      "
    networks:
      - ai-agent-network
    profiles:
      - backup

volumes:
  postgres_data_prod:
    driver: local
  redis_data_prod:
    driver: local
  kafka_data_prod:
    driver: local
  prometheus_data_prod:
    driver: local
  grafana_data_prod:
    driver: local
  backend_logs:
    driver: local
  nginx_logs:
    driver: local