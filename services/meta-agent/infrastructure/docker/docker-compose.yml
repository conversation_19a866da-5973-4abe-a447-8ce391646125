version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ai_agent_postgres
    environment:
      POSTGRES_DB: ai_agent_platform
      POSTGRES_USER: ai_agent
      POSTGRES_PASSWORD: ai_agent_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ai_agent -d ai_agent_platform"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - ai_agent_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: ai_agent_redis
    command: redis-server --appendonly yes --replica-read-only no
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - ai_agent_network

  # Apache Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: ai_agent_zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    networks:
      - ai_agent_network

  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: ai_agent_kafka
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    ports:
      - "9092:9092"
    networks:
      - ai_agent_network
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 10s
      timeout: 10s
      retries: 5

  # AI Agent Platform Backend
  backend:
    build:
      context: ../../backend
      dockerfile: ../infrastructure/docker/Dockerfile.backend
    container_name: ai_agent_backend
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - POSTGRES_SERVER=postgres
      - POSTGRES_USER=ai_agent
      - POSTGRES_PASSWORD=ai_agent_password
      - POSTGRES_DB=ai_agent_platform
      - REDIS_SERVER=redis
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - SECRET_KEY=dev-secret-key-change-in-production
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      kafka:
        condition: service_healthy
    volumes:
      - ../../backend:/app
      - /app/.venv
    networks:
      - ai_agent_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Development Server (Next.js)
  frontend:
    build:
      context: ../../frontend
      dockerfile: ../infrastructure/docker/Dockerfile.frontend
    container_name: ai_agent_frontend
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    ports:
      - "3000:3000"
    depends_on:
      - backend
    volumes:
      - ../../frontend:/app
      - /app/node_modules
      - /app/.next
    networks:
      - ai_agent_network

  # Prometheus (Monitoring)
  prometheus:
    image: prom/prometheus:latest
    container_name: ai_agent_prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - ai_agent_network

  # Grafana (Visualization)
  grafana:
    image: grafana/grafana:latest
    container_name: ai_agent_grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - ai_agent_network

  # Jaeger (Distributed Tracing)
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: ai_agent_jaeger
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    ports:
      - "14250:14250"
      - "16686:16686"
    networks:
      - ai_agent_network

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  ai_agent_network:
    driver: bridge