version: '3.8'

services:
  # Database Services
  postgres:
    image: postgres:15-alpine
    container_name: ai-agent-postgres
    environment:
      POSTGRES_DB: ai_agent_platform
      POSTGRES_USER: ai_agent_user
      POSTGRES_PASSWORD: ai_agent_password
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - ai-agent-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ai_agent_user -d ai_agent_platform"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: ai-agent-redis
    command: redis-server --appendonly yes --requirepass ai_agent_redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - ai-agent-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    restart: unless-stopped

  # Message Queue Services
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: ai-agent-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    networks:
      - ai-agent-network
    restart: unless-stopped

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: ai-agent-kafka
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
      KAFKA_DELETE_TOPIC_ENABLE: true
    volumes:
      - kafka_data:/var/lib/kafka/data
    ports:
      - "9092:9092"
      - "29092:29092"
    networks:
      - ai-agent-network
    healthcheck:
      test: kafka-broker-api-versions --bootstrap-server kafka:9092
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Vector Database
  qdrant:
    image: qdrant/qdrant:v1.7.4
    container_name: ai-agent-qdrant
    volumes:
      - qdrant_data:/qdrant/storage
    ports:
      - "6333:6333"
      - "6334:6334"
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
    networks:
      - ai-agent-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Application Services
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: ai-agent-backend
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      kafka:
        condition: service_healthy
      qdrant:
        condition: service_healthy
    environment:
      - DATABASE_URL=**********************************************************/ai_agent_platform
      - REDIS_URL=redis://:ai_agent_redis_password@redis:6379/0
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - QDRANT_URL=http://qdrant:6333
      - SECRET_KEY=super-secret-key-change-in-production
      - ENVIRONMENT=development
      - DEBUG=true
      - CORS_ORIGINS=http://localhost:3000,http://frontend:3000
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
    volumes:
      - ./backend/src:/app/src
      - ./backend/alembic:/app/alembic
      - ./backend/logs:/app/logs
    ports:
      - "8000:8000"
    networks:
      - ai-agent-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    command: >
      sh -c "
        echo 'Waiting for database to be ready...' &&
        python -c 'import time; time.sleep(10)' &&
        alembic upgrade head &&
        uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload
      "

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    container_name: ai-agent-frontend
    depends_on:
      - backend
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NEXT_PUBLIC_WS_URL=ws://localhost:8000
    volumes:
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
      - /app/node_modules
      - /app/.next
    ports:
      - "3000:3000"
    networks:
      - ai-agent-network
    restart: unless-stopped

  # Monitoring Services
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: ai-agent-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - ai-agent-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:10.0.0
    container_name: ai-agent-grafana
    depends_on:
      - prometheus
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3001:3000"
    networks:
      - ai-agent-network
    restart: unless-stopped

  # Load Balancer (for production)
  nginx:
    image: nginx:1.25-alpine
    container_name: ai-agent-nginx
    depends_on:
      - backend
      - frontend
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    ports:
      - "80:80"
      - "443:443"
    networks:
      - ai-agent-network
    restart: unless-stopped
    profiles:
      - production

# Networks
networks:
  ai-agent-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  kafka_data:
    driver: local
  zookeeper_data:
    driver: local
  zookeeper_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  qdrant_data:
    driver: local