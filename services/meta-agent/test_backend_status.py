#!/usr/bin/env python3
"""
Test backend status and agents endpoint
"""

import requests

def test_backend():
    print('🔍 Testing Backend Status')
    print('=' * 30)
    
    try:
        # Test health endpoint
        response = requests.get('http://localhost:8000/health', timeout=5)
        print(f'✅ Backend Health: {response.status_code}')
        print(f'   Response: {response.json()}')
        
        # Test agents endpoint
        agents_response = requests.get('http://localhost:8000/api/v1/public/agents', timeout=5)
        print(f'✅ Agents Endpoint: {agents_response.status_code}')
        agents_data = agents_response.json()
        print(f'   Agents Count: {agents_data.get("total", 0)}')
        print(f'   Items: {len(agents_data.get("items", []))}')
        
        # Test CORS
        headers = {'Origin': 'http://localhost:3000'}
        cors_response = requests.get('http://localhost:8000/api/v1/public/agents', headers=headers, timeout=5)
        print(f'✅ CORS Test: {cors_response.status_code}')
        
        return True
        
    except Exception as e:
        print(f'❌ Backend Error: {e}')
        return False

if __name__ == "__main__":
    if test_backend():
        print('\n🎉 Backend is working properly!')
    else:
        print('\n❌ Backend has issues. Check if server is running.')
