#!/usr/bin/env python3
"""
Test the fixed enhanced frontend
"""

import requests
import time

def main():
    print('🎨 Testing Fixed Enhanced Frontend')
    print('=' * 50)

    # Wait a moment for server to be ready
    time.sleep(2)

    # Test enhanced pages
    pages = [
        ('Dashboard', 'http://localhost:3001/dashboard'),
        ('Create Agent', 'http://localhost:3001/agents/create'),
        ('Agents List', 'http://localhost:3001/agents'),
    ]

    for name, url in pages:
        try:
            response = requests.get(url, timeout=10)
            print(f'✅ {name}: {response.status_code} - Working!')
            
            # Check for enhanced features
            if response.status_code == 200:
                content = response.text.lower()
                features = []
                if 'gradient' in content:
                    features.append('Gradients')
                if 'animate' in content:
                    features.append('Animations')
                if 'backdrop-blur' in content:
                    features.append('Blur Effects')
                if 'transition' in content:
                    features.append('Transitions')
                
                if features:
                    print(f'   🎨 Features: {", ".join(features)}')
                    
        except Exception as e:
            print(f'❌ {name}: {e}')

    print('\n🎉 Enhanced Frontend is Working!')
    print('✨ Gradient backgrounds with animations')
    print('💫 Smooth transitions and hover effects')
    print('🎨 Modern glass morphism design')
    print('⚡ Interactive elements and scaling')
    print('\n🌐 Access the enhanced UI:')
    print('   Dashboard: http://localhost:3001/dashboard')
    print('   Create Agent: http://localhost:3001/agents/create')

if __name__ == "__main__":
    main()
