#!/usr/bin/env python3
"""
Complete setup and execution script for AI Agent Platform
This script will:
1. Check system requirements
2. Start the backend (if needed)
3. Create and deploy two fullstack agents
4. Report the results
"""

import os
import sys
import time
import subprocess
import requests
import signal
from pathlib import Path

def check_requirements():
    """Check if all requirements are met"""
    print("🔍 Checking system requirements...")
    
    issues = []
    
    # Check Python version
    if sys.version_info < (3, 8):
        issues.append("Python 3.8+ is required")
    
    # Check if backend directory exists
    backend_dir = Path("/Users/<USER>/workspaces/git/ai-agent/backend")
    if not backend_dir.exists():
        issues.append("Backend directory not found")
    
    # Check if virtual environment exists
    venv_dir = backend_dir / "venv"
    if not venv_dir.exists():
        issues.append("Python virtual environment not found in backend directory")
    
    # Check if .env file exists
    env_file = backend_dir / ".env"
    if not env_file.exists():
        issues.append(".env file not found in backend directory")
    
    if issues:
        print("❌ System requirements not met:")
        for issue in issues:
            print(f"   • {issue}")
        return False
    
    print("✅ System requirements are met")
    return True

def is_backend_running():
    """Check if backend is already running"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def start_backend():
    """Start the backend server"""
    if is_backend_running():
        print("✅ Backend is already running")
        return True
    
    print("🚀 Starting backend server...")
    
    backend_dir = "/Users/<USER>/workspaces/git/ai-agent/backend"
    venv_python = f"{backend_dir}/venv/bin/python"
    
    # Create the command
    cmd = [
        venv_python, "-m", "uvicorn", "src.main:app",
        "--host", "0.0.0.0",
        "--port", "8000",
        "--reload"
    ]
    
    try:
        # Start backend in background
        process = subprocess.Popen(
            cmd,
            cwd=backend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            preexec_fn=os.setsid  # Create new process group
        )
        
        # Wait for backend to start
        print("⏳ Waiting for backend to start...")
        for i in range(30):  # Wait up to 30 seconds
            if is_backend_running():
                print("✅ Backend started successfully")
                return process
            time.sleep(1)
        
        print("❌ Backend failed to start within 30 seconds")
        # Try to get error output
        try:
            stdout, stderr = process.communicate(timeout=1)
            if stderr:
                print(f"Error output: {stderr.decode()}")
        except:
            pass
        
        return None
        
    except Exception as e:
        print(f"❌ Failed to start backend: {e}")
        return None

def check_docker():
    """Check if Docker is available and running"""
    try:
        result = subprocess.run(["docker", "--version"], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ Docker is available")
            
            # Check if Docker daemon is running
            result = subprocess.run(["docker", "ps"], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("✅ Docker daemon is running")
                return True
            else:
                print("⚠️  Docker is installed but daemon may not be running")
                return False
        else:
            print("⚠️  Docker is not available")
            return False
    except Exception as e:
        print(f"⚠️  Could not check Docker status: {e}")
        return False

def create_and_deploy_agents():
    """Create and deploy the agents using our script"""
    print("\n" + "="*60)
    print("🤖 Creating and deploying agents...")
    print("="*60)
    
    # Import and run the agent creation script
    try:
        sys.path.insert(0, "/Users/<USER>/workspaces/git/ai-agent")
        from create_agents import main as create_agents_main
        
        return create_agents_main()
        
    except Exception as e:
        print(f"❌ Failed to run agent creation script: {e}")
        return 1

def cleanup_on_exit(process):
    """Cleanup function to stop backend when script exits"""
    if process:
        print("\n🛑 Stopping backend server...")
        try:
            # Kill the process group
            os.killpg(os.getpgid(process.pid), signal.SIGTERM)
            process.wait(timeout=10)
            print("✅ Backend stopped")
        except Exception as e:
            print(f"⚠️  Error stopping backend: {e}")

def main():
    print("🚀 AI Agent Platform - Complete Setup and Deployment")
    print("=" * 60)
    
    backend_process = None
    
    try:
        # Check requirements
        if not check_requirements():
            return 1
        
        # Check Docker (optional but recommended)
        docker_available = check_docker()
        if not docker_available:
            print("⚠️  Docker is not available. Agent deployment may fail.")
            response = input("Continue anyway? (y/N): ")
            if response.lower() != 'y':
                return 1
        
        # Start backend if not running
        if not is_backend_running():
            backend_process = start_backend()
            if not backend_process:
                return 1
        
        # Create and deploy agents
        result = create_and_deploy_agents()
        
        if result == 0:
            print("\n🎉 SUCCESS! Both agents have been created and deployed.")
            print("\n📋 Next steps:")
            print("1. The backend server is running at http://localhost:8000")
            print("2. Check the agent frontend URLs printed above")
            print("3. Test the functionality of both calculators")
            print("4. The backend will continue running until you stop this script (Ctrl+C)")
            
            if backend_process:
                print("\n⏳ Press Ctrl+C to stop the backend server and exit...")
                try:
                    backend_process.wait()
                except KeyboardInterrupt:
                    pass
        else:
            print("\n❌ Agent creation/deployment failed. Check the errors above.")
            
        return result
        
    except KeyboardInterrupt:
        print("\n\n🛑 Interrupted by user")
        return 0
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1
    finally:
        cleanup_on_exit(backend_process)

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)