#!/usr/bin/env python3
"""
Test deployed agent interface functionality
"""

import requests
import json

BASE_URL = 'http://localhost:8000'

def main():
    print('🧪 Testing Deployed Agent Interface Fix')
    print('=' * 50)

    # Step 1: Create a test agent
    print('1️⃣ Creating test agent...')
    agent_data = {
        'name': 'Interface Test Agent',
        'description': 'Testing deployed agent interface functionality',
        'type': 'assistant',
        'config': {},
        'capabilities': ['conversation', 'natural_language']
    }

    try:
        response = requests.post(f'{BASE_URL}/api/v1/public/agents', json=agent_data, timeout=10)
        response.raise_for_status()
        agent = response.json()
        agent_id = agent['id']
        print(f'✅ Agent created: {agent["name"]} (ID: {agent_id})')
    except Exception as e:
        print(f'❌ Failed to create agent: {e}')
        return False

    # Step 2: Deploy the agent
    print('\n2️⃣ Deploying agent...')
    try:
        response = requests.post(f'{BASE_URL}/api/v1/public/agents/{agent_id}/deploy', timeout=10)
        response.raise_for_status()
        deployment = response.json()
        print(f'✅ Agent deployed: {deployment["deployment_url"]}')
    except Exception as e:
        print(f'❌ Failed to deploy agent: {e}')
        return False

    # Step 3: Test the deployed agent interface
    print('\n3️⃣ Testing deployed agent interface...')
    try:
        response = requests.get(f'{BASE_URL}/api/v1/public/deployed/{agent_id}', timeout=10)
        response.raise_for_status()
        interface_data = response.json()
        print(f'✅ Interface accessible!')
        print(f'   Title: {interface_data["interface"]["title"]}')
        print(f'   Status: {interface_data["interface"]["status"]}')
        print(f'   Health: {interface_data["interface"]["health"]}')
        print(f'   Endpoints: {len(interface_data["interface"]["endpoints"])}')
    except Exception as e:
        print(f'❌ Failed to get interface: {e}')
        return False

    # Step 4: Test chat functionality
    print('\n4️⃣ Testing chat functionality...')
    try:
        chat_data = {'message': 'Hello, can you help me?'}
        response = requests.post(f'{BASE_URL}/api/v1/public/deployed/{agent_id}/chat', json=chat_data, timeout=10)
        response.raise_for_status()
        chat_response = response.json()
        print(f'✅ Chat working!')
        print(f'   Agent: {chat_response["agent_name"]}')
        print(f'   Response: {chat_response["response"][:100]}...')
    except Exception as e:
        print(f'❌ Failed to chat: {e}')
        return False

    print(f'\n🎯 Summary:')
    print(f'✅ Agent creation: Working')
    print(f'✅ Agent deployment: Working') 
    print(f'✅ Deployed interface: Working')
    print(f'✅ Chat functionality: Working')
    print(f'\n🌐 Frontend URL to test:')
    print(f'   http://localhost:3000/deployed/{agent_id}')
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print('\n🎉 All tests passed! The deployed agent interface is working.')
    else:
        print('\n❌ Some tests failed. Check the errors above.')
