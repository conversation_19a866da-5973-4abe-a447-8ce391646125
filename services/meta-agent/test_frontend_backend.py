#!/usr/bin/env python3
"""
Test frontend-backend connectivity and agent creation flow
"""

import requests
import json
import time
from datetime import datetime

def test_frontend_backend_connectivity():
    """Test that frontend can communicate with backend"""
    
    print("🔗 Testing Frontend-Backend Connectivity")
    print("=" * 50)
    
    # Test backend is running
    try:
        backend_response = requests.get("http://localhost:8000/health", timeout=5)
        print(f"✅ Backend: {backend_response.status_code} - {backend_response.json()}")
    except Exception as e:
        print(f"❌ Backend error: {e}")
        return False
    
    # Test frontend is running
    try:
        frontend_response = requests.get("http://localhost:3000", timeout=5)
        print(f"✅ Frontend: {frontend_response.status_code}")
    except Exception as e:
        print(f"❌ Frontend error: {e}")
        return False
    
    # Test CORS by simulating frontend API call
    try:
        headers = {
            'Origin': 'http://localhost:3000',
            'Content-Type': 'application/json'
        }
        
        # Test system stats (dashboard endpoint)
        stats_response = requests.get(
            "http://localhost:8000/api/v1/public/runtime/system/stats",
            headers=headers,
            timeout=5
        )
        print(f"✅ CORS Test (System Stats): {stats_response.status_code}")
        
        # Test agent creation
        agent_data = {
            "name": "Frontend Test Agent",
            "description": "Testing frontend-backend connectivity",
            "type": "assistant",
            "config": {},
            "capabilities": ["conversation"]
        }
        
        create_response = requests.post(
            "http://localhost:8000/api/v1/public/agents",
            json=agent_data,
            headers=headers,
            timeout=5
        )
        print(f"✅ CORS Test (Agent Creation): {create_response.status_code}")
        
        if create_response.status_code == 200:
            agent = create_response.json()
            print(f"   Created agent: {agent['name']} (ID: {agent['id']})")
            return True
        
    except Exception as e:
        print(f"❌ CORS test error: {e}")
        return False
    
    return True

def test_specific_endpoints():
    """Test specific endpoints that dashboard and agent creation use"""
    
    print(f"\n🎯 Testing Specific Dashboard Endpoints")
    print("-" * 50)
    
    endpoints = [
        ("System Stats", "GET", "http://localhost:8000/api/v1/public/runtime/system/stats"),
        ("System Health", "GET", "http://localhost:8000/api/v1/public/runtime/system/health"),
        ("List Agents", "GET", "http://localhost:8000/api/v1/public/agents"),
        ("Active Agents", "GET", "http://localhost:8000/api/v1/runtime/agents/active/test"),
    ]
    
    for name, method, url in endpoints:
        try:
            if method == "GET":
                response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {name}: OK")
                
                # Show key data for debugging
                if name == "System Stats":
                    print(f"   Total agents: {data.get('total_agents', 0)}")
                    print(f"   Active agents: {data.get('active_agents', 0)}")
                elif name == "List Agents":
                    print(f"   Agents found: {data.get('total', 0)}")
                elif name == "System Health":
                    print(f"   Status: {data.get('status', 'unknown')}")
                    
            else:
                print(f"❌ {name}: {response.status_code} - {response.text[:100]}")
                
        except Exception as e:
            print(f"❌ {name}: Exception - {e}")

def main():
    print("🚀 Frontend-Backend Connectivity Test")
    print("=" * 60)
    
    # Test basic connectivity
    connectivity_ok = test_frontend_backend_connectivity()
    
    # Test specific endpoints
    test_specific_endpoints()
    
    print(f"\n📋 Summary")
    print("-" * 30)
    
    if connectivity_ok:
        print("✅ Frontend-Backend connectivity is working!")
        print("✅ CORS is configured correctly")
        print("✅ Agent creation API is functional")
        print("\n💡 If you're still seeing errors in the browser:")
        print("   1. Check browser console for JavaScript errors")
        print("   2. Verify API calls in Network tab")
        print("   3. Check for TypeScript compilation errors")
        print("   4. Refresh the browser pages")
    else:
        print("❌ There are connectivity issues")
        print("💡 Check:")
        print("   1. Both frontend and backend are running")
        print("   2. No firewall blocking ports 3000/8000")
        print("   3. CORS configuration in backend")

if __name__ == "__main__":
    main()
