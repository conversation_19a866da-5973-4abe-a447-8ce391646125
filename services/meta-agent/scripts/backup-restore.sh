#!/bin/bash

# AI Agent Platform - Backup & Restore Script
# This script provides backup and restore functionality for the platform

set -e

echo "💾 AI Agent Platform - Backup & Restore"
echo "==========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
BACKUP_DIR="${BACKUP_DIR:-./backups}"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="ai-platform-backup-${TIMESTAMP}"
COMPOSE_FILE="docker-compose.prod.yml"
ENV_FILE=".env.prod"
RETENTION_DAYS=30

# Parse arguments
COMMAND="$1"
BACKUP_FILE="$2"

show_help() {
    echo "Usage: $0 <command> [options]"
    echo
    echo "Commands:"
    echo "  backup                   Create a full backup"
    echo "  restore <backup-file>    Restore from backup file"
    echo "  list                     List available backups"
    echo "  cleanup                  Remove old backups"
    echo "  export                   Export configuration only"
    echo "  import <config-file>     Import configuration"
    echo
    echo "Options:"
    echo "  --backup-dir DIR        Backup directory (default: ./backups)"
    echo "  --retention-days DAYS   Backup retention in days (default: 30)"
    echo "  --include-logs          Include logs in backup"
    echo "  --exclude-data          Exclude user data"
    echo
    echo "Examples:"
    echo "  $0 backup --include-logs"
    echo "  $0 restore ai-platform-backup-20241122_143022.tar.gz"
    echo "  $0 cleanup --retention-days 7"
}

# Create backup directory
ensure_backup_dir() {
    mkdir -p "$BACKUP_DIR"
    print_status "Backup directory: $BACKUP_DIR"
}

# Backup database
backup_database() {
    print_status "Backing up PostgreSQL database..."
    
    if docker-compose -f "$COMPOSE_FILE" ps postgres | grep -q "Up"; then
        docker-compose -f "$COMPOSE_FILE" exec -T postgres pg_dump \
            -U "${DB_USER:-postgres}" \
            -d ai_agent_platform \
            --no-owner --no-privileges \
            > "${BACKUP_DIR}/${BACKUP_NAME}/database.sql"
        print_success "Database backup completed"
    else
        print_warning "PostgreSQL container not running, skipping database backup"
    fi
}

# Backup Redis data
backup_redis() {
    print_status "Backing up Redis data..."
    
    if docker-compose -f "$COMPOSE_FILE" ps redis | grep -q "Up"; then
        docker-compose -f "$COMPOSE_FILE" exec -T redis redis-cli \
            --rdb /data/dump.rdb BGSAVE
        
        # Wait for background save to complete
        sleep 2
        
        docker cp $(docker-compose -f "$COMPOSE_FILE" ps -q redis):/data/dump.rdb \
            "${BACKUP_DIR}/${BACKUP_NAME}/redis-dump.rdb"
        print_success "Redis backup completed"
    else
        print_warning "Redis container not running, skipping Redis backup"
    fi
}

# Backup application files
backup_application() {
    print_status "Backing up application files..."
    
    # Create application backup directory
    mkdir -p "${BACKUP_DIR}/${BACKUP_NAME}/app"
    
    # Backup configuration files
    cp -r backend/src "${BACKUP_DIR}/${BACKUP_NAME}/app/backend-src" 2>/dev/null || true
    cp -r frontend/src "${BACKUP_DIR}/${BACKUP_NAME}/app/frontend-src" 2>/dev/null || true
    
    # Backup data directories
    if [[ -d "data" ]]; then
        cp -r data "${BACKUP_DIR}/${BACKUP_NAME}/app/" 2>/dev/null || true
    fi
    
    # Backup Docker configurations
    cp docker-compose*.yml "${BACKUP_DIR}/${BACKUP_NAME}/app/" 2>/dev/null || true
    cp backend/Dockerfile "${BACKUP_DIR}/${BACKUP_NAME}/app/backend-dockerfile" 2>/dev/null || true
    cp frontend/Dockerfile "${BACKUP_DIR}/${BACKUP_NAME}/app/frontend-dockerfile" 2>/dev/null || true
    
    # Backup Kubernetes manifests
    if [[ -d "k8s" ]]; then
        cp -r k8s "${BACKUP_DIR}/${BACKUP_NAME}/app/" 2>/dev/null || true
    fi
    
    # Backup monitoring configs
    if [[ -d "monitoring" ]]; then
        cp -r monitoring "${BACKUP_DIR}/${BACKUP_NAME}/app/" 2>/dev/null || true
    fi
    
    print_success "Application files backup completed"
}

# Backup logs
backup_logs() {
    if [[ "$INCLUDE_LOGS" == "true" ]]; then
        print_status "Backing up logs..."
        
        if [[ -d "logs" ]]; then
            cp -r logs "${BACKUP_DIR}/${BACKUP_NAME}/" 2>/dev/null || true
            print_success "Logs backup completed"
        else
            print_warning "No logs directory found"
        fi
    fi
}

# Create configuration backup
backup_configuration() {
    print_status "Backing up configuration..."
    
    mkdir -p "${BACKUP_DIR}/${BACKUP_NAME}/config"
    
    # Backup environment files (excluding sensitive data)
    if [[ -f "$ENV_FILE" ]]; then
        # Create sanitized version without secrets
        grep -v -E '(PASSWORD|SECRET|KEY)=' "$ENV_FILE" > "${BACKUP_DIR}/${BACKUP_NAME}/config/env-template" || true
        echo "# Secrets removed for security" >> "${BACKUP_DIR}/${BACKUP_NAME}/config/env-template"
    fi
    
    # Backup nginx config
    if [[ -d "nginx" ]]; then
        cp -r nginx "${BACKUP_DIR}/${BACKUP_NAME}/config/" 2>/dev/null || true
    fi
    
    print_success "Configuration backup completed"
}

# Create full backup
perform_backup() {
    print_status "Starting full backup: $BACKUP_NAME"
    
    ensure_backup_dir
    
    # Create backup directory
    mkdir -p "${BACKUP_DIR}/${BACKUP_NAME}"
    
    # Create backup manifest
    cat > "${BACKUP_DIR}/${BACKUP_NAME}/manifest.json" << EOF
{
  "backup_name": "$BACKUP_NAME",
  "timestamp": "$TIMESTAMP",
  "platform_version": "2.0.0",
  "backup_type": "full",
  "components": {
    "database": true,
    "redis": true,
    "application": true,
    "configuration": true,
    "logs": ${INCLUDE_LOGS:-false}
  },
  "created_by": "$(whoami)",
  "hostname": "$(hostname)"
}
EOF
    
    # Perform backups
    backup_database
    backup_redis
    backup_application
    backup_configuration
    backup_logs
    
    # Create compressed archive
    print_status "Creating compressed archive..."
    cd "$BACKUP_DIR"
    tar -czf "${BACKUP_NAME}.tar.gz" "$BACKUP_NAME"/
    rm -rf "$BACKUP_NAME"
    cd - > /dev/null
    
    print_success "Backup completed: ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
    
    # Show backup size
    if command -v du &> /dev/null; then
        BACKUP_SIZE=$(du -h "${BACKUP_DIR}/${BACKUP_NAME}.tar.gz" | cut -f1)
        print_status "Backup size: $BACKUP_SIZE"
    fi
}

# Restore from backup
perform_restore() {
    if [[ -z "$BACKUP_FILE" ]]; then
        print_error "Backup file not specified"
        exit 1
    fi
    
    if [[ ! -f "$BACKUP_FILE" ]]; then
        print_error "Backup file not found: $BACKUP_FILE"
        exit 1
    fi
    
    print_warning "This will restore the platform from backup and may overwrite existing data"
    read -p "Are you sure? (yes/no): " confirm
    
    if [[ "$confirm" != "yes" ]]; then
        print_status "Restore cancelled"
        exit 0
    fi
    
    print_status "Starting restore from: $BACKUP_FILE"
    
    # Extract backup
    RESTORE_DIR="$(mktemp -d)"
    tar -xzf "$BACKUP_FILE" -C "$RESTORE_DIR"
    
    BACKUP_DIR_NAME=$(basename "$BACKUP_FILE" .tar.gz)
    RESTORE_PATH="${RESTORE_DIR}/${BACKUP_DIR_NAME}"
    
    if [[ ! -d "$RESTORE_PATH" ]]; then
        print_error "Invalid backup file structure"
        rm -rf "$RESTORE_DIR"
        exit 1
    fi
    
    # Stop services
    print_status "Stopping services..."
    docker-compose -f "$COMPOSE_FILE" down || true
    
    # Restore database
    if [[ -f "${RESTORE_PATH}/database.sql" ]]; then
        print_status "Restoring database..."
        
        # Start only database
        docker-compose -f "$COMPOSE_FILE" up -d postgres
        sleep 10
        
        # Restore database
        docker-compose -f "$COMPOSE_FILE" exec -T postgres psql \
            -U "${DB_USER:-postgres}" \
            -d ai_agent_platform \
            < "${RESTORE_PATH}/database.sql"
        
        print_success "Database restored"
    fi
    
    # Restore Redis
    if [[ -f "${RESTORE_PATH}/redis-dump.rdb" ]]; then
        print_status "Restoring Redis data..."
        
        # Start Redis
        docker-compose -f "$COMPOSE_FILE" up -d redis
        sleep 5
        
        # Copy Redis dump
        docker cp "${RESTORE_PATH}/redis-dump.rdb" \
            $(docker-compose -f "$COMPOSE_FILE" ps -q redis):/data/dump.rdb
        
        # Restart Redis to load data
        docker-compose -f "$COMPOSE_FILE" restart redis
        
        print_success "Redis data restored"
    fi
    
    # Restore application files
    if [[ -d "${RESTORE_PATH}/app" ]]; then
        print_status "Restoring application files..."
        
        # Restore configurations
        cp "${RESTORE_PATH}"/app/docker-compose*.yml . 2>/dev/null || true
        cp "${RESTORE_PATH}"/app/*-dockerfile backend/Dockerfile 2>/dev/null || true
        cp "${RESTORE_PATH}"/app/*-dockerfile frontend/Dockerfile 2>/dev/null || true
        
        # Restore Kubernetes manifests
        if [[ -d "${RESTORE_PATH}/app/k8s" ]]; then
            cp -r "${RESTORE_PATH}/app/k8s" . 2>/dev/null || true
        fi
        
        # Restore monitoring configs
        if [[ -d "${RESTORE_PATH}/app/monitoring" ]]; then
            cp -r "${RESTORE_PATH}/app/monitoring" . 2>/dev/null || true
        fi
        
        print_success "Application files restored"
    fi
    
    # Restore configuration
    if [[ -d "${RESTORE_PATH}/config" ]]; then
        print_status "Restoring configuration..."
        
        # Restore nginx config
        if [[ -d "${RESTORE_PATH}/config/nginx" ]]; then
            cp -r "${RESTORE_PATH}/config/nginx" . 2>/dev/null || true
        fi
        
        print_success "Configuration restored"
        print_warning "Remember to update environment variables in $ENV_FILE"
    fi
    
    # Start all services
    print_status "Starting all services..."
    docker-compose -f "$COMPOSE_FILE" up -d
    
    # Cleanup
    rm -rf "$RESTORE_DIR"
    
    print_success "Restore completed successfully!"
    print_status "Please verify that all services are running correctly"
}

# List available backups
list_backups() {
    print_status "Available backups in $BACKUP_DIR:"
    
    if [[ ! -d "$BACKUP_DIR" ]] || [[ -z "$(ls -A "$BACKUP_DIR" 2>/dev/null)" ]]; then
        print_warning "No backups found"
        return
    fi
    
    echo
    printf "%-40s %-15s %-10s\n" "Backup File" "Date" "Size"
    echo "--------------------------------------------------------------------"
    
    for backup in "$BACKUP_DIR"/*.tar.gz; do
        if [[ -f "$backup" ]]; then
            filename=$(basename "$backup")
            if command -v stat &> /dev/null; then
                if [[ "$(uname)" == "Darwin" ]]; then
                    date=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M" "$backup")
                else
                    date=$(stat -c "%y" "$backup" | cut -d' ' -f1,2 | cut -d':' -f1,2)
                fi
            else
                date="Unknown"
            fi
            
            if command -v du &> /dev/null; then
                size=$(du -h "$backup" | cut -f1)
            else
                size="Unknown"
            fi
            
            printf "%-40s %-15s %-10s\n" "$filename" "$date" "$size"
        fi
    done
    echo
}

# Cleanup old backups
cleanup_backups() {
    print_status "Cleaning up backups older than $RETENTION_DAYS days..."
    
    if [[ ! -d "$BACKUP_DIR" ]]; then
        print_warning "Backup directory does not exist"
        return
    fi
    
    # Find and remove old backups
    if command -v find &> /dev/null; then
        old_backups=$(find "$BACKUP_DIR" -name "*.tar.gz" -type f -mtime +"$RETENTION_DAYS" 2>/dev/null || true)
        
        if [[ -n "$old_backups" ]]; then
            echo "$old_backups" | while read -r backup; do
                if [[ -f "$backup" ]]; then
                    print_status "Removing old backup: $(basename "$backup")"
                    rm "$backup"
                fi
            done
            print_success "Cleanup completed"
        else
            print_status "No old backups to remove"
        fi
    else
        print_warning "find command not available, skipping cleanup"
    fi
}

# Export configuration only
export_configuration() {
    print_status "Exporting configuration..."
    
    CONFIG_BACKUP="config-export-${TIMESTAMP}"
    ensure_backup_dir
    mkdir -p "${BACKUP_DIR}/${CONFIG_BACKUP}"
    
    backup_configuration
    
    # Create compressed archive
    cd "$BACKUP_DIR"
    tar -czf "${CONFIG_BACKUP}.tar.gz" "$CONFIG_BACKUP"/
    rm -rf "$CONFIG_BACKUP"
    cd - > /dev/null
    
    print_success "Configuration exported: ${BACKUP_DIR}/${CONFIG_BACKUP}.tar.gz"
}

# Import configuration
import_configuration() {
    if [[ -z "$BACKUP_FILE" ]]; then
        print_error "Configuration file not specified"
        exit 1
    fi
    
    print_status "Importing configuration from: $BACKUP_FILE"
    
    # Extract and import logic would go here
    print_warning "Configuration import is not yet implemented"
}

# Parse additional arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --backup-dir)
            BACKUP_DIR="$2"
            shift 2
            ;;
        --retention-days)
            RETENTION_DAYS="$2"
            shift 2
            ;;
        --include-logs)
            INCLUDE_LOGS="true"
            shift
            ;;
        --exclude-data)
            EXCLUDE_DATA="true"
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        -*)
            # Skip unknown options
            shift
            ;;
        *)
            # Skip non-option arguments
            shift
            ;;
    esac
done

# Main command handling
case "$COMMAND" in
    backup)
        perform_backup
        ;;
    restore)
        perform_restore
        ;;
    list)
        list_backups
        ;;
    cleanup)
        cleanup_backups
        ;;
    export)
        export_configuration
        ;;
    import)
        import_configuration
        ;;
    -h|--help|"")
        show_help
        ;;
    *)
        print_error "Unknown command: $COMMAND"
        show_help
        exit 1
        ;;
esac