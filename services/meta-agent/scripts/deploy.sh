#!/bin/bash

# AI Agent Platform - Production Deployment Script
# This script handles production deployment with Docker and Kubernetes support

set -e

echo "🚀 AI Agent Platform - Production Deployment"
echo "============================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default configuration
ENVIRONMENT="production"
BUILD_IMAGES="true"
PUSH_IMAGES="false"
DEPLOY_METHOD="docker-compose"  # or "kubernetes"
REGISTRY=""
IMAGE_TAG="latest"
NAMESPACE="ai-platform"
DOMAIN=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --no-build)
            BUILD_IMAGES="false"
            shift
            ;;
        --push-images)
            PUSH_IMAGES="true"
            shift
            ;;
        -m|--method)
            DEPLOY_METHOD="$2"
            shift 2
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        -t|--tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -d|--domain)
            DOMAIN="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -e, --environment ENV    Environment (production, staging, development)"
            echo "  --no-build              Skip building Docker images"
            echo "  --push-images           Push images to registry"
            echo "  -m, --method METHOD     Deployment method (docker-compose, kubernetes)"
            echo "  -r, --registry URL      Container registry URL"
            echo "  -t, --tag TAG           Image tag (default: latest)"
            echo "  -n, --namespace NS      Kubernetes namespace"
            echo "  -d, --domain DOMAIN     Application domain"
            echo "  -h, --help              Show this help"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_status "Deployment Configuration:"
print_status "Environment: $ENVIRONMENT"
print_status "Build Images: $BUILD_IMAGES"
print_status "Push Images: $PUSH_IMAGES"
print_status "Deploy Method: $DEPLOY_METHOD"
print_status "Registry: ${REGISTRY:-"(none)"}"
print_status "Image Tag: $IMAGE_TAG"
print_status "Namespace: $NAMESPACE"
print_status "Domain: ${DOMAIN:-"(none)"}"

# Check requirements
check_requirements() {
    print_status "Checking deployment requirements..."
    
    if [[ "$BUILD_IMAGES" == "true" ]] && ! command -v docker &> /dev/null; then
        print_error "Docker is required for building images"
        exit 1
    fi
    
    if [[ "$DEPLOY_METHOD" == "kubernetes" ]] && ! command -v kubectl &> /dev/null; then
        print_error "kubectl is required for Kubernetes deployment"
        exit 1
    fi
    
    if [[ "$DEPLOY_METHOD" == "docker-compose" ]] && ! command -v docker-compose &> /dev/null; then
        if ! docker compose version &> /dev/null; then
            print_error "docker-compose or 'docker compose' is required"
            exit 1
        fi
    fi
    
    print_success "Requirements check passed"
}

# Build Docker images
build_images() {
    if [[ "$BUILD_IMAGES" != "true" ]]; then
        print_status "Skipping image build"
        return
    fi
    
    print_status "Building Docker images..."
    
    # Build backend image
    print_status "Building backend image..."
    docker build -t ai-platform-backend:${IMAGE_TAG} \
        --build-arg ENVIRONMENT=${ENVIRONMENT} \
        -f backend/Dockerfile backend/
    
    if [[ -n "$REGISTRY" ]]; then
        docker tag ai-platform-backend:${IMAGE_TAG} ${REGISTRY}/ai-platform-backend:${IMAGE_TAG}
    fi
    
    # Build frontend image
    print_status "Building frontend image..."
    docker build -t ai-platform-frontend:${IMAGE_TAG} \
        --build-arg NEXT_PUBLIC_API_URL=${API_URL:-"https://api.${DOMAIN:-"localhost"}"} \
        --build-arg ENVIRONMENT=${ENVIRONMENT} \
        -f frontend/Dockerfile frontend/
    
    if [[ -n "$REGISTRY" ]]; then
        docker tag ai-platform-frontend:${IMAGE_TAG} ${REGISTRY}/ai-platform-frontend:${IMAGE_TAG}
    fi
    
    print_success "Images built successfully"
}

# Push images to registry
push_images() {
    if [[ "$PUSH_IMAGES" != "true" ]] || [[ -z "$REGISTRY" ]]; then
        print_status "Skipping image push"
        return
    fi
    
    print_status "Pushing images to registry..."
    
    docker push ${REGISTRY}/ai-platform-backend:${IMAGE_TAG}
    docker push ${REGISTRY}/ai-platform-frontend:${IMAGE_TAG}
    
    print_success "Images pushed successfully"
}

# Create production Docker Compose configuration
create_docker_compose() {
    print_status "Creating production docker-compose.yml..."
    
    cat > docker-compose.prod.yml << EOF
version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: ai-platform-postgres-prod
    environment:
      POSTGRES_DB: ai_agent_platform
      POSTGRES_USER: \${DB_USER:-postgres}
      POSTGRES_PASSWORD: \${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    restart: unless-stopped
    networks:
      - ai-platform
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U \${DB_USER:-postgres}"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: ai-platform-redis-prod
    command: redis-server --requirepass \${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - ai-platform
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  backend:
    image: ${REGISTRY:+${REGISTRY}/}ai-platform-backend:${IMAGE_TAG}
    container_name: ai-platform-backend-prod
    environment:
      DATABASE_URL: postgresql://\${DB_USER:-postgres}:\${DB_PASSWORD}@postgres:5432/ai_agent_platform
      REDIS_URL: redis://:\${REDIS_PASSWORD}@redis:6379
      SECRET_KEY: \${SECRET_KEY}
      JWT_SECRET_KEY: \${JWT_SECRET_KEY}
      OPENAI_API_KEY: \${OPENAI_API_KEY}
      ANTHROPIC_API_KEY: \${ANTHROPIC_API_KEY}
      GOOGLE_API_KEY: \${GOOGLE_API_KEY}
      ENVIRONMENT: ${ENVIRONMENT}
      LOG_LEVEL: info
      CORS_ORIGINS: https://\${DOMAIN:-localhost}
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - ai-platform
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    image: ${REGISTRY:+${REGISTRY}/}ai-platform-frontend:${IMAGE_TAG}
    container_name: ai-platform-frontend-prod
    environment:
      NEXT_PUBLIC_API_URL: https://\${DOMAIN:-localhost}
      ENVIRONMENT: ${ENVIRONMENT}
    ports:
      - "3000:3000"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - ai-platform
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    container_name: ai-platform-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    networks:
      - ai-platform

  prometheus:
    image: prom/prometheus:latest
    container_name: ai-platform-prometheus-prod
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - ai-platform

  grafana:
    image: grafana/grafana:latest
    container_name: ai-platform-grafana-prod
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: \${GRAFANA_PASSWORD}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    restart: unless-stopped
    networks:
      - ai-platform

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  ai-platform:
    driver: bridge
EOF
    
    print_success "Docker Compose configuration created"
}

# Create Nginx configuration
create_nginx_config() {
    print_status "Creating Nginx configuration..."
    
    mkdir -p nginx
    
    cat > nginx/nginx.conf << EOF
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:8000;
    }
    
    upstream frontend {
        server frontend:3000;
    }
    
    # Rate limiting
    limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone \$binary_remote_addr zone=general:10m rate=50r/s;
    
    server {
        listen 80;
        server_name ${DOMAIN:-localhost};
        
        # Redirect HTTP to HTTPS in production
        return 301 https://\$host\$request_uri;
    }
    
    server {
        listen 443 ssl http2;
        server_name ${DOMAIN:-localhost};
        
        # SSL Configuration (add your SSL certificates)
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_session_cache shared:le_nginx_SSL:10m;
        ssl_session_timeout 1440m;
        ssl_session_tickets off;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_prefer_server_ciphers off;
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
        
        # API routes
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://backend;
            proxy_set_header Host \$http_host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
            proxy_connect_timeout 300;
            proxy_send_timeout 300;
            proxy_read_timeout 300;
            send_timeout 300;
        }
        
        # WebSocket support
        location /ws/ {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade \$http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host \$http_host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }
        
        # Frontend routes
        location / {
            limit_req zone=general burst=100 nodelay;
            proxy_pass http://frontend;
            proxy_set_header Host \$http_host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }
        
        # Static files caching
        location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
EOF
    
    print_success "Nginx configuration created"
}

# Create environment file template
create_env_template() {
    print_status "Creating production environment template..."
    
    cat > .env.prod.template << EOF
# AI Agent Platform - Production Environment Variables
# Copy this to .env.prod and fill in your actual values

# Database
DB_USER=postgres
DB_PASSWORD=your_secure_db_password_here

# Redis
REDIS_PASSWORD=your_secure_redis_password_here

# Security
SECRET_KEY=your_very_secure_secret_key_here
JWT_SECRET_KEY=your_jwt_secret_key_here

# API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here

# Application
DOMAIN=your-domain.com

# Monitoring
GRAFANA_PASSWORD=your_grafana_admin_password_here

# Optional: Container Registry
REGISTRY_USERNAME=your_registry_username
REGISTRY_PASSWORD=your_registry_password
EOF
    
    print_warning "Created .env.prod.template - copy to .env.prod and configure!"
}

# Deploy with Docker Compose
deploy_docker_compose() {
    print_status "Deploying with Docker Compose..."
    
    if [[ ! -f ".env.prod" ]]; then
        print_error ".env.prod file not found. Copy from .env.prod.template and configure!"
        exit 1
    fi
    
    # Stop existing deployment
    print_status "Stopping existing services..."
    docker-compose -f docker-compose.prod.yml --env-file .env.prod down || true
    
    # Start new deployment
    print_status "Starting services..."
    docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d
    
    # Wait for services to be healthy
    print_status "Waiting for services to be healthy..."
    sleep 30
    
    # Check service health
    print_status "Checking service health..."
    if docker-compose -f docker-compose.prod.yml --env-file .env.prod ps | grep -q "unhealthy"; then
        print_error "Some services are unhealthy!"
        docker-compose -f docker-compose.prod.yml --env-file .env.prod ps
        exit 1
    fi
    
    print_success "Docker Compose deployment completed!"
}

# Create Kubernetes manifests
create_kubernetes_manifests() {
    print_status "Creating Kubernetes manifests..."
    
    mkdir -p k8s
    
    # Namespace
    cat > k8s/namespace.yaml << EOF
apiVersion: v1
kind: Namespace
metadata:
  name: ${NAMESPACE}
  labels:
    name: ${NAMESPACE}
EOF
    
    # PostgreSQL
    cat > k8s/postgres.yaml << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-config
  namespace: ${NAMESPACE}
data:
  POSTGRES_DB: ai_agent_platform
  POSTGRES_USER: postgres

---
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secret
  namespace: ${NAMESPACE}
type: Opaque
data:
  POSTGRES_PASSWORD: cG9zdGdyZXM=  # base64 encoded 'postgres'

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: ${NAMESPACE}
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: ${NAMESPACE}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15
        ports:
        - containerPort: 5432
        envFrom:
        - configMapRef:
            name: postgres-config
        - secretRef:
            name: postgres-secret
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: ${NAMESPACE}
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
EOF
    
    # Backend deployment
    cat > k8s/backend.yaml << EOF
apiVersion: v1
kind: Secret
metadata:
  name: backend-secret
  namespace: ${NAMESPACE}
type: Opaque
data:
  SECRET_KEY: eW91cl9zZWNyZXRfa2V5X2hlcmU=
  JWT_SECRET_KEY: eW91cl9qd3Rfc2VjcmV0X2hlcmU=
  OPENAI_API_KEY: eW91cl9vcGVuYWlfa2V5X2hlcmU=

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: ${NAMESPACE}
spec:
  replicas: 3
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
      - name: backend
        image: ${REGISTRY:+${REGISTRY}/}ai-platform-backend:${IMAGE_TAG}
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          value: ****************************************************/ai_agent_platform
        - name: ENVIRONMENT
          value: production
        envFrom:
        - secretRef:
            name: backend-secret
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: ${NAMESPACE}
spec:
  selector:
    app: backend
  ports:
  - port: 8000
    targetPort: 8000
EOF
    
    # Frontend deployment
    cat > k8s/frontend.yaml << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
  namespace: ${NAMESPACE}
spec:
  replicas: 2
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
    spec:
      containers:
      - name: frontend
        image: ${REGISTRY:+${REGISTRY}/}ai-platform-frontend:${IMAGE_TAG}
        ports:
        - containerPort: 3000
        env:
        - name: NEXT_PUBLIC_API_URL
          value: https://${DOMAIN:-localhost}

---
apiVersion: v1
kind: Service
metadata:
  name: frontend-service
  namespace: ${NAMESPACE}
spec:
  selector:
    app: frontend
  ports:
  - port: 3000
    targetPort: 3000
EOF
    
    # Ingress
    if [[ -n "$DOMAIN" ]]; then
        cat > k8s/ingress.yaml << EOF
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-platform-ingress
  namespace: ${NAMESPACE}
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/rate-limit: "100"
spec:
  tls:
  - hosts:
    - ${DOMAIN}
    secretName: ai-platform-tls
  rules:
  - host: ${DOMAIN}
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 8000
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend-service
            port:
              number: 3000
EOF
    fi
    
    print_success "Kubernetes manifests created"
}

# Deploy to Kubernetes
deploy_kubernetes() {
    print_status "Deploying to Kubernetes..."
    
    # Apply manifests
    kubectl apply -f k8s/namespace.yaml
    kubectl apply -f k8s/postgres.yaml
    kubectl apply -f k8s/backend.yaml
    kubectl apply -f k8s/frontend.yaml
    
    if [[ -f "k8s/ingress.yaml" ]]; then
        kubectl apply -f k8s/ingress.yaml
    fi
    
    # Wait for deployment
    print_status "Waiting for deployments to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/backend -n ${NAMESPACE}
    kubectl wait --for=condition=available --timeout=300s deployment/frontend -n ${NAMESPACE}
    
    print_success "Kubernetes deployment completed!"
    
    # Show status
    print_status "Deployment status:"
    kubectl get all -n ${NAMESPACE}
}

# Main deployment function
main() {
    check_requirements
    build_images
    push_images
    
    if [[ "$DEPLOY_METHOD" == "docker-compose" ]]; then
        create_docker_compose
        create_nginx_config
        create_env_template
        deploy_docker_compose
        
        print_success "🎉 Deployment completed!"
        if [[ -n "$DOMAIN" ]]; then
            echo "Application URL: https://${DOMAIN}"
        else
            echo "Application URL: http://localhost"
        fi
        echo "API URL: http://localhost:8000"
        echo "Grafana URL: http://localhost:3001"
        
    elif [[ "$DEPLOY_METHOD" == "kubernetes" ]]; then
        create_kubernetes_manifests
        deploy_kubernetes
        
        print_success "🎉 Kubernetes deployment completed!"
        if [[ -n "$DOMAIN" ]]; then
            echo "Application URL: https://${DOMAIN}"
        else
            echo "Use kubectl port-forward to access services"
        fi
        
    else
        print_error "Unknown deployment method: $DEPLOY_METHOD"
        exit 1
    fi
    
    echo
    echo "Deployment logs:"
    echo "- Docker: docker-compose logs -f"
    echo "- Kubernetes: kubectl logs -f deployment/backend -n ${NAMESPACE}"
    echo
    echo "Health checks:"
    echo "- Backend: curl http://localhost:8000/health"
    echo "- Frontend: curl http://localhost:3000/api/health"
}

# Run main function
main "$@"