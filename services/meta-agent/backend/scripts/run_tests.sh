#!/bin/bash
set -euo pipefail

# Navigate to backend directory
cd "$(dirname "$0")/.."

echo "🧪 Running Meta-Agent Backend Tests..."

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    source venv/bin/activate
else
    echo "⚠️ Virtual environment not found. Run 'bazel run //services/meta-agent/backend:install' first"
    exit 1
fi

# Load environment variables for testing
if [ -f ".env" ]; then
    echo "🔧 Loading environment variables from .env"
    export $(cat .env | grep -v '^#' | xargs)
fi

# Set test environment
export ENVIRONMENT=test
export DATABASE_URL="sqlite:///./test_database.db"

# Run pytest with coverage
echo "🎯 Running pytest with coverage..."
pytest tests/ \
    --cov=src \
    --cov-report=term-missing \
    --cov-report=html:htmlcov \
    --verbose \
    -x

echo "✅ Tests completed successfully!"
echo "📊 Coverage report generated in htmlcov/ directory"