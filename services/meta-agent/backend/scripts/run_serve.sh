#!/bin/bash
set -euo pipefail

# Navigate to backend directory
cd "$(dirname "$0")/.."

echo "🚀 Starting Meta-Agent Backend Production Server..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install dependencies if needed
if [ ! -f "venv/installed.flag" ]; then
    echo "📦 Installing dependencies..."
    pip install --upgrade pip
    pip install -r requirements.txt
    pip install -e .
    touch venv/installed.flag
fi

# Load environment variables
if [ -f ".env" ]; then
    echo "🔧 Loading environment variables from .env"
    export $(cat .env | grep -v '^#' | xargs)
fi

# Set production environment
export ENVIRONMENT=production
export DEBUG=false

# Start FastAPI production server
echo "🏭 Starting FastAPI production server on http://localhost:8001"
uvicorn src.main:app --host 0.0.0.0 --port 8001 --workers 4 --log-level warning