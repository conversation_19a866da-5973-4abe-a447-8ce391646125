#!/bin/bash
set -euo pipefail

# Navigate to backend directory
cd "$(dirname "$0")/.."

echo "🗃️ Running Meta-Agent Backend Database Migrations..."

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    source venv/bin/activate
else
    echo "⚠️ Virtual environment not found. Run 'bazel run //services/meta-agent/backend:install' first"
    exit 1
fi

# Load environment variables
if [ -f ".env" ]; then
    echo "🔧 Loading environment variables from .env"
    export $(cat .env | grep -v '^#' | xargs)
fi

# Run Alembic migrations
echo "🏗️ Running Alembic database migrations..."
alembic upgrade head

echo "✅ Database migrations completed successfully!"