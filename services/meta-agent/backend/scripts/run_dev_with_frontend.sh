#!/bin/bash
set -euo pipefail

# Navigate to backend directory
cd "$(dirname "$0")/.."

echo "🚀 Starting Meta-Agent Backend with Frontend Integration..."

# Step 1: Generate OpenAPI specification
echo "1️⃣ Generating latest OpenAPI specification..."
./scripts/generate_openapi.sh

# Step 2: Check if frontend is running
echo "2️⃣ Checking frontend connectivity..."
if ! curl -f http://localhost:3000 2>/dev/null; then
    echo "⚠️ Frontend doesn't seem to be running on http://localhost:3000"
    echo "   Please start the frontend service first:"
    echo "   bazel run //services/meta-agent/frontend:dev"
    echo ""
    echo "   Or start both frontend and backend together:"
    echo "   bazel run //services/meta-agent:dev"
    echo ""
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Step 3: Start backend development server
echo "3️⃣ Starting backend development server..."
echo "🔥 Starting FastAPI dev server on http://localhost:8001"
echo "🔗 Frontend expected at http://localhost:3000"
echo "📚 API docs available at http://localhost:8001/docs"

# Start the backend with the development script
./scripts/run_dev.sh