#!/bin/bash
set -euo pipefail

# Navigate to backend directory
cd "$(dirname "$0")/.."

echo "🔄 Generating OpenAPI Specification..."

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    source venv/bin/activate
else
    echo "⚠️ Virtual environment not found. Run 'bazel run //services/meta-agent/backend:install' first"
    exit 1
fi

# Load environment variables
if [ -f ".env" ]; then
    echo "🔧 Loading environment variables from .env"
    export $(cat .env | grep -v '^#' | xargs)
fi

# Create openapi directory if it doesn't exist
mkdir -p openapi

# Generate OpenAPI specification
echo "🏗️ Generating OpenAPI spec from FastAPI application..."
if [ -f "generate_openapi_from_app.py" ]; then
    python generate_openapi_from_app.py
elif [ -f "openapi_generator.py" ]; then
    python openapi_generator.py
else
    echo "📝 Using built-in OpenAPI generation..."
    python -c "
import sys
sys.path.append('src')
from main import app
import json
import yaml

# Generate OpenAPI JSON
openapi_dict = app.openapi()

# Save as JSON
with open('openapi/openapi.json', 'w') as f:
    json.dump(openapi_dict, f, indent=2)
    
# Save as YAML
with open('openapi/openapi.yaml', 'w') as f:
    yaml.dump(openapi_dict, f, default_flow_style=False, sort_keys=False)

print('✅ OpenAPI specification generated successfully!')
print('📁 Files saved to openapi/openapi.json and openapi/openapi.yaml')
"
fi

echo "✅ OpenAPI specification generated successfully!"
echo "📁 Generated files are in the 'openapi' directory"