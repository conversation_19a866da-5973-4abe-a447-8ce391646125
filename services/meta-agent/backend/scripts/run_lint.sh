#!/bin/bash
set -euo pipefail

# Navigate to backend directory
cd "$(dirname "$0")/.."

echo "🔍 Running Meta-Agent Backend Code Analysis..."

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    source venv/bin/activate
else
    echo "⚠️ Virtual environment not found. Run 'bazel run //services/meta-agent/backend:install' first"
    exit 1
fi

# Check formatting with black
echo "🖤 Checking Black formatting..."
black --check src/ tests/ --line-length 88

# Check import sorting with isort
echo "📋 Checking import sorting..."
isort --check-only src/ tests/ --profile black

# Additional linting could be added here (flake8, pylint, etc.)
# echo "🔧 Running flake8..."
# flake8 src/ tests/

echo "✅ Code analysis completed successfully!"