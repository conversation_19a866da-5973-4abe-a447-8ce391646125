#!/bin/bash
set -euo pipefail

# Navigate to backend directory
cd "$(dirname "$0")/.."

echo "🗃️ Setting up Meta-Agent Backend Database..."

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    source venv/bin/activate
else
    echo "⚠️ Virtual environment not found. Run 'bazel run //services/meta-agent/backend:install' first"
    exit 1
fi

# Load environment variables
if [ -f ".env" ]; then
    echo "🔧 Loading environment variables from .env"
    export $(cat .env | grep -v '^#' | xargs)
fi

# Create database tables using the application
echo "🏗️ Creating database tables..."
python -c "
import sys
sys.path.append('src')
import asyncio
from database.connection import create_tables

async def setup():
    await create_tables()
    print('✅ Database tables created successfully!')

asyncio.run(setup())
"

echo "✅ Database setup completed successfully!"