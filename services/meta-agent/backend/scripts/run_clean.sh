#!/bin/bash
set -euo pipefail

# Navigate to backend directory
cd "$(dirname "$0")/.."

echo "🧹 Cleaning Meta-Agent Backend Build Artifacts..."

# Remove Python cache files
if [ -d "src/__pycache__" ]; then
    echo "🗑️ Removing Python __pycache__ directories..."
    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
fi

# Remove .pyc files
if find . -name "*.pyc" | grep -q .; then
    echo "🗑️ Removing .pyc files..."
    find . -name "*.pyc" -delete
fi

# Remove test databases
if [ -f "test_database.db" ]; then
    echo "🗑️ Removing test database..."
    rm -f test_database.db
fi

if [ -f "test_integration_database.db" ]; then
    echo "🗑️ Removing integration test database..."
    rm -f test_integration_database.db
fi

# Remove coverage reports
if [ -d "htmlcov" ]; then
    echo "🗑️ Removing coverage reports..."
    rm -rf htmlcov
fi

if [ -f ".coverage" ]; then
    echo "🗑️ Removing coverage data..."
    rm -f .coverage
fi

# Remove pytest cache
if [ -d ".pytest_cache" ]; then
    echo "🗑️ Removing pytest cache..."
    rm -rf .pytest_cache
fi

# Remove mypy cache
if [ -d ".mypy_cache" ]; then
    echo "🗑️ Removing mypy cache..."
    rm -rf .mypy_cache
fi

# Remove build directories
if [ -d "build" ]; then
    echo "🗑️ Removing build directory..."
    rm -rf build
fi

if [ -d "dist" ]; then
    echo "🗑️ Removing dist directory..."
    rm -rf dist
fi

if [ -d "*.egg-info" ]; then
    echo "🗑️ Removing egg-info directories..."
    rm -rf *.egg-info
fi

echo "✅ Cleanup completed successfully!"
echo "💡 To clean virtual environment as well, run: rm -rf venv"