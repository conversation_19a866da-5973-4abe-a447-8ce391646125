#!/bin/bash
set -euo pipefail

# Navigate to backend directory
cd "$(dirname "$0")/.."

echo "🎨 Formatting Meta-Agent Backend Code..."

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    source venv/bin/activate
else
    echo "⚠️ Virtual environment not found. Run 'bazel run //services/meta-agent/backend:install' first"
    exit 1
fi

# Format with black
echo "🖤 Running Black formatter..."
black src/ tests/ --line-length 88

# Sort imports with isort
echo "📋 Sorting imports with isort..."
isort src/ tests/ --profile black

echo "✅ Code formatting completed successfully!"