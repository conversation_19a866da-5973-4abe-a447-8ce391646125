#!/bin/bash
set -euo pipefail

# Navigate to backend directory
cd "$(dirname "$0")/.."

echo "📦 Installing Meta-Agent Backend Dependencies..."

# Remove existing virtual environment for clean install
if [ -d "venv" ]; then
    echo "🧹 Cleaning existing virtual environment..."
    rm -rf venv
fi

# Create fresh virtual environment
echo "🏗️ Creating virtual environment..."
python3 -m venv venv

# Activate virtual environment
source venv/bin/activate

# Upgrade pip
echo "⬆️ Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "📦 Installing production dependencies..."
pip install -r requirements.txt

echo "📦 Installing development dependencies..."
pip install -r requirements-dev.txt

# Install package in editable mode
echo "🔧 Installing package in editable mode..."
pip install -e .

# Mark installation as complete
touch venv/installed.flag

echo "✅ Dependencies installed successfully!"
echo "💡 To activate the environment manually: source venv/bin/activate"