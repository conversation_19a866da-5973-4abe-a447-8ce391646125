#!/bin/bash
set -euo pipefail

# Navigate to backend directory
cd "$(dirname "$0")/.."

echo "🔍 Running Meta-Agent Backend Type Check..."

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    source venv/bin/activate
else
    echo "⚠️ Virtual environment not found. Run 'bazel run //services/meta-agent/backend:install' first"
    exit 1
fi

# Run MyPy type checking
echo "🐍 Running MyPy type checker..."
mypy src/ --config-file pyproject.toml

echo "✅ Type check completed successfully!"