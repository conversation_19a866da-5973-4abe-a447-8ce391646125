#!/bin/bash
set -euo pipefail

# Navigate to openapi directory
cd "$(dirname "$0")/.."

echo "🔄 Updating Meta-Agent OpenAPI Specification..."

# Generate fresh OpenAPI spec from backend
echo "1️⃣ Generating OpenAPI spec from backend..."
../scripts/generate_openapi.sh

# Copy generated files to this directory
echo "2️⃣ Copying generated files..."
if [ -f "../openapi/openapi.yaml" ]; then
    cp ../openapi/openapi.yaml ./openapi.yaml
    echo "✅ Updated openapi.yaml"
else
    echo "⚠️ Generated openapi.yaml not found"
fi

if [ -f "../openapi/openapi.json" ]; then
    cp ../openapi/openapi.json ./openapi.json
    echo "✅ Updated openapi.json"
else
    echo "⚠️ Generated openapi.json not found"
fi

# Validate the updated specifications
echo "3️⃣ Validating updated specifications..."
./scripts/validate_openapi.sh

echo "✅ OpenAPI specification update completed successfully!"