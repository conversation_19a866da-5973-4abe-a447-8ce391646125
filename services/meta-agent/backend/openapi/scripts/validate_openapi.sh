#!/bin/bash
set -euo pipefail

# Navigate to openapi directory
cd "$(dirname "$0")/.."

echo "🔍 Validating Meta-Agent OpenAPI Specification..."

# Check if OpenAPI files exist
if [ ! -f "openapi.yaml" ]; then
    echo "❌ openapi.yaml not found"
    exit 1
fi

if [ ! -f "openapi.json" ]; then
    echo "❌ openapi.json not found"
    exit 1
fi

# Basic YAML syntax validation
echo "📋 Validating YAML syntax..."
python3 -c "
import yaml
import sys

try:
    with open('openapi.yaml', 'r') as f:
        yaml.safe_load(f)
    print('✅ YAML syntax is valid')
except yaml.YAMLError as e:
    print(f'❌ YAML syntax error: {e}')
    sys.exit(1)
"

# Basic JSON syntax validation
echo "📋 Validating JSON syntax..."
python3 -c "
import json
import sys

try:
    with open('openapi.json', 'r') as f:
        json.load(f)
    print('✅ JSON syntax is valid')
except json.JSONDecodeError as e:
    print(f'❌ JSON syntax error: {e}')
    sys.exit(1)
"

# Check for required OpenAPI fields
echo "📋 Validating OpenAPI structure..."
python3 -c "
import yaml
import sys

try:
    with open('openapi.yaml', 'r') as f:
        spec = yaml.safe_load(f)
    
    required_fields = ['openapi', 'info', 'paths']
    missing_fields = [field for field in required_fields if field not in spec]
    
    if missing_fields:
        print(f'❌ Missing required fields: {missing_fields}')
        sys.exit(1)
    
    if 'title' not in spec.get('info', {}):
        print('❌ Missing required field: info.title')
        sys.exit(1)
        
    if 'version' not in spec.get('info', {}):
        print('❌ Missing required field: info.version')
        sys.exit(1)
    
    print('✅ OpenAPI structure is valid')
    print(f'   Title: {spec[\"info\"][\"title\"]}')
    print(f'   Version: {spec[\"info\"][\"version\"]}')
    print(f'   OpenAPI Version: {spec[\"openapi\"]}')
    
except Exception as e:
    print(f'❌ Structure validation error: {e}')
    sys.exit(1)
"

echo "✅ OpenAPI specification validation completed successfully!"