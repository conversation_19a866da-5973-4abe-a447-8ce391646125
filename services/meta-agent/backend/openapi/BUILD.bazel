package(default_visibility = ["//visibility:public"])

# OpenAPI specification files for Meta-Agent API
filegroup(
    name = "openapi.yaml",
    srcs = ["openapi.yaml"],
    visibility = ["//visibility:public"],
)

filegroup(
    name = "openapi.json", 
    srcs = ["openapi.json"],
    visibility = ["//visibility:public"],
)

# All OpenAPI specification files
filegroup(
    name = "openapi_specs",
    srcs = [
        "openapi.yaml",
        "openapi.json",
    ],
    visibility = ["//visibility:public"],
)

# Generate OpenAPI spec from FastAPI application
genrule(
    name = "generate_openapi_spec",
    srcs = ["//services/meta-agent/backend:backend_lib"],
    outs = [
        "generated_openapi.yaml",
        "generated_openapi.json",
    ],
    cmd = """
        # Set up Python path
        export PYTHONPATH="$$(dirname $(location //services/meta-agent/backend:backend_lib))/src:$$PYTHONPATH"
        
        # Generate OpenAPI specification from the FastAPI app
        python3 -c "
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname('$(location //services/meta-agent/backend:backend_lib)'), 'src'))

try:
    from main import app
    import json
    import yaml
    
    # Generate OpenAPI dict
    openapi_dict = app.openapi()
    
    # Write JSON
    with open('$(location generated_openapi.json)', 'w') as f:
        json.dump(openapi_dict, f, indent=2)
    
    # Write YAML
    with open('$(location generated_openapi.yaml)', 'w') as f:
        yaml.dump(openapi_dict, f, default_flow_style=False, sort_keys=False)
        
    print('OpenAPI specification generated successfully')
    
except Exception as e:
    print(f'Error generating OpenAPI spec: {e}')
    # Create empty files as fallback
    with open('$(location generated_openapi.json)', 'w') as f:
        f.write('{}')
    with open('$(location generated_openapi.yaml)', 'w') as f:
        f.write('openapi: 3.0.0\ninfo:\n  title: Meta-Agent API\n  version: 1.0.0\npaths: {}')
"
    """,
    tools = ["//services/meta-agent/backend:backend_lib"],
)

# Validate OpenAPI specification
sh_binary(
    name = "validate",
    srcs = ["scripts/validate_openapi.sh"],
    data = [
        ":openapi_specs",
    ],
)

# Update OpenAPI specs from backend
sh_binary(
    name = "update",
    srcs = ["scripts/update_openapi.sh"],
    data = [
        "//services/meta-agent/backend:generate_openapi",
    ],
)