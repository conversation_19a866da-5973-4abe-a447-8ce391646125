#!/usr/bin/env python3
"""
Test AI Code Generation Service
"""

import asyncio
import sys
import os
sys.path.insert(0, '/Users/<USER>/workspaces/git/ai-agent/backend/src')

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv('/Users/<USER>/workspaces/git/ai-agent/backend/.env')

from services.code_generator import code_generator
from services.ai_service import ai_service

async def test_ai_service():
    """Test basic AI service functionality"""
    print("Testing AI service...")
    
    try:
        # Debug environment variables
        print(f"OPENAI_API_KEY present: {bool(os.getenv('OPENAI_API_KEY'))}")
        print(f"ANTHROPIC_API_KEY present: {bool(os.getenv('ANTHROPIC_API_KEY'))}")
        print(f"GOOGLE_API_KEY present: {bool(os.getenv('GOOGLE_API_KEY'))}")
        
        # Ensure providers are initialized
        ai_service._ensure_providers_initialized()
        
        print(f"Available providers: {list(ai_service.providers.keys())}")
        print(f"Providers initialized: {ai_service._providers_initialized}")
        
        if not ai_service.providers:
            print("No providers configured!")
            return False
        
        # Test simple generation
        from services.ai_service import AIRequest, AICapability
        
        request = AIRequest(
            prompt="Generate a simple 'Hello World' Python function with proper documentation",
            capability=AICapability.CODE_GENERATION,
            temperature=0.1,
            max_tokens=500
        )
        
        response = await ai_service.generate(request)
        print(f"AI Service Response: {response.content[:200]}...")
        return True
        
    except Exception as e:
        print(f"AI Service Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_code_generator():
    """Test the code generator service"""
    print("Testing code generator...")
    
    try:
        config = {
            "name": "test-calculator",
            "type": "api_service",
            "language": "python",
            "framework": "fastapi",
            "capabilities": ["calculation", "mathematics"]
        }
        
        requirements = "Create a simple calculator that can add, subtract, multiply and divide two numbers. Include error handling for division by zero."
        
        print("Starting code generation...")
        # Add timeout to prevent hanging
        generated_code = await asyncio.wait_for(
            code_generator.generate_agent_code(config, requirements),
            timeout=60  # 60 second timeout
        )
        
        print(f"Code generation completed!")
        print(f"Generated components: {list(generated_code.get('components', {}).keys())}")
        
        # Check if main.py was generated
        api_components = generated_code.get('components', {}).get('api', {})
        if 'main.py' in api_components:
            main_py_content = api_components['main.py']
            print(f"main.py content length: {len(main_py_content)}")
            print(f"main.py preview: {main_py_content[:300]}...")
        
        return True
        
    except Exception as e:
        print(f"Code Generator Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("Starting AI Generation Tests...")
    
    # Test 1: Basic AI Service
    ai_success = await test_ai_service()
    print(f"AI Service Test: {'PASSED' if ai_success else 'FAILED'}")
    
    if not ai_success:
        print("Skipping code generator test due to AI service failure")
        return
    
    # Test 2: Code Generator
    gen_success = await test_code_generator()
    print(f"Code Generator Test: {'PASSED' if gen_success else 'FAILED'}")
    
    print("Tests completed!")

if __name__ == "__main__":
    asyncio.run(main())