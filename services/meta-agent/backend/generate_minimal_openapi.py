#!/usr/bin/env python3
"""
Generate OpenAPI spec from a minimal FastAPI application
This bypasses complex dependencies and focuses on API structure
"""

import json
import yaml
import os
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_minimal_app():
    """Create a minimal FastAPI app with essential routes for OpenAPI generation"""
    from fastapi import FastAPI
    from pydantic import BaseModel
    from typing import List, Optional, Dict, Any
    
    app = FastAPI(
        title="AI Agent Platform API",
        description="Comprehensive API for AI Agent Platform - Agent Creation, Management, and Orchestration",
        version="2.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # Define Pydantic models for API documentation
    class AgentBase(BaseModel):
        name: str
        description: Optional[str] = None
        type: str = "chat"
        status: str = "active"
        
    class AgentCreate(AgentBase):
        configuration: Optional[Dict[str, Any]] = {}
        
    class Agent(AgentBase):
        id: str
        created_at: str
        updated_at: str
        
    class TaskBase(BaseModel):
        title: str
        description: Optional[str] = None
        priority: str = "medium"
        
    class TaskCreate(TaskBase):
        agent_id: Optional[str] = None
        
    class Task(TaskBase):
        id: str
        agent_id: Optional[str] = None
        status: str = "pending"
        created_at: str
        updated_at: str
        
    class WorkflowBase(BaseModel):
        name: str
        description: Optional[str] = None
        
    class WorkflowCreate(WorkflowBase):
        steps: List[Dict[str, Any]] = []
        
    class Workflow(WorkflowBase):
        id: str
        steps: List[Dict[str, Any]]
        created_at: str
        updated_at: str
        
    class OrchestrationCommand(BaseModel):
        command_type: str
        target_agents: List[str]
        parameters: Dict[str, Any] = {}
        
    class DeploymentRequest(BaseModel):
        agent_id: str
        environment: str = "production"
        configuration: Dict[str, Any] = {}
        
    class MessageExchange(BaseModel):
        from_agent: str
        to_agent: str
        message_type: str
        content: Dict[str, Any]
        
    class IntelligenceRequest(BaseModel):
        query: str
        context: Optional[Dict[str, Any]] = {}
        model_preferences: Optional[Dict[str, str]] = {}
        
    class HealthCheck(BaseModel):
        status: str
        version: str
        environment: str
        
    class ApiResponse(BaseModel):
        success: bool
        message: str
        data: Optional[Any] = None
        
    # Define API routes
    
    # Health and system endpoints
    @app.get("/", response_model=HealthCheck)
    async def root():
        """Root endpoint - system information"""
        return {"status": "running", "version": "2.0.0", "environment": "development"}
    
    @app.get("/health", response_model=HealthCheck)
    async def health_check():
        """Health check endpoint"""
        return {"status": "healthy", "version": "2.0.0", "environment": "development"}
    
    @app.get("/metrics")
    async def metrics():
        """Prometheus metrics endpoint"""
        return {"metrics": "placeholder"}
    
    # Agent management endpoints
    @app.post("/api/v1/agents", response_model=Agent)
    async def create_agent(agent: AgentCreate):
        """Create a new AI agent"""
        pass
    
    @app.get("/api/v1/agents", response_model=List[Agent])
    async def list_agents():
        """List all agents"""
        pass
    
    @app.get("/api/v1/agents/{agent_id}", response_model=Agent)
    async def get_agent(agent_id: str):
        """Get agent by ID"""
        pass
    
    @app.put("/api/v1/agents/{agent_id}", response_model=Agent)
    async def update_agent(agent_id: str, agent: AgentCreate):
        """Update an existing agent"""
        pass
    
    @app.delete("/api/v1/agents/{agent_id}", response_model=ApiResponse)
    async def delete_agent(agent_id: str):
        """Delete an agent"""
        pass
    
    # Task management endpoints
    @app.post("/api/v1/tasks", response_model=Task)
    async def create_task(task: TaskCreate):
        """Create a new task"""
        pass
    
    @app.get("/api/v1/tasks", response_model=List[Task])
    async def list_tasks():
        """List all tasks"""
        pass
    
    @app.get("/api/v1/tasks/{task_id}", response_model=Task)
    async def get_task(task_id: str):
        """Get task by ID"""
        pass
    
    @app.put("/api/v1/tasks/{task_id}", response_model=Task)
    async def update_task(task_id: str, task: TaskCreate):
        """Update an existing task"""
        pass
    
    @app.delete("/api/v1/tasks/{task_id}", response_model=ApiResponse)
    async def delete_task(task_id: str):
        """Delete a task"""
        pass
    
    # Workflow management endpoints
    @app.post("/api/v1/workflows", response_model=Workflow)
    async def create_workflow(workflow: WorkflowCreate):
        """Create a new workflow"""
        pass
    
    @app.get("/api/v1/workflows", response_model=List[Workflow])
    async def list_workflows():
        """List all workflows"""
        pass
    
    @app.get("/api/v1/workflows/{workflow_id}", response_model=Workflow)
    async def get_workflow(workflow_id: str):
        """Get workflow by ID"""
        pass
    
    @app.post("/api/v1/workflows/{workflow_id}/execute", response_model=ApiResponse)
    async def execute_workflow(workflow_id: str):
        """Execute a workflow"""
        pass
    
    # Orchestration endpoints
    @app.post("/api/v1/orchestration/command", response_model=ApiResponse)
    async def send_orchestration_command(command: OrchestrationCommand):
        """Send orchestration command to multiple agents"""
        pass
    
    @app.get("/api/v1/orchestration/status", response_model=Dict[str, Any])
    async def get_orchestration_status():
        """Get orchestration system status"""
        pass
    
    # Deployment endpoints
    @app.post("/api/v1/deployment/deploy", response_model=ApiResponse)
    async def deploy_agent(deployment: DeploymentRequest):
        """Deploy an agent to specified environment"""
        pass
    
    @app.get("/api/v1/deployment/status/{agent_id}")
    async def get_deployment_status(agent_id: str):
        """Get deployment status for an agent"""
        pass
    
    # Agent-to-Agent communication
    @app.post("/api/v1/a2a/message", response_model=ApiResponse)
    async def send_a2a_message(message: MessageExchange):
        """Send message between agents"""
        pass
    
    @app.get("/api/v1/a2a/messages/{agent_id}")
    async def get_agent_messages(agent_id: str):
        """Get messages for an agent"""
        pass
    
    # Intelligence/AI endpoints
    @app.post("/api/v1/intelligence/query", response_model=Dict[str, Any])
    async def intelligence_query(request: IntelligenceRequest):
        """Submit query to AI intelligence system"""
        pass
    
    @app.get("/api/v1/intelligence/models")
    async def list_available_models():
        """List available AI models"""
        pass
    
    # Vector database endpoints
    @app.post("/api/v1/vector/embed")
    async def create_embeddings(content: Dict[str, Any]):
        """Create vector embeddings"""
        pass
    
    @app.post("/api/v1/vector/search")
    async def search_vectors(query: Dict[str, Any]):
        """Search vector database"""
        pass
    
    # Generation endpoints
    @app.post("/api/v1/generation/agent")
    async def generate_agent(specification: Dict[str, Any]):
        """Generate new agent from specification"""
        pass
    
    @app.post("/api/v1/generation/workflow")
    async def generate_workflow(requirements: Dict[str, Any]):
        """Generate workflow from requirements"""
        pass
    
    return app

def generate_openapi_spec():
    """Generate OpenAPI specification from the minimal FastAPI app"""
    try:
        # Create the minimal app
        app = create_minimal_app()
        
        # Get the OpenAPI schema
        openapi_schema = app.openapi()
        
        # Ensure output directory exists
        output_dir = Path("openapi")
        output_dir.mkdir(exist_ok=True)
        
        # Write JSON version
        json_path = output_dir / "openapi.json"
        with open(json_path, 'w') as f:
            json.dump(openapi_schema, f, indent=2)
        
        # Write YAML version
        yaml_path = output_dir / "openapi.yaml"
        with open(yaml_path, 'w') as f:
            yaml.dump(openapi_schema, f, default_flow_style=False, sort_keys=False)
        
        print(f"✅ OpenAPI specification generated successfully!")
        print(f"📄 JSON: {json_path}")
        print(f"📄 YAML: {yaml_path}")
        
        # Print some stats
        schemas = openapi_schema.get('components', {}).get('schemas', {})
        paths = openapi_schema.get('paths', {})
        
        print(f"📊 Generated {len(schemas)} schemas and {len(paths)} API paths")
        print(f"🔧 Schemas: {', '.join(list(schemas.keys())[:10])}{'...' if len(schemas) > 10 else ''}")
        print(f"🛣️ Paths: {', '.join(list(paths.keys())[:5])}{'...' if len(paths) > 5 else ''}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to generate OpenAPI spec: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = generate_openapi_spec()
    sys.exit(0 if success else 1)