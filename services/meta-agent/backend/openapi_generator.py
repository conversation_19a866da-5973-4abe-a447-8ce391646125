#!/usr/bin/env python3
"""
OpenAPI Schema Generator for AI Agent Platform

This script generates comprehensive OpenAPI specifications for all backend endpoints
and creates TypeScript client code with full type safety.
"""

import json
import yaml
import os
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path

@dataclass
class APIEndpoint:
    """API endpoint definition."""
    path: str
    method: str
    summary: str
    description: str
    tags: List[str]
    parameters: List[Dict[str, Any]]
    request_body: Optional[Dict[str, Any]]
    responses: Dict[str, Dict[str, Any]]
    security: Optional[List[Dict[str, Any]]] = None

@dataclass
class APISchema:
    """Complete API schema definition."""
    name: str
    properties: Dict[str, Dict[str, Any]]
    required: List[str]
    description: str

class OpenAPIGenerator:
    """Generates OpenAPI specifications for the platform."""
    
    def __init__(self):
        self.version = "2.0.0"
        self.title = "AI Agent Platform API"
        self.description = "Comprehensive API for managing AI agents, workflows, and platform operations"
        self.base_url = "http://localhost:8000"
        
        # Schema definitions
        self.schemas = {}
        self.endpoints = []
        
        self._define_schemas()
        self._define_endpoints()
    
    def _define_schemas(self) -> None:
        """Define all data schemas used by the API."""
        
        # Agent schemas
        self.schemas["Agent"] = {
            "type": "object",
            "properties": {
                "id": {"type": "string", "format": "uuid", "description": "Unique agent identifier"},
                "name": {"type": "string", "description": "Agent display name"},
                "description": {"type": "string", "description": "Agent description"},
                "type": {"type": "string", "enum": ["api_service", "background_worker", "scheduled_task", "data_processor"], "description": "Agent type"},
                "status": {"type": "string", "enum": ["created", "building", "ready", "running", "paused", "error", "stopped"], "description": "Current status"},
                "framework": {"type": "string", "enum": ["fastapi", "flask", "django", "express", "custom"], "description": "Framework used"},
                "language": {"type": "string", "enum": ["python", "javascript", "typescript", "go", "rust"], "description": "Programming language"},
                "capabilities": {"type": "array", "items": {"type": "string"}, "description": "Agent capabilities"},
                "configuration": {"type": "object", "additionalProperties": True, "description": "Agent configuration"},
                "deployment_config": {"$ref": "#/components/schemas/DeploymentConfig"},
                "created_at": {"type": "string", "format": "date-time"},
                "updated_at": {"type": "string", "format": "date-time"},
                "version": {"type": "string", "description": "Agent version"}
            },
            "required": ["id", "name", "type", "status", "framework", "language"],
            "description": "AI Agent definition"
        }
        
        self.schemas["DeploymentConfig"] = {
            "type": "object",
            "properties": {
                "port": {"type": "integer", "minimum": 1024, "maximum": 65535},
                "environment": {"type": "object", "additionalProperties": {"type": "string"}},
                "resources": {"$ref": "#/components/schemas/ResourceLimits"},
                "auto_scale": {"type": "boolean", "default": False},
                "replicas": {"type": "integer", "minimum": 1, "default": 1}
            },
            "description": "Agent deployment configuration"
        }
        
        self.schemas["ResourceLimits"] = {
            "type": "object",
            "properties": {
                "cpu_limit": {"type": "string", "pattern": "^\\d+m?$", "description": "CPU limit (e.g., '500m', '1')"},
                "memory_limit": {"type": "string", "pattern": "^\\d+[KMG]i?$", "description": "Memory limit (e.g., '512Mi', '1Gi')"},
                "disk_limit": {"type": "string", "pattern": "^\\d+[KMG]i?$", "description": "Disk limit"}
            },
            "description": "Resource limits for agent deployment"
        }
        
        # Generation request schemas
        self.schemas["GenerationRequest"] = {
            "type": "object",
            "properties": {
                "name": {"type": "string", "minLength": 1, "maxLength": 100},
                "description": {"type": "string", "maxLength": 500},
                "requirements": {"type": "string", "description": "Natural language requirements"},
                "type": {"type": "string", "enum": ["api_service", "background_worker", "data_processor", "integration"]},
                "framework": {"type": "string", "enum": ["fastapi", "flask", "express", "custom"], "default": "fastapi"},
                "language": {"type": "string", "enum": ["python", "javascript", "typescript"], "default": "python"},
                "capabilities": {"type": "array", "items": {"type": "string"}},
                "deployment": {"$ref": "#/components/schemas/DeploymentConfig"},
                "advanced_options": {"$ref": "#/components/schemas/AdvancedOptions"}
            },
            "required": ["name", "description", "requirements", "type"],
            "description": "Request to generate a new agent"
        }
        
        self.schemas["AdvancedOptions"] = {
            "type": "object",
            "properties": {
                "use_ai_workflow": {"type": "boolean", "default": True},
                "custom_templates": {"type": "array", "items": {"type": "string"}},
                "integration_endpoints": {"type": "array", "items": {"type": "string"}},
                "testing_enabled": {"type": "boolean", "default": True},
                "documentation_level": {"type": "string", "enum": ["minimal", "standard", "comprehensive"], "default": "standard"}
            },
            "description": "Advanced generation options"
        }
        
        # Task schemas
        self.schemas["Task"] = {
            "type": "object",
            "properties": {
                "id": {"type": "string", "format": "uuid"},
                "agent_id": {"type": "string", "format": "uuid"},
                "name": {"type": "string"},
                "description": {"type": "string"},
                "type": {"type": "string", "enum": ["generation", "deployment", "execution", "monitoring"]},
                "status": {"type": "string", "enum": ["pending", "running", "completed", "failed", "cancelled"]},
                "priority": {"type": "string", "enum": ["low", "medium", "high", "critical"], "default": "medium"},
                "progress": {"type": "number", "minimum": 0, "maximum": 100},
                "metadata": {"type": "object", "additionalProperties": True},
                "result": {"type": "object", "additionalProperties": True},
                "error": {"type": "string"},
                "created_at": {"type": "string", "format": "date-time"},
                "started_at": {"type": "string", "format": "date-time"},
                "completed_at": {"type": "string", "format": "date-time"},
                "logs": {"type": "array", "items": {"$ref": "#/components/schemas/TaskLog"}}
            },
            "required": ["id", "name", "type", "status"],
            "description": "Platform task definition"
        }
        
        self.schemas["TaskLog"] = {
            "type": "object",
            "properties": {
                "timestamp": {"type": "string", "format": "date-time"},
                "level": {"type": "string", "enum": ["debug", "info", "warning", "error"]},
                "message": {"type": "string"},
                "details": {"type": "object", "additionalProperties": True}
            },
            "required": ["timestamp", "level", "message"],
            "description": "Task execution log entry"
        }
        
        # Migration schemas
        self.schemas["ApplicationAnalysis"] = {
            "type": "object",
            "properties": {
                "id": {"type": "string", "format": "uuid"},
                "app_path": {"type": "string"},
                "app_type": {"type": "string", "enum": ["web_app", "api_service", "cli_tool", "microservice", "monolith"]},
                "technology_stack": {"type": "array", "items": {"type": "string"}},
                "complexity_score": {"type": "number", "minimum": 0, "maximum": 10},
                "migration_feasibility": {"type": "number", "minimum": 0, "maximum": 10},
                "recommended_strategy": {"type": "string", "enum": ["wrapper", "rewrite", "hybrid", "interface", "decompose"]},
                "estimated_effort_hours": {"type": "integer", "minimum": 0},
                "endpoints_count": {"type": "integer", "minimum": 0},
                "models_count": {"type": "integer", "minimum": 0},
                "dependencies_count": {"type": "integer", "minimum": 0},
                "external_integrations_count": {"type": "integer", "minimum": 0},
                "created_at": {"type": "string", "format": "date-time"}
            },
            "required": ["id", "app_path", "app_type", "complexity_score", "migration_feasibility"],
            "description": "Application analysis results for migration"
        }
        
        self.schemas["MigrationPlan"] = {
            "type": "object",
            "properties": {
                "id": {"type": "string", "format": "uuid"},
                "analysis_id": {"type": "string", "format": "uuid"},
                "strategy": {"type": "string", "enum": ["wrapper", "rewrite", "hybrid", "interface", "decompose"]},
                "estimated_timeline_days": {"type": "integer", "minimum": 1},
                "phases_count": {"type": "integer", "minimum": 1},
                "agents_to_generate": {"type": "integer", "minimum": 1},
                "phases": {"type": "array", "items": {"$ref": "#/components/schemas/MigrationPhase"}},
                "success_criteria": {"type": "array", "items": {"type": "string"}},
                "risk_assessment": {"type": "array", "items": {"$ref": "#/components/schemas/RiskItem"}},
                "resource_requirements": {"type": "object", "additionalProperties": True}
            },
            "required": ["id", "analysis_id", "strategy", "estimated_timeline_days"],
            "description": "Detailed migration plan"
        }
        
        self.schemas["MigrationPhase"] = {
            "type": "object",
            "properties": {
                "phase": {"type": "integer", "minimum": 1},
                "name": {"type": "string"},
                "description": {"type": "string"},
                "tasks": {"type": "array", "items": {"type": "string"}},
                "duration_days": {"type": "integer", "minimum": 1}
            },
            "required": ["phase", "name", "duration_days"],
            "description": "Migration phase definition"
        }
        
        self.schemas["RiskItem"] = {
            "type": "object",
            "properties": {
                "risk": {"type": "string"},
                "probability": {"type": "string", "enum": ["low", "medium", "high"]},
                "impact": {"type": "string", "enum": ["low", "medium", "high"]},
                "mitigation": {"type": "string"}
            },
            "required": ["risk", "probability", "impact", "mitigation"],
            "description": "Risk assessment item"
        }
        
        # System schemas
        self.schemas["SystemStats"] = {
            "type": "object",
            "properties": {
                "total_agents": {"type": "integer", "minimum": 0},
                "active_agents": {"type": "integer", "minimum": 0},
                "queued_tasks": {"type": "integer", "minimum": 0},
                "completed_tasks": {"type": "integer", "minimum": 0},
                "failed_tasks": {"type": "integer", "minimum": 0},
                "avg_response_time": {"type": "number", "minimum": 0},
                "system_load": {"type": "number", "minimum": 0, "maximum": 100},
                "memory_usage": {"type": "number", "minimum": 0, "maximum": 100},
                "disk_usage": {"type": "number", "minimum": 0, "maximum": 100},
                "uptime": {"type": "string"},
                "success_rate": {"type": "number", "minimum": 0, "maximum": 100}
            },
            "description": "System performance statistics"
        }
        
        # Error schemas
        self.schemas["Error"] = {
            "type": "object",
            "properties": {
                "error": {"type": "string", "description": "Error message"},
                "error_code": {"type": "string", "description": "Machine-readable error code"},
                "details": {"type": "object", "additionalProperties": True, "description": "Additional error details"},
                "timestamp": {"type": "string", "format": "date-time"},
                "request_id": {"type": "string", "format": "uuid"}
            },
            "required": ["error"],
            "description": "Standard error response"
        }
        
        # Pagination schemas
        self.schemas["PaginationInfo"] = {
            "type": "object",
            "properties": {
                "page": {"type": "integer", "minimum": 1},
                "size": {"type": "integer", "minimum": 1, "maximum": 100},
                "total": {"type": "integer", "minimum": 0},
                "pages": {"type": "integer", "minimum": 0},
                "has_next": {"type": "boolean"},
                "has_prev": {"type": "boolean"}
            },
            "required": ["page", "size", "total", "pages", "has_next", "has_prev"],
            "description": "Pagination information"
        }

    def _define_endpoints(self) -> None:
        """Define all API endpoints."""
        
        # Agent Management Endpoints
        self.endpoints.extend([
            APIEndpoint(
                path="/api/v1/agents",
                method="get",
                summary="List all agents",
                description="Retrieve a paginated list of all agents with optional filtering",
                tags=["Agents"],
                parameters=[
                    {"name": "page", "in": "query", "schema": {"type": "integer", "minimum": 1, "default": 1}},
                    {"name": "size", "in": "query", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}},
                    {"name": "status", "in": "query", "schema": {"type": "string", "enum": ["created", "building", "ready", "running", "paused", "error", "stopped"]}},
                    {"name": "type", "in": "query", "schema": {"type": "string", "enum": ["api_service", "background_worker", "scheduled_task", "data_processor"]}},
                    {"name": "search", "in": "query", "schema": {"type": "string", "description": "Search in name and description"}}
                ],
                request_body=None,
                responses={
                    "200": {
                        "description": "List of agents",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "agents": {"type": "array", "items": {"$ref": "#/components/schemas/Agent"}},
                                        "pagination": {"$ref": "#/components/schemas/PaginationInfo"}
                                    }
                                }
                            }
                        }
                    }
                }
            ),
            
            APIEndpoint(
                path="/api/v1/agents",
                method="post",
                summary="Create a new agent",
                description="Generate and create a new AI agent based on requirements",
                tags=["Agents"],
                parameters=[],
                request_body={
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {"$ref": "#/components/schemas/GenerationRequest"}
                        }
                    }
                },
                responses={
                    "201": {
                        "description": "Agent created successfully",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "agent": {"$ref": "#/components/schemas/Agent"},
                                        "task_id": {"type": "string", "format": "uuid", "description": "ID of the generation task"}
                                    }
                                }
                            }
                        }
                    }
                }
            ),
            
            APIEndpoint(
                path="/api/v1/agents/{agent_id}",
                method="get",
                summary="Get agent details",
                description="Retrieve detailed information about a specific agent",
                tags=["Agents"],
                parameters=[
                    {"name": "agent_id", "in": "path", "required": True, "schema": {"type": "string", "format": "uuid"}}
                ],
                request_body=None,
                responses={
                    "200": {
                        "description": "Agent details",
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/Agent"}
                            }
                        }
                    }
                }
            ),
            
            APIEndpoint(
                path="/api/v1/agents/{agent_id}",
                method="put",
                summary="Update agent",
                description="Update agent configuration and settings",
                tags=["Agents"],
                parameters=[
                    {"name": "agent_id", "in": "path", "required": True, "schema": {"type": "string", "format": "uuid"}}
                ],
                request_body={
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {
                                "type": "object",
                                "properties": {
                                    "name": {"type": "string"},
                                    "description": {"type": "string"},
                                    "configuration": {"type": "object", "additionalProperties": True},
                                    "deployment_config": {"$ref": "#/components/schemas/DeploymentConfig"}
                                }
                            }
                        }
                    }
                },
                responses={
                    "200": {
                        "description": "Agent updated successfully",
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/Agent"}
                            }
                        }
                    }
                }
            ),
            
            APIEndpoint(
                path="/api/v1/agents/{agent_id}",
                method="delete",
                summary="Delete agent",
                description="Delete an agent and stop all associated processes",
                tags=["Agents"],
                parameters=[
                    {"name": "agent_id", "in": "path", "required": True, "schema": {"type": "string", "format": "uuid"}}
                ],
                request_body=None,
                responses={
                    "204": {"description": "Agent deleted successfully"}
                }
            ),
            
            APIEndpoint(
                path="/api/v1/agents/{agent_id}/start",
                method="post",
                summary="Start agent",
                description="Start a deployed agent",
                tags=["Agents"],
                parameters=[
                    {"name": "agent_id", "in": "path", "required": True, "schema": {"type": "string", "format": "uuid"}}
                ],
                request_body=None,
                responses={
                    "200": {
                        "description": "Agent started successfully",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "status": {"type": "string"},
                                        "port": {"type": "integer"},
                                        "process_id": {"type": "integer"}
                                    }
                                }
                            }
                        }
                    }
                }
            ),
            
            APIEndpoint(
                path="/api/v1/agents/{agent_id}/stop",
                method="post",
                summary="Stop agent",
                description="Stop a running agent",
                tags=["Agents"],
                parameters=[
                    {"name": "agent_id", "in": "path", "required": True, "schema": {"type": "string", "format": "uuid"}}
                ],
                request_body=None,
                responses={
                    "200": {
                        "description": "Agent stopped successfully",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "status": {"type": "string"},
                                        "message": {"type": "string"}
                                    }
                                }
                            }
                        }
                    }
                }
            )
        ])
        
        # Migration Endpoints
        self.endpoints.extend([
            APIEndpoint(
                path="/api/v1/migration/analyze",
                method="post",
                summary="Analyze application for migration",
                description="Analyze an existing application to assess migration feasibility",
                tags=["Migration"],
                parameters=[],
                request_body={
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {
                                "type": "object",
                                "properties": {
                                    "app_path": {"type": "string", "description": "Path to the application to analyze"}
                                },
                                "required": ["app_path"]
                            }
                        }
                    }
                },
                responses={
                    "200": {
                        "description": "Analysis completed",
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/ApplicationAnalysis"}
                            }
                        }
                    }
                }
            ),
            
            APIEndpoint(
                path="/api/v1/migration/plan",
                method="post",
                summary="Create migration plan",
                description="Create a detailed migration plan based on application analysis",
                tags=["Migration"],
                parameters=[],
                request_body={
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {
                                "type": "object",
                                "properties": {
                                    "analysis_id": {"type": "string", "format": "uuid"},
                                    "custom_strategy": {"type": "string", "enum": ["wrapper", "rewrite", "hybrid", "interface", "decompose"]}
                                },
                                "required": ["analysis_id"]
                            }
                        }
                    }
                },
                responses={
                    "200": {
                        "description": "Migration plan created",
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/MigrationPlan"}
                            }
                        }
                    }
                }
            ),
            
            APIEndpoint(
                path="/api/v1/migration/start",
                method="post",
                summary="Start migration",
                description="Start executing a migration plan",
                tags=["Migration"],
                parameters=[],
                request_body={
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {
                                "type": "object",
                                "properties": {
                                    "plan_id": {"type": "string", "format": "uuid"}
                                },
                                "required": ["plan_id"]
                            }
                        }
                    }
                },
                responses={
                    "200": {
                        "description": "Migration started",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "project_id": {"type": "string", "format": "uuid"},
                                        "status": {"type": "string"},
                                        "estimated_timeline_days": {"type": "integer"}
                                    }
                                }
                            }
                        }
                    }
                }
            )
        ])
        
        # Task Management Endpoints
        self.endpoints.extend([
            APIEndpoint(
                path="/api/v1/tasks",
                method="get",
                summary="List tasks",
                description="Retrieve a list of platform tasks with optional filtering",
                tags=["Tasks"],
                parameters=[
                    {"name": "page", "in": "query", "schema": {"type": "integer", "minimum": 1, "default": 1}},
                    {"name": "size", "in": "query", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}},
                    {"name": "status", "in": "query", "schema": {"type": "string", "enum": ["pending", "running", "completed", "failed", "cancelled"]}},
                    {"name": "type", "in": "query", "schema": {"type": "string", "enum": ["generation", "deployment", "execution", "monitoring"]}},
                    {"name": "agent_id", "in": "query", "schema": {"type": "string", "format": "uuid"}}
                ],
                request_body=None,
                responses={
                    "200": {
                        "description": "List of tasks",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "tasks": {"type": "array", "items": {"$ref": "#/components/schemas/Task"}},
                                        "pagination": {"$ref": "#/components/schemas/PaginationInfo"}
                                    }
                                }
                            }
                        }
                    }
                }
            ),
            
            APIEndpoint(
                path="/api/v1/tasks/{task_id}",
                method="get",
                summary="Get task details",
                description="Retrieve detailed information about a specific task",
                tags=["Tasks"],
                parameters=[
                    {"name": "task_id", "in": "path", "required": True, "schema": {"type": "string", "format": "uuid"}}
                ],
                request_body=None,
                responses={
                    "200": {
                        "description": "Task details",
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/Task"}
                            }
                        }
                    }
                }
            )
        ])
        
        # System Endpoints
        self.endpoints.extend([
            APIEndpoint(
                path="/api/v1/system/stats",
                method="get",
                summary="Get system statistics",
                description="Retrieve current system performance and usage statistics",
                tags=["System"],
                parameters=[],
                request_body=None,
                responses={
                    "200": {
                        "description": "System statistics",
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/SystemStats"}
                            }
                        }
                    }
                }
            ),
            
            APIEndpoint(
                path="/api/v1/system/health",
                method="get",
                summary="Health check",
                description="Check system health and availability",
                tags=["System"],
                parameters=[],
                request_body=None,
                responses={
                    "200": {
                        "description": "System is healthy",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "status": {"type": "string", "enum": ["healthy", "degraded", "unhealthy"]},
                                        "version": {"type": "string"},
                                        "uptime": {"type": "string"},
                                        "components": {
                                            "type": "object",
                                            "additionalProperties": {
                                                "type": "object",
                                                "properties": {
                                                    "status": {"type": "string"},
                                                    "message": {"type": "string"}
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            )
        ])

    def generate_openapi_spec(self) -> Dict[str, Any]:
        """Generate complete OpenAPI specification."""
        
        spec = {
            "openapi": "3.0.3",
            "info": {
                "title": self.title,
                "description": self.description,
                "version": self.version,
                "contact": {
                    "name": "AI Agent Platform Team",
                    "url": "https://github.com/ai-agent-platform/platform",
                    "email": "<EMAIL>"
                },
                "license": {
                    "name": "MIT",
                    "url": "https://opensource.org/licenses/MIT"
                }
            },
            "servers": [
                {
                    "url": "http://localhost:8000",
                    "description": "Development server"
                },
                {
                    "url": "https://api.ai-agent-platform.com",
                    "description": "Production server"
                }
            ],
            "tags": [
                {"name": "Agents", "description": "AI agent management operations"},
                {"name": "Migration", "description": "Application-to-agent migration operations"},
                {"name": "Tasks", "description": "Task management and monitoring"},
                {"name": "System", "description": "System health and statistics"}
            ],
            "paths": {},
            "components": {
                "schemas": self.schemas,
                "securitySchemes": {
                    "BearerAuth": {
                        "type": "http",
                        "scheme": "bearer",
                        "bearerFormat": "JWT"
                    },
                    "ApiKeyAuth": {
                        "type": "apiKey",
                        "in": "header",
                        "name": "X-API-Key"
                    }
                }
            },
            "security": [
                {"BearerAuth": []},
                {"ApiKeyAuth": []}
            ]
        }
        
        # Add endpoints to paths
        for endpoint in self.endpoints:
            if endpoint.path not in spec["paths"]:
                spec["paths"][endpoint.path] = {}
            
            operation = {
                "summary": endpoint.summary,
                "description": endpoint.description,
                "tags": endpoint.tags,
                "operationId": self._generate_operation_id(endpoint),
                "parameters": endpoint.parameters,
                "responses": endpoint.responses
            }
            
            # Add request body if present
            if endpoint.request_body:
                operation["requestBody"] = endpoint.request_body
            
            # Add security if specified
            if endpoint.security:
                operation["security"] = endpoint.security
            
            # Add common error responses
            operation["responses"].update({
                "400": {
                    "description": "Bad request",
                    "content": {
                        "application/json": {
                            "schema": {"$ref": "#/components/schemas/Error"}
                        }
                    }
                },
                "401": {
                    "description": "Unauthorized",
                    "content": {
                        "application/json": {
                            "schema": {"$ref": "#/components/schemas/Error"}
                        }
                    }
                },
                "404": {
                    "description": "Resource not found",
                    "content": {
                        "application/json": {
                            "schema": {"$ref": "#/components/schemas/Error"}
                        }
                    }
                },
                "500": {
                    "description": "Internal server error",
                    "content": {
                        "application/json": {
                            "schema": {"$ref": "#/components/schemas/Error"}
                        }
                    }
                }
            })
            
            spec["paths"][endpoint.path][endpoint.method] = operation
        
        return spec
    
    def _generate_operation_id(self, endpoint: APIEndpoint) -> str:
        """Generate operation ID for endpoint."""
        
        method_name = {
            "get": "get" if "{" not in endpoint.path else "getById",
            "post": "create",
            "put": "update",
            "delete": "delete",
            "patch": "patch"
        }.get(endpoint.method, endpoint.method)
        
        # Extract resource name from path
        path_parts = [p for p in endpoint.path.split("/") if p and not p.startswith("{")]
        resource = path_parts[-1] if path_parts else "resource"
        
        # Handle special cases
        if "start" in endpoint.path:
            if "agents" in endpoint.path:
                return "startAgent"
            elif "migration" in endpoint.path:
                return "startMigration"
            elif "orchestration" in endpoint.path:
                return "startOrchestration"
            else:
                return f"start{resource.capitalize()}"
        elif "stop" in endpoint.path:
            if "agents" in endpoint.path:
                return "stopAgent"
            elif "orchestration" in endpoint.path:
                return "stopOrchestration"
            else:
                return f"stop{resource.capitalize()}"
        elif "analyze" in endpoint.path:
            return f"analyze{resource.capitalize()}"
        elif "plan" in endpoint.path:
            return f"create{resource.capitalize()}Plan"
        
        return f"{method_name}{resource.capitalize()}"
    
    def save_specifications(self, output_dir: str = "openapi") -> None:
        """Save OpenAPI specifications to files."""
        
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        spec = self.generate_openapi_spec()
        
        # Save JSON version
        with open(output_path / "openapi.json", "w") as f:
            json.dump(spec, f, indent=2, default=str)
        
        # Save YAML version
        with open(output_path / "openapi.yaml", "w") as f:
            yaml.dump(spec, f, default_flow_style=False, sort_keys=False)
        
        print(f"OpenAPI specifications saved to {output_path}/")
        return str(output_path)

def main():
    """Generate OpenAPI specifications."""
    generator = OpenAPIGenerator()
    output_dir = generator.save_specifications()
    
    print("OpenAPI specification generation completed!")
    print(f"Files generated in: {output_dir}")
    print("\nNext steps:")
    print("1. Review the generated specifications")
    print("2. Run TypeScript client generation")
    print("3. Update frontend to use typed client")

if __name__ == "__main__":
    main()