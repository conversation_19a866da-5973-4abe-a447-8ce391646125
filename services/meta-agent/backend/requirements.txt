# AI Agent Platform - Backend Dependencies

# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9
redis==5.0.1

# Message Queue
kafka-python==2.0.2
confluent-kafka==2.3.0

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
cryptography>=41.0.7,<46.0.0

# AI/ML Integration
openai>=1.6.1,<2.0.0
anthropic>=0.7.8,<1.0.0
google-cloud-aiplatform>=1.38.1,<2.0.0
google-generativeai>=0.3.2,<1.0.0
google-auth>=2.25.2,<3.0.0
google-auth-oauthlib>=1.1.0,<2.0.0
autogen-agentchat>=0.2.36
langchain>=0.1.0,<1.0.0
langchain-openai>=0.0.2,<1.0.0

# Vector Database & Embeddings
qdrant-client==1.7.0
sentence-transformers==2.2.2
numpy==1.24.4

# HTTP & API
httpx==0.25.2
requests==2.31.0
aiohttp==3.9.1

# Configuration & Monitoring
python-dotenv==1.0.0
structlog==23.2.0
prometheus-client==0.19.0
sentry-sdk[fastapi]==1.39.2

# Development
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
mypy==1.7.1
pre-commit==3.6.0

# Utilities
click==8.1.7
python-dateutil==2.8.2
pytz==2023.3
pyyaml==6.0.1