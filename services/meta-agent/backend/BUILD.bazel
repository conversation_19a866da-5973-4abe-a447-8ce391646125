load("@rules_python//python:defs.bzl", "py_binary", "py_library", "py_test")
load("@rules_oci//oci:defs.bzl", "oci_image")
load("@rules_pkg//pkg:tar.bzl", "pkg_tar")

package(default_visibility = ["//visibility:public"])

# Main backend library
py_library(
    name = "backend_lib",
    srcs = glob([
        "src/**/*.py",
        "*.py",
    ], exclude = [
        "**/*_test.py", 
        "**/test_*.py",
        "tests/**/*.py",
    ], allow_empty = True),
    data = [
        "pyproject.toml",
        "requirements.txt",
        "requirements-dev.txt",
        ".env",
    ] + glob([
        "src/**/*.yaml",
        "src/**/*.yml", 
        "src/**/*.json",
        "openapi/**/*",
    ], allow_empty = True),
    deps = [
        # Python dependencies would go here if using pip rules
        # For now, we rely on runtime installation
    ],
    visibility = ["//visibility:public"],
)

# Main backend binary
py_binary(
    name = "backend",
    srcs = ["src/main.py"],
    main = "src/main.py",
    deps = [":backend_lib"],
    data = [
        "pyproject.toml",
        "requirements.txt",
        ".env",
    ] + glob([
        "openapi/**/*",
    ], allow_empty = True),
    python_version = "PY3",
    visibility = ["//visibility:public"],
)

# Alternative startup scripts
py_binary(
    name = "start_simple",
    srcs = ["start_backend.py"],
    main = "start_backend.py", 
    deps = [":backend_lib"],
    data = [
        "pyproject.toml",
        "requirements.txt",
    ],
    python_version = "PY3",
    visibility = ["//visibility:public"],
)

# Shell-based development server (preserves npm-style workflow)
sh_binary(
    name = "dev",
    srcs = ["scripts/run_dev.sh"],
    data = [
        "pyproject.toml",
        "requirements.txt",
        "requirements-dev.txt",
        ".env",
    ] + glob([
        "src/**/*",
        "openapi/**/*",
        "tests/**/*",
    ], allow_empty = True),
)

# Production server 
sh_binary(
    name = "serve",
    srcs = ["scripts/run_serve.sh"],
    data = [
        ":backend_lib",
        "pyproject.toml",
        "requirements.txt",
        ".env",
    ],
)

# Install dependencies
sh_binary(
    name = "install",
    srcs = ["scripts/run_install.sh"],
    data = [
        "pyproject.toml",
        "requirements.txt",
        "requirements-dev.txt",
    ],
)

# Code formatting and linting
sh_binary(
    name = "format",
    srcs = ["scripts/run_format.sh"],
    data = [
        "pyproject.toml",
    ] + glob([
        "src/**/*.py",
        "tests/**/*.py",
    ], allow_empty = True),
)

sh_binary(
    name = "lint",
    srcs = ["scripts/run_lint.sh"],
    data = [
        "pyproject.toml",
    ] + glob([
        "src/**/*.py",
        "tests/**/*.py",
    ], allow_empty = True),
)

# Type checking
sh_binary(
    name = "type_check",
    srcs = ["scripts/run_type_check.sh"],
    data = [
        "pyproject.toml",
    ] + glob([
        "src/**/*.py",
        "tests/**/*.py",
    ], allow_empty = True),
)

# Generate OpenAPI specification
sh_binary(
    name = "generate_openapi",
    srcs = ["scripts/generate_openapi.sh"],
    data = [
        ":backend_lib",
        "generate_openapi_from_app.py",
        "openapi_generator.py",
    ],
)

# Unit tests using pytest
py_test(
    name = "backend_tests",
    srcs = glob([
        "tests/**/*_test.py",
        "tests/**/test_*.py",
    ], allow_empty = True),
    deps = [
        ":backend_lib",
    ],
    data = [
        "pytest.ini",
        "pyproject.toml",
        ".env",
    ],
    python_version = "PY3",
    tags = ["unit"],
)

# Shell-based test runner (preserves pytest workflow)
sh_binary(
    name = "test",
    srcs = ["scripts/run_tests.sh"],
    data = [
        ":backend_lib",
        "pytest.ini",
        "pyproject.toml",
    ] + glob([
        "tests/**/*",
    ], allow_empty = True),
)

# Integration tests
sh_binary(
    name = "test_integration",
    srcs = ["scripts/run_integration_tests.sh"],
    data = [
        ":backend_lib",
        "pytest.ini",
        "pyproject.toml",
    ] + glob([
        "tests/**/*",
    ], allow_empty = True),
    tags = ["integration"],
)

# Database operations
sh_binary(
    name = "migrate",
    srcs = ["scripts/run_migrate.sh"],
    data = [
        ":backend_lib",
        ".env",
    ],
)

sh_binary(
    name = "db_setup",
    srcs = ["scripts/setup_db.sh"],
    data = [
        ":backend_lib",
        ".env",
    ],
)

# Clean build artifacts
sh_binary(
    name = "clean",
    srcs = ["scripts/run_clean.sh"],
)

# Development with frontend connectivity
sh_binary(
    name = "dev_with_frontend",
    srcs = ["scripts/run_dev_with_frontend.sh"],
    data = [
        ":generate_openapi",
        "//services/meta-agent/frontend/api-client:generate_client",
    ],
    tags = ["web", "dev", "frontend"],
)

# Package backend files for container deployment
pkg_tar(
    name = "backend_files",
    srcs = [
        ":backend",
        "requirements.txt",
        ".env",
    ] + glob([
        "src/**/*",
        "openapi/**/*",
    ], allow_empty = True),
    package_dir = "/app",
)

# Package Python runtime setup
pkg_tar(
    name = "python_setup",
    srcs = [
        "Dockerfile",
        "requirements.txt",
        "pyproject.toml",
    ],
    package_dir = "/app",
)

# OCI image for backend deployment
oci_image(
    name = "backend_image",
    base = "@distroless_base",
    exposed_ports = ["8001"],
    tars = [
        ":backend_files",
        ":python_setup",
    ],
    env = {
        "PORT": "8001",
        "PYTHONPATH": "/app/src",
        "PYTHONUNBUFFERED": "1",
    },
    cmd = [
        "python",
        "/app/src/main.py"
    ],
    visibility = ["//visibility:public"],
)

# Alternative Docker build using existing Dockerfile
genrule(
    name = "backend_docker_build",
    srcs = [
        "Dockerfile",
        ":backend_lib",
        "requirements.txt",
        "pyproject.toml",
        ".env",
    ] + glob([
        "src/**/*",
        "openapi/**/*",
    ], allow_empty = True),
    outs = ["backend_image.tar"],
    cmd = """
        # Copy all source files to a temporary directory
        mkdir -p $(@D)/backend_build
        cp -r $(SRCS) $(@D)/backend_build/
        
        # Build Docker image
        cd $(@D)/backend_build
        docker build -t meta-agent-backend:latest .
        docker save meta-agent-backend:latest > $(@D)/backend_image.tar
    """,
    visibility = ["//visibility:public"],
)

# Test suite for backend
test_suite(
    name = "backend_test_suite",
    tags = ["backend", "meta-agent"],
    tests = [
        ":backend_tests",
        ":type_check",
        ":lint",
    ],
)