"""
AI Agent Platform - Protocols Package
Contains communication protocols for agent interactions
"""

from .a2a import (
    a2a_protocol,
    get_a2a_protocol,
    A2AProtocolHandler,
    A2AMessage,
    A2AResponse,
    MessageType,
    MessagePriority,
    ProtocolVersion,
    AgentCapability,
    AgentHandshake
)

__all__ = [
    "a2a_protocol",
    "get_a2a_protocol",
    "A2AProtocolHandler",
    "A2AMessage",
    "A2AResponse",
    "MessageType",
    "MessagePriority",
    "ProtocolVersion",
    "AgentCapability",
    "AgentHandshake"
]