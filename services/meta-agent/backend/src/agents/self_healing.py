"""Self-healing agent monitoring and recovery system.

This module implements an intelligent monitoring agent that can detect issues,
diagnose problems, and automatically heal or recover failed agents.
"""

from typing import Dict, Any, List, Optional, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
import asyncio
import json
import uuid
from datetime import datetime, timedelta
import aiohttp
import psutil
from intelligence.gateway import AIGateway
from intelligence.providers.base import AIMessage
from utils.logging import get_logger, LoggerMixin
from utils.exceptions import AgentError
from services.auto_deployment import AutoDeploymentSystem, DeploymentStatus

logger = get_logger(__name__)

class HealthStatus(str, Enum):
    """Health status levels."""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    FAILED = "failed"
    RECOVERING = "recovering"
    UNKNOWN = "unknown"

class HealingAction(str, Enum):
    """Types of healing actions."""
    RESTART = "restart"
    SCALE_UP = "scale_up"
    SCALE_DOWN = "scale_down"
    ROLLBACK = "rollback"
    REDEPLOY = "redeploy"
    CONFIG_UPDATE = "config_update"
    RESOURCE_ADJUSTMENT = "resource_adjustment"
    NETWORK_RESET = "network_reset"

@dataclass
class HealthMetric:
    """Health metric data point."""
    name: str
    value: float
    unit: str
    threshold_warning: float
    threshold_critical: float
    timestamp: datetime = field(default_factory=datetime.now)
    
    @property
    def status(self) -> HealthStatus:
        """Determine status based on thresholds."""
        if self.value >= self.threshold_critical:
            return HealthStatus.CRITICAL
        elif self.value >= self.threshold_warning:
            return HealthStatus.WARNING
        else:
            return HealthStatus.HEALTHY

@dataclass
class AgentHealth:
    """Complete health assessment for an agent."""
    agent_id: str
    status: HealthStatus
    metrics: Dict[str, HealthMetric] = field(default_factory=dict)
    issues: List[str] = field(default_factory=list)
    last_check: datetime = field(default_factory=datetime.now)
    uptime: timedelta = field(default_factory=lambda: timedelta(0))
    error_rate: float = 0.0
    response_time: float = 0.0
    
@dataclass  
class HealingEvent:
    """Record of a healing action taken."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    agent_id: str = ""
    trigger: str = ""
    action: HealingAction = HealingAction.RESTART
    description: str = ""
    success: bool = False
    error: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    duration: float = 0.0
    details: Dict[str, Any] = field(default_factory=dict)

class SelfHealingAgent(LoggerMixin):
    """Intelligent self-healing monitoring and recovery agent."""
    
    def __init__(
        self,
        ai_gateway: AIGateway,
        deployment_system: AutoDeploymentSystem,
        check_interval: int = 30
    ):
        self.ai_gateway = ai_gateway
        self.deployment_system = deployment_system
        self.check_interval = check_interval
        self.monitored_agents: Dict[str, AgentHealth] = {}
        self.healing_history: List[HealingEvent] = []
        self.active_healings: Dict[str, HealingEvent] = {}
        self.monitoring_task: Optional[asyncio.Task] = None
        self.running = False
        
        # Default thresholds
        self.default_thresholds = {
            "cpu_usage": {"warning": 70.0, "critical": 90.0},
            "memory_usage": {"warning": 80.0, "critical": 95.0},
            "error_rate": {"warning": 5.0, "critical": 15.0},
            "response_time": {"warning": 2000.0, "critical": 5000.0},  # ms
            "disk_usage": {"warning": 85.0, "critical": 95.0}
        }
    
    async def start_monitoring(self) -> None:
        """Start continuous monitoring of agents."""
        if self.running:
            return
        
        self.running = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        
        self.log_operation("self_healing_started", check_interval=self.check_interval)
    
    async def stop_monitoring(self) -> None:
        """Stop monitoring."""
        self.running = False
        
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        self.log_operation("self_healing_stopped")
    
    async def _monitoring_loop(self) -> None:
        """Main monitoring loop."""
        
        while self.running:
            try:
                # Get list of active deployments
                active_deployments = self.deployment_system.list_active_deployments()
                
                # Monitor each deployment
                monitoring_tasks = []
                for deployment in active_deployments:
                    if deployment.status == DeploymentStatus.RUNNING:
                        task = self._monitor_agent(deployment)
                        monitoring_tasks.append(task)
                
                # Execute monitoring tasks concurrently
                if monitoring_tasks:
                    await asyncio.gather(*monitoring_tasks, return_exceptions=True)
                
                # Process healing actions
                await self._process_healing_actions()
                
                # Sleep until next check
                await asyncio.sleep(self.check_interval)
                
            except Exception as e:
                self.log_error("monitoring_loop_error", e)
                await asyncio.sleep(self.check_interval)
    
    async def _monitor_agent(self, deployment: DeploymentStatus) -> None:
        """Monitor a specific agent deployment."""
        
        try:
            agent_id = deployment.agent_id
            
            # Initialize agent health if not exists
            if agent_id not in self.monitored_agents:
                self.monitored_agents[agent_id] = AgentHealth(
                    agent_id=agent_id,
                    status=HealthStatus.UNKNOWN
                )
            
            agent_health = self.monitored_agents[agent_id]
            
            # Collect metrics
            metrics = await self._collect_agent_metrics(deployment)
            agent_health.metrics.update(metrics)
            
            # Perform health checks
            health_status = await self._perform_health_checks(deployment)
            agent_health.status = health_status
            
            # Update last check time
            agent_health.last_check = datetime.now()
            
            # Check if healing is needed
            if health_status in [HealthStatus.CRITICAL, HealthStatus.FAILED]:
                await self._trigger_healing(agent_id, agent_health)
            
        except Exception as e:
            self.log_error("agent_monitoring_failed", e, agent_id=deployment.agent_id)
    
    async def _collect_agent_metrics(self, deployment: DeploymentStatus) -> Dict[str, HealthMetric]:
        """Collect performance and health metrics for an agent."""
        
        metrics = {}
        
        try:
            # Collect Kubernetes pod metrics
            k8s_metrics = await self._collect_k8s_metrics(deployment)
            metrics.update(k8s_metrics)
            
            # Collect application-level metrics
            app_metrics = await self._collect_app_metrics(deployment)
            metrics.update(app_metrics)
            
            # Collect custom metrics
            custom_metrics = await self._collect_custom_metrics(deployment)
            metrics.update(custom_metrics)
            
        except Exception as e:
            self.logger.warning(f"Metric collection failed for {deployment.agent_id}: {e}")
        
        return metrics
    
    async def _collect_k8s_metrics(self, deployment: DeploymentStatus) -> Dict[str, HealthMetric]:
        """Collect Kubernetes pod metrics."""
        
        metrics = {}
        
        try:
            # Get pod metrics via kubectl
            agent_name = f"agent-{deployment.agent_id}"
            
            # CPU usage
            cpu_process = await asyncio.create_subprocess_exec(
                "kubectl", "top", "pod", "-l", f"app={agent_name}",
                "--no-headers", "--namespace", "ai-agents",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, _ = await cpu_process.communicate()
            
            if cpu_process.returncode == 0 and stdout:
                lines = stdout.decode().strip().split('\n')
                for line in lines:
                    parts = line.split()
                    if len(parts) >= 3:
                        cpu_str = parts[1]  # e.g., "150m"
                        memory_str = parts[2]  # e.g., "256Mi"
                        
                        # Parse CPU (millicores to percentage)
                        if cpu_str.endswith('m'):
                            cpu_value = float(cpu_str[:-1]) / 10  # Convert to percentage
                        else:
                            cpu_value = float(cpu_str) * 100
                        
                        metrics["cpu_usage"] = HealthMetric(
                            name="cpu_usage",
                            value=cpu_value,
                            unit="%",
                            threshold_warning=self.default_thresholds["cpu_usage"]["warning"],
                            threshold_critical=self.default_thresholds["cpu_usage"]["critical"]
                        )
                        
                        # Parse Memory (convert to percentage)
                        if memory_str.endswith('Mi'):
                            memory_mb = float(memory_str[:-2])
                            # Assume 512Mi limit for percentage calculation
                            memory_percentage = (memory_mb / 512) * 100
                        else:
                            memory_percentage = 50  # Default assumption
                        
                        metrics["memory_usage"] = HealthMetric(
                            name="memory_usage",
                            value=memory_percentage,
                            unit="%",
                            threshold_warning=self.default_thresholds["memory_usage"]["warning"],
                            threshold_critical=self.default_thresholds["memory_usage"]["critical"]
                        )
                        
                        break
                        
        except Exception as e:
            self.logger.warning(f"K8s metrics collection failed: {e}")
        
        return metrics
    
    async def _collect_app_metrics(self, deployment: DeploymentStatus) -> Dict[str, HealthMetric]:
        """Collect application-level metrics."""
        
        metrics = {}
        
        try:
            # Try to get metrics from agent's /metrics endpoint
            if deployment.ports:
                main_port = deployment.ports[0]
                metrics_url = f"http://localhost:{main_port.port}/metrics"
                
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
                    async with session.get(metrics_url) as response:
                        if response.status == 200:
                            metrics_data = await response.json()
                            
                            # Parse application metrics
                            if "error_rate" in metrics_data:
                                metrics["error_rate"] = HealthMetric(
                                    name="error_rate",
                                    value=float(metrics_data["error_rate"]),
                                    unit="%",
                                    threshold_warning=self.default_thresholds["error_rate"]["warning"],
                                    threshold_critical=self.default_thresholds["error_rate"]["critical"]
                                )
                            
                            if "response_time" in metrics_data:
                                metrics["response_time"] = HealthMetric(
                                    name="response_time", 
                                    value=float(metrics_data["response_time"]),
                                    unit="ms",
                                    threshold_warning=self.default_thresholds["response_time"]["warning"],
                                    threshold_critical=self.default_thresholds["response_time"]["critical"]
                                )
                                
        except Exception as e:
            # Metrics endpoint not available or failed
            pass
        
        return metrics
    
    async def _collect_custom_metrics(self, deployment: DeploymentStatus) -> Dict[str, HealthMetric]:
        """Collect custom agent-specific metrics."""
        
        metrics = {}
        
        try:
            # Collect system-level metrics if running locally
            if hasattr(psutil, 'disk_usage'):
                disk_usage = psutil.disk_usage('/')
                disk_percentage = (disk_usage.used / disk_usage.total) * 100
                
                metrics["disk_usage"] = HealthMetric(
                    name="disk_usage",
                    value=disk_percentage,
                    unit="%",
                    threshold_warning=self.default_thresholds["disk_usage"]["warning"],
                    threshold_critical=self.default_thresholds["disk_usage"]["critical"]
                )
                
        except Exception as e:
            self.logger.warning(f"Custom metrics collection failed: {e}")
        
        return metrics
    
    async def _perform_health_checks(self, deployment: DeploymentStatus) -> HealthStatus:
        """Perform comprehensive health checks."""
        
        health_issues = []
        overall_status = HealthStatus.HEALTHY
        
        try:
            # Check if deployment is running
            if deployment.status != DeploymentStatus.RUNNING:
                health_issues.append(f"Deployment status is {deployment.status}")
                overall_status = HealthStatus.FAILED
                return overall_status
            
            # Check replica health
            if deployment.replicas["ready"] == 0:
                health_issues.append("No ready replicas")
                overall_status = HealthStatus.FAILED
            elif deployment.replicas["ready"] < deployment.replicas["desired"]:
                health_issues.append("Some replicas not ready")
                overall_status = max(overall_status, HealthStatus.WARNING)
            
            # Check HTTP endpoint health
            endpoint_healthy = await self._check_http_health(deployment)
            if not endpoint_healthy:
                health_issues.append("HTTP health check failed")
                overall_status = max(overall_status, HealthStatus.CRITICAL)
            
            # Check metric thresholds
            agent_health = self.monitored_agents.get(deployment.agent_id)
            if agent_health:
                for metric_name, metric in agent_health.metrics.items():
                    if metric.status == HealthStatus.CRITICAL:
                        health_issues.append(f"{metric_name} critical: {metric.value}{metric.unit}")
                        overall_status = max(overall_status, HealthStatus.CRITICAL)
                    elif metric.status == HealthStatus.WARNING:
                        health_issues.append(f"{metric_name} warning: {metric.value}{metric.unit}")
                        overall_status = max(overall_status, HealthStatus.WARNING)
            
            # Store issues
            if agent_health:
                agent_health.issues = health_issues
                
        except Exception as e:
            self.log_error("health_check_failed", e, agent_id=deployment.agent_id)
            overall_status = HealthStatus.UNKNOWN
        
        return overall_status
    
    async def _check_http_health(self, deployment: DeploymentStatus) -> bool:
        """Check HTTP health endpoint."""
        
        if not deployment.ports:
            return True  # No HTTP ports to check
        
        try:
            main_port = deployment.ports[0]
            health_url = f"http://localhost:{main_port.port}/health"
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
                async with session.get(health_url) as response:
                    return response.status == 200
                    
        except Exception:
            return False
    
    async def _trigger_healing(self, agent_id: str, agent_health: AgentHealth) -> None:
        """Trigger intelligent healing for an unhealthy agent."""
        
        if agent_id in self.active_healings:
            # Healing already in progress
            return
        
        # Analyze issues and determine best healing action
        healing_action = await self._analyze_and_recommend_healing(agent_health)
        
        healing_event = HealingEvent(
            agent_id=agent_id,
            trigger=f"Status: {agent_health.status}, Issues: {', '.join(agent_health.issues[:3])}",
            action=healing_action,
            description=f"Healing {agent_id} with action: {healing_action.value}"
        )
        
        self.active_healings[agent_id] = healing_event
        
        self.log_operation(
            "healing_triggered",
            agent_id=agent_id,
            action=healing_action.value,
            trigger=healing_event.trigger
        )
        
        # Execute healing action
        try:
            start_time = datetime.now()
            success = await self._execute_healing_action(healing_action, agent_id, agent_health)
            
            healing_event.success = success
            healing_event.duration = (datetime.now() - start_time).total_seconds()
            
            if success:
                # Mark agent as recovering
                agent_health.status = HealthStatus.RECOVERING
                self.log_operation("healing_successful", agent_id=agent_id, action=healing_action.value)
            else:
                self.log_operation("healing_failed", agent_id=agent_id, action=healing_action.value)
                
        except Exception as e:
            healing_event.success = False
            healing_event.error = str(e)
            self.log_error("healing_execution_failed", e, agent_id=agent_id)
        
        # Complete healing
        self.healing_history.append(healing_event)
        del self.active_healings[agent_id]
    
    async def _analyze_and_recommend_healing(self, agent_health: AgentHealth) -> HealingAction:
        """Use AI to analyze issues and recommend healing action."""
        
        analysis_prompt = f"""Analyze this agent health situation and recommend the best healing action:

Agent ID: {agent_health.agent_id}
Status: {agent_health.status}
Issues: {agent_health.issues}
Metrics:
{json.dumps({name: {'value': m.value, 'unit': m.unit, 'status': m.status.value} for name, m in agent_health.metrics.items()}, indent=2)}

Error Rate: {agent_health.error_rate}%
Response Time: {agent_health.response_time}ms
Uptime: {agent_health.uptime}

Available Actions:
1. RESTART - Restart the agent containers
2. SCALE_UP - Increase replica count
3. SCALE_DOWN - Decrease replica count  
4. ROLLBACK - Rollback to previous version
5. REDEPLOY - Complete redeployment
6. CONFIG_UPDATE - Update configuration
7. RESOURCE_ADJUSTMENT - Adjust CPU/memory limits
8. NETWORK_RESET - Reset network connections

Consider:
- Severity of issues
- Resource constraints
- Impact on users
- Recovery time
- Success probability

Respond with only the action name (e.g., "RESTART")."""
        
        try:
            messages = [
                AIMessage(role="system", content="You are an expert system administrator specializing in automated healing of distributed systems."),
                AIMessage(role="user", content=analysis_prompt)
            ]
            
            response = await self.ai_gateway.chat_completion(
                messages=messages,
                model="gpt-4o",
                temperature=0.3,
                max_tokens=100
            )
            
            action_str = response.content.strip()
            try:
                return HealingAction(action_str.lower())
            except ValueError:
                # Fallback to restart if AI response is invalid
                return HealingAction.RESTART
                
        except Exception as e:
            self.logger.warning(f"AI healing analysis failed: {e}")
            # Fallback logic based on status
            if agent_health.status == HealthStatus.FAILED:
                return HealingAction.RESTART
            elif "memory" in " ".join(agent_health.issues).lower():
                return HealingAction.RESOURCE_ADJUSTMENT
            elif "cpu" in " ".join(agent_health.issues).lower():
                return HealingAction.RESOURCE_ADJUSTMENT
            else:
                return HealingAction.RESTART
    
    async def _execute_healing_action(
        self,
        action: HealingAction,
        agent_id: str,
        agent_health: AgentHealth
    ) -> bool:
        """Execute a specific healing action."""
        
        try:
            if action == HealingAction.RESTART:
                return await self._restart_agent(agent_id)
            elif action == HealingAction.SCALE_UP:
                return await self._scale_agent(agent_id, scale_up=True)
            elif action == HealingAction.SCALE_DOWN:
                return await self._scale_agent(agent_id, scale_up=False)
            elif action == HealingAction.RESOURCE_ADJUSTMENT:
                return await self._adjust_resources(agent_id, agent_health)
            elif action == HealingAction.REDEPLOY:
                return await self._redeploy_agent(agent_id)
            else:
                self.logger.warning(f"Healing action {action} not implemented")
                return False
                
        except Exception as e:
            self.log_error("healing_action_failed", e, agent_id=agent_id, action=action.value)
            return False
    
    async def _restart_agent(self, agent_id: str) -> bool:
        """Restart agent pods."""
        
        try:
            agent_name = f"agent-{agent_id}"
            
            # Restart by deleting pods (deployment will recreate them)
            restart_process = await asyncio.create_subprocess_exec(
                "kubectl", "delete", "pods", "-l", f"app={agent_name}",
                "-n", "ai-agents",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await restart_process.communicate()
            
            return restart_process.returncode == 0
            
        except Exception as e:
            self.logger.error(f"Agent restart failed: {e}")
            return False
    
    async def _scale_agent(self, agent_id: str, scale_up: bool) -> bool:
        """Scale agent replicas up or down."""
        
        try:
            agent_name = f"agent-{agent_id}"
            
            # Get current replica count
            get_process = await asyncio.create_subprocess_exec(
                "kubectl", "get", "deployment", agent_name, "-n", "ai-agents",
                "-o", "jsonpath={.spec.replicas}",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await get_process.communicate()
            
            if get_process.returncode != 0:
                return False
            
            current_replicas = int(stdout.decode().strip() or "1")
            
            if scale_up:
                new_replicas = min(current_replicas + 1, 5)  # Max 5 replicas
            else:
                new_replicas = max(current_replicas - 1, 1)  # Min 1 replica
            
            if new_replicas == current_replicas:
                return True  # No change needed
            
            # Scale the deployment
            scale_process = await asyncio.create_subprocess_exec(
                "kubectl", "scale", "deployment", agent_name,
                "-n", "ai-agents", f"--replicas={new_replicas}",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await scale_process.communicate()
            
            return scale_process.returncode == 0
            
        except Exception as e:
            self.logger.error(f"Agent scaling failed: {e}")
            return False
    
    async def _adjust_resources(self, agent_id: str, agent_health: AgentHealth) -> bool:
        """Adjust CPU/memory resources based on metrics."""
        
        try:
            # Analyze metrics to determine resource adjustments
            cpu_metric = agent_health.metrics.get("cpu_usage")
            memory_metric = agent_health.metrics.get("memory_usage")
            
            adjustments = []
            
            if cpu_metric and cpu_metric.status == HealthStatus.CRITICAL:
                adjustments.append("cpu=200m,cpu_limit=1000m")
            
            if memory_metric and memory_metric.status == HealthStatus.CRITICAL:
                adjustments.append("memory=256Mi,memory_limit=1Gi")
            
            if not adjustments:
                return True  # No adjustments needed
            
            # Apply resource adjustments (would need more complex Kubernetes patching in real implementation)
            self.logger.info(f"Would adjust resources for {agent_id}: {adjustments}")
            return True
            
        except Exception as e:
            self.logger.error(f"Resource adjustment failed: {e}")
            return False
    
    async def _redeploy_agent(self, agent_id: str) -> bool:
        """Completely redeploy an agent."""
        
        try:
            # This would trigger a complete redeployment through the deployment system
            # For now, simulate with a restart
            return await self._restart_agent(agent_id)
            
        except Exception as e:
            self.logger.error(f"Agent redeployment failed: {e}")
            return False
    
    async def _process_healing_actions(self) -> None:
        """Process and monitor ongoing healing actions."""
        
        for agent_id, healing_event in list(self.active_healings.items()):
            # Check if healing has been running too long
            elapsed = (datetime.now() - healing_event.timestamp).total_seconds()
            
            if elapsed > 300:  # 5 minutes timeout
                healing_event.success = False
                healing_event.error = "Healing action timeout"
                self.healing_history.append(healing_event)
                del self.active_healings[agent_id]
    
    def get_agent_health(self, agent_id: str) -> Optional[AgentHealth]:
        """Get current health status for an agent."""
        return self.monitored_agents.get(agent_id)
    
    def get_all_agent_health(self) -> Dict[str, AgentHealth]:
        """Get health status for all monitored agents."""
        return self.monitored_agents.copy()
    
    def get_healing_history(self, agent_id: Optional[str] = None) -> List[HealingEvent]:
        """Get healing history, optionally filtered by agent."""
        if agent_id:
            return [event for event in self.healing_history if event.agent_id == agent_id]
        return self.healing_history.copy()
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get overall health summary."""
        
        status_counts = {}
        total_agents = len(self.monitored_agents)
        
        for health in self.monitored_agents.values():
            status = health.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
        
        return {
            "total_agents": total_agents,
            "status_counts": status_counts,
            "active_healings": len(self.active_healings),
            "healing_events_24h": len([
                event for event in self.healing_history
                if event.timestamp > datetime.now() - timedelta(days=1)
            ])
        }