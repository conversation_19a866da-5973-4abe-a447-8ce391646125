import { Router, Route } from 'preact-router'
import { Header } from '@/components/Header'
import { HomePage } from '@/pages/HomePage'
import { ApiInfoPage } from '@/pages/ApiInfoPage'
{{COMPONENT_IMPORTS}}

export function App() {
  return (
    <div class="min-h-screen bg-gray-50">
      <Header />
      <main class="container mx-auto px-4 py-8">
        <Router>
          <Route path="/" component={HomePage} />
          <Route path="/api-info" component={ApiInfoPage} />
          {{ROUTES}}
        </Router>
      </main>
    </div>
  )
}