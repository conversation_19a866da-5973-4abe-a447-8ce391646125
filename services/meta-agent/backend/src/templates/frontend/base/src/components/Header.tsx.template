import { Link } from 'preact-router/match'

export function Header() {
  return (
    <header class="bg-white shadow-sm border-b">
      <div class="container mx-auto px-4">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center space-x-8">
            <h1 class="text-xl font-bold text-gray-900">
              {{AGENT_NAME}}
            </h1>
            <nav class="hidden md:flex space-x-4">
              <Link 
                href="/" 
                class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                activeClassName="bg-gray-100 text-gray-900"
              >
                Home
              </Link>
              <Link 
                href="/api-info" 
                class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                activeClassName="bg-gray-100 text-gray-900"
              >
                API Info
              </Link>
              {{NAV_LINKS}}
            </nav>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-500">v{{VERSION}}</span>
            <div class="w-2 h-2 bg-green-500 rounded-full" title="Connected"></div>
          </div>
        </div>
      </div>
    </header>
  )
}