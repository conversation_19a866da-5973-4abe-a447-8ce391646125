import axios, { AxiosInstance } from 'axios'

// API base URL - uses proxy in development, direct path in production
const API_BASE_URL = import.meta.env.DEV ? '/api' : '/api'

// Generated API types
{{API_TYPES}}

// Base API client class
export class ApiClient {
  private client: AxiosInstance

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 30000,
    })

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Add auth token if available
        const token = localStorage.getItem('auth_token')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => Promise.reject(error)
    )

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Handle unauthorized
          localStorage.removeItem('auth_token')
          window.location.href = '/login'
        }
        return Promise.reject(error)
      }
    )
  }

  // Health check
  async checkHealth(): Promise<{ status: string }> {
    const response = await this.client.get('/health')
    return response.data
  }

  // Get agent info
  async getAgentInfo(): Promise<AgentInfo> {
    const response = await this.client.get('/')
    return response.data
  }

  // Generated API methods
  {{API_METHODS}}
}

// Export singleton instance
export const apiClient = new ApiClient()

// Convenience functions (generated)
{{CONVENIENCE_FUNCTIONS}}

// Common types
export interface AgentInfo {
  agent_id: string
  name: string
  type: string
  capabilities: string[]
  status: string
  version: string
  description: string
  created_at: string
  a2a_protocol: string
  endpoints: Array<{
    path: string
    description: string
  }>
}