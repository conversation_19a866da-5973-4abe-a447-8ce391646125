"""
Authentication and Authorization Dependencies
Enhanced security with OAuth, MFA, and RBAC
"""

from fastapi import Depends, HTTPException, status, Request
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from typing import Dict, Any, Optional, Callable
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import select
from uuid import UUID
import structlog

from config.settings import settings
from database.connection import get_db
from database.models import User, LoginAttempt
from auth.rbac import rbac_service

logger = structlog.get_logger(__name__)

# Security scheme
security = HTTPBearer()


async def get_current_user(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """Get current authenticated user with enhanced security"""
    
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # Decode JWT token
        token = credentials.credentials
        payload = jwt.decode(
            token,
            settings.security.secret_key,
            algorithms=[settings.security.algorithm]
        )
        
        # Extract user information
        user_id: str = payload.get("sub")
        if user_id is None:
            logger.warning("Token missing user ID")
            raise credentials_exception
        
        # Check token expiration
        exp = payload.get("exp")
        if exp and datetime.utcnow().timestamp() > exp:
            logger.warning("Token has expired", user_id=user_id)
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
    except JWTError as e:
        logger.warning(f"JWT validation failed: {e}")
        raise credentials_exception
    
    # Get user from database
    from sqlalchemy import select
    result = await db.execute(select(User).where(User.id == UUID(user_id)))
    user = result.scalar_one_or_none()
    
    if user is None:
        logger.warning("User not found", user_id=user_id)
        raise credentials_exception
    
    # Check if user is active
    if not user.is_active:
        logger.warning("Inactive user attempted access", user_id=user_id)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User account is disabled"
        )
    
    # Log successful authentication
    await _log_authentication_success(request, user, db)
    
    # Update last login
    user.last_login_at = datetime.utcnow()
    await db.commit()
    
    return user


async def get_current_user_optional(
    request: Request,
    db: Session = Depends(get_db)
) -> Optional[User]:
    """Get current user if authenticated, otherwise return None"""
    # Try to get authorization header manually
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        return None
    
    try:
        # Create credentials manually
        from fastapi.security import HTTPAuthorizationCredentials
        credentials = HTTPAuthorizationCredentials(
            scheme="Bearer",
            credentials=auth_header.split(" ", 1)[1]
        )
        return await get_current_user(request, credentials, db)
    except HTTPException:
        return None


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """Get current active user"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


async def get_current_superuser(
    current_user: User = Depends(get_current_user)
) -> User:
    """Get current superuser"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user


def require_permission(permission: str) -> Callable:
    """Dependency factory for requiring specific permissions"""
    
    async def permission_dependency(
        current_user: User = Depends(get_current_user),
        db: Session = Depends(get_db)
    ) -> bool:
        """Check if user has required permission"""
        has_permission = await rbac_service.check_permission(current_user, permission, db)
        
        if not has_permission:
            logger.warning(
                f"Access denied: User lacks permission",
                user_id=str(current_user.id),
                permission=permission
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions: {permission} required"
            )
        
        return True
    
    return permission_dependency


def require_resource_access(resource: str, action: str) -> Callable:
    """Dependency factory for requiring resource-specific access"""
    
    async def resource_dependency(
        current_user: User = Depends(get_current_user),
        db: Session = Depends(get_db)
    ) -> bool:
        """Check if user can access resource with specific action"""
        has_access = await rbac_service.check_resource_access(current_user, resource, action, db)
        
        if not has_access:
            logger.warning(
                f"Resource access denied",
                user_id=str(current_user.id),
                resource=resource,
                action=action
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Cannot {action} {resource}: insufficient permissions"
            )
        
        return True
    
    return resource_dependency


async def _log_authentication_success(request: Request, user: User, db: Session):
    """Log successful authentication"""
    try:
        login_attempt = LoginAttempt(
            email=user.email,
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent"),
            success=True,
            oauth_provider=user.oauth_provider,
        )
        db.add(login_attempt)
        await db.commit()
    except Exception as e:
        logger.error(f"Failed to log authentication success: {e}")


# Optional authentication (user might not be logged in)
async def get_optional_user(
    current_user: Optional[User] = Depends(get_current_user_optional)
) -> Optional[User]:
    """Get current user if authenticated, None otherwise"""
    return current_user


async def verify_agent_token(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """Verify agent-to-agent communication token"""
    
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate agent token",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # Decode JWT token
        token = credentials.credentials
        payload = jwt.decode(
            token,
            settings.security.secret_key,
            algorithms=[settings.security.algorithm]
        )
        
        # Check if this is an agent token
        token_type = payload.get("type")
        agent_id = payload.get("agent_id")
        
        if token_type != "agent" or not agent_id:
            logger.warning("Invalid agent token format")
            raise credentials_exception
        
        # Verify token hasn't expired
        exp = payload.get("exp")
        if exp is None or datetime.utcfromtimestamp(exp) < datetime.utcnow():
            logger.warning("Agent token expired")
            raise credentials_exception
        
        return {
            "agent_id": agent_id,
            "permissions": payload.get("permissions", []),
            "capabilities": payload.get("capabilities", [])
        }
        
    except JWTError:
        logger.warning("Agent token decode failed")
        raise credentials_exception