"""
OAuth 2.0 Authentication Provider
Advanced authentication with multiple OAuth providers
"""

from datetime import datetime, timedelta
import secrets
import base64
import hashlib
from typing import Dict, Any, Optional, List
from urllib.parse import urlencode, parse_qs
import httpx
from jose import jwt, JW<PERSON>rror
from passlib.context import Crypt<PERSON>ontext
from sqlalchemy.orm import Session
from fastapi import H<PERSON><PERSON>Ex<PERSON>, status
import structlog

from config.settings import settings
from database.models import User, OAuthToken
from database.connection import get_db

logger = structlog.get_logger(__name__)

# OAuth Provider Configurations
OAUTH_PROVIDERS = {
    "google": {
        "client_id": settings.oauth.google_client_id,
        "client_secret": settings.oauth.google_client_secret,
        "authorize_url": "https://accounts.google.com/o/oauth2/v2/auth",
        "token_url": "https://oauth2.googleapis.com/token",
        "user_info_url": "https://www.googleapis.com/oauth2/v2/userinfo",
        "scopes": ["openid", "email", "profile"],
        "redirect_uri": f"{settings.backend_url}/auth/oauth/google/callback"
    },
    "github": {
        "client_id": settings.oauth.github_client_id,
        "client_secret": settings.oauth.github_client_secret,
        "authorize_url": "https://github.com/login/oauth/authorize",
        "token_url": "https://github.com/login/oauth/access_token",
        "user_info_url": "https://api.github.com/user",
        "scopes": ["user:email"],
        "redirect_uri": f"{settings.backend_url}/auth/oauth/github/callback"
    },
    "microsoft": {
        "client_id": settings.oauth.microsoft_client_id,
        "client_secret": settings.oauth.microsoft_client_secret,
        "authorize_url": "https://login.microsoftonline.com/common/oauth2/v2.0/authorize",
        "token_url": "https://login.microsoftonline.com/common/oauth2/v2.0/token",
        "user_info_url": "https://graph.microsoft.com/v1.0/me",
        "scopes": ["User.Read"],
        "redirect_uri": f"{settings.backend_url}/auth/oauth/microsoft/callback"
    }
}

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class OAuthProvider:
    """OAuth 2.0 provider handler"""
    
    def __init__(self, provider_name: str):
        if provider_name not in OAUTH_PROVIDERS:
            raise ValueError(f"Unsupported OAuth provider: {provider_name}")
        
        self.provider_name = provider_name
        self.config = OAUTH_PROVIDERS[provider_name]
        self.client_id = self.config["client_id"]
        self.client_secret = self.config["client_secret"]
        self.authorize_url = self.config["authorize_url"]
        self.token_url = self.config["token_url"]
        self.user_info_url = self.config["user_info_url"]
        self.scopes = self.config["scopes"]
        self.redirect_uri = self.config["redirect_uri"]
        
        logger.info(f"Initialized OAuth provider: {provider_name}")
    
    def generate_authorization_url(self, state: str) -> str:
        """Generate OAuth authorization URL"""
        params = {
            "client_id": self.client_id,
            "response_type": "code",
            "scope": " ".join(self.scopes),
            "redirect_uri": self.redirect_uri,
            "state": state,
        }
        
        # Provider-specific parameters
        if self.provider_name == "google":
            params["access_type"] = "offline"
            params["prompt"] = "consent"
        elif self.provider_name == "github":
            params["allow_signup"] = "true"
        elif self.provider_name == "microsoft":
            params["response_mode"] = "query"
        
        return f"{self.authorize_url}?{urlencode(params)}"
    
    async def exchange_code_for_token(self, code: str, state: str) -> Dict[str, Any]:
        """Exchange authorization code for access token"""
        data = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": self.redirect_uri,
        }
        
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        # GitHub requires specific Accept header
        if self.provider_name == "github":
            headers["Accept"] = "application/json"
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.token_url,
                    data=data,
                    headers=headers,
                    timeout=30.0
                )
                response.raise_for_status()
                
                token_data = response.json()
                logger.info(f"Successfully exchanged code for token with {self.provider_name}")
                return token_data
                
        except Exception as e:
            logger.error(f"Failed to exchange code for token with {self.provider_name}: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to exchange authorization code for token"
            )
    
    async def get_user_info(self, access_token: str) -> Dict[str, Any]:
        """Get user information using access token"""
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json"
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    self.user_info_url,
                    headers=headers,
                    timeout=30.0
                )
                response.raise_for_status()
                
                user_data = response.json()
                logger.info(f"Successfully retrieved user info from {self.provider_name}")
                return user_data
                
        except Exception as e:
            logger.error(f"Failed to get user info from {self.provider_name}: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to retrieve user information"
            )


class OAuthService:
    """OAuth authentication service"""
    
    def __init__(self):
        self.providers = {
            name: OAuthProvider(name) 
            for name in OAUTH_PROVIDERS.keys()
        }
        self.active_states: Dict[str, Dict[str, Any]] = {}
        logger.info("OAuth service initialized with providers:", providers=list(self.providers.keys()))
    
    def generate_state(self, provider: str, redirect_url: Optional[str] = None) -> str:
        """Generate secure state parameter for OAuth flow"""
        state = secrets.token_urlsafe(32)
        
        self.active_states[state] = {
            "provider": provider,
            "created_at": datetime.utcnow(),
            "redirect_url": redirect_url,
        }
        
        # Clean up old states (older than 10 minutes)
        self._cleanup_expired_states()
        
        return state
    
    def validate_state(self, state: str) -> Dict[str, Any]:
        """Validate state parameter and return associated data"""
        if state not in self.active_states:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired state parameter"
            )
        
        state_data = self.active_states.pop(state)
        
        # Check if state is not too old (10 minutes max)
        if datetime.utcnow() - state_data["created_at"] > timedelta(minutes=10):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="State parameter has expired"
            )
        
        return state_data
    
    def _cleanup_expired_states(self):
        """Remove expired state parameters"""
        cutoff = datetime.utcnow() - timedelta(minutes=10)
        expired_states = [
            state for state, data in self.active_states.items()
            if data["created_at"] < cutoff
        ]
        
        for state in expired_states:
            del self.active_states[state]
    
    def get_provider(self, provider_name: str) -> OAuthProvider:
        """Get OAuth provider by name"""
        if provider_name not in self.providers:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported OAuth provider: {provider_name}"
            )
        
        return self.providers[provider_name]
    
    def get_available_providers(self) -> List[Dict[str, Any]]:
        """Get list of available OAuth providers"""
        return [
            {
                "name": name,
                "display_name": name.title(),
                "enabled": bool(config["client_id"] and config["client_secret"])
            }
            for name, config in OAUTH_PROVIDERS.items()
        ]
    
    async def authenticate_user(self, provider_name: str, code: str, state: str, db: Session) -> Dict[str, Any]:
        """Complete OAuth authentication flow"""
        # Validate state
        state_data = self.validate_state(state)
        if state_data["provider"] != provider_name:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="State provider mismatch"
            )
        
        # Get provider
        provider = self.get_provider(provider_name)
        
        # Exchange code for token
        token_data = await provider.exchange_code_for_token(code, state)
        
        # Get user information
        user_info = await provider.get_user_info(token_data["access_token"])
        
        # Normalize user data based on provider
        normalized_user = self._normalize_user_data(provider_name, user_info)
        
        # Find or create user
        user = self._find_or_create_user(db, normalized_user, provider_name)
        
        # Store/update OAuth token
        self._store_oauth_token(db, user.id, provider_name, token_data)
        
        # Generate JWT token for our system
        access_token = self._create_access_token(
            data={"sub": str(user.id), "email": user.email, "provider": provider_name}
        )
        
        logger.info(f"User authenticated via {provider_name}", user_id=user.id, email=user.email)
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": {
                "id": user.id,
                "email": user.email,
                "full_name": user.full_name,
                "avatar_url": user.avatar_url,
                "provider": provider_name,
                "is_verified": True,  # OAuth users are considered verified
            },
            "redirect_url": state_data.get("redirect_url")
        }
    
    def _normalize_user_data(self, provider: str, user_info: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize user data from different OAuth providers"""
        if provider == "google":
            return {
                "email": user_info.get("email"),
                "full_name": user_info.get("name"),
                "first_name": user_info.get("given_name"),
                "last_name": user_info.get("family_name"),
                "avatar_url": user_info.get("picture"),
                "provider_user_id": user_info.get("id"),
                "is_verified": user_info.get("verified_email", False),
            }
        elif provider == "github":
            return {
                "email": user_info.get("email"),
                "full_name": user_info.get("name") or user_info.get("login"),
                "first_name": user_info.get("name", "").split(" ")[0] if user_info.get("name") else None,
                "last_name": " ".join(user_info.get("name", "").split(" ")[1:]) if user_info.get("name") else None,
                "avatar_url": user_info.get("avatar_url"),
                "provider_user_id": str(user_info.get("id")),
                "is_verified": True,  # GitHub accounts are considered verified
            }
        elif provider == "microsoft":
            return {
                "email": user_info.get("mail") or user_info.get("userPrincipalName"),
                "full_name": user_info.get("displayName"),
                "first_name": user_info.get("givenName"),
                "last_name": user_info.get("surname"),
                "avatar_url": None,  # Microsoft Graph doesn't provide avatar in basic info
                "provider_user_id": user_info.get("id"),
                "is_verified": True,  # Microsoft accounts are considered verified
            }
        else:
            raise ValueError(f"Unknown provider: {provider}")
    
    def _find_or_create_user(self, db: Session, user_data: Dict[str, Any], provider: str) -> User:
        """Find existing user or create new one"""
        # First, try to find user by email
        user = db.query(User).filter(User.email == user_data["email"]).first()
        
        if user:
            # Update user information if needed
            if not user.full_name and user_data.get("full_name"):
                user.full_name = user_data["full_name"]
            if not user.avatar_url and user_data.get("avatar_url"):
                user.avatar_url = user_data["avatar_url"]
            if not user.is_verified and user_data.get("is_verified"):
                user.is_verified = True
                user.verified_at = datetime.utcnow()
            
            user.last_login_at = datetime.utcnow()
            user.oauth_provider = provider
            user.oauth_provider_id = user_data.get("provider_user_id")
            
            db.commit()
            db.refresh(user)
            
            logger.info(f"Updated existing user via OAuth", user_id=user.id, provider=provider)
        else:
            # Create new user
            user = User(
                email=user_data["email"],
                full_name=user_data.get("full_name"),
                first_name=user_data.get("first_name"),
                last_name=user_data.get("last_name"),
                avatar_url=user_data.get("avatar_url"),
                is_verified=user_data.get("is_verified", False),
                verified_at=datetime.utcnow() if user_data.get("is_verified") else None,
                oauth_provider=provider,
                oauth_provider_id=user_data.get("provider_user_id"),
                created_at=datetime.utcnow(),
                last_login_at=datetime.utcnow(),
                is_active=True,
            )
            
            db.add(user)
            db.commit()
            db.refresh(user)
            
            logger.info(f"Created new user via OAuth", user_id=user.id, provider=provider, email=user.email)
        
        return user
    
    def _store_oauth_token(self, db: Session, user_id: int, provider: str, token_data: Dict[str, Any]):
        """Store or update OAuth token for user"""
        # Find existing token
        oauth_token = db.query(OAuthToken).filter(
            OAuthToken.user_id == user_id,
            OAuthToken.provider == provider
        ).first()
        
        if oauth_token:
            # Update existing token
            oauth_token.access_token = token_data.get("access_token")
            oauth_token.refresh_token = token_data.get("refresh_token")
            oauth_token.token_type = token_data.get("token_type", "Bearer")
            oauth_token.expires_in = token_data.get("expires_in")
            oauth_token.expires_at = (
                datetime.utcnow() + timedelta(seconds=token_data["expires_in"])
                if token_data.get("expires_in") else None
            )
            oauth_token.scope = token_data.get("scope")
            oauth_token.updated_at = datetime.utcnow()
        else:
            # Create new token
            oauth_token = OAuthToken(
                user_id=user_id,
                provider=provider,
                access_token=token_data.get("access_token"),
                refresh_token=token_data.get("refresh_token"),
                token_type=token_data.get("token_type", "Bearer"),
                expires_in=token_data.get("expires_in"),
                expires_at=(
                    datetime.utcnow() + timedelta(seconds=token_data["expires_in"])
                    if token_data.get("expires_in") else None
                ),
                scope=token_data.get("scope"),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )
            db.add(oauth_token)
        
        db.commit()
        logger.info(f"Stored OAuth token for user", user_id=user_id, provider=provider)
    
    def _create_access_token(self, data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """Create JWT access token"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
        
        to_encode.update({"exp": expire, "iat": datetime.utcnow()})
        
        encoded_jwt = jwt.encode(
            to_encode, 
            settings.secret_key, 
            algorithm=settings.algorithm
        )
        
        return encoded_jwt
    
    async def refresh_oauth_token(self, user_id: int, provider: str, db: Session) -> Optional[Dict[str, Any]]:
        """Refresh OAuth token if possible"""
        oauth_token = db.query(OAuthToken).filter(
            OAuthToken.user_id == user_id,
            OAuthToken.provider == provider
        ).first()
        
        if not oauth_token or not oauth_token.refresh_token:
            return None
        
        # Check if token needs refresh (expires within 5 minutes)
        if oauth_token.expires_at and oauth_token.expires_at > datetime.utcnow() + timedelta(minutes=5):
            return {
                "access_token": oauth_token.access_token,
                "token_type": oauth_token.token_type,
                "expires_at": oauth_token.expires_at,
            }
        
        # Refresh the token
        oauth_provider = self.get_provider(provider)
        
        data = {
            "client_id": oauth_provider.client_id,
            "client_secret": oauth_provider.client_secret,
            "grant_type": "refresh_token",
            "refresh_token": oauth_token.refresh_token,
        }
        
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    oauth_provider.token_url,
                    data=data,
                    headers=headers,
                    timeout=30.0
                )
                response.raise_for_status()
                
                new_token_data = response.json()
                
                # Update stored token
                oauth_token.access_token = new_token_data.get("access_token")
                if new_token_data.get("refresh_token"):
                    oauth_token.refresh_token = new_token_data["refresh_token"]
                oauth_token.expires_in = new_token_data.get("expires_in")
                oauth_token.expires_at = (
                    datetime.utcnow() + timedelta(seconds=new_token_data["expires_in"])
                    if new_token_data.get("expires_in") else None
                )
                oauth_token.updated_at = datetime.utcnow()
                
                db.commit()
                
                logger.info(f"Refreshed OAuth token for user", user_id=user_id, provider=provider)
                
                return {
                    "access_token": oauth_token.access_token,
                    "token_type": oauth_token.token_type,
                    "expires_at": oauth_token.expires_at,
                }
                
        except Exception as e:
            logger.error(f"Failed to refresh OAuth token for user {user_id}, provider {provider}: {e}")
            return None


# Global OAuth service instance
oauth_service = OAuthService()