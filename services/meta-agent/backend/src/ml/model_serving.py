"""
AI Agent Platform - ML Model Serving Infrastructure with Ray Serve
"""

import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional, Union, Callable
from enum import Enum
import structlog
from pydantic import BaseModel, Field
import numpy as np
import pickle
import joblib
from pathlib import Path

# Ray Serve imports (would be installed separately)
try:
    import ray
    from ray import serve
    from ray.serve import deployment
    RAY_AVAILABLE = True
except ImportError:
    RAY_AVAILABLE = False
    # Mock classes for when Ray is not available
    class MockDeployment:
        def __init__(self, *args, **kwargs):
            pass
        def __call__(self, func):
            return func
    deployment = MockDeployment

from database.models import AIModel
from services.ai_service import AIService

logger = structlog.get_logger()


class ModelFramework(str, Enum):
    """Supported ML model frameworks"""
    SCIKIT_LEARN = "scikit_learn"
    PYTORCH = "pytorch"
    TENSORFLOW = "tensorflow"
    HUGGINGFACE = "huggingface"
    XGBOOST = "xgboost"
    LIGHTGBM = "lightgbm"
    ONNX = "onnx"
    CUSTOM = "custom"


class ModelType(str, Enum):
    """Types of ML models"""
    CLASSIFICATION = "classification"
    REGRESSION = "regression"
    CLUSTERING = "clustering"
    NLP = "nlp"
    COMPUTER_VISION = "computer_vision"
    RECOMMENDATION = "recommendation"
    TIME_SERIES = "time_series"
    EMBEDDING = "embedding"
    CUSTOM = "custom"


class ModelStatus(str, Enum):
    """Model deployment status"""
    LOADING = "loading"
    READY = "ready"
    ERROR = "error"
    UPDATING = "updating"
    STOPPED = "stopped"


class ModelMetadata(BaseModel):
    """ML model metadata"""
    id: str
    name: str
    version: str
    framework: ModelFramework
    model_type: ModelType
    description: Optional[str] = None
    
    # Model specifications
    input_schema: Dict[str, Any] = Field(default_factory=dict)
    output_schema: Dict[str, Any] = Field(default_factory=dict)
    model_size_mb: Optional[float] = None
    
    # Performance metrics
    accuracy: Optional[float] = None
    latency_ms: Optional[float] = None
    throughput_rps: Optional[float] = None
    
    # Deployment config
    replicas: int = 1
    cpu_request: float = 1.0
    memory_request_mb: int = 512
    gpu_request: int = 0
    
    # Metadata
    tags: List[str] = Field(default_factory=list)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class ModelPredictionRequest(BaseModel):
    """Model prediction request"""
    model_id: str
    inputs: Union[Dict[str, Any], List[Dict[str, Any]]]
    parameters: Dict[str, Any] = Field(default_factory=dict)
    return_probabilities: bool = False
    return_explanations: bool = False


class ModelPredictionResponse(BaseModel):
    """Model prediction response"""
    model_id: str
    predictions: Union[Any, List[Any]]
    probabilities: Optional[List[float]] = None
    explanations: Optional[Dict[str, Any]] = None
    processing_time_ms: float
    timestamp: datetime = Field(default_factory=datetime.utcnow)


@deployment(num_replicas=1, ray_actor_options={"num_cpus": 1})
class MLModelDeployment:
    """Ray Serve deployment for ML models"""
    
    def __init__(self, model_metadata: ModelMetadata, model_path: str):
        self.metadata = model_metadata
        self.model_path = model_path
        self.model = None
        self.status = ModelStatus.LOADING
        
        # Load model
        self._load_model()
        
        logger.info(
            "ML model deployment initialized",
            model_id=self.metadata.id,
            framework=self.metadata.framework.value
        )
    
    def _load_model(self):
        """Load model based on framework"""
        try:
            if self.metadata.framework == ModelFramework.SCIKIT_LEARN:
                self.model = joblib.load(self.model_path)
            elif self.metadata.framework == ModelFramework.PYTORCH:
                import torch
                self.model = torch.load(self.model_path, map_location='cpu')
                self.model.eval()
            elif self.metadata.framework == ModelFramework.TENSORFLOW:
                import tensorflow as tf
                self.model = tf.keras.models.load_model(self.model_path)
            elif self.metadata.framework == ModelFramework.HUGGINGFACE:
                from transformers import AutoModel, AutoTokenizer
                self.model = AutoModel.from_pretrained(self.model_path)
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            elif self.metadata.framework == ModelFramework.XGBOOST:
                import xgboost as xgb
                self.model = xgb.Booster()
                self.model.load_model(self.model_path)
            elif self.metadata.framework == ModelFramework.LIGHTGBM:
                import lightgbm as lgb
                self.model = lgb.Booster(model_file=self.model_path)
            elif self.metadata.framework == ModelFramework.ONNX:
                import onnxruntime as ort
                self.model = ort.InferenceSession(self.model_path)
            else:
                # Custom model loading
                with open(self.model_path, 'rb') as f:
                    self.model = pickle.load(f)
            
            self.status = ModelStatus.READY
            logger.info(
                "Model loaded successfully",
                model_id=self.metadata.id
            )
            
        except Exception as e:
            self.status = ModelStatus.ERROR
            logger.error(
                "Failed to load model",
                model_id=self.metadata.id,
                error=str(e)
            )
            raise
    
    async def __call__(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle prediction request"""
        try:
            start_time = datetime.utcnow()
            
            # Parse request
            pred_request = ModelPredictionRequest(**request)
            
            # Validate model is ready
            if self.status != ModelStatus.READY:
                raise ValueError(f"Model {self.metadata.id} is not ready (status: {self.status})")
            
            # Make prediction
            predictions = await self._predict(pred_request)
            
            # Calculate processing time
            processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            
            # Create response
            response = ModelPredictionResponse(
                model_id=self.metadata.id,
                predictions=predictions,
                processing_time_ms=processing_time
            )
            
            return response.model_dump()
            
        except Exception as e:
            logger.error(
                "Prediction failed",
                model_id=self.metadata.id,
                error=str(e)
            )
            raise
    
    async def _predict(self, request: ModelPredictionRequest) -> Any:
        """Make prediction based on model framework"""
        inputs = request.inputs
        
        if self.metadata.framework == ModelFramework.SCIKIT_LEARN:
            return self._predict_sklearn(inputs)
        elif self.metadata.framework == ModelFramework.PYTORCH:
            return await self._predict_pytorch(inputs)
        elif self.metadata.framework == ModelFramework.TENSORFLOW:
            return await self._predict_tensorflow(inputs)
        elif self.metadata.framework == ModelFramework.HUGGINGFACE:
            return await self._predict_huggingface(inputs)
        elif self.metadata.framework == ModelFramework.XGBOOST:
            return self._predict_xgboost(inputs)
        elif self.metadata.framework == ModelFramework.LIGHTGBM:
            return self._predict_lightgbm(inputs)
        elif self.metadata.framework == ModelFramework.ONNX:
            return self._predict_onnx(inputs)
        else:
            return self._predict_custom(inputs)
    
    def _predict_sklearn(self, inputs: Any) -> Any:
        """Scikit-learn prediction"""
        if isinstance(inputs, dict):
            # Convert dict to array
            feature_array = np.array([[inputs[k] for k in sorted(inputs.keys())]])
        else:
            feature_array = np.array(inputs)
        
        return self.model.predict(feature_array).tolist()
    
    async def _predict_pytorch(self, inputs: Any) -> Any:
        """PyTorch prediction"""
        import torch
        
        if isinstance(inputs, dict):
            # Convert to tensor
            tensor_inputs = {k: torch.tensor(v) for k, v in inputs.items()}
        else:
            tensor_inputs = torch.tensor(inputs)
        
        with torch.no_grad():
            outputs = self.model(tensor_inputs)
            return outputs.cpu().numpy().tolist()
    
    async def _predict_tensorflow(self, inputs: Any) -> Any:
        """TensorFlow prediction"""
        import tensorflow as tf
        
        if isinstance(inputs, dict):
            # Convert to tensor
            tensor_inputs = {k: tf.constant(v) for k, v in inputs.items()}
        else:
            tensor_inputs = tf.constant(inputs)
        
        outputs = self.model(tensor_inputs)
        return outputs.numpy().tolist()
    
    async def _predict_huggingface(self, inputs: Any) -> Any:
        """HuggingFace prediction"""
        if isinstance(inputs, str):
            text = inputs
        elif isinstance(inputs, dict):
            text = inputs.get('text', '')
        else:
            text = str(inputs)
        
        # Tokenize and predict
        tokens = self.tokenizer(text, return_tensors='pt', padding=True, truncation=True)
        
        with torch.no_grad():
            outputs = self.model(**tokens)
            return outputs.last_hidden_state.mean(dim=1).cpu().numpy().tolist()
    
    def _predict_xgboost(self, inputs: Any) -> Any:
        """XGBoost prediction"""
        import xgboost as xgb
        
        if isinstance(inputs, dict):
            feature_array = np.array([[inputs[k] for k in sorted(inputs.keys())]])
        else:
            feature_array = np.array(inputs)
        
        dmatrix = xgb.DMatrix(feature_array)
        return self.model.predict(dmatrix).tolist()
    
    def _predict_lightgbm(self, inputs: Any) -> Any:
        """LightGBM prediction"""
        if isinstance(inputs, dict):
            feature_array = np.array([[inputs[k] for k in sorted(inputs.keys())]])
        else:
            feature_array = np.array(inputs)
        
        return self.model.predict(feature_array).tolist()
    
    def _predict_onnx(self, inputs: Any) -> Any:
        """ONNX prediction"""
        # Get input name
        input_name = self.model.get_inputs()[0].name
        
        if isinstance(inputs, dict):
            feature_array = np.array([[inputs[k] for k in sorted(inputs.keys())]], dtype=np.float32)
        else:
            feature_array = np.array(inputs, dtype=np.float32)
        
        outputs = self.model.run(None, {input_name: feature_array})
        return outputs[0].tolist()
    
    def _predict_custom(self, inputs: Any) -> Any:
        """Custom model prediction"""
        # Assume model has a predict method
        if hasattr(self.model, 'predict'):
            return self.model.predict(inputs)
        else:
            # Call model directly
            return self.model(inputs)


class MLModelRegistry:
    """ML Model Registry for managing models"""
    
    def __init__(self):
        self.models: Dict[str, ModelMetadata] = {}
        self.deployments: Dict[str, str] = {}  # model_id -> deployment_name
        self.model_store_path = Path("models")
        self.model_store_path.mkdir(exist_ok=True)
        
        logger.info("ML Model Registry initialized")
    
    async def register_model(
        self,
        model_metadata: ModelMetadata,
        model_file_path: Optional[str] = None,
        model_data: Optional[bytes] = None
    ) -> str:
        """Register a new ML model"""
        try:
            model_id = model_metadata.id
            
            # Store model file
            if model_file_path:
                stored_path = await self._store_model_file(model_id, model_file_path)
            elif model_data:
                stored_path = await self._store_model_data(model_id, model_data)
            else:
                raise ValueError("Either model_file_path or model_data must be provided")
            
            # Update metadata with stored path
            model_metadata.updated_at = datetime.utcnow()
            
            # Register in registry
            self.models[model_id] = model_metadata
            
            logger.info(
                "Model registered successfully",
                model_id=model_id,
                name=model_metadata.name,
                framework=model_metadata.framework.value
            )
            
            return model_id
            
        except Exception as e:
            logger.error(
                "Failed to register model",
                model_id=model_metadata.id,
                error=str(e)
            )
            raise
    
    async def deploy_model(self, model_id: str) -> bool:
        """Deploy a registered model using Ray Serve"""
        try:
            if not RAY_AVAILABLE:
                logger.error("Ray Serve is not available")
                return False
            
            if model_id not in self.models:
                raise ValueError(f"Model {model_id} not found in registry")
            
            model_metadata = self.models[model_id]
            model_path = self.model_store_path / f"{model_id}.pkl"
            
            # Create deployment
            deployment_name = f"model_{model_id.replace('-', '_')}"
            
            # Configure Ray Serve deployment
            model_deployment = MLModelDeployment.options(
                name=deployment_name,
                num_replicas=model_metadata.replicas,
                ray_actor_options={
                    "num_cpus": model_metadata.cpu_request,
                    "memory": model_metadata.memory_request_mb * 1024 * 1024,
                    "num_gpus": model_metadata.gpu_request
                }
            ).bind(model_metadata, str(model_path))
            
            # Deploy
            serve.run(model_deployment, name=deployment_name, route_prefix=f"/models/{model_id}")
            
            # Track deployment
            self.deployments[model_id] = deployment_name
            
            logger.info(
                "Model deployed successfully",
                model_id=model_id,
                deployment_name=deployment_name
            )
            
            return True
            
        except Exception as e:
            logger.error(
                "Failed to deploy model",
                model_id=model_id,
                error=str(e)
            )
            return False
    
    async def undeploy_model(self, model_id: str) -> bool:
        """Undeploy a model"""
        try:
            if model_id not in self.deployments:
                logger.warning(f"Model {model_id} is not deployed")
                return True
            
            deployment_name = self.deployments[model_id]
            
            # Delete deployment
            serve.delete(deployment_name)
            
            # Remove from tracking
            del self.deployments[model_id]
            
            logger.info(
                "Model undeployed successfully",
                model_id=model_id,
                deployment_name=deployment_name
            )
            
            return True
            
        except Exception as e:
            logger.error(
                "Failed to undeploy model",
                model_id=model_id,
                error=str(e)
            )
            return False
    
    async def predict(self, request: ModelPredictionRequest) -> ModelPredictionResponse:
        """Make prediction using deployed model"""
        try:
            model_id = request.model_id
            
            if model_id not in self.deployments:
                raise ValueError(f"Model {model_id} is not deployed")
            
            # Get deployment handle
            deployment_name = self.deployments[model_id]
            handle = serve.get_deployment(deployment_name).get_handle()
            
            # Make prediction
            result = await handle.remote(request.model_dump())
            
            return ModelPredictionResponse(**result)
            
        except Exception as e:
            logger.error(
                "Prediction failed",
                model_id=request.model_id,
                error=str(e)
            )
            raise
    
    async def get_model_info(self, model_id: str) -> Optional[ModelMetadata]:
        """Get model information"""
        return self.models.get(model_id)
    
    async def list_models(self) -> List[ModelMetadata]:
        """List all registered models"""
        return list(self.models.values())
    
    async def list_deployed_models(self) -> List[str]:
        """List deployed model IDs"""
        return list(self.deployments.keys())
    
    async def get_model_stats(self, model_id: str) -> Dict[str, Any]:
        """Get model performance statistics"""
        try:
            if model_id not in self.deployments:
                return {"error": "Model not deployed"}
            
            # TODO: Implement metrics collection from Ray Serve
            # This would require Ray's metrics and monitoring integration
            
            return {
                "model_id": model_id,
                "status": "deployed",
                "requests_count": 0,
                "average_latency_ms": 0.0,
                "error_rate": 0.0
            }
            
        except Exception as e:
            logger.error(
                "Failed to get model stats",
                model_id=model_id,
                error=str(e)
            )
            return {"error": str(e)}
    
    async def _store_model_file(self, model_id: str, file_path: str) -> str:
        """Store model file in registry"""
        import shutil
        
        source_path = Path(file_path)
        target_path = self.model_store_path / f"{model_id}.pkl"
        
        shutil.copy2(source_path, target_path)
        return str(target_path)
    
    async def _store_model_data(self, model_id: str, model_data: bytes) -> str:
        """Store model data in registry"""
        target_path = self.model_store_path / f"{model_id}.pkl"
        
        with open(target_path, 'wb') as f:
            f.write(model_data)
        
        return str(target_path)


class MLModelService:
    """High-level ML Model Service"""
    
    def __init__(self):
        self.registry = MLModelRegistry()
        self.ai_service = AIService()
        
        # Initialize Ray if available
        if RAY_AVAILABLE:
            try:
                ray.init(ignore_reinit_error=True)
                serve.start()
                logger.info("Ray Serve initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize Ray Serve: {e}")
        
        logger.info("ML Model Service initialized")
    
    async def create_model_from_data(
        self,
        name: str,
        framework: ModelFramework,
        model_type: ModelType,
        training_data: Dict[str, Any],
        config: Dict[str, Any] = None
    ) -> str:
        """Create and train a model from data"""
        try:
            # Generate model ID
            model_id = str(uuid.uuid4())
            
            # Create model metadata
            metadata = ModelMetadata(
                id=model_id,
                name=name,
                version="1.0.0",
                framework=framework,
                model_type=model_type,
                description=f"Auto-generated {model_type.value} model using {framework.value}"
            )
            
            # Train model (simplified example)
            model = await self._train_model(framework, model_type, training_data, config or {})
            
            # Serialize model
            model_data = pickle.dumps(model)
            
            # Register model
            await self.registry.register_model(metadata, model_data=model_data)
            
            logger.info(
                "Model created from data",
                model_id=model_id,
                name=name,
                framework=framework.value
            )
            
            return model_id
            
        except Exception as e:
            logger.error(
                "Failed to create model from data",
                name=name,
                error=str(e)
            )
            raise
    
    async def _train_model(
        self,
        framework: ModelFramework,
        model_type: ModelType,
        training_data: Dict[str, Any],
        config: Dict[str, Any]
    ) -> Any:
        """Train a model based on framework and type"""
        
        if framework == ModelFramework.SCIKIT_LEARN:
            return await self._train_sklearn_model(model_type, training_data, config)
        else:
            # For other frameworks, return a mock model
            return {"framework": framework.value, "type": model_type.value, "trained": True}
    
    async def _train_sklearn_model(
        self,
        model_type: ModelType,
        training_data: Dict[str, Any],
        config: Dict[str, Any]
    ) -> Any:
        """Train scikit-learn model"""
        from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
        from sklearn.cluster import KMeans
        
        X = np.array(training_data.get('features', []))
        y = np.array(training_data.get('targets', [])) if 'targets' in training_data else None
        
        if model_type == ModelType.CLASSIFICATION:
            model = RandomForestClassifier(**config)
            model.fit(X, y)
        elif model_type == ModelType.REGRESSION:
            model = RandomForestRegressor(**config)
            model.fit(X, y)
        elif model_type == ModelType.CLUSTERING:
            model = KMeans(**config)
            model.fit(X)
        else:
            raise ValueError(f"Unsupported model type for scikit-learn: {model_type}")
        
        return model
    
    async def auto_select_model(
        self,
        task_description: str,
        data_sample: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Auto-select best model for a task using AI"""
        try:
            # Use AI service to analyze task and recommend model
            prompt = f"""
            Analyze this machine learning task and recommend the best model:
            
            Task: {task_description}
            Data sample: {json.dumps(data_sample, indent=2)}
            
            Provide recommendations for:
            1. Model type (classification, regression, etc.)
            2. Framework (scikit_learn, pytorch, etc.)
            3. Specific algorithm
            4. Key parameters
            
            Respond in JSON format.
            """
            
            response = await self.ai_service.generate_text(
                prompt=prompt,
                provider="openai",
                model="gpt-4",
                max_tokens=500
            )
            
            # Parse AI recommendation
            try:
                recommendation = json.loads(response)
            except:
                # Fallback recommendation
                recommendation = {
                    "model_type": "classification",
                    "framework": "scikit_learn",
                    "algorithm": "random_forest",
                    "parameters": {"n_estimators": 100}
                }
            
            logger.info(
                "Model auto-selection completed",
                task_description=task_description,
                recommendation=recommendation
            )
            
            return recommendation
            
        except Exception as e:
            logger.error(
                "Auto model selection failed",
                error=str(e)
            )
            # Return default recommendation
            return {
                "model_type": "classification",
                "framework": "scikit_learn",
                "algorithm": "random_forest",
                "parameters": {"n_estimators": 100}
            }


# Global ML model service instance
ml_model_service: Optional[MLModelService] = None


def initialize_ml_service():
    """Initialize ML model service"""
    global ml_model_service
    ml_model_service = MLModelService()
    return ml_model_service


async def register_model(metadata: ModelMetadata, model_path: str) -> str:
    """Register a model"""
    if ml_model_service:
        return await ml_model_service.registry.register_model(metadata, model_path)
    raise RuntimeError("ML service not initialized")


async def deploy_model(model_id: str) -> bool:
    """Deploy a model"""
    if ml_model_service:
        return await ml_model_service.registry.deploy_model(model_id)
    raise RuntimeError("ML service not initialized")


async def make_prediction(request: ModelPredictionRequest) -> ModelPredictionResponse:
    """Make a prediction"""
    if ml_model_service:
        return await ml_model_service.registry.predict(request)
    raise RuntimeError("ML service not initialized")


async def create_model_from_data(
    name: str,
    framework: str,
    model_type: str,
    training_data: Dict[str, Any]
) -> str:
    """Create model from training data"""
    if ml_model_service:
        return await ml_model_service.create_model_from_data(
            name=name,
            framework=ModelFramework(framework),
            model_type=ModelType(model_type),
            training_data=training_data
        )
    raise RuntimeError("ML service not initialized")