"""
AI Assistant API - Unified AI service endpoints
Provides AI capabilities for requirement parsing, code analysis, and documentation
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
import structlog

from services.ai_service import ai_service, AIRequest, AICapability, AIProvider

logger = structlog.get_logger()

router = APIRouter(prefix="/ai", tags=["ai-assistant"])

class RequirementsParseRequest(BaseModel):
    requirements: str = Field(..., description="Natural language requirements for agent")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context")

class RequirementsParseResponse(BaseModel):
    name: str
    type: str
    language: str
    framework: str
    capabilities: List[str]
    deployment: Dict[str, Any]
    advanced_options: Dict[str, Any]
    reasoning: str
    confidence: float = Field(default=0.8, description="Confidence in parsing accuracy")

class CodeAnalysisRequest(BaseModel):
    code: str = Field(..., description="Code to analyze")
    language: str = Field(..., description="Programming language")
    analysis_type: str = Field(default="full", description="Type of analysis to perform")

class CodeAnalysisResponse(BaseModel):
    quality_score: int
    issues: List[str]
    suggestions: List[str]
    complexity: str
    maintainability: str
    security_concerns: List[str]
    performance_notes: List[str]

class DocumentationRequest(BaseModel):
    code: str = Field(..., description="Code to document")
    language: str = Field(..., description="Programming language")
    doc_type: str = Field(default="api", description="Type of documentation")

class DocumentationResponse(BaseModel):
    documentation: str
    format: str = "markdown"

class AIGenerationRequest(BaseModel):
    prompt: str = Field(..., description="AI prompt")
    capability: str = Field(..., description="AI capability to use")
    max_tokens: int = Field(default=4000, description="Maximum tokens to generate")
    temperature: float = Field(default=0.7, description="Temperature for generation")
    system_message: Optional[str] = Field(None, description="System message")
    preferred_provider: Optional[str] = Field(None, description="Preferred AI provider")

class AIGenerationResponse(BaseModel):
    content: str
    provider: str
    model: str
    usage: Dict[str, Any]
    metadata: Dict[str, Any]

@router.get("/providers")
async def get_available_providers():
    """Get list of available AI providers"""
    try:
        providers = ai_service.get_available_providers()
        return {
            "providers": [provider.value for provider in providers],
            "total": len(providers),
            "status": "active" if providers else "no_providers_configured"
        }
    except Exception as e:
        logger.error("Failed to get AI providers", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get AI providers")

@router.post("/parse-requirements", response_model=RequirementsParseResponse)
async def parse_requirements(request: RequirementsParseRequest):
    """Parse natural language requirements into agent configuration"""
    try:
        logger.info("Parsing requirements", requirements_length=len(request.requirements))
        
        config = await ai_service.parse_requirements_to_config(request.requirements)
        
        return RequirementsParseResponse(
            name=config.get("name", "generated-agent"),
            type=config.get("type", "api_service"),
            language=config.get("language", "python"),
            framework=config.get("framework", "fastapi"),
            capabilities=config.get("capabilities", []),
            deployment=config.get("deployment", {}),
            advanced_options=config.get("advanced_options", {}),
            reasoning=config.get("reasoning", "Configuration generated from requirements"),
            confidence=0.85
        )
        
    except Exception as e:
        logger.error("Failed to parse requirements", error=str(e))
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to parse requirements: {str(e)}"
        )

@router.post("/analyze-code", response_model=CodeAnalysisResponse)
async def analyze_code(request: CodeAnalysisRequest):
    """Analyze code for quality, security, and performance"""
    try:
        logger.info("Analyzing code", language=request.language, code_length=len(request.code))
        
        analysis = await ai_service.analyze_code(request.code, request.language)
        
        return CodeAnalysisResponse(
            quality_score=analysis.get("quality_score", 70),
            issues=analysis.get("issues", []),
            suggestions=analysis.get("suggestions", []),
            complexity=analysis.get("complexity", "medium"),
            maintainability=analysis.get("maintainability", "fair"),
            security_concerns=analysis.get("security_concerns", []),
            performance_notes=analysis.get("performance_notes", [])
        )
        
    except Exception as e:
        logger.error("Failed to analyze code", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to analyze code: {str(e)}"
        )

@router.post("/generate-documentation", response_model=DocumentationResponse)
async def generate_documentation(request: DocumentationRequest):
    """Generate documentation for code"""
    try:
        logger.info("Generating documentation", language=request.language, doc_type=request.doc_type)
        
        documentation = await ai_service.generate_documentation(
            request.code, 
            request.language, 
            request.doc_type
        )
        
        return DocumentationResponse(
            documentation=documentation,
            format="markdown"
        )
        
    except Exception as e:
        logger.error("Failed to generate documentation", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate documentation: {str(e)}"
        )

@router.post("/generate", response_model=AIGenerationResponse)
async def generate_ai_content(request: AIGenerationRequest):
    """Generate AI content using specified capability"""
    try:
        logger.info("Generating AI content", capability=request.capability)
        
        # Map string capability to enum
        capability_map = {
            "text_generation": AICapability.TEXT_GENERATION,
            "code_generation": AICapability.CODE_GENERATION,
            "analysis": AICapability.ANALYSIS,
            "planning": AICapability.PLANNING,
            "requirements_parsing": AICapability.REQUIREMENTS_PARSING,
            "configuration": AICapability.CONFIGURATION
        }
        
        capability = capability_map.get(request.capability)
        if not capability:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid capability: {request.capability}"
            )
        
        # Map string provider to enum if specified
        preferred_provider = None
        if request.preferred_provider:
            provider_map = {
                "openai": AIProvider.OPENAI,
                "anthropic": AIProvider.ANTHROPIC,
                "google": AIProvider.GOOGLE
            }
            preferred_provider = provider_map.get(request.preferred_provider)
        
        ai_request = AIRequest(
            prompt=request.prompt,
            capability=capability,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            system_message=request.system_message
        )
        
        response = await ai_service.generate(ai_request, preferred_provider)
        
        return AIGenerationResponse(
            content=response.content,
            provider=response.provider.value,
            model=response.model,
            usage=response.usage,
            metadata=response.metadata
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to generate AI content", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate AI content: {str(e)}"
        )

@router.get("/capabilities")
async def get_ai_capabilities():
    """Get available AI capabilities"""
    capabilities = {
        "text_generation": "Generate text content, articles, descriptions",
        "code_generation": "Generate code in various programming languages",
        "analysis": "Analyze data, code, or content for insights",
        "planning": "Create plans, strategies, and roadmaps",
        "requirements_parsing": "Parse natural language requirements into structured data",
        "configuration": "Generate configuration files and settings"
    }
    
    return {
        "capabilities": [
            {"name": name, "description": description}
            for name, description in capabilities.items()
        ]
    }

@router.get("/health")
async def ai_service_health():
    """Check AI service health and provider status"""
    try:
        providers = ai_service.get_available_providers()
        
        # Test each provider with a simple request
        provider_status = {}
        for provider in providers:
            try:
                test_request = AIRequest(
                    prompt="Say 'OK' if you can hear me",
                    capability=AICapability.TEXT_GENERATION,
                    max_tokens=10,
                    temperature=0.1
                )
                test_response = await ai_service.generate(test_request, provider)
                provider_status[provider.value] = {
                    "status": "healthy",
                    "model": test_response.model,
                    "response_time": "fast"  # Could add actual timing
                }
            except Exception as e:
                provider_status[provider.value] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
        
        return {
            "status": "healthy" if providers else "no_providers",
            "providers": provider_status,
            "total_providers": len(providers),
            "healthy_providers": len([p for p in provider_status.values() if p.get("status") == "healthy"])
        }
        
    except Exception as e:
        logger.error("AI service health check failed", error=str(e))
        return {
            "status": "unhealthy",
            "error": str(e),
            "providers": {},
            "total_providers": 0,
            "healthy_providers": 0
        }