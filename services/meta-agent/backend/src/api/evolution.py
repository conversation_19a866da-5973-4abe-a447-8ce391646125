"""Platform self-evolution monitoring and management API endpoints."""

from fastapi import APIRouter, Depends, HTTPException, Query, Body
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime, timedelta

from database.connection import get_db
from auth.dependencies import get_current_user
from services.self_evolution import PlatformEvolutionSystem
from utils.logging import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/evolution", tags=["platform-evolution"])

# Evolution system will be initialized when needed
evolution_system = None

def get_evolution_system() -> PlatformEvolutionSystem:
    """Get evolution system instance with lazy initialization."""
    global evolution_system
    if evolution_system is None:
        from intelligence.gateway import AIGateway
        from generation.ai_workflow_generator import AgentWorkflowGenerator
        
        ai_gateway = AIGateway()
        workflow_generator = AgentWorkflowGenerator()
        evolution_system = PlatformEvolutionSystem(ai_gateway, workflow_generator)
    return evolution_system

# Pydantic models
class EvolutionMetrics(BaseModel):
    total_improvements: int
    active_initiatives: int
    completed_initiatives: int
    performance_improvements: float
    capability_enhancements: int
    usability_improvements: int
    last_update: str

class ImprovementInitiative(BaseModel):
    id: str
    title: str
    description: str
    category: str  # performance, capability, usability
    priority: str  # high, medium, low
    status: str    # identified, planned, in_progress, completed, cancelled
    impact_score: float
    effort_estimate: int  # hours
    created_at: str
    updated_at: str
    completion_date: Optional[str] = None
    metrics: Dict[str, Any]

class UsagePattern(BaseModel):
    pattern_id: str
    name: str
    description: str
    frequency: int
    trend: str  # increasing, decreasing, stable
    impact_areas: List[str]
    improvement_opportunities: List[str]
    identified_at: str

class PlatformInsights(BaseModel):
    overall_health_score: float
    performance_trends: Dict[str, float]
    usage_patterns: List[UsagePattern]
    improvement_suggestions: List[str]
    bottlenecks: List[str]
    optimization_opportunities: List[str]
    generated_at: str

@router.get("/metrics", response_model=EvolutionMetrics)
async def get_evolution_metrics(
    current_user = Depends(get_current_user)
):
    """Get platform evolution metrics and statistics."""
    
    try:
        metrics = await get_evolution_system().get_evolution_metrics()
        
        return EvolutionMetrics(
            total_improvements=metrics.get("total_improvements", 0),
            active_initiatives=metrics.get("active_initiatives", 0),
            completed_initiatives=metrics.get("completed_initiatives", 0),
            performance_improvements=metrics.get("performance_improvements", 0.0),
            capability_enhancements=metrics.get("capability_enhancements", 0),
            usability_improvements=metrics.get("usability_improvements", 0),
            last_update=metrics.get("last_update", datetime.utcnow().isoformat())
        )
        
    except Exception as e:
        logger.error(f"Error fetching evolution metrics: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch evolution metrics")

@router.get("/initiatives", response_model=List[ImprovementInitiative])
async def get_improvement_initiatives(
    status: Optional[str] = None,
    category: Optional[str] = None,
    limit: int = 50,
    current_user = Depends(get_current_user)
):
    """Get list of improvement initiatives with optional filtering."""
    
    try:
        initiatives = await get_evolution_system().get_improvement_initiatives(
            status_filter=status,
            category_filter=category,
            limit=limit
        )
        
        return [
            ImprovementInitiative(
                id=init["id"],
                title=init["title"],
                description=init["description"],
                category=init["category"],
                priority=init["priority"],
                status=init["status"],
                impact_score=init["impact_score"],
                effort_estimate=init["effort_estimate"],
                created_at=init["created_at"].isoformat(),
                updated_at=init["updated_at"].isoformat(),
                completion_date=init["completion_date"].isoformat() if init.get("completion_date") else None,
                metrics=init.get("metrics", {})
            )
            for init in initiatives
        ]
        
    except Exception as e:
        logger.error(f"Error fetching improvement initiatives: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch initiatives")

@router.get("/patterns", response_model=List[UsagePattern])
async def get_usage_patterns(
    time_range: str = "7d",
    current_user = Depends(get_current_user)
):
    """Get identified usage patterns and trends."""
    
    try:
        patterns = await get_evolution_system().get_usage_patterns(time_range=time_range)
        
        return [
            UsagePattern(
                pattern_id=pattern["id"],
                name=pattern["name"],
                description=pattern["description"],
                frequency=pattern["frequency"],
                trend=pattern["trend"],
                impact_areas=pattern["impact_areas"],
                improvement_opportunities=pattern["improvement_opportunities"],
                identified_at=pattern["identified_at"].isoformat()
            )
            for pattern in patterns
        ]
        
    except Exception as e:
        logger.error(f"Error fetching usage patterns: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch usage patterns")

@router.get("/insights", response_model=PlatformInsights)
async def get_platform_insights(
    current_user = Depends(get_current_user)
):
    """Get AI-generated platform insights and recommendations."""
    
    try:
        insights = await get_evolution_system().generate_platform_insights()
        
        return PlatformInsights(
            overall_health_score=insights.get("health_score", 0.0),
            performance_trends=insights.get("performance_trends", {}),
            usage_patterns=[
                UsagePattern(
                    pattern_id=pattern["id"],
                    name=pattern["name"],
                    description=pattern["description"],
                    frequency=pattern["frequency"],
                    trend=pattern["trend"],
                    impact_areas=pattern["impact_areas"],
                    improvement_opportunities=pattern["improvement_opportunities"],
                    identified_at=pattern["identified_at"].isoformat()
                )
                for pattern in insights.get("usage_patterns", [])
            ],
            improvement_suggestions=insights.get("suggestions", []),
            bottlenecks=insights.get("bottlenecks", []),
            optimization_opportunities=insights.get("optimizations", []),
            generated_at=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Error generating platform insights: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to generate insights")

@router.post("/initiatives/{initiative_id}/approve")
async def approve_initiative(
    initiative_id: str,
    current_user = Depends(get_current_user)
):
    """Approve an improvement initiative for implementation."""
    
    try:
        result = await get_evolution_system().approve_initiative(initiative_id)
        
        logger.info(
            f"Initiative approved",
            initiative_id=initiative_id,
            user_id=current_user.id
        )
        
        return {
            "message": "Initiative approved successfully",
            "initiative_id": initiative_id,
            "status": result["status"],
            "implementation_plan": result.get("implementation_plan")
        }
        
    except Exception as e:
        logger.error(f"Error approving initiative: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to approve initiative")

@router.post("/initiatives/{initiative_id}/reject")
async def reject_initiative(
    initiative_id: str,
    reason: str = Query(..., min_length=1),
    current_user = Depends(get_current_user)
):
    """Reject an improvement initiative with reason."""
    
    try:
        result = await get_evolution_system().reject_initiative(initiative_id, reason)
        
        logger.info(
            f"Initiative rejected",
            initiative_id=initiative_id,
            reason=reason,
            user_id=current_user.id
        )
        
        return {
            "message": "Initiative rejected",
            "initiative_id": initiative_id,
            "status": result["status"],
            "reason": reason
        }
        
    except Exception as e:
        logger.error(f"Error rejecting initiative: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to reject initiative")

@router.post("/scan")
async def trigger_evolution_scan(
    force_full_scan: bool = False,
    current_user = Depends(get_current_user)
):
    """Manually trigger a platform evolution analysis scan."""
    
    try:
        result = await get_evolution_system().run_evolution_scan(force_full=force_full_scan)
        
        logger.info(
            f"Evolution scan triggered",
            force_full=force_full_scan,
            user_id=current_user.id
        )
        
        return {
            "message": "Evolution scan initiated",
            "scan_id": result["scan_id"],
            "status": result["status"],
            "estimated_duration": result.get("estimated_duration", "unknown")
        }
        
    except Exception as e:
        logger.error(f"Error triggering evolution scan: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to trigger scan")

@router.get("/scan/{scan_id}/status")
async def get_scan_status(
    scan_id: str,
    current_user = Depends(get_current_user)
):
    """Get status of an evolution scan."""
    
    try:
        status = await get_evolution_system().get_scan_status(scan_id)
        
        if not status:
            raise HTTPException(status_code=404, detail="Scan not found")
        
        return {
            "scan_id": scan_id,
            "status": status["status"],
            "progress": status["progress"],
            "phase": status["phase"],
            "findings": status.get("findings", []),
            "started_at": status["started_at"].isoformat(),
            "updated_at": status["updated_at"].isoformat(),
            "completed_at": status["completed_at"].isoformat() if status.get("completed_at") else None
        }
        
    except Exception as e:
        logger.error(f"Error fetching scan status: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch scan status")

@router.get("/history")
async def get_evolution_history(
    limit: int = 100,
    days: int = 30,
    current_user = Depends(get_current_user)
):
    """Get platform evolution history and changes over time."""
    
    try:
        history = await get_evolution_system().get_evolution_history(
            limit=limit,
            days_back=days
        )
        
        return {
            "history": history,
            "total_events": len(history),
            "time_range": f"{days} days",
            "categories": {
                "performance": len([h for h in history if h.get("category") == "performance"]),
                "capability": len([h for h in history if h.get("category") == "capability"]),
                "usability": len([h for h in history if h.get("category") == "usability"])
            }
        }
        
    except Exception as e:
        logger.error(f"Error fetching evolution history: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch evolution history")

@router.get("/config")
async def get_evolution_config(
    current_user = Depends(get_current_user)
):
    """Get platform evolution system configuration."""
    
    try:
        config = await get_evolution_system().get_configuration()
        
        return {
            "evolution_enabled": config.get("enabled", True),
            "scan_frequency": config.get("scan_frequency", "daily"),
            "auto_approval_threshold": config.get("auto_approval_threshold", 0.8),
            "categories": config.get("categories", ["performance", "capability", "usability"]),
            "ml_models": config.get("ml_models", {}),
            "thresholds": config.get("thresholds", {})
        }
        
    except Exception as e:
        logger.error(f"Error fetching evolution config: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch configuration")

@router.post("/config")
async def update_evolution_config(
    config: Dict[str, Any],
    current_user = Depends(get_current_user)
):
    """Update platform evolution system configuration."""
    
    try:
        result = await get_evolution_system().update_configuration(config)
        
        logger.info(
            f"Evolution configuration updated",
            user_id=current_user.id,
            config_keys=list(config.keys())
        )
        
        return {
            "message": "Configuration updated successfully",
            "updated_config": result
        }
        
    except Exception as e:
        logger.error(f"Error updating evolution config: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to update configuration")

@router.get("/health")
async def evolution_service_health():
    """Check health of the evolution service."""
    
    try:
        health_status = await get_evolution_system().check_service_health()
        
        return {
            "status": "healthy",
            "evolution_system": health_status,
            "last_scan": health_status.get("last_scan"),
            "active_initiatives": health_status.get("active_initiatives", 0),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Evolution service health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }