"""
Vector Database API endpoints for knowledge management and semantic search
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from database.connection import get_db
from database.models import User, Agent
from services.vector_db import get_vector_db, VectorDatabase
from auth.dependencies import get_current_user

router = APIRouter(prefix="/vector", tags=["vector-db"])


# Request/Response Models
class StoreKnowledgeRequest(BaseModel):
    agent_id: str = Field(..., description="Agent ID to store knowledge for")
    content: str = Field(..., min_length=1, max_length=10000, description="Knowledge content")
    category: str = Field(default="general", description="Knowledge category")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Additional metadata")


class StoreKnowledgeResponse(BaseModel):
    success: bool
    point_id: str
    message: str


class SearchKnowledgeRequest(BaseModel):
    query: str = Field(..., min_length=1, max_length=1000, description="Search query")
    agent_id: Optional[str] = Field(default=None, description="Filter by agent ID")
    category: Optional[str] = Field(default=None, description="Filter by category")
    limit: int = Field(default=10, ge=1, le=50, description="Maximum results to return")
    score_threshold: float = Field(default=0.7, ge=0.0, le=1.0, description="Minimum similarity score")


class KnowledgeResult(BaseModel):
    id: str
    score: float
    content: str
    agent_id: str
    category: str
    metadata: Dict[str, Any]
    created_at: str


class SearchKnowledgeResponse(BaseModel):
    success: bool
    results: List[KnowledgeResult]
    total_found: int


class StoreConversationRequest(BaseModel):
    agent_id: str = Field(..., description="Agent ID")
    user_id: str = Field(..., description="User ID")
    conversation_id: str = Field(..., description="Conversation ID")
    message: str = Field(..., min_length=1, max_length=5000, description="Message content")
    role: str = Field(..., description="Message role (user or assistant)")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Additional metadata")


class StoreConversationResponse(BaseModel):
    success: bool
    point_id: str
    message: str


class GetContextRequest(BaseModel):
    agent_id: str = Field(..., description="Agent ID")
    query: str = Field(..., min_length=1, max_length=1000, description="Context query")
    limit: int = Field(default=5, ge=1, le=20, description="Maximum context items")
    score_threshold: float = Field(default=0.6, ge=0.0, le=1.0, description="Minimum similarity score")


class ContextResult(BaseModel):
    id: str
    score: float
    message: str
    role: str
    conversation_id: str
    created_at: str
    metadata: Dict[str, Any]


class GetContextResponse(BaseModel):
    success: bool
    context: List[ContextResult]
    total_found: int


class StoreCodeSnippetRequest(BaseModel):
    title: str = Field(..., min_length=1, max_length=200, description="Code snippet title")
    code: str = Field(..., min_length=1, max_length=20000, description="Code content")
    language: str = Field(..., description="Programming language")
    description: str = Field(..., min_length=1, max_length=1000, description="Code description")
    tags: Optional[List[str]] = Field(default=None, description="Tags for categorization")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Additional metadata")


class StoreCodeSnippetResponse(BaseModel):
    success: bool
    point_id: str
    message: str


class SearchCodeRequest(BaseModel):
    query: str = Field(..., min_length=1, max_length=1000, description="Search query")
    language: Optional[str] = Field(default=None, description="Filter by programming language")
    tags: Optional[List[str]] = Field(default=None, description="Filter by tags")
    limit: int = Field(default=10, ge=1, le=50, description="Maximum results to return")
    score_threshold: float = Field(default=0.6, ge=0.0, le=1.0, description="Minimum similarity score")


class CodeResult(BaseModel):
    id: str
    score: float
    title: str
    code: str
    language: str
    description: str
    tags: List[str]
    created_at: str
    metadata: Dict[str, Any]


class SearchCodeResponse(BaseModel):
    success: bool
    results: List[CodeResult]
    total_found: int


class CollectionInfoResponse(BaseModel):
    success: bool
    info: Dict[str, Any]


class HealthResponse(BaseModel):
    success: bool
    status: str
    details: Dict[str, Any]


class DeleteDataRequest(BaseModel):
    collection_name: str = Field(..., description="Collection name (without prefix)")
    filter_conditions: Dict[str, Any] = Field(..., description="Filter conditions for deletion")


class DeleteDataResponse(BaseModel):
    success: bool
    message: str


# API Endpoints

@router.post("/knowledge/store", response_model=StoreKnowledgeResponse)
async def store_agent_knowledge(
    request: StoreKnowledgeRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    vector_db: VectorDatabase = Depends(get_vector_db)
):
    """Store knowledge for an agent"""
    try:
        # Verify agent exists and user has access
        agent = db.query(Agent).filter(
            Agent.id == request.agent_id,
            Agent.user_id == current_user.id
        ).first()
        
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found or access denied"
            )
        
        # Store knowledge
        point_id = await vector_db.store_agent_knowledge(
            agent_id=request.agent_id,
            content=request.content,
            metadata=request.metadata,
            category=request.category
        )
        
        return StoreKnowledgeResponse(
            success=True,
            point_id=point_id,
            message="Knowledge stored successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to store knowledge: {str(e)}"
        )


@router.post("/knowledge/search", response_model=SearchKnowledgeResponse)
async def search_agent_knowledge(
    request: SearchKnowledgeRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    vector_db: VectorDatabase = Depends(get_vector_db)
):
    """Search agent knowledge base"""
    try:
        # If agent_id is specified, verify access
        if request.agent_id:
            agent = db.query(Agent).filter(
                Agent.id == request.agent_id,
                Agent.user_id == current_user.id
            ).first()
            
            if not agent:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Agent not found or access denied"
                )
        
        # Search knowledge
        results = await vector_db.search_agent_knowledge(
            query=request.query,
            agent_id=request.agent_id,
            category=request.category,
            limit=request.limit,
            score_threshold=request.score_threshold
        )
        
        # Convert to response format
        knowledge_results = [
            KnowledgeResult(
                id=result["id"],
                score=result["score"],
                content=result["content"],
                agent_id=result["agent_id"],
                category=result["category"],
                metadata=result["metadata"],
                created_at=result["created_at"]
            )
            for result in results
        ]
        
        return SearchKnowledgeResponse(
            success=True,
            results=knowledge_results,
            total_found=len(knowledge_results)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search knowledge: {str(e)}"
        )


@router.post("/conversation/store", response_model=StoreConversationResponse)
async def store_conversation_context(
    request: StoreConversationRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    vector_db: VectorDatabase = Depends(get_vector_db)
):
    """Store conversation context for an agent"""
    try:
        # Verify agent exists and user has access
        agent = db.query(Agent).filter(
            Agent.id == request.agent_id,
            Agent.user_id == current_user.id
        ).first()
        
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found or access denied"
            )
        
        # Store conversation context
        point_id = await vector_db.store_conversation_context(
            agent_id=request.agent_id,
            user_id=request.user_id,
            conversation_id=request.conversation_id,
            message=request.message,
            role=request.role,
            metadata=request.metadata
        )
        
        return StoreConversationResponse(
            success=True,
            point_id=point_id,
            message="Conversation context stored successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to store conversation context: {str(e)}"
        )


@router.post("/conversation/context", response_model=GetContextResponse)
async def get_conversation_context(
    request: GetContextRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    vector_db: VectorDatabase = Depends(get_vector_db)
):
    """Get relevant conversation context for current query"""
    try:
        # Verify agent exists and user has access
        agent = db.query(Agent).filter(
            Agent.id == request.agent_id,
            Agent.user_id == current_user.id
        ).first()
        
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found or access denied"
            )
        
        # Get conversation context
        results = await vector_db.get_conversation_context(
            agent_id=request.agent_id,
            query=request.query,
            limit=request.limit,
            score_threshold=request.score_threshold
        )
        
        # Convert to response format
        context_results = [
            ContextResult(
                id=result["id"],
                score=result["score"],
                message=result["message"],
                role=result["role"],
                conversation_id=result["conversation_id"],
                created_at=result["created_at"],
                metadata=result["metadata"]
            )
            for result in results
        ]
        
        return GetContextResponse(
            success=True,
            context=context_results,
            total_found=len(context_results)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get conversation context: {str(e)}"
        )


@router.post("/code/store", response_model=StoreCodeSnippetResponse)
async def store_code_snippet(
    request: StoreCodeSnippetRequest,
    current_user: User = Depends(get_current_user),
    vector_db: VectorDatabase = Depends(get_vector_db)
):
    """Store code snippet for agent reference"""
    try:
        # Store code snippet
        point_id = await vector_db.store_code_snippet(
            title=request.title,
            code=request.code,
            language=request.language,
            description=request.description,
            tags=request.tags,
            metadata=request.metadata
        )
        
        return StoreCodeSnippetResponse(
            success=True,
            point_id=point_id,
            message="Code snippet stored successfully"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to store code snippet: {str(e)}"
        )


@router.post("/code/search", response_model=SearchCodeResponse)
async def search_code_snippets(
    request: SearchCodeRequest,
    current_user: User = Depends(get_current_user),
    vector_db: VectorDatabase = Depends(get_vector_db)
):
    """Search code snippets"""
    try:
        # Search code snippets
        results = await vector_db.search_code_snippets(
            query=request.query,
            language=request.language,
            tags=request.tags,
            limit=request.limit,
            score_threshold=request.score_threshold
        )
        
        # Convert to response format
        code_results = [
            CodeResult(
                id=result["id"],
                score=result["score"],
                title=result["title"],
                code=result["code"],
                language=result["language"],
                description=result["description"],
                tags=result["tags"],
                created_at=result["created_at"],
                metadata=result["metadata"]
            )
            for result in results
        ]
        
        return SearchCodeResponse(
            success=True,
            results=code_results,
            total_found=len(code_results)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search code snippets: {str(e)}"
        )


@router.delete("/data", response_model=DeleteDataResponse)
async def delete_collection_data(
    request: DeleteDataRequest,
    current_user: User = Depends(get_current_user),
    vector_db: VectorDatabase = Depends(get_vector_db)
):
    """Delete data from collection based on filter conditions"""
    try:
        # Only allow deletion of own agent's data
        if "agent_id" in request.filter_conditions:
            agent_id = request.filter_conditions["agent_id"]
            # This would need additional validation in production
            # to ensure user owns the agent
        
        success = await vector_db.delete_collection_data(
            collection_name=request.collection_name,
            filter_conditions=request.filter_conditions
        )
        
        if success:
            return DeleteDataResponse(
                success=True,
                message="Data deleted successfully"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to delete data"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete data: {str(e)}"
        )


@router.get("/collections/{collection_name}/info", response_model=CollectionInfoResponse)
async def get_collection_info(
    collection_name: str,
    current_user: User = Depends(get_current_user),
    vector_db: VectorDatabase = Depends(get_vector_db)
):
    """Get information about a collection"""
    try:
        info = await vector_db.get_collection_info(collection_name)
        
        return CollectionInfoResponse(
            success=True,
            info=info
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get collection info: {str(e)}"
        )


@router.get("/health", response_model=HealthResponse)
async def vector_db_health_check(
    vector_db: VectorDatabase = Depends(get_vector_db)
):
    """Check vector database health"""
    try:
        health_info = await vector_db.health_check()
        
        return HealthResponse(
            success=health_info["status"] == "ok",
            status=health_info["status"],
            details=health_info
        )
        
    except Exception as e:
        return HealthResponse(
            success=False,
            status="error",
            details={"error": str(e)}
        )