"""
AI Integration API Routes
Provides endpoints for AI service integration and management
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field
from uuid import uuid4
import logging

from auth.dependencies import get_current_user
from database.models import User
from services.ai_integration import (
    ai_integration_service, 
    AIRequest, 
    AIResponse, 
    AIProvider, 
    TaskType
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/v1/ai", tags=["AI Integration"])


class AIRequestSchema(BaseModel):
    """AI request schema"""
    task_type: TaskType
    provider: Optional[AIProvider] = None
    model: str
    input_data: Dict[str, Any]
    parameters: Optional[Dict[str, Any]] = None
    timeout: Optional[int] = 300


class AIResponseSchema(BaseModel):
    """AI response schema"""
    task_id: str
    provider: str
    model: str
    result: Any
    metadata: Dict[str, Any]
    usage: Optional[Dict[str, Any]] = None
    execution_time: float
    success: bool
    error: Optional[str] = None


class ChatMessage(BaseModel):
    """Chat message schema"""
    role: str = Field(..., description="Message role: system, user, or assistant")
    content: str = Field(..., description="Message content")


class ChatRequest(BaseModel):
    """Chat completion request"""
    messages: List[ChatMessage]
    model: Optional[str] = "gpt-3.5-turbo"
    provider: Optional[AIProvider] = None
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 1000
    top_p: Optional[float] = 1.0
    frequency_penalty: Optional[float] = 0.0
    presence_penalty: Optional[float] = 0.0


class CompletionRequest(BaseModel):
    """Text completion request"""
    prompt: str
    model: Optional[str] = "gpt-3.5-turbo"
    provider: Optional[AIProvider] = None
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 1000


class CodeGenerationRequest(BaseModel):
    """Code generation request"""
    prompt: str
    language: Optional[str] = "python"
    model: Optional[str] = "gpt-3.5-turbo"
    provider: Optional[AIProvider] = None
    max_tokens: Optional[int] = 2000


class EmbeddingRequest(BaseModel):
    """Embeddings request"""
    text: str
    model: Optional[str] = "text-embedding-ada-002"
    provider: Optional[AIProvider] = AIProvider.OPENAI


class ModelInfo(BaseModel):
    """Model information"""
    name: str
    provider: str
    description: Optional[str] = None
    capabilities: List[str] = []


class ProviderHealth(BaseModel):
    """Provider health status"""
    provider: str
    healthy: bool
    last_check: Optional[str] = None


@router.post("/chat", response_model=AIResponseSchema)
async def chat_completion(
    request: ChatRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Generate chat completion using AI providers
    """
    try:
        # Convert request to internal format
        messages = [{"role": msg.role, "content": msg.content} for msg in request.messages]
        
        ai_request = AIRequest(
            task_type=TaskType.CHAT,
            provider=request.provider or AIProvider.OPENAI,
            model=request.model,
            input_data={"messages": messages},
            parameters={
                "temperature": request.temperature,
                "max_tokens": request.max_tokens,
                "top_p": request.top_p,
                "frequency_penalty": request.frequency_penalty,
                "presence_penalty": request.presence_penalty,
            },
            context={"task_id": str(uuid4()), "user_id": str(current_user.id)}
        )
        
        # If no provider specified, get optimal provider
        if not request.provider:
            optimal_provider = await ai_integration_service.get_optimal_provider(TaskType.CHAT)
            if optimal_provider:
                ai_request.provider = optimal_provider
        
        response = await ai_integration_service.process_ai_request(ai_request)
        
        return AIResponseSchema(
            task_id=response.task_id,
            provider=response.provider.value,
            model=response.model,
            result=response.result,
            metadata=response.metadata,
            usage=response.usage,
            execution_time=response.execution_time,
            success=response.success,
            error=response.error
        )
        
    except Exception as e:
        logger.error(f"Chat completion failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/completion", response_model=AIResponseSchema)
async def text_completion(
    request: CompletionRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Generate text completion using AI providers
    """
    try:
        ai_request = AIRequest(
            task_type=TaskType.TEXT_COMPLETION,
            provider=request.provider or AIProvider.OPENAI,
            model=request.model,
            input_data={"prompt": request.prompt},
            parameters={
                "temperature": request.temperature,
                "max_tokens": request.max_tokens,
            },
            context={"task_id": str(uuid4()), "user_id": str(current_user.id)}
        )
        
        if not request.provider:
            optimal_provider = await ai_integration_service.get_optimal_provider(TaskType.TEXT_COMPLETION)
            if optimal_provider:
                ai_request.provider = optimal_provider
        
        response = await ai_integration_service.process_ai_request(ai_request)
        
        return AIResponseSchema(
            task_id=response.task_id,
            provider=response.provider.value,
            model=response.model,
            result=response.result,
            metadata=response.metadata,
            usage=response.usage,
            execution_time=response.execution_time,
            success=response.success,
            error=response.error
        )
        
    except Exception as e:
        logger.error(f"Text completion failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/code", response_model=AIResponseSchema)
async def generate_code(
    request: CodeGenerationRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Generate code using AI providers
    """
    try:
        ai_request = AIRequest(
            task_type=TaskType.CODE_GENERATION,
            provider=request.provider or AIProvider.OPENAI,
            model=request.model,
            input_data={
                "prompt": request.prompt,
                "language": request.language
            },
            parameters={"max_tokens": request.max_tokens},
            context={"task_id": str(uuid4()), "user_id": str(current_user.id)}
        )
        
        if not request.provider:
            optimal_provider = await ai_integration_service.get_optimal_provider(
                TaskType.CODE_GENERATION, 
                {"language": request.language}
            )
            if optimal_provider:
                ai_request.provider = optimal_provider
        
        response = await ai_integration_service.process_ai_request(ai_request)
        
        return AIResponseSchema(
            task_id=response.task_id,
            provider=response.provider.value,
            model=response.model,
            result=response.result,
            metadata=response.metadata,
            usage=response.usage,
            execution_time=response.execution_time,
            success=response.success,
            error=response.error
        )
        
    except Exception as e:
        logger.error(f"Code generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/embeddings", response_model=AIResponseSchema)
async def generate_embeddings(
    request: EmbeddingRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Generate text embeddings using AI providers
    """
    try:
        ai_request = AIRequest(
            task_type=TaskType.EMBEDDINGS,
            provider=request.provider,
            model=request.model,
            input_data={"text": request.text},
            context={"task_id": str(uuid4()), "user_id": str(current_user.id)}
        )
        
        response = await ai_integration_service.process_ai_request(ai_request)
        
        return AIResponseSchema(
            task_id=response.task_id,
            provider=response.provider.value,
            model=response.model,
            result=response.result,
            metadata=response.metadata,
            usage=response.usage,
            execution_time=response.execution_time,
            success=response.success,
            error=response.error
        )
        
    except Exception as e:
        logger.error(f"Embeddings generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/process", response_model=AIResponseSchema)
async def process_ai_request(
    request: AIRequestSchema,
    current_user: User = Depends(get_current_user)
):
    """
    Process generic AI request
    """
    try:
        ai_request = AIRequest(
            task_type=request.task_type,
            provider=request.provider,
            model=request.model,
            input_data=request.input_data,
            parameters=request.parameters,
            timeout=request.timeout,
            context={"task_id": str(uuid4()), "user_id": str(current_user.id)}
        )
        
        # Auto-select provider if not specified
        if not request.provider:
            optimal_provider = await ai_integration_service.get_optimal_provider(request.task_type)
            if optimal_provider:
                ai_request.provider = optimal_provider
        
        response = await ai_integration_service.process_ai_request(ai_request)
        
        return AIResponseSchema(
            task_id=response.task_id,
            provider=response.provider.value,
            model=response.model,
            result=response.result,
            metadata=response.metadata,
            usage=response.usage,
            execution_time=response.execution_time,
            success=response.success,
            error=response.error
        )
        
    except Exception as e:
        logger.error(f"AI request processing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/models")
async def get_available_models(
    current_user: User = Depends(get_current_user)
) -> Dict[str, List[str]]:
    """
    Get all available AI models by provider
    """
    try:
        models = await ai_integration_service.get_available_models()
        return models
    except Exception as e:
        logger.error(f"Failed to get available models: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve available models")


@router.get("/providers")
async def get_providers(
    current_user: User = Depends(get_current_user)
) -> List[str]:
    """
    Get list of configured AI providers
    """
    try:
        providers = list(ai_integration_service.providers.keys())
        return [provider.value for provider in providers]
    except Exception as e:
        logger.error(f"Failed to get providers: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve providers")


@router.get("/health")
async def health_check(
    current_user: User = Depends(get_current_user)
) -> Dict[str, bool]:
    """
    Check health status of all AI providers
    """
    try:
        health_status = await ai_integration_service.health_check()
        return health_status
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Health check failed")


@router.get("/capabilities")
async def get_capabilities(
    current_user: User = Depends(get_current_user)
) -> Dict[str, List[str]]:
    """
    Get AI capabilities by task type
    """
    return {
        "text_processing": ["chat", "completion", "summarization", "translation"],
        "code": ["code_generation", "code_review", "code_explanation"],
        "analysis": ["classification", "extraction", "sentiment_analysis"],
        "embeddings": ["text_embeddings", "similarity_search"],
        "multimodal": ["image_analysis", "speech_to_text", "text_to_speech"]
    }


@router.post("/batch")
async def batch_process(
    requests: List[AIRequestSchema],
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
):
    """
    Process multiple AI requests in batch
    """
    if len(requests) > 10:
        raise HTTPException(status_code=400, detail="Batch size cannot exceed 10 requests")
    
    try:
        batch_id = str(uuid4())
        
        # Convert to internal format
        ai_requests = []
        for i, request in enumerate(requests):
            ai_request = AIRequest(
                task_type=request.task_type,
                provider=request.provider,
                model=request.model,
                input_data=request.input_data,
                parameters=request.parameters,
                timeout=request.timeout,
                context={
                    "task_id": f"{batch_id}_{i}",
                    "batch_id": batch_id,
                    "user_id": str(current_user.id)
                }
            )
            ai_requests.append(ai_request)
        
        # Process requests concurrently
        import asyncio
        responses = await asyncio.gather(*[
            ai_integration_service.process_ai_request(req) for req in ai_requests
        ], return_exceptions=True)
        
        # Convert responses
        result = []
        for i, response in enumerate(responses):
            if isinstance(response, Exception):
                result.append({
                    "task_id": f"{batch_id}_{i}",
                    "success": False,
                    "error": str(response)
                })
            else:
                result.append(AIResponseSchema(
                    task_id=response.task_id,
                    provider=response.provider.value,
                    model=response.model,
                    result=response.result,
                    metadata=response.metadata,
                    usage=response.usage,
                    execution_time=response.execution_time,
                    success=response.success,
                    error=response.error
                ))
        
        return {
            "batch_id": batch_id,
            "total_requests": len(requests),
            "successful": sum(1 for r in result if r.get("success", False)),
            "failed": sum(1 for r in result if not r.get("success", False)),
            "results": result
        }
        
    except Exception as e:
        logger.error(f"Batch processing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))