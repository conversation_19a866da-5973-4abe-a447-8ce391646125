"""
Google ADK (AI Development Kit) API endpoints
"""

import logging
from datetime import datetime
from typing import List, Optional, Dict, Any, AsyncGenerator
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
import base64

from database.connection import get_db
from database.models import User, Agent
from services.google_adk import google_adk_service, get_google_adk_service, GoogleADKService
from auth.dependencies import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/google-adk", tags=["google-adk"])


# Request/Response Models
class TextGenerationRequest(BaseModel):
    prompt: str = Field(..., min_length=1, max_length=32000, description="Generation prompt")
    model: str = Field(default="gemini-pro", description="Model to use")
    temperature: Optional[float] = Field(None, ge=0.0, le=1.0, description="Temperature")
    max_output_tokens: Optional[int] = Field(None, ge=1, le=32768, description="Max tokens")
    top_p: Optional[float] = Field(None, ge=0.0, le=1.0, description="Top P")
    top_k: Optional[int] = Field(None, ge=1, le=100, description="Top K")
    agent_context: Optional[Dict[str, Any]] = Field(None, description="Agent context")


class TextGenerationResponse(BaseModel):
    success: bool
    text: str
    model: str
    tokens_used: Optional[int] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class CodeGenerationRequest(BaseModel):
    prompt: str = Field(..., min_length=1, max_length=8000, description="Code generation prompt")
    language: str = Field(default="python", description="Programming language")
    model: str = Field(default="code-bison", description="Model to use")
    temperature: Optional[float] = Field(None, ge=0.0, le=1.0, description="Temperature")
    max_output_tokens: Optional[int] = Field(None, ge=1, le=2048, description="Max tokens")


class CodeGenerationResponse(BaseModel):
    success: bool
    code: str
    language: str
    model: str
    metadata: Dict[str, Any] = Field(default_factory=dict)


class ImageAnalysisRequest(BaseModel):
    prompt: str = Field(..., min_length=1, max_length=1000, description="Analysis prompt")
    image_base64: Optional[str] = Field(None, description="Base64 encoded image")
    model: str = Field(default="gemini-pro-vision", description="Model to use")
    temperature: Optional[float] = Field(None, ge=0.0, le=1.0, description="Temperature")


class ImageAnalysisResponse(BaseModel):
    success: bool
    analysis: str
    model: str
    metadata: Dict[str, Any] = Field(default_factory=dict)


class EmbeddingRequest(BaseModel):
    texts: List[str] = Field(..., min_items=1, max_items=100, description="Texts to embed")
    model: str = Field(default="textembedding-gecko", description="Embedding model")
    task_type: str = Field(default="RETRIEVAL_DOCUMENT", description="Task type")


class EmbeddingResponse(BaseModel):
    success: bool
    embeddings: List[List[float]]
    model: str
    dimensions: int
    metadata: Dict[str, Any] = Field(default_factory=dict)


class ChatMessage(BaseModel):
    role: str = Field(..., description="Message role (user/assistant)")
    content: str = Field(..., description="Message content")


class ChatRequest(BaseModel):
    messages: List[ChatMessage] = Field(..., min_items=1, description="Chat messages")
    model: str = Field(default="gemini-pro", description="Model to use")
    temperature: Optional[float] = Field(None, ge=0.0, le=1.0, description="Temperature")
    max_output_tokens: Optional[int] = Field(None, ge=1, le=32768, description="Max tokens")
    stream: bool = Field(default=False, description="Stream response")


class ChatResponse(BaseModel):
    success: bool
    response: str
    model: str
    metadata: Dict[str, Any] = Field(default_factory=dict)


class AgentADKRequest(BaseModel):
    agent_type: str = Field(..., description="Type of agent")
    capabilities: List[str] = Field(..., description="Agent capabilities")
    model_preferences: Dict[str, str] = Field(default_factory=dict, description="Model preferences")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class AgentADKResponse(BaseModel):
    success: bool
    agent_config: Dict[str, Any]
    recommended_models: Dict[str, str]
    features: Dict[str, bool]


class KnowledgeEnhancementRequest(BaseModel):
    agent_id: str = Field(..., description="Agent ID to enhance")
    knowledge_sources: List[Dict[str, Any]] = Field(..., description="Knowledge sources")


class KnowledgeEnhancementResponse(BaseModel):
    success: bool
    sources_processed: int
    message: str


class PerformanceAnalysisRequest(BaseModel):
    agent_id: str = Field(..., description="Agent ID to analyze")
    metrics: Dict[str, Any] = Field(..., description="Performance metrics")


class PerformanceAnalysisResponse(BaseModel):
    success: bool
    analysis: str
    recommendations: List[str]
    metrics_summary: Dict[str, Any]
    timestamp: str


class ModelInfoResponse(BaseModel):
    model: str
    name: str
    description: str
    capabilities: List[str]
    metadata: Dict[str, Any]


class HealthCheckResponse(BaseModel):
    status: str
    vertex_ai: Dict[str, Any]
    generative_ai: Dict[str, Any]
    available_models: List[str]
    timestamp: str


# API Endpoints

@router.post("/generate/text", response_model=TextGenerationResponse)
async def generate_text(
    request: TextGenerationRequest,
    current_user: User = Depends(get_current_user),
    adk_service: GoogleADKService = Depends(get_google_adk_service)
):
    """Generate text using Google's generative AI models"""
    try:
        # Generate text
        text = await adk_service.generate_text(
            prompt=request.prompt,
            model=request.model,
            agent_context=request.agent_context,
            temperature=request.temperature,
            max_output_tokens=request.max_output_tokens,
            top_p=request.top_p,
            top_k=request.top_k
        )
        
        return TextGenerationResponse(
            success=True,
            text=text,
            model=request.model,
            metadata={
                "user_id": str(current_user.id),
                "prompt_length": len(request.prompt)
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to generate text: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate text: {str(e)}"
        )


@router.post("/generate/code", response_model=CodeGenerationResponse)
async def generate_code(
    request: CodeGenerationRequest,
    current_user: User = Depends(get_current_user),
    adk_service: GoogleADKService = Depends(get_google_adk_service)
):
    """Generate code using Google's code generation models"""
    try:
        # Generate code
        code = await adk_service.generate_code(
            prompt=request.prompt,
            language=request.language,
            model=request.model,
            temperature=request.temperature,
            max_output_tokens=request.max_output_tokens
        )
        
        return CodeGenerationResponse(
            success=True,
            code=code,
            language=request.language,
            model=request.model,
            metadata={
                "user_id": str(current_user.id),
                "prompt_length": len(request.prompt)
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to generate code: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate code: {str(e)}"
        )


@router.post("/analyze/image", response_model=ImageAnalysisResponse)
async def analyze_image_base64(
    request: ImageAnalysisRequest,
    current_user: User = Depends(get_current_user),
    adk_service: GoogleADKService = Depends(get_google_adk_service)
):
    """Analyze image using Google's vision models (base64 input)"""
    try:
        if not request.image_base64:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Image data is required"
            )
        
        # Analyze image
        analysis = await adk_service.analyze_image(
            image_data=request.image_base64,
            prompt=request.prompt,
            model=request.model,
            temperature=request.temperature
        )
        
        return ImageAnalysisResponse(
            success=True,
            analysis=analysis,
            model=request.model,
            metadata={
                "user_id": str(current_user.id),
                "prompt_length": len(request.prompt)
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to analyze image: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to analyze image: {str(e)}"
        )


@router.post("/analyze/image/upload", response_model=ImageAnalysisResponse)
async def analyze_image_upload(
    prompt: str,
    file: UploadFile = File(...),
    model: str = "gemini-pro-vision",
    current_user: User = Depends(get_current_user),
    adk_service: GoogleADKService = Depends(get_google_adk_service)
):
    """Analyze uploaded image using Google's vision models"""
    try:
        # Read image data
        image_data = await file.read()
        
        # Analyze image
        analysis = await adk_service.analyze_image(
            image_data=image_data,
            prompt=prompt,
            model=model
        )
        
        return ImageAnalysisResponse(
            success=True,
            analysis=analysis,
            model=model,
            metadata={
                "user_id": str(current_user.id),
                "filename": file.filename,
                "content_type": file.content_type
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to analyze uploaded image: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to analyze image: {str(e)}"
        )


@router.post("/embeddings", response_model=EmbeddingResponse)
async def generate_embeddings(
    request: EmbeddingRequest,
    current_user: User = Depends(get_current_user),
    adk_service: GoogleADKService = Depends(get_google_adk_service)
):
    """Generate embeddings using Google's embedding models"""
    try:
        # Generate embeddings
        embeddings = await adk_service.generate_embeddings(
            texts=request.texts,
            model=request.model,
            task_type=request.task_type
        )
        
        return EmbeddingResponse(
            success=True,
            embeddings=embeddings,
            model=request.model,
            dimensions=len(embeddings[0]) if embeddings else 0,
            metadata={
                "user_id": str(current_user.id),
                "texts_count": len(request.texts)
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to generate embeddings: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate embeddings: {str(e)}"
        )


@router.post("/chat")
async def chat(
    request: ChatRequest,
    current_user: User = Depends(get_current_user),
    adk_service: GoogleADKService = Depends(get_google_adk_service)
):
    """Chat with Google's conversational AI models"""
    try:
        # Convert messages to dict format
        messages = [{"role": msg.role, "content": msg.content} for msg in request.messages]
        
        if request.stream:
            # Stream response
            async def generate():
                async for chunk in adk_service.chat_stream(
                    messages=messages,
                    model=request.model,
                    temperature=request.temperature,
                    max_output_tokens=request.max_output_tokens
                ):
                    yield chunk
            
            return StreamingResponse(generate(), media_type="text/plain")
        else:
            # Regular response
            response_text = ""
            async for chunk in adk_service.chat_stream(
                messages=messages,
                model=request.model,
                temperature=request.temperature,
                max_output_tokens=request.max_output_tokens
            ):
                response_text += chunk
            
            return ChatResponse(
                success=True,
                response=response_text,
                model=request.model,
                metadata={
                    "user_id": str(current_user.id),
                    "messages_count": len(messages)
                }
            )
            
    except Exception as e:
        logger.error(f"Failed to process chat: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process chat: {str(e)}"
        )


@router.post("/agent/create", response_model=AgentADKResponse)
async def create_agent_with_adk(
    request: AgentADKRequest,
    current_user: User = Depends(get_current_user),
    adk_service: GoogleADKService = Depends(get_google_adk_service)
):
    """Create an AI agent configuration optimized for Google ADK"""
    try:
        # Create agent configuration
        agent_config = await adk_service.create_agent_with_adk(
            agent_type=request.agent_type,
            capabilities=request.capabilities,
            model_preferences=request.model_preferences,
            metadata=request.metadata
        )
        
        return AgentADKResponse(
            success=True,
            agent_config=agent_config,
            recommended_models=agent_config.get("models", {}),
            features=agent_config.get("features", {})
        )
        
    except Exception as e:
        logger.error(f"Failed to create agent with ADK: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create agent configuration: {str(e)}"
        )


@router.post("/agent/enhance-knowledge", response_model=KnowledgeEnhancementResponse)
async def enhance_agent_knowledge(
    request: KnowledgeEnhancementRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    adk_service: GoogleADKService = Depends(get_google_adk_service)
):
    """Enhance agent with knowledge using Google's AI capabilities"""
    try:
        # Verify agent ownership
        agent = db.query(Agent).filter(
            Agent.id == request.agent_id,
            Agent.user_id == current_user.id
        ).first()
        
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found or access denied"
            )
        
        # Enhance agent knowledge
        success = await adk_service.enhance_agent_with_knowledge(
            agent_id=request.agent_id,
            knowledge_sources=request.knowledge_sources
        )
        
        if success:
            return KnowledgeEnhancementResponse(
                success=True,
                sources_processed=len(request.knowledge_sources),
                message=f"Successfully enhanced agent {request.agent_id} with {len(request.knowledge_sources)} knowledge sources"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to enhance agent knowledge"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to enhance agent knowledge: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to enhance agent knowledge: {str(e)}"
        )


@router.post("/agent/analyze-performance", response_model=PerformanceAnalysisResponse)
async def analyze_agent_performance(
    request: PerformanceAnalysisRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    adk_service: GoogleADKService = Depends(get_google_adk_service)
):
    """Analyze agent performance using Google's AI"""
    try:
        # Verify agent ownership
        agent = db.query(Agent).filter(
            Agent.id == request.agent_id,
            Agent.user_id == current_user.id
        ).first()
        
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found or access denied"
            )
        
        # Analyze performance
        analysis_result = await adk_service.analyze_agent_performance(
            agent_id=request.agent_id,
            metrics=request.metrics
        )
        
        return PerformanceAnalysisResponse(
            success=True,
            analysis=analysis_result["analysis"],
            recommendations=analysis_result["recommendations"],
            metrics_summary=analysis_result["metrics"],
            timestamp=analysis_result["timestamp"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to analyze agent performance: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to analyze performance: {str(e)}"
        )


@router.get("/models/{model}/info", response_model=ModelInfoResponse)
async def get_model_info(
    model: str,
    current_user: User = Depends(get_current_user),
    adk_service: GoogleADKService = Depends(get_google_adk_service)
):
    """Get information about a specific Google AI model"""
    try:
        model_info = await adk_service.get_model_info(model)
        
        return ModelInfoResponse(
            model=model,
            name=model_info.get("name", model),
            description=model_info.get("description", ""),
            capabilities=model_info.get("capabilities", []),
            metadata=model_info
        )
        
    except Exception as e:
        logger.error(f"Failed to get model info: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get model information: {str(e)}"
        )


@router.get("/health", response_model=HealthCheckResponse)
async def health_check(
    adk_service: GoogleADKService = Depends(get_google_adk_service)
):
    """Check Google ADK service health"""
    try:
        health_status = await adk_service.health_check()
        
        return HealthCheckResponse(
            status=health_status["status"],
            vertex_ai=health_status["vertex_ai"],
            generative_ai=health_status["generative_ai"],
            available_models=health_status["available_models"],
            timestamp=health_status["timestamp"]
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthCheckResponse(
            status="unhealthy",
            vertex_ai={"initialized": False},
            generative_ai={"initialized": False},
            available_models=[],
            timestamp=datetime.utcnow().isoformat()
        )