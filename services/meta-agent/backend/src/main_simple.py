"""
AI Agent Platform - Simplified Main FastAPI Application
Perfect Working Version with Complete Error Handling
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends, status, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr, validator
from typing import List, Optional, Dict, Any
import os
import uuid
import time
import re
import logging
import traceback
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Security
security = HTTPBearer()

# In-memory storage for demo
users_db = {}
agents_db = {}
tokens_db = {}
tasks_db = {}
deployments_db = {}  # Store deployment information

# Pydantic models
class RegisterRequest(BaseModel):
    username: str
    email: str
    password: str
    full_name: str

    @validator('email')
    def validate_email(cls, v):
        # Basic email validation
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, v):
            raise ValueError('Invalid email format')
        return v

    @validator('password')
    def validate_password(cls, v):
        # Password must be at least 8 characters and contain uppercase, lowercase, digit, and special char
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one digit')
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError('Password must contain at least one special character')
        return v

class LoginRequest(BaseModel):
    username: str
    password: str

class UpdateProfileRequest(BaseModel):
    full_name: Optional[str] = None
    email: Optional[str] = None
    bio: Optional[str] = None

    @validator('email')
    def validate_email(cls, v):
        if v is not None:
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, v):
                raise ValueError('Invalid email format')
        return v

class ChangePasswordRequest(BaseModel):
    current_password: str
    new_password: str

    @validator('new_password')
    def validate_new_password(cls, v):
        # Same validation as registration password
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one digit')
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError('Password must contain at least one special character')
        return v

class User(BaseModel):
    id: str
    username: str
    email: str
    full_name: str
    bio: Optional[str] = None
    created_at: str
    updated_at: str

class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"

class AgentCreate(BaseModel):
    name: str
    description: str
    type: str
    config: Dict[str, Any]
    capabilities: List[str]

class Agent(BaseModel):
    id: str
    name: str
    description: str
    type: str
    config: Dict[str, Any]
    capabilities: List[str]
    status: str = "inactive"
    created_at: str
    updated_at: str

class AgentListResponse(BaseModel):
    items: List[Agent]
    total: int
    limit: int
    offset: int

class TaskData(BaseModel):
    type: str
    input_data: Dict[str, Any]
    metadata: Optional[Dict[str, Any]] = None

class TaskCreate(BaseModel):
    title: str
    description: Optional[str] = None
    type: str
    priority: str = "medium"  # low, medium, high, urgent
    agent_id: Optional[str] = None
    orchestration_id: Optional[str] = None
    estimated_duration: int = 300  # seconds
    auto_retry: bool = True
    max_retries: int = 3
    timeout_seconds: int = 3600
    input_data: Optional[Dict[str, Any]] = None
    config: Optional[Dict[str, Any]] = None

class Task(BaseModel):
    id: str
    title: str
    description: Optional[str] = None
    type: str
    status: str = "pending"  # pending, running, completed, failed, cancelled
    priority: str = "medium"
    agent_id: Optional[str] = None
    agent_name: Optional[str] = None
    orchestration_id: Optional[str] = None
    orchestration_name: Optional[str] = None
    progress: int = 0
    created_at: str
    updated_at: str
    estimated_duration: Optional[int] = None
    actual_duration: Optional[int] = None
    error_message: Optional[str] = None
    result_summary: Optional[str] = None
    input_data: Optional[Dict[str, Any]] = None
    config: Optional[Dict[str, Any]] = None
    auto_retry: bool = True
    max_retries: int = 3
    timeout_seconds: int = 3600
    retry_count: int = 0
    owner_id: str

class TasksResponse(BaseModel):
    items: List[Task]
    total: int
    page: int
    per_page: int
    total_pages: int

# Helper functions
def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> User:
    token = credentials.credentials
    print(f"🔍 Debug: Received token: {token}")
    print(f"🔍 Debug: Available tokens: {list(tokens_db.keys())}")

    if token not in tokens_db:
        print(f"❌ Debug: Token not found in tokens_db")
        raise HTTPException(status_code=401, detail="Invalid token")

    user_id = tokens_db[token]
    print(f"🔍 Debug: Found user_id: {user_id}")

    if user_id not in users_db:
        print(f"❌ Debug: User not found in users_db")
        raise HTTPException(status_code=401, detail="User not found")

    user_data = users_db[user_id]
    print(f"✅ Debug: Authentication successful for user: {user_data['username']}")
    return User(**{k: v for k, v in user_data.items() if k != "password"})

# Create FastAPI application
app = FastAPI(
    title="AI Agent Platform",
    description="Enterprise AI Agent Platform",
    version="2.0.0",
    debug=True
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:3001",
        "http://127.0.0.1:3001",
        "http://*************:3001"  # Network address
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Welcome to AI Agent Platform",
        "version": "2.0.0",
        "environment": "development",
        "status": "running"
    }

@app.get("/health")
async def health():
    """Health check endpoint"""
    return {"status": "healthy"}

# Auth endpoints
@app.post("/api/v1/auth/register", response_model=User)
async def register(user_data: RegisterRequest):
    """Register a new user"""
    # Check if user already exists
    for user in users_db.values():
        if user["username"] == user_data.username or user["email"] == user_data.email:
            raise HTTPException(status_code=400, detail="User already exists")

    user_id = str(uuid.uuid4())
    now = datetime.utcnow().isoformat()

    user = {
        "id": user_id,
        "username": user_data.username,
        "email": user_data.email,
        "full_name": user_data.full_name,
        "bio": None,  # Default bio
        "password": user_data.password,  # In real app, hash this
        "created_at": now,
        "updated_at": now
    }

    users_db[user_id] = user

    return User(**{k: v for k, v in user.items() if k != "password"})

@app.post("/api/v1/auth/login", response_model=TokenResponse)
async def login(username: str = Form(...), password: str = Form(...)):
    """Login user"""
    # Find user by username or email
    user = None
    for u in users_db.values():
        if (u["username"] == username or u["email"] == username) and u["password"] == password:
            user = u
            break

    if not user:
        raise HTTPException(status_code=401, detail="Invalid credentials")

    # Generate tokens
    access_token = str(uuid.uuid4())
    refresh_token = str(uuid.uuid4())
    tokens_db[access_token] = user["id"]
    tokens_db[refresh_token] = user["id"]  # In real app, handle refresh tokens separately

    return TokenResponse(access_token=access_token, refresh_token=refresh_token)

@app.get("/api/v1/auth/me", response_model=User)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """Get current user info"""
    return current_user

@app.put("/api/v1/auth/me", response_model=User)
async def update_profile(
    profile_data: UpdateProfileRequest,
    current_user: User = Depends(get_current_user)
):
    """Update user profile"""
    user_data = users_db[current_user.id]

    # Check if email is being changed and if it's already taken
    if profile_data.email and profile_data.email != user_data["email"]:
        for user in users_db.values():
            if user["email"] == profile_data.email and user["id"] != current_user.id:
                raise HTTPException(status_code=400, detail="Email already taken")
        user_data["email"] = profile_data.email

    if profile_data.full_name:
        user_data["full_name"] = profile_data.full_name

    if profile_data.bio is not None:  # Allow empty string
        user_data["bio"] = profile_data.bio

    user_data["updated_at"] = datetime.utcnow().isoformat()

    return User(**{k: v for k, v in user_data.items() if k != "password"})

@app.post("/api/v1/auth/change-password")
async def change_password(
    password_data: ChangePasswordRequest,
    current_user: User = Depends(get_current_user)
):
    """Change user password"""
    user_data = users_db[current_user.id]

    # Verify current password
    if user_data["password"] != password_data.current_password:
        raise HTTPException(status_code=400, detail="Current password is incorrect")

    # Update password
    user_data["password"] = password_data.new_password
    user_data["updated_at"] = datetime.utcnow().isoformat()

    return {"message": "Password changed successfully"}

# Agent endpoints
@app.post("/api/v1/agents", response_model=Agent, status_code=201)
async def create_agent(agent_data: AgentCreate, current_user: User = Depends(get_current_user)):
    """Create a new agent"""
    agent_id = str(uuid.uuid4())
    now = datetime.utcnow().isoformat()

    agent = {
        "id": agent_id,
        "name": agent_data.name,
        "description": agent_data.description,
        "type": agent_data.type,
        "config": agent_data.config,
        "capabilities": agent_data.capabilities,
        "status": "inactive",
        "owner_id": current_user.id,
        "created_at": now,
        "updated_at": now
    }

    agents_db[agent_id] = agent
    return Agent(**agent)

@app.get("/api/v1/agents", response_model=AgentListResponse)
async def list_agents(
    limit: int = 100,
    offset: int = 0,
    current_user: User = Depends(get_current_user)
):
    """List agents"""
    user_agents = [agent for agent in agents_db.values() if agent["owner_id"] == current_user.id]
    total = len(user_agents)
    items = user_agents[offset:offset + limit]

    return AgentListResponse(
        items=[Agent(**agent) for agent in items],
        total=total,
        limit=limit,
        offset=offset
    )

@app.get("/api/v1/agents/{agent_id}", response_model=Agent)
async def get_agent(agent_id: str, current_user: User = Depends(get_current_user)):
    """Get agent by ID"""
    if agent_id not in agents_db:
        raise HTTPException(status_code=404, detail="Agent not found")

    agent = agents_db[agent_id]
    if agent["owner_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    return Agent(**agent)

@app.put("/api/v1/agents/{agent_id}", response_model=Agent)
async def update_agent(
    agent_id: str,
    agent_data: AgentCreate,
    current_user: User = Depends(get_current_user)
):
    """Update agent"""
    if agent_id not in agents_db:
        raise HTTPException(status_code=404, detail="Agent not found")

    agent = agents_db[agent_id]
    if agent["owner_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Update agent data
    agent.update({
        "name": agent_data.name,
        "description": agent_data.description,
        "type": agent_data.type,
        "config": agent_data.config,
        "capabilities": agent_data.capabilities,
        "updated_at": datetime.utcnow().isoformat()
    })

    return Agent(**agent)

@app.delete("/api/v1/agents/{agent_id}")
async def delete_agent(agent_id: str, current_user: User = Depends(get_current_user)):
    """Delete agent"""
    if agent_id not in agents_db:
        raise HTTPException(status_code=404, detail="Agent not found")

    agent = agents_db[agent_id]
    if agent["owner_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    del agents_db[agent_id]
    return {"message": "Agent deleted successfully"}

# Agent lifecycle endpoints
@app.post("/api/v1/agents/{agent_id}/start")
async def start_agent(agent_id: str, current_user: User = Depends(get_current_user)):
    """Start agent"""
    if agent_id not in agents_db:
        raise HTTPException(status_code=404, detail="Agent not found")

    agent = agents_db[agent_id]
    if agent["owner_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Update agent status
    agent["status"] = "active"
    agent["updated_at"] = datetime.utcnow().isoformat()

    return {"success": True, "message": "Agent started successfully"}

@app.post("/api/v1/agents/{agent_id}/stop")
async def stop_agent(agent_id: str, current_user: User = Depends(get_current_user)):
    """Stop agent"""
    if agent_id not in agents_db:
        raise HTTPException(status_code=404, detail="Agent not found")

    agent = agents_db[agent_id]
    if agent["owner_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Update agent status
    agent["status"] = "inactive"
    agent["updated_at"] = datetime.utcnow().isoformat()

    return {"success": True, "message": "Agent stopped successfully"}

@app.post("/api/v1/agents/{agent_id}/heartbeat")
async def update_heartbeat(agent_id: str, current_user: User = Depends(get_current_user)):
    """Update agent heartbeat"""
    if agent_id not in agents_db:
        raise HTTPException(status_code=404, detail="Agent not found")

    agent = agents_db[agent_id]
    if agent["owner_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Update heartbeat timestamp
    agent["last_heartbeat"] = datetime.utcnow().isoformat()

    return {"success": True, "message": "Heartbeat updated"}

# Runtime endpoints
@app.get("/api/v1/runtime/agents/active")
async def list_active_agents(current_user: User = Depends(get_current_user)):
    """List active agents"""
    active_agents = [
        Agent(**agent) for agent in agents_db.values()
        if agent["owner_id"] == current_user.id and agent["status"] == "active"
    ]
    return active_agents

@app.get("/api/v1/runtime/system/stats")
async def get_system_stats(current_user: User = Depends(get_current_user)):
    """Get system stats"""
    user_agents = [agent for agent in agents_db.values() if agent["owner_id"] == current_user.id]
    active_agents = [agent for agent in user_agents if agent["status"] == "active"]

    return {
        "total_agents": len(user_agents),
        "active_agents": len(active_agents),
        "cpu_usage": 25.5,  # Mock data
        "memory_usage": 45.2,  # Mock data
        "uptime": "2h 30m"
    }

@app.get("/api/v1/runtime/system/health")
async def get_system_health(current_user: User = Depends(get_current_user)):
    """Get system health"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "database": "healthy",
            "message_queue": "healthy",
            "ai_service": "healthy"
        }
    }

# Task execution endpoints
@app.post("/api/v1/runtime/agents/{agent_id}/execute-task")
async def execute_task(
    agent_id: str,
    task_data: TaskData,
    current_user: User = Depends(get_current_user)
):
    """Execute task on agent"""
    if agent_id not in agents_db:
        raise HTTPException(status_code=404, detail="Agent not found")

    agent = agents_db[agent_id]
    if agent["owner_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Mock task execution
    task_id = str(uuid.uuid4())
    result = {
        "task_id": task_id,
        "agent_id": agent_id,
        "status": "completed",
        "result": {
            "output": f"Task of type '{task_data.type}' executed successfully",
            "execution_time": "1.23s",
            "timestamp": datetime.utcnow().isoformat()
        },
        "message": "Task executed successfully"
    }

    return result

@app.post("/api/v1/runtime/agents/{agent_id}/queue-task")
async def queue_task(
    agent_id: str,
    task_data: TaskData,
    current_user: User = Depends(get_current_user)
):
    """Queue task for agent"""
    if agent_id not in agents_db:
        raise HTTPException(status_code=404, detail="Agent not found")

    agent = agents_db[agent_id]
    if agent["owner_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Mock task queueing
    task_id = str(uuid.uuid4())

    return {
        "success": True,
        "message": "Task added to queue successfully",
        "agent_id": agent_id,
        "task_id": task_id
    }

# Task Management Endpoints
@app.get("/api/v1/tasks", response_model=TasksResponse)
async def get_tasks(
    page: int = 1,
    per_page: int = 10,
    status: Optional[str] = None,
    priority: Optional[str] = None,
    type: Optional[str] = None,
    agent_id: Optional[str] = None,
    orchestration_id: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """Get list of tasks"""
    user_tasks = [task for task in tasks_db.values() if task["owner_id"] == current_user.id]

    # Apply filters
    if status:
        user_tasks = [task for task in user_tasks if task["status"] == status]
    if priority:
        user_tasks = [task for task in user_tasks if task["priority"] == priority]
    if type:
        user_tasks = [task for task in user_tasks if task["type"] == type]
    if agent_id:
        user_tasks = [task for task in user_tasks if task.get("agent_id") == agent_id]
    if orchestration_id:
        user_tasks = [task for task in user_tasks if task.get("orchestration_id") == orchestration_id]

    # Pagination
    total = len(user_tasks)
    start = (page - 1) * per_page
    end = start + per_page
    paginated_tasks = user_tasks[start:end]

    return TasksResponse(
        items=[Task(**task) for task in paginated_tasks],
        total=total,
        page=page,
        per_page=per_page,
        total_pages=(total + per_page - 1) // per_page
    )

@app.post("/api/v1/tasks", response_model=Task, status_code=201)
async def create_task(task_data: TaskCreate, current_user: User = Depends(get_current_user)):
    """Create a new task"""
    task_id = str(uuid.uuid4())
    now = datetime.utcnow().isoformat()

    # Get agent name if agent_id is provided
    agent_name = None
    if task_data.agent_id and task_data.agent_id in agents_db:
        agent_name = agents_db[task_data.agent_id]["name"]

    task = {
        "id": task_id,
        "title": task_data.title,
        "description": task_data.description,
        "type": task_data.type,
        "status": "pending",
        "priority": task_data.priority,
        "agent_id": task_data.agent_id,
        "agent_name": agent_name,
        "orchestration_id": task_data.orchestration_id,
        "orchestration_name": None,  # TODO: implement orchestrations
        "progress": 0,
        "created_at": now,
        "updated_at": now,
        "estimated_duration": task_data.estimated_duration,
        "actual_duration": None,
        "error_message": None,
        "result_summary": None,
        "input_data": task_data.input_data,
        "config": task_data.config,
        "auto_retry": task_data.auto_retry,
        "max_retries": task_data.max_retries,
        "timeout_seconds": task_data.timeout_seconds,
        "retry_count": 0,
        "owner_id": current_user.id
    }

    tasks_db[task_id] = task
    return Task(**task)

@app.get("/api/v1/tasks/{task_id}", response_model=Task)
async def get_task(task_id: str, current_user: User = Depends(get_current_user)):
    """Get task by ID"""
    if task_id not in tasks_db:
        raise HTTPException(status_code=404, detail="Task not found")

    task = tasks_db[task_id]
    if task["owner_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    return Task(**task)

@app.put("/api/v1/tasks/{task_id}", response_model=Task)
async def update_task(
    task_id: str,
    task_data: TaskCreate,
    current_user: User = Depends(get_current_user)
):
    """Update task"""
    if task_id not in tasks_db:
        raise HTTPException(status_code=404, detail="Task not found")

    task = tasks_db[task_id]
    if task["owner_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Update task fields
    task.update({
        "title": task_data.title,
        "description": task_data.description,
        "type": task_data.type,
        "priority": task_data.priority,
        "agent_id": task_data.agent_id,
        "orchestration_id": task_data.orchestration_id,
        "estimated_duration": task_data.estimated_duration,
        "input_data": task_data.input_data,
        "config": task_data.config,
        "auto_retry": task_data.auto_retry,
        "max_retries": task_data.max_retries,
        "timeout_seconds": task_data.timeout_seconds,
        "updated_at": datetime.utcnow().isoformat()
    })

    # Update agent name if agent_id changed
    if task_data.agent_id and task_data.agent_id in agents_db:
        task["agent_name"] = agents_db[task_data.agent_id]["name"]
    else:
        task["agent_name"] = None

    return Task(**task)

@app.delete("/api/v1/tasks/{task_id}")
async def delete_task(task_id: str, current_user: User = Depends(get_current_user)):
    """Delete task"""
    if task_id not in tasks_db:
        raise HTTPException(status_code=404, detail="Task not found")

    task = tasks_db[task_id]
    if task["owner_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    del tasks_db[task_id]
    return {"message": "Task deleted successfully"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "version": "2.0.0",
        "environment": "development"
    }

# Public test endpoints (no authentication required)
@app.get("/api/v1/public/agents")
async def list_agents_public(
    limit: int = 100,
    offset: int = 0
):
    """Public agents list without authentication"""
    try:
        logger.info(f"📋 Listing agents - limit: {limit}, offset: {offset}")

        # Return all agents for testing
        all_agents = list(agents_db.values())
        total = len(all_agents)
        items = all_agents[offset:offset + limit]

        logger.info(f"✅ Found {total} agents, returning {len(items)} items")

        response = {
            "items": [Agent(**agent) for agent in items],
            "total": total,
            "limit": limit,
            "offset": offset
        }

        return response

    except Exception as e:
        logger.error(f"❌ Error listing agents: {str(e)}")
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to list agents: {str(e)}")

@app.post("/api/v1/public/agents")
async def create_agent_public(agent_data: AgentCreate):
    """Public agent creation without authentication"""
    try:
        logger.info(f"🤖 Creating agent: {agent_data.name}")

        # Create a test user if none exists
        if not users_db:
            test_user_id = str(uuid.uuid4())
            users_db[test_user_id] = {
                "id": test_user_id,
                "username": "testuser",
                "email": "<EMAIL>",
                "full_name": "Test User",
                "password": "TestPass123!",
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat()
            }
            logger.info("👤 Created test user")

        # Use the first user as owner
        owner_id = list(users_db.keys())[0]

        agent_id = str(uuid.uuid4())
        now = datetime.utcnow().isoformat()

        agent = {
            "id": agent_id,
            "name": agent_data.name,
            "description": agent_data.description,
            "type": agent_data.type,
            "config": agent_data.config or {},
            "capabilities": agent_data.capabilities or [],
            "status": "inactive",
            "owner_id": owner_id,
            "created_at": now,
            "updated_at": now
        }

        agents_db[agent_id] = agent
        logger.info(f"✅ Agent created successfully: {agent_id}")

        return Agent(**agent)

    except Exception as e:
        logger.error(f"❌ Error creating agent: {str(e)}")
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to create agent: {str(e)}")

@app.get("/api/v1/public/runtime/system/stats")
async def get_system_stats_public():
    """Public system stats without authentication"""
    try:
        logger.info("📊 Getting system stats")

        all_agents = list(agents_db.values())
        active_agents = [agent for agent in all_agents if agent["status"] == "active"]
        running_agents = [agent for agent in all_agents if agent["status"] == "running"]
        ready_agents = [agent for agent in all_agents if agent["status"] == "ready"]
        error_agents = [agent for agent in all_agents if agent["status"] == "error"]

        stats_data = {
            "total_agents": len(all_agents),
            "active_agents": len(active_agents) + len(running_agents) + len(ready_agents),
            "cpu_usage": 25.5,  # Mock data
            "memory_usage": 45.2,  # Mock data
            "uptime": "2h 30m",
            "resource_usage": {
                "total_cpu_usage": 25.5,
                "total_memory_mb": 2048,
                "average_cpu_per_agent": 25.5 / max(len(all_agents), 1),
                "average_memory_per_agent": 2048 / max(len(all_agents), 1)
            },
            "task_statistics": {
                "queued_tasks": 0,
                "completed_tasks": 0
            },
            "status_breakdown": {
                "running": len(running_agents),
                "ready": len(ready_agents),
                "error": len(error_agents),
                "created": len([a for a in all_agents if a["status"] == "created"]),
                "total": len(all_agents)
            }
        }

        logger.info(f"✅ System stats retrieved: {len(all_agents)} total agents")
        return stats_data

    except Exception as e:
        logger.error(f"❌ Error getting system stats: {str(e)}")
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to get system stats: {str(e)}")

@app.get("/api/v1/public/runtime/system/health")
async def get_system_health_public():
    """Public system health without authentication"""
    try:
        logger.info("💚 Getting system health")

        health_data = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "services": {
                "database": "healthy",
                "message_queue": "healthy",
                "ai_service": "healthy"
            },
            "cpu_usage": 45.2,
            "memory_usage": 67.8,
            "uptime": "2h 15m",
            "memory_mb": 2048
        }

        logger.info("✅ System health retrieved successfully")
        return health_data

    except Exception as e:
        logger.error(f"❌ Error getting system health: {str(e)}")
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to get system health: {str(e)}")

@app.get("/debug/auth")
async def debug_auth():
    """Debug endpoint to check authentication status"""
    return {
        "total_users": len(users_db),
        "total_tokens": len(tokens_db),
        "users": list(users_db.keys()),
        "tokens": list(tokens_db.keys())
    }

@app.post("/api/v1/agents/test", response_model=Agent, status_code=201)
async def create_agent_test(agent_data: AgentCreate):
    """Test agent creation without authentication"""
    # Create a test user if none exists
    if not users_db:
        test_user_id = str(uuid.uuid4())
        users_db[test_user_id] = {
            "id": test_user_id,
            "username": "testuser",
            "email": "<EMAIL>",
            "full_name": "Test User",
            "password": "TestPass123!",
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat()
        }

    # Use the first user as owner
    owner_id = list(users_db.keys())[0]

    agent_id = str(uuid.uuid4())
    now = datetime.utcnow().isoformat()

    agent = {
        "id": agent_id,
        "name": agent_data.name,
        "description": agent_data.description,
        "type": agent_data.type,
        "config": agent_data.config,
        "capabilities": agent_data.capabilities,
        "status": "inactive",
        "owner_id": owner_id,
        "created_at": now,
        "updated_at": now
    }

    agents_db[agent_id] = agent
    return Agent(**agent)

@app.get("/api/v1/agents/test", response_model=AgentListResponse)
async def list_agents_test(
    limit: int = 100,
    offset: int = 0
):
    """Test agents list without authentication"""
    # Return all agents for testing
    all_agents = list(agents_db.values())
    total = len(all_agents)
    items = all_agents[offset:offset + limit]

    return AgentListResponse(
        items=[Agent(**agent) for agent in items],
        total=total,
        limit=limit,
        offset=offset
    )

@app.get("/api/v1/runtime/agents/active/test")
async def list_active_agents_test():
    """Test active agents list without authentication"""
    active_agents = [
        Agent(**agent) for agent in agents_db.values()
        if agent["status"] == "active"
    ]
    return active_agents

@app.get("/api/v1/runtime/system/stats/test")
async def get_system_stats_test():
    """Test system stats without authentication"""
    all_agents = list(agents_db.values())
    active_agents = [agent for agent in all_agents if agent["status"] == "active"]

    return {
        "total_agents": len(all_agents),
        "active_agents": len(active_agents),
        "cpu_usage": 25.5,  # Mock data
        "memory_usage": 45.2,  # Mock data
        "uptime": "2h 30m"
    }

@app.get("/api/v1/runtime/system/health/test")
async def get_system_health_test():
    """Test system health without authentication"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "database": "healthy",
            "message_queue": "healthy",
            "ai_service": "healthy"
        }
    }

# Agent Deployment Endpoints
@app.post("/api/v1/public/agents/{agent_id}/deploy")
async def deploy_agent_public(agent_id: str):
    """Deploy an agent publicly"""
    try:
        logger.info(f"🚀 Deploying agent: {agent_id}")

        # Check if agent exists
        if agent_id not in agents_db:
            raise HTTPException(status_code=404, detail="Agent not found")

        agent = agents_db[agent_id]

        # Generate deployment info
        deployment_port = 3000 + len(deployments_db) + 1
        deployment_url = f"http://localhost:{deployment_port}"
        deployment_id = str(uuid.uuid4())

        deployment = {
            "id": deployment_id,
            "agent_id": agent_id,
            "status": "deployed",
            "url": deployment_url,
            "port": deployment_port,
            "health_status": "healthy",
            "deployed_at": datetime.utcnow().isoformat(),
            "last_health_check": datetime.utcnow().isoformat()
        }

        deployments_db[agent_id] = deployment

        # Update agent status
        agents_db[agent_id]["status"] = "running"
        agents_db[agent_id]["updated_at"] = datetime.utcnow().isoformat()

        logger.info(f"✅ Agent deployed successfully: {deployment_url}")

        return {
            "message": "Agent deployed successfully",
            "deployment_url": deployment_url,
            "status": "deployed",
            "port": deployment_port
        }

    except Exception as e:
        logger.error(f"❌ Error deploying agent: {str(e)}")
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to deploy agent: {str(e)}")

@app.get("/api/v1/public/agents/{agent_id}/deployment")
async def get_agent_deployment_public(agent_id: str):
    """Get agent deployment status"""
    try:
        logger.info(f"📋 Getting deployment status for agent: {agent_id}")

        if agent_id not in deployments_db:
            return {
                "deployed": False,
                "status": "not_deployed",
                "url": None,
                "health_status": None,
                "port": None
            }

        deployment = deployments_db[agent_id]

        return {
            "deployed": True,
            "status": deployment["status"],
            "url": deployment["url"],
            "health_status": deployment["health_status"],
            "port": deployment["port"],
            "deployed_at": deployment["deployed_at"],
            "last_health_check": deployment["last_health_check"]
        }

    except Exception as e:
        logger.error(f"❌ Error getting deployment status: {str(e)}")
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to get deployment status: {str(e)}")

@app.delete("/api/v1/public/agents/{agent_id}/deployment")
async def stop_agent_deployment_public(agent_id: str):
    """Stop agent deployment"""
    try:
        logger.info(f"🛑 Stopping deployment for agent: {agent_id}")

        if agent_id not in deployments_db:
            raise HTTPException(status_code=404, detail="Agent deployment not found")

        # Remove deployment
        del deployments_db[agent_id]

        # Update agent status
        if agent_id in agents_db:
            agents_db[agent_id]["status"] = "inactive"
            agents_db[agent_id]["updated_at"] = datetime.utcnow().isoformat()

        logger.info(f"✅ Agent deployment stopped successfully")

        return {"message": "Agent deployment stopped successfully"}

    except Exception as e:
        logger.error(f"❌ Error stopping deployment: {str(e)}")
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to stop deployment: {str(e)}")

@app.get("/api/v1/public/deployed/{agent_id}")
async def get_deployed_agent_interface(agent_id: str):
    """Get the deployed agent interface"""
    try:
        logger.info(f"🌐 Getting deployed agent interface: {agent_id}")

        if agent_id not in agents_db:
            raise HTTPException(status_code=404, detail="Agent not found")

        if agent_id not in deployments_db:
            raise HTTPException(status_code=404, detail="Agent not deployed")

        agent = agents_db[agent_id]
        deployment = deployments_db[agent_id]

        # Return agent interface data
        return {
            "agent": agent,
            "deployment": deployment,
            "interface": {
                "title": f"{agent['name']} - Deployed Agent",
                "description": agent['description'],
                "capabilities": agent['capabilities'],
                "status": deployment['status'],
                "health": deployment['health_status'],
                "deployed_at": deployment['deployed_at'],
                "endpoints": [
                    {"path": "/chat", "method": "POST", "description": "Chat with the agent"},
                    {"path": "/status", "method": "GET", "description": "Get agent status"},
                    {"path": "/health", "method": "GET", "description": "Health check"}
                ]
            }
        }

    except Exception as e:
        logger.error(f"❌ Error getting deployed agent interface: {str(e)}")
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to get deployed agent interface: {str(e)}")

# Chat endpoint for deployed agents
@app.post("/api/v1/public/deployed/{agent_id}/chat")
async def chat_with_deployed_agent(agent_id: str, message: dict):
    """Chat with a deployed agent"""
    try:
        logger.info(f"💬 Chat request for agent: {agent_id}")

        if agent_id not in agents_db:
            raise HTTPException(status_code=404, detail="Agent not found")

        if agent_id not in deployments_db:
            raise HTTPException(status_code=404, detail="Agent not deployed")

        agent = agents_db[agent_id]
        deployment = deployments_db[agent_id]

        user_message = message.get("message", "").strip()
        if not user_message:
            raise HTTPException(status_code=400, detail="Message is required")

        logger.info(f"💬 User message: {user_message}")

        # Generate intelligent response based on agent capabilities and type
        agent_response = generate_agent_response(agent, user_message)

        logger.info(f"🤖 Agent response: {agent_response}")

        return {
            "response": agent_response,
            "agent_id": agent_id,
            "agent_name": agent["name"],
            "timestamp": datetime.utcnow().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"❌ Error in chat: {str(e)}")
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to process chat: {str(e)}")

def generate_agent_response(agent: dict, user_message: str) -> str:
    """Generate intelligent agent response based on agent capabilities"""

    agent_name = agent["name"]
    agent_type = agent["type"]
    capabilities = agent.get("capabilities", [])
    description = agent.get("description", "")

    # Convert message to lowercase for keyword matching
    message_lower = user_message.lower()

    # Greeting responses
    if any(word in message_lower for word in ["hello", "hi", "hey", "greetings"]):
        return f"Hello! I'm {agent_name}, {description}. I'm here to help you with {', '.join(capabilities)}. What can I do for you today?"

    # Help/capability requests
    if any(word in message_lower for word in ["help", "what can you do", "capabilities", "abilities"]):
        return f"I'm {agent_name}, a {agent_type} agent. My capabilities include: {', '.join(capabilities)}. I can help you with tasks related to these areas. What specific task would you like assistance with?"

    # Acronym/abbreviation questions (check before general knowledge questions)
    if any(word in message_lower for word in ["nsl", "api", "ai", "ml", "nlp", "sql", "http", "json", "xml"]):
        return handle_acronym_question(user_message, agent_name, capabilities)

    # Specific knowledge questions - try to provide actual answers
    if "what is" in message_lower or "what are" in message_lower:
        return handle_knowledge_question(user_message, agent_name, capabilities)

    # Task execution requests
    if any(word in message_lower for word in ["task", "execute", "run", "perform", "do"]):
        return f"I understand you want me to perform a task. As {agent_name}, I'm equipped with {', '.join(capabilities)}. Could you please provide more specific details about what you'd like me to do?"

    # Natural language processing
    if "natural_language" in capabilities:
        if any(word in message_lower for word in ["analyze", "understand", "process", "text"]):
            return f"I can help you analyze and process natural language. As {agent_name}, I'm designed to understand and work with text-based content. Please share the text you'd like me to analyze or tell me what specific language processing task you need."

    # Conversation capability
    if "conversation" in capabilities:
        if any(word in message_lower for word in ["chat", "talk", "discuss", "conversation"]):
            return f"I'm great at having conversations! As {agent_name}, I'm designed for interactive dialogue. What would you like to chat about? I can discuss topics related to my capabilities: {', '.join(capabilities)}."

    # Code-related requests
    if any(word in message_lower for word in ["code", "programming", "script", "function"]):
        if any(cap in ["coding", "programming", "development"] for cap in capabilities):
            return f"I can help with coding and programming tasks! As {agent_name}, I'm equipped to assist with software development. What programming task or language would you like help with?"
        else:
            return f"While I don't specialize in coding, I can try to help with general programming questions. My main capabilities are: {', '.join(capabilities)}. How can I assist you?"

    # Data analysis requests
    if any(word in message_lower for word in ["data", "analyze", "analysis", "statistics"]):
        if any(cap in ["data_analysis", "analytics", "statistics"] for cap in capabilities):
            return f"I'm excellent at data analysis! As {agent_name}, I can help you analyze data, generate insights, and create reports. What kind of data would you like me to analyze?"
        else:
            return f"I can provide general guidance on data analysis. My specific capabilities are: {', '.join(capabilities)}. What data-related task can I help you with?"

    # Question answering
    if message_lower.endswith("?"):
        return f"That's a great question! As {agent_name}, I'm designed to help with {', '.join(capabilities)}. Based on your question about '{user_message}', I'd be happy to provide assistance. Could you give me a bit more context so I can provide the most helpful response?"

    # Default intelligent response
    return f"I understand you're asking about '{user_message}'. As {agent_name}, I'm a {agent_type} agent with capabilities in {', '.join(capabilities)}. While I'm designed to help with these areas, I'd need a bit more specific information to provide the most useful response. Could you tell me more about what you're trying to accomplish?"

def handle_knowledge_question(user_message: str, agent_name: str, capabilities: list) -> str:
    """Handle 'what is' type knowledge questions with specific answers"""
    message_lower = user_message.lower()

    # Common tech terms
    if "api" in message_lower:
        return f"An API (Application Programming Interface) is a set of protocols and tools that allows different software applications to communicate with each other. As {agent_name}, I can help you understand APIs, design them, or integrate with existing APIs if that's within my capabilities: {', '.join(capabilities)}."

    if "ai" in message_lower and "artificial intelligence" in message_lower:
        return f"Artificial Intelligence (AI) is the simulation of human intelligence in machines that are programmed to think and learn. As {agent_name}, I'm actually an AI agent myself! I can help you with AI-related tasks based on my capabilities: {', '.join(capabilities)}."

    if "machine learning" in message_lower or "ml" in message_lower:
        return f"Machine Learning (ML) is a subset of AI that enables computers to learn and improve from experience without being explicitly programmed. As {agent_name}, I can help explain ML concepts or assist with ML-related tasks if that's in my capabilities: {', '.join(capabilities)}."

    if "nlp" in message_lower or "natural language processing" in message_lower:
        return f"Natural Language Processing (NLP) is a branch of AI that helps computers understand, interpret, and generate human language. As {agent_name}, I use NLP to understand and respond to your messages! I can help with NLP-related tasks based on my capabilities: {', '.join(capabilities)}."

    if "database" in message_lower or "sql" in message_lower:
        return f"A database is an organized collection of data stored electronically. SQL (Structured Query Language) is used to manage and query databases. As {agent_name}, I can help with database concepts or SQL queries if that's within my capabilities: {', '.join(capabilities)}."

    # Default knowledge response
    return f"That's an interesting question about '{user_message}'. As {agent_name}, I'd be happy to help explain concepts related to my capabilities: {', '.join(capabilities)}. Could you be more specific about what aspect you'd like to know about?"

def handle_acronym_question(user_message: str, agent_name: str, capabilities: list) -> str:
    """Handle acronym and abbreviation questions with specific definitions"""
    message_lower = user_message.lower()

    if "nsl" in message_lower:
        return f"NSL can refer to several things depending on the context:\n• **Network Security Lab** - A laboratory for testing network security\n• **National Security Letter** - A legal document used by US federal agencies\n• **Name Service Library** - A programming library for name resolution\n• **Network Simulation Library** - Tools for network simulation\n\nAs {agent_name}, I can help you with more specific information if you tell me which context you're interested in. My capabilities include: {', '.join(capabilities)}."

    if "api" in message_lower:
        return f"API stands for **Application Programming Interface**. It's a set of protocols, routines, and tools for building software applications. APIs specify how software components should interact. As {agent_name}, I can help you understand, design, or work with APIs based on my capabilities: {', '.join(capabilities)}."

    if "ai" in message_lower:
        return f"AI stands for **Artificial Intelligence** - the simulation of human intelligence in machines. As {agent_name}, I'm an AI agent myself! I can help you with AI-related questions and tasks based on my capabilities: {', '.join(capabilities)}."

    if "ml" in message_lower:
        return f"ML stands for **Machine Learning** - a subset of AI that enables computers to learn from data without explicit programming. As {agent_name}, I can help explain ML concepts or assist with ML tasks if that's in my capabilities: {', '.join(capabilities)}."

    if "nlp" in message_lower:
        return f"NLP stands for **Natural Language Processing** - AI technology that helps computers understand and generate human language. As {agent_name}, I use NLP to communicate with you! I can help with NLP-related tasks based on my capabilities: {', '.join(capabilities)}."

    if "sql" in message_lower:
        return f"SQL stands for **Structured Query Language** - a programming language designed for managing data in relational databases. As {agent_name}, I can help with SQL queries and database concepts if that's within my capabilities: {', '.join(capabilities)}."

    if "http" in message_lower:
        return f"HTTP stands for **HyperText Transfer Protocol** - the foundation of data communication on the World Wide Web. As {agent_name}, I can help explain web protocols and technologies based on my capabilities: {', '.join(capabilities)}."

    if "json" in message_lower:
        return f"JSON stands for **JavaScript Object Notation** - a lightweight data interchange format that's easy for humans to read and write. As {agent_name}, I can help you work with JSON data if that's within my capabilities: {', '.join(capabilities)}."

    # Default acronym response
    return f"I see you're asking about an acronym or abbreviation in '{user_message}'. As {agent_name}, I can help explain technical terms and concepts related to my capabilities: {', '.join(capabilities)}. Could you provide more context about what specific term you'd like to know about?"

@app.get("/api/v1/simple/agents")
async def list_simple_agents():
    """List simple agents endpoint"""
    return {
        "agents": [],
        "total": 0,
        "page": 1,
        "size": 10
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main_simple:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )