"""Frontend code generator using AI and templates."""
import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple

from .ai_service import AIService, AICapability, AIRequest
from .template_processor import TemplateProcessor

logger = logging.getLogger(__name__)


class FrontendGenerator:
    """Generate frontend applications using AI and templates."""
    
    def __init__(self, ai_service: AIService):
        self.ai_service = ai_service
        self.template_base_path = Path(__file__).parent.parent / "templates/frontend/base"
        self.template_processor = TemplateProcessor(self.template_base_path)
    
    async def generate_frontend(
        self,
        agent_id: str,
        agent_name: str,
        requirements: str,
        api_endpoints: List[Dict[str, Any]],
        output_path: Path
    ) -> Dict[str, Any]:
        """Generate complete frontend application."""
        try:
            logger.info(f"Generating frontend for agent {agent_id}: {agent_name}")
            
            # Step 1: Analyze requirements and generate component specifications
            component_specs = await self._analyze_requirements(requirements, api_endpoints)
            
            # Step 2: Generate custom components using AI
            custom_components = await self._generate_components(component_specs, api_endpoints)
            
            # Step 3: Process templates with generated components
            generated_files = self.template_processor.generate_frontend_structure(
                agent_name=agent_name,
                agent_id=agent_id,
                api_endpoints=api_endpoints,
                custom_components=custom_components,
                output_path=output_path
            )
            
            # Step 4: Generate package.json with specific dependencies
            await self._update_package_dependencies(output_path, component_specs)
            
            logger.info(f"Frontend generation completed for {agent_name}")
            
            return {
                "status": "success",
                "agent_id": agent_id,
                "output_path": str(output_path),
                "generated_files": list(generated_files.keys()),
                "components": list(custom_components.keys()),
                "component_specs": component_specs
            }
            
        except Exception as e:
            logger.error(f"Frontend generation failed for {agent_name}: {e}")
            return {
                "status": "error",
                "agent_id": agent_id,
                "error": str(e)
            }
    
    async def _analyze_requirements(
        self,
        requirements: str,
        api_endpoints: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Analyze requirements to determine component specifications."""
        
        # Create endpoint summary for AI context
        endpoints_summary = "\n".join([
            f"- {ep.get('method', 'GET')} {ep['path']}: {ep.get('description', 'No description')}"
            for ep in api_endpoints
        ])
        
        prompt = f"""
You are a frontend architect. Analyze the following requirements and API endpoints to determine what custom React/Preact components should be built.

REQUIREMENTS:
{requirements}

AVAILABLE API ENDPOINTS:
{endpoints_summary}

Based on this information, provide a JSON response with the following structure:
{{
  "components": [
    {{
      "name": "ComponentName",
      "type": "page|form|display|interactive",
      "description": "What this component does",
      "props": ["prop1", "prop2"],
      "uses_api": ["endpoint1", "endpoint2"],
      "features": ["feature1", "feature2"]
    }}
  ],
  "additional_dependencies": ["package1", "package2"],
  "ui_patterns": ["forms", "tables", "charts", "modals"],
  "complexity": "simple|medium|complex"
}}

Focus on creating components that provide meaningful user interfaces for the API functionality.
Keep components focused and reusable. Prefer composition over large monolithic components.

Respond with only the JSON, no other text.
"""
        
        try:
            response = await self.ai_service.generate(
                request=AIRequest(
                    prompt=prompt,
                    capability=AICapability.CODE_GENERATION,
                    temperature=0.3,
                    max_tokens=2000
                )
            )
            
            # Clean and parse JSON response
            content = response.content.strip()
            if content.startswith('```json'):
                content = content[7:]
            if content.endswith('```'):
                content = content[:-3]
            
            return json.loads(content)
            
        except Exception as e:
            logger.warning(f"Failed to analyze requirements with AI: {e}")
            # Fallback to basic component structure
            return {
                "components": [
                    {
                        "name": "MainPage",
                        "type": "page",
                        "description": "Main interaction page for the agent",
                        "props": [],
                        "uses_api": [ep['path'] for ep in api_endpoints[:2]],
                        "features": ["api_interaction", "result_display"]
                    }
                ],
                "additional_dependencies": [],
                "ui_patterns": ["forms"],
                "complexity": "simple"
            }
    
    async def _generate_components(
        self,
        component_specs: Dict[str, Any],
        api_endpoints: List[Dict[str, Any]]
    ) -> Dict[str, str]:
        """Generate component code using AI."""
        custom_components = {}
        
        # Generate each component
        for component_spec in component_specs.get("components", []):
            try:
                component_code = await self._generate_single_component(
                    component_spec, api_endpoints
                )
                custom_components[component_spec["name"]] = component_code
            except Exception as e:
                logger.error(f"Failed to generate component {component_spec['name']}: {e}")
                # Create a simple fallback component
                custom_components[component_spec["name"]] = self._create_fallback_component(
                    component_spec["name"], component_spec.get("description", "")
                )
        
        return custom_components
    
    async def _generate_single_component(
        self,
        component_spec: Dict[str, Any],
        api_endpoints: List[Dict[str, Any]]
    ) -> str:
        """Generate a single component using AI."""
        
        # Find relevant endpoints for this component
        relevant_endpoints = [
            ep for ep in api_endpoints 
            if ep['path'] in component_spec.get("uses_api", [])
        ]
        
        endpoint_details = ""
        if relevant_endpoints:
            endpoint_details = "\n".join([
                f"- {ep.get('method', 'GET')} {ep['path']}: {ep.get('description', '')}"
                for ep in relevant_endpoints
            ])
        
        prompt = f"""
Create a React/Preact component with TypeScript for the following specification:

COMPONENT NAME: {component_spec["name"]}
COMPONENT TYPE: {component_spec["type"]}
DESCRIPTION: {component_spec["description"]}
FEATURES: {component_spec.get("features", [])}

RELEVANT API ENDPOINTS:
{endpoint_details or "No specific endpoints"}

REQUIREMENTS:
1. Use Preact with TypeScript
2. Use Tailwind CSS for styling
3. Import from '@/services/api' for API calls
4. Use proper TypeScript types
5. Include error handling and loading states
6. Follow modern React patterns (hooks, functional components)
7. Make it responsive and user-friendly
8. Include proper form validation if it's a form component
9. For charts, use 'recharts' library (NOT react-chartjs-2 or other chart libraries)
10. For forms, use 'react-hook-form' with 'zod' validation

IMPORTANT GUIDELINES:
- Use 'class' instead of 'className' (Preact syntax)
- Import hooks from 'preact/hooks'
- Keep the component focused and reusable
- Include JSDoc comments for props
- Handle edge cases gracefully

Generate ONLY the component code, no explanations or markdown formatting.
Start directly with the imports.
"""
        
        response = await self.ai_service.generate(
            request=AIRequest(
                prompt=prompt,
                capability=AICapability.CODE_GENERATION,
                temperature=0.2,
                max_tokens=3000
            )
        )
        
        # Clean up the response
        code = response.content.strip()
        
        # Remove markdown formatting if present
        if code.startswith('```'):
            lines = code.split('\n')
            if lines[0].startswith('```'):
                lines = lines[1:]
            if lines and lines[-1].strip() == '```':
                lines = lines[:-1]
            code = '\n'.join(lines)
        
        return code
    
    def _create_fallback_component(self, name: str, description: str) -> str:
        """Create a simple fallback component when AI generation fails."""
        return f"""import {{ useState, useEffect }} from 'preact/hooks'

/**
 * {description}
 */
export function {name}() {{
  const [loading, setLoading] = useState(false)

  return (
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-xl font-semibold mb-4">{self._to_kebab_case(name).replace('-', ' ').title()}</h2>
      <p class="text-gray-600 mb-4">{description}</p>
      
      <div class="text-center py-8">
        <p class="text-gray-500">Component implementation in progress...</p>
      </div>
    </div>
  )
}}"""
    
    async def _update_package_dependencies(
        self,
        output_path: Path,
        component_specs: Dict[str, Any]
    ):
        """Update package.json with additional dependencies based on component needs."""
        package_json_path = output_path / "package.json"
        
        if not package_json_path.exists():
            return
        
        try:
            with open(package_json_path, 'r') as f:
                package_data = json.load(f)
            
            # Add additional dependencies from component specs
            additional_deps = component_specs.get("additional_dependencies", [])
            
            # Common dependencies based on UI patterns
            ui_patterns = component_specs.get("ui_patterns", [])
            if "charts" in ui_patterns:
                additional_deps.extend(["recharts", "@types/recharts", "chart.js"])
            if "modals" in ui_patterns:
                additional_deps.append("@headlessui/react")
            if "forms" in ui_patterns:
                additional_deps.extend(["react-hook-form", "@hookform/resolvers", "zod"])
            
            # Add to dependencies with proper version numbers
            deps = package_data.setdefault("dependencies", {})
            
            # Version mapping for common dependencies
            version_map = {
                "@headlessui/react": "^1.7.18",
                "react-hook-form": "^7.49.3",
                "@hookform/resolvers": "^3.3.4",
                "zod": "^3.22.4",
                "recharts": "^2.10.4",
                "@types/recharts": "^1.8.29",
                "chart.js": "^4.4.1",
                "react-chartjs-2": "^5.2.0",
                "date-fns": "^3.3.1",
                "lucide-react": "^0.323.0"
            }
            
            for dep in additional_deps:
                if dep not in deps:
                    # Use mapped version or fallback to a reasonable default
                    deps[dep] = version_map.get(dep, "^1.0.0")
            
            # Save updated package.json
            with open(package_json_path, 'w') as f:
                json.dump(package_data, f, indent=2)
            
        except Exception as e:
            logger.error(f"Failed to update package dependencies: {e}")

    def _to_kebab_case(self, text: str) -> str:
        """Convert text to kebab-case."""
        import re
        # Insert hyphens before uppercase letters
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1-\2', text)
        # Insert hyphens before uppercase letters followed by lowercase
        s2 = re.sub('([a-z0-9])([A-Z])', r'\1-\2', s1)
        # Convert to lowercase and replace spaces/underscores with hyphens
        return s2.lower().replace(' ', '-').replace('_', '-')


def extract_api_endpoints_from_code(agent_code: str) -> List[Dict[str, Any]]:
    """Extract API endpoint information from generated agent code."""
    endpoints = []
    
    # Basic patterns to extract FastAPI routes
    import re
    
    # Pattern for @app.method("/path") decorators with function bodies
    route_pattern = r'@app\.(get|post|put|delete|patch)\(["\']([^"\']+)["\'].*?\)\s*(?:async\s+)?def\s+(\w+)\([^)]*\).*?(?=@app\.|def\s+\w+|$)'
    
    matches = re.findall(route_pattern, agent_code, re.MULTILINE | re.DOTALL)
    
    for method, path, func_name in matches:
        endpoint = {
            "method": method.upper(),
            "path": path,
            "operation_id": func_name,
            "description": f"{func_name.replace('_', ' ').title()} endpoint"
        }
        
        # Extract function body to infer schemas
        func_pattern = rf'def\s+{func_name}\([^)]*\).*?(?=\ndef|\Z)'
        func_match = re.search(func_pattern, agent_code, re.DOTALL)
        
        if func_match:
            func_body = func_match.group(0)
            
            # Try to extract pydantic models or parameter types
            if method.upper() in ['POST', 'PUT', 'PATCH']:
                # Look for parameter patterns like "data: SomeModel"
                param_pattern = r'(\w+):\s*(\w+Model|\w+Request|\w+Details|\w+Input|\w+Data)'
                param_match = re.search(param_pattern, func_body)
                if param_match:
                    endpoint["request_schema"] = f"{{ {param_match.group(1)}: any }}"
            
            # Try to infer response schema from return statements
            return_pattern = r'return\s*\{[^}]+\}'
            return_matches = re.findall(return_pattern, func_body)
            if return_matches:
                # Use the first return statement as basis for response schema
                return_example = return_matches[0]
                # Extract field names
                field_pattern = r'"([^"]+)":\s*[^,}]+'
                fields = re.findall(field_pattern, return_example)
                if fields:
                    schema_fields = []
                    for field in fields:
                        if any(word in field.lower() for word in ['salary', 'income', 'tax', 'amount', 'liability']):
                            schema_fields.append(f"{field}: number")
                        else:
                            schema_fields.append(f"{field}: any")
                    endpoint["response_schema"] = f"{{ {', '.join(schema_fields)} }}"
        
        endpoints.append(endpoint)
    
    # Always include the root endpoint for A2A protocol
    if not any(ep["path"] == "/" for ep in endpoints):
        endpoints.insert(0, {
            "method": "GET",
            "path": "/",
            "operation_id": "get_agent_info",
            "description": "Get agent information and A2A protocol details"
        })
    
    return endpoints