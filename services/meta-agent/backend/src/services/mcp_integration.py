"""Model Context Protocol (MCP) Server Integration.

This module provides integration with MCP servers to extend platform capabilities
with external tools, resources, and data sources.
"""

from typing import Dict, Any, List, Optional, Union, AsyncIterator
from dataclasses import dataclass, field
from enum import Enum
import asyncio
import json
import uuid
from datetime import datetime
import aiohttp
import websockets
from utils.logging import get_logger, LoggerMixin
from utils.exceptions import AgentError, ExternalServiceError
from intelligence.gateway import AIGateway
from intelligence.providers.base import AIMessage

logger = get_logger(__name__)

class MCPServerStatus(str, Enum):
    """MCP Server status states."""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"
    UNAVAILABLE = "unavailable"

class MCPResourceType(str, Enum):
    """Types of MCP resources."""
    TOOL = "tool"
    PROMPT = "prompt"
    RESOURCE = "resource"
    DATASOURCE = "datasource"

@dataclass
class MCPServer:
    """MCP Server configuration and state."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    url: str = ""
    protocol: str = "websocket"  # websocket, http, stdio
    capabilities: List[str] = field(default_factory=list)
    status: MCPServerStatus = MCPServerStatus.DISCONNECTED
    last_ping: Optional[datetime] = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    connection: Optional[Any] = None
    tools: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    prompts: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    resources: Dict[str, Dict[str, Any]] = field(default_factory=dict)

@dataclass
class MCPTool:
    """MCP Tool definition."""
    name: str
    description: str
    server_id: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    schema: Dict[str, Any] = field(default_factory=dict)
    examples: List[Dict[str, Any]] = field(default_factory=list)

@dataclass
class MCPResource:
    """MCP Resource definition."""
    uri: str
    name: str
    description: str
    server_id: str
    mime_type: Optional[str] = None
    size: Optional[int] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class MCPPrompt:
    """MCP Prompt template."""
    name: str
    description: str
    server_id: str
    template: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    examples: List[Dict[str, Any]] = field(default_factory=list)

class MCPIntegrationManager(LoggerMixin):
    """Manager for MCP server integrations."""
    
    def __init__(self, ai_gateway: AIGateway):
        self.ai_gateway = ai_gateway
        self.servers: Dict[str, MCPServer] = {}
        self.connection_tasks: Dict[str, asyncio.Task] = {}
        self.tools_registry: Dict[str, MCPTool] = {}
        self.resources_registry: Dict[str, MCPResource] = {}
        self.prompts_registry: Dict[str, MCPPrompt] = {}
        
        # Default MCP servers configuration
        self.default_servers = self._get_default_servers()
    
    def _get_default_servers(self) -> List[Dict[str, Any]]:
        """Get default MCP servers to integrate."""
        
        return [
            {
                "name": "filesystem",
                "description": "File system operations and management",
                "url": "ws://localhost:8001/mcp",
                "capabilities": ["read_files", "write_files", "list_directories", "file_search"]
            },
            {
                "name": "database",
                "description": "Database operations and queries", 
                "url": "ws://localhost:8002/mcp",
                "capabilities": ["query", "execute", "schema_info", "table_list"]
            },
            {
                "name": "web_scraper",
                "description": "Web scraping and content extraction",
                "url": "ws://localhost:8003/mcp",
                "capabilities": ["scrape_url", "extract_content", "screenshot", "pdf_extract"]
            },
            {
                "name": "code_analysis",
                "description": "Code analysis and repository operations",
                "url": "ws://localhost:8004/mcp",
                "capabilities": ["analyze_code", "git_operations", "dependency_scan", "security_scan"]
            },
            {
                "name": "api_client",
                "description": "Generic API client for external services",
                "url": "ws://localhost:8005/mcp",
                "capabilities": ["http_request", "oauth_flow", "api_discovery", "rate_limiting"]
            },
            {
                "name": "data_processor",
                "description": "Data processing and transformation",
                "url": "ws://localhost:8006/mcp",
                "capabilities": ["csv_processing", "json_transform", "data_validation", "format_conversion"]
            },
            {
                "name": "notification",
                "description": "Notification and communication services",
                "url": "ws://localhost:8007/mcp",
                "capabilities": ["send_email", "slack_message", "webhook", "sms"]
            }
        ]
    
    async def initialize_default_servers(self) -> None:
        """Initialize connections to default MCP servers."""
        
        for server_config in self.default_servers:
            await self.add_server(
                name=server_config["name"],
                description=server_config["description"],
                url=server_config["url"],
                capabilities=server_config["capabilities"]
            )
    
    async def add_server(
        self,
        name: str,
        description: str,
        url: str,
        capabilities: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Add and connect to an MCP server."""
        
        server = MCPServer(
            name=name,
            description=description,
            url=url,
            capabilities=capabilities or [],
            metadata=metadata or {}
        )
        
        self.servers[server.id] = server
        
        # Start connection
        await self.connect_server(server.id)
        
        self.log_operation("mcp_server_added", server_id=server.id, name=name, url=url)
        
        return server.id
    
    async def connect_server(self, server_id: str) -> bool:
        """Connect to an MCP server."""
        
        server = self.servers.get(server_id)
        if not server:
            return False
        
        if server.status == MCPServerStatus.CONNECTED:
            return True
        
        # Cancel existing connection task
        if server_id in self.connection_tasks:
            self.connection_tasks[server_id].cancel()
        
        # Start new connection task
        task = asyncio.create_task(self._connect_server_impl(server))
        self.connection_tasks[server_id] = task
        
        try:
            return await task
        except Exception as e:
            server.status = MCPServerStatus.ERROR
            server.error = str(e)
            self.log_error("mcp_server_connection_failed", e, server_id=server_id)
            return False
    
    async def _connect_server_impl(self, server: MCPServer) -> bool:
        """Implementation of server connection."""
        
        server.status = MCPServerStatus.CONNECTING
        
        try:
            if server.protocol == "websocket":
                return await self._connect_websocket(server)
            elif server.protocol == "http":
                return await self._connect_http(server)
            else:
                raise AgentError(f"Unsupported protocol: {server.protocol}")
                
        except Exception as e:
            server.status = MCPServerStatus.ERROR
            server.error = str(e)
            raise
    
    async def _connect_websocket(self, server: MCPServer) -> bool:
        """Connect to MCP server via WebSocket."""
        
        try:
            websocket = await websockets.connect(server.url)
            server.connection = websocket
            
            # Send initialization message
            init_message = {
                "jsonrpc": "2.0",
                "method": "initialize",
                "id": str(uuid.uuid4()),
                "params": {
                    "protocolVersion": "1.0",
                    "clientInfo": {
                        "name": "AI Agent Platform",
                        "version": "1.0.0"
                    },
                    "capabilities": ["tools", "prompts", "resources"]
                }
            }
            
            await websocket.send(json.dumps(init_message))
            
            # Wait for initialization response
            response = await asyncio.wait_for(websocket.recv(), timeout=10)
            response_data = json.loads(response)
            
            if "error" in response_data:
                raise ExternalServiceError(f"MCP initialization failed: {response_data['error']}")
            
            # Start message handling task
            asyncio.create_task(self._handle_websocket_messages(server, websocket))
            
            # Discover capabilities
            await self._discover_server_capabilities(server)
            
            server.status = MCPServerStatus.CONNECTED
            server.last_ping = datetime.now()
            
            self.log_operation("mcp_websocket_connected", server_id=server.id, url=server.url)
            
            return True
            
        except Exception as e:
            server.status = MCPServerStatus.ERROR
            server.error = str(e)
            raise ExternalServiceError(f"WebSocket connection failed: {e}")
    
    async def _connect_http(self, server: MCPServer) -> bool:
        """Connect to MCP server via HTTP."""
        
        try:
            async with aiohttp.ClientSession() as session:
                # Test connection with capabilities endpoint
                async with session.get(f"{server.url}/capabilities") as response:
                    if response.status == 200:
                        capabilities = await response.json()
                        server.capabilities = capabilities.get("capabilities", [])
                        
                        # Discover tools and resources
                        await self._discover_http_capabilities(server, session)
                        
                        server.status = MCPServerStatus.CONNECTED
                        server.last_ping = datetime.now()
                        
                        self.log_operation("mcp_http_connected", server_id=server.id, url=server.url)
                        
                        return True
                    else:
                        raise ExternalServiceError(f"HTTP connection failed: {response.status}")
                        
        except Exception as e:
            server.status = MCPServerStatus.ERROR
            server.error = str(e)
            raise ExternalServiceError(f"HTTP connection failed: {e}")
    
    async def _handle_websocket_messages(self, server: MCPServer, websocket) -> None:
        """Handle incoming WebSocket messages from MCP server."""
        
        try:
            while True:
                message = await websocket.recv()
                data = json.loads(message)
                
                # Handle different message types
                if "method" in data:
                    await self._handle_server_notification(server, data)
                elif "result" in data or "error" in data:
                    await self._handle_server_response(server, data)
                
                # Update last ping
                server.last_ping = datetime.now()
                
        except websockets.exceptions.ConnectionClosed:
            server.status = MCPServerStatus.DISCONNECTED
            server.connection = None
            self.log_operation("mcp_websocket_disconnected", server_id=server.id)
            
            # Attempt reconnection
            await asyncio.sleep(5)
            await self.connect_server(server.id)
            
        except Exception as e:
            self.log_error("mcp_message_handling_error", e, server_id=server.id)
    
    async def _handle_server_notification(self, server: MCPServer, data: Dict[str, Any]) -> None:
        """Handle notification from MCP server."""
        
        method = data.get("method")
        params = data.get("params", {})
        
        if method == "tools/list_changed":
            await self._refresh_server_tools(server)
        elif method == "prompts/list_changed":
            await self._refresh_server_prompts(server)
        elif method == "resources/list_changed":
            await self._refresh_server_resources(server)
        elif method == "server/ping":
            # Respond to ping
            pong_message = {
                "jsonrpc": "2.0",
                "method": "server/pong",
                "id": data.get("id")
            }
            await server.connection.send(json.dumps(pong_message))
    
    async def _handle_server_response(self, server: MCPServer, data: Dict[str, Any]) -> None:
        """Handle response from MCP server."""
        
        # This would typically correlate with pending requests
        # For now, just log the response
        self.logger.debug(f"MCP server response: {data}")
    
    async def _discover_server_capabilities(self, server: MCPServer) -> None:
        """Discover tools, prompts, and resources from MCP server."""
        
        if server.connection and server.protocol == "websocket":
            # Discover tools
            await self._discover_websocket_tools(server)
            
            # Discover prompts
            await self._discover_websocket_prompts(server)
            
            # Discover resources
            await self._discover_websocket_resources(server)
    
    async def _discover_websocket_tools(self, server: MCPServer) -> None:
        """Discover tools via WebSocket."""
        
        try:
            list_tools_message = {
                "jsonrpc": "2.0",
                "method": "tools/list",
                "id": str(uuid.uuid4()),
                "params": {}
            }
            
            await server.connection.send(json.dumps(list_tools_message))
            
            # Wait for response (simplified - would need proper request tracking)
            response = await asyncio.wait_for(server.connection.recv(), timeout=10)
            response_data = json.loads(response)
            
            if "result" in response_data and "tools" in response_data["result"]:
                tools = response_data["result"]["tools"]
                
                for tool_data in tools:
                    tool = MCPTool(
                        name=tool_data["name"],
                        description=tool_data.get("description", ""),
                        server_id=server.id,
                        parameters=tool_data.get("inputSchema", {}),
                        schema=tool_data.get("inputSchema", {}),
                        examples=tool_data.get("examples", [])
                    )
                    
                    tool_key = f"{server.id}:{tool.name}"
                    self.tools_registry[tool_key] = tool
                    server.tools[tool.name] = tool_data
                
                self.log_operation("mcp_tools_discovered", server_id=server.id, count=len(tools))
                
        except Exception as e:
            self.log_error("mcp_tool_discovery_failed", e, server_id=server.id)
    
    async def _discover_websocket_prompts(self, server: MCPServer) -> None:
        """Discover prompts via WebSocket."""
        
        try:
            list_prompts_message = {
                "jsonrpc": "2.0", 
                "method": "prompts/list",
                "id": str(uuid.uuid4()),
                "params": {}
            }
            
            await server.connection.send(json.dumps(list_prompts_message))
            
            response = await asyncio.wait_for(server.connection.recv(), timeout=10)
            response_data = json.loads(response)
            
            if "result" in response_data and "prompts" in response_data["result"]:
                prompts = response_data["result"]["prompts"]
                
                for prompt_data in prompts:
                    prompt = MCPPrompt(
                        name=prompt_data["name"],
                        description=prompt_data.get("description", ""),
                        server_id=server.id,
                        template=prompt_data.get("template", ""),
                        parameters=prompt_data.get("arguments", {}),
                        examples=prompt_data.get("examples", [])
                    )
                    
                    prompt_key = f"{server.id}:{prompt.name}"
                    self.prompts_registry[prompt_key] = prompt
                    server.prompts[prompt.name] = prompt_data
                
                self.log_operation("mcp_prompts_discovered", server_id=server.id, count=len(prompts))
                
        except Exception as e:
            self.log_error("mcp_prompt_discovery_failed", e, server_id=server.id)
    
    async def _discover_websocket_resources(self, server: MCPServer) -> None:
        """Discover resources via WebSocket."""
        
        try:
            list_resources_message = {
                "jsonrpc": "2.0",
                "method": "resources/list", 
                "id": str(uuid.uuid4()),
                "params": {}
            }
            
            await server.connection.send(json.dumps(list_resources_message))
            
            response = await asyncio.wait_for(server.connection.recv(), timeout=10)
            response_data = json.loads(response)
            
            if "result" in response_data and "resources" in response_data["result"]:
                resources = response_data["result"]["resources"]
                
                for resource_data in resources:
                    resource = MCPResource(
                        uri=resource_data["uri"],
                        name=resource_data.get("name", resource_data["uri"]),
                        description=resource_data.get("description", ""),
                        server_id=server.id,
                        mime_type=resource_data.get("mimeType"),
                        metadata=resource_data.get("metadata", {})
                    )
                    
                    resource_key = f"{server.id}:{resource.uri}"
                    self.resources_registry[resource_key] = resource
                    server.resources[resource.uri] = resource_data
                
                self.log_operation("mcp_resources_discovered", server_id=server.id, count=len(resources))
                
        except Exception as e:
            self.log_error("mcp_resource_discovery_failed", e, server_id=server.id)
    
    async def _discover_http_capabilities(self, server: MCPServer, session: aiohttp.ClientSession) -> None:
        """Discover capabilities via HTTP."""
        
        try:
            # Discover tools
            async with session.get(f"{server.url}/tools") as response:
                if response.status == 200:
                    tools_data = await response.json()
                    tools = tools_data.get("tools", [])
                    
                    for tool_data in tools:
                        tool = MCPTool(
                            name=tool_data["name"],
                            description=tool_data.get("description", ""),
                            server_id=server.id,
                            parameters=tool_data.get("parameters", {}),
                            schema=tool_data.get("schema", {})
                        )
                        
                        tool_key = f"{server.id}:{tool.name}"
                        self.tools_registry[tool_key] = tool
                        server.tools[tool.name] = tool_data
            
            # Similar for prompts and resources
            # ... (implementation would be similar to WebSocket version)
            
        except Exception as e:
            self.log_error("mcp_http_discovery_failed", e, server_id=server.id)
    
    async def execute_tool(
        self,
        tool_name: str,
        parameters: Dict[str, Any],
        server_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Execute an MCP tool."""
        
        # Find the tool
        tool = None
        if server_id:
            tool_key = f"{server_id}:{tool_name}"
            tool = self.tools_registry.get(tool_key)
        else:
            # Find first matching tool
            for key, t in self.tools_registry.items():
                if t.name == tool_name:
                    tool = t
                    break
        
        if not tool:
            raise AgentError(f"Tool {tool_name} not found")
        
        server = self.servers.get(tool.server_id)
        if not server or server.status != MCPServerStatus.CONNECTED:
            raise ExternalServiceError(f"MCP server {tool.server_id} not available")
        
        try:
            if server.protocol == "websocket":
                return await self._execute_websocket_tool(server, tool, parameters)
            else:
                return await self._execute_http_tool(server, tool, parameters)
                
        except Exception as e:
            self.log_error("mcp_tool_execution_failed", e, tool_name=tool_name, server_id=server.id)
            raise ExternalServiceError(f"Tool execution failed: {e}")
    
    async def _execute_websocket_tool(
        self,
        server: MCPServer,
        tool: MCPTool,
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute tool via WebSocket."""
        
        request_id = str(uuid.uuid4())
        
        tool_message = {
            "jsonrpc": "2.0",
            "method": "tools/call",
            "id": request_id,
            "params": {
                "name": tool.name,
                "arguments": parameters
            }
        }
        
        await server.connection.send(json.dumps(tool_message))
        
        # Wait for response (simplified - would need proper request tracking)
        response = await asyncio.wait_for(server.connection.recv(), timeout=30)
        response_data = json.loads(response)
        
        if "error" in response_data:
            raise ExternalServiceError(f"Tool execution error: {response_data['error']}")
        
        return response_data.get("result", {})
    
    async def _execute_http_tool(
        self,
        server: MCPServer,
        tool: MCPTool,
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute tool via HTTP."""
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{server.url}/tools/{tool.name}",
                json=parameters
            ) as response:
                
                if response.status != 200:
                    raise ExternalServiceError(f"HTTP tool execution failed: {response.status}")
                
                return await response.json()
    
    async def get_resource(self, uri: str, server_id: Optional[str] = None) -> bytes:
        """Get resource content from MCP server."""
        
        # Find the resource
        resource = None
        if server_id:
            resource_key = f"{server_id}:{uri}"
            resource = self.resources_registry.get(resource_key)
        else:
            # Find first matching resource
            for key, r in self.resources_registry.items():
                if r.uri == uri:
                    resource = r
                    break
        
        if not resource:
            raise AgentError(f"Resource {uri} not found")
        
        server = self.servers.get(resource.server_id)
        if not server or server.status != MCPServerStatus.CONNECTED:
            raise ExternalServiceError(f"MCP server {resource.server_id} not available")
        
        if server.protocol == "websocket":
            return await self._get_websocket_resource(server, resource)
        else:
            return await self._get_http_resource(server, resource)
    
    async def _get_websocket_resource(self, server: MCPServer, resource: MCPResource) -> bytes:
        """Get resource via WebSocket."""
        
        request_id = str(uuid.uuid4())
        
        resource_message = {
            "jsonrpc": "2.0",
            "method": "resources/read",
            "id": request_id,
            "params": {
                "uri": resource.uri
            }
        }
        
        await server.connection.send(json.dumps(resource_message))
        
        response = await asyncio.wait_for(server.connection.recv(), timeout=30)
        response_data = json.loads(response)
        
        if "error" in response_data:
            raise ExternalServiceError(f"Resource read error: {response_data['error']}")
        
        content = response_data.get("result", {}).get("contents", [])
        
        if content and len(content) > 0:
            # Handle different content types
            content_item = content[0]
            if "blob" in content_item:
                import base64
                return base64.b64decode(content_item["blob"])
            elif "text" in content_item:
                return content_item["text"].encode()
        
        return b""
    
    async def _get_http_resource(self, server: MCPServer, resource: MCPResource) -> bytes:
        """Get resource via HTTP."""
        
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{server.url}/resources", params={"uri": resource.uri}) as response:
                if response.status != 200:
                    raise ExternalServiceError(f"HTTP resource read failed: {response.status}")
                
                return await response.read()
    
    async def render_prompt(
        self,
        prompt_name: str,
        variables: Dict[str, Any],
        server_id: Optional[str] = None
    ) -> str:
        """Render an MCP prompt with variables."""
        
        # Find the prompt
        prompt = None
        if server_id:
            prompt_key = f"{server_id}:{prompt_name}"
            prompt = self.prompts_registry.get(prompt_key)
        else:
            # Find first matching prompt
            for key, p in self.prompts_registry.items():
                if p.name == prompt_name:
                    prompt = p
                    break
        
        if not prompt:
            raise AgentError(f"Prompt {prompt_name} not found")
        
        server = self.servers.get(prompt.server_id)
        if not server or server.status != MCPServerStatus.CONNECTED:
            raise ExternalServiceError(f"MCP server {prompt.server_id} not available")
        
        if server.protocol == "websocket":
            return await self._render_websocket_prompt(server, prompt, variables)
        else:
            return await self._render_http_prompt(server, prompt, variables)
    
    async def _render_websocket_prompt(
        self,
        server: MCPServer,
        prompt: MCPPrompt,
        variables: Dict[str, Any]
    ) -> str:
        """Render prompt via WebSocket."""
        
        request_id = str(uuid.uuid4())
        
        prompt_message = {
            "jsonrpc": "2.0",
            "method": "prompts/get",
            "id": request_id,
            "params": {
                "name": prompt.name,
                "arguments": variables
            }
        }
        
        await server.connection.send(json.dumps(prompt_message))
        
        response = await asyncio.wait_for(server.connection.recv(), timeout=10)
        response_data = json.loads(response)
        
        if "error" in response_data:
            raise ExternalServiceError(f"Prompt render error: {response_data['error']}")
        
        messages = response_data.get("result", {}).get("messages", [])
        
        if messages:
            # Combine all message content
            rendered_content = ""
            for message in messages:
                if "content" in message and "text" in message["content"]:
                    rendered_content += message["content"]["text"] + "\n"
            
            return rendered_content.strip()
        
        return ""
    
    async def _render_http_prompt(
        self,
        server: MCPServer,
        prompt: MCPPrompt,
        variables: Dict[str, Any]
    ) -> str:
        """Render prompt via HTTP."""
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{server.url}/prompts/{prompt.name}",
                json=variables
            ) as response:
                
                if response.status != 200:
                    raise ExternalServiceError(f"HTTP prompt render failed: {response.status}")
                
                result = await response.json()
                return result.get("rendered", "")
    
    def get_available_tools(self, server_id: Optional[str] = None) -> List[MCPTool]:
        """Get list of available MCP tools."""
        
        if server_id:
            return [tool for tool in self.tools_registry.values() if tool.server_id == server_id]
        
        return list(self.tools_registry.values())
    
    def get_available_resources(self, server_id: Optional[str] = None) -> List[MCPResource]:
        """Get list of available MCP resources."""
        
        if server_id:
            return [resource for resource in self.resources_registry.values() if resource.server_id == server_id]
        
        return list(self.resources_registry.values())
    
    def get_available_prompts(self, server_id: Optional[str] = None) -> List[MCPPrompt]:
        """Get list of available MCP prompts."""
        
        if server_id:
            return [prompt for prompt in self.prompts_registry.values() if prompt.server_id == server_id]
        
        return list(self.prompts_registry.values())
    
    def get_server_status(self, server_id: Optional[str] = None) -> Union[Dict[str, MCPServer], MCPServer]:
        """Get status of MCP servers."""
        
        if server_id:
            return self.servers.get(server_id)
        
        return self.servers.copy()
    
    async def disconnect_server(self, server_id: str) -> bool:
        """Disconnect from an MCP server."""
        
        server = self.servers.get(server_id)
        if not server:
            return False
        
        try:
            if server.connection:
                if server.protocol == "websocket":
                    await server.connection.close()
                server.connection = None
            
            server.status = MCPServerStatus.DISCONNECTED
            
            # Cancel connection task
            if server_id in self.connection_tasks:
                self.connection_tasks[server_id].cancel()
                del self.connection_tasks[server_id]
            
            # Remove tools/resources/prompts from registries
            tools_to_remove = [key for key, tool in self.tools_registry.items() if tool.server_id == server_id]
            for key in tools_to_remove:
                del self.tools_registry[key]
            
            resources_to_remove = [key for key, resource in self.resources_registry.items() if resource.server_id == server_id]
            for key in resources_to_remove:
                del self.resources_registry[key]
            
            prompts_to_remove = [key for key, prompt in self.prompts_registry.items() if prompt.server_id == server_id]
            for key in prompts_to_remove:
                del self.prompts_registry[key]
            
            self.log_operation("mcp_server_disconnected", server_id=server_id)
            
            return True
            
        except Exception as e:
            self.log_error("mcp_server_disconnect_failed", e, server_id=server_id)
            return False
    
    async def remove_server(self, server_id: str) -> bool:
        """Remove an MCP server completely."""
        
        success = await self.disconnect_server(server_id)
        
        if success and server_id in self.servers:
            del self.servers[server_id]
            self.log_operation("mcp_server_removed", server_id=server_id)
        
        return success
    
    def get_integration_summary(self) -> Dict[str, Any]:
        """Get MCP integration summary."""
        
        connected_servers = sum(1 for s in self.servers.values() if s.status == MCPServerStatus.CONNECTED)
        
        return {
            "total_servers": len(self.servers),
            "connected_servers": connected_servers,
            "total_tools": len(self.tools_registry),
            "total_resources": len(self.resources_registry),
            "total_prompts": len(self.prompts_registry),
            "server_status": {
                server.name: server.status.value
                for server in self.servers.values()
            }
        }