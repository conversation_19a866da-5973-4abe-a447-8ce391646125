"""Auto-deployment system for generated agents with dynamic port assignment.

This module provides automated deployment capabilities for AI agents,
including dynamic port allocation, health monitoring, and rollback procedures.
"""

from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import asyncio
import json
import uuid
import yaml
import tempfile
import os
import socket
from datetime import datetime, timedelta
from pathlib import Path
from utils.logging import get_logger, LoggerMixin
from utils.exceptions import AgentError, DeploymentError
import subprocess
import aiofiles
import shutil
import random

logger = get_logger(__name__)

class DeploymentStatus(str, Enum):
    """Deployment status states."""
    PENDING = "pending"
    PREPARING = "preparing"
    BUILDING = "building"
    TESTING = "testing"
    DEPLOYING = "deploying"
    RUNNING = "running"
    FAILED = "failed"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ROLLBACK = "rollback"

class DeploymentStrategy(str, Enum):
    """Deployment strategies."""
    BLUE_GREEN = "blue_green"
    ROLLING = "rolling"
    CANARY = "canary"
    RECREATE = "recreate"

@dataclass
class PortAssignment:
    """Dynamic port assignment."""
    port: int
    protocol: str = "http"
    is_external: bool = True
    service_name: str = ""
    assigned_at: datetime = field(default_factory=datetime.now)

@dataclass
class DeploymentConfig:
    """Deployment configuration."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    agent_id: str = ""
    environment: str = "development"
    strategy: DeploymentStrategy = DeploymentStrategy.ROLLING
    resources: Dict[str, Any] = field(default_factory=lambda: {
        "cpu": "100m",
        "memory": "128Mi",
        "cpu_limit": "500m",
        "memory_limit": "512Mi"
    })
    scaling: Dict[str, Any] = field(default_factory=lambda: {
        "min_replicas": 1,
        "max_replicas": 3,
        "target_cpu": 70
    })
    ports: List[PortAssignment] = field(default_factory=list)
    env_vars: Dict[str, str] = field(default_factory=dict)
    volumes: List[Dict[str, Any]] = field(default_factory=list)
    health_check: Dict[str, Any] = field(default_factory=lambda: {
        "endpoint": "/health",
        "initial_delay": 30,
        "period": 10,
        "timeout": 5,
        "failure_threshold": 3
    })

@dataclass
class DeploymentStatus:
    """Deployment status tracking."""
    deployment_id: str
    agent_id: str
    status: DeploymentStatus
    environment: str
    ports: List[PortAssignment]
    replicas: Dict[str, int] = field(default_factory=lambda: {"desired": 0, "ready": 0, "available": 0})
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error: Optional[str] = None
    logs: List[str] = field(default_factory=list)
    metrics: Dict[str, Any] = field(default_factory=dict)

class PortManager:
    """Dynamic port assignment and management."""
    
    def __init__(self, port_range_start: int = 30000, port_range_end: int = 32000):
        self.port_range_start = port_range_start
        self.port_range_end = port_range_end
        self.allocated_ports: Dict[int, PortAssignment] = {}
        self.reserved_ports = set([22, 80, 443, 8080, 9090])  # Common reserved ports
    
    def is_port_available(self, port: int) -> bool:
        """Check if a port is available for binding."""
        if port in self.reserved_ports or port in self.allocated_ports:
            return False
        
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                result = sock.bind(('localhost', port))
                return True
        except OSError:
            return False
    
    def allocate_port(
        self,
        service_name: str,
        protocol: str = "http",
        is_external: bool = True,
        preferred_port: Optional[int] = None
    ) -> Optional[PortAssignment]:
        """Allocate a dynamic port for a service."""
        
        # Try preferred port first
        if preferred_port and self.is_port_available(preferred_port):
            assignment = PortAssignment(
                port=preferred_port,
                protocol=protocol,
                is_external=is_external,
                service_name=service_name
            )
            self.allocated_ports[preferred_port] = assignment
            return assignment
        
        # Find available port in range
        for _ in range(100):  # Try up to 100 times
            port = random.randint(self.port_range_start, self.port_range_end)
            if self.is_port_available(port):
                assignment = PortAssignment(
                    port=port,
                    protocol=protocol,
                    is_external=is_external,
                    service_name=service_name
                )
                self.allocated_ports[port] = assignment
                return assignment
        
        return None
    
    def release_port(self, port: int) -> bool:
        """Release an allocated port."""
        if port in self.allocated_ports:
            del self.allocated_ports[port]
            return True
        return False
    
    def get_allocated_ports(self, service_name: Optional[str] = None) -> List[PortAssignment]:
        """Get allocated ports, optionally filtered by service name."""
        if service_name:
            return [
                assignment for assignment in self.allocated_ports.values()
                if assignment.service_name == service_name
            ]
        return list(self.allocated_ports.values())

class AutoDeploymentSystem(LoggerMixin):
    """Automated deployment system for generated agents."""
    
    def __init__(self, k8s_namespace: str = "ai-agents", docker_registry: str = "localhost:5000"):
        self.k8s_namespace = k8s_namespace
        self.docker_registry = docker_registry
        self.port_manager = PortManager()
        self.active_deployments: Dict[str, DeploymentStatus] = {}
        self.deployment_history: List[DeploymentStatus] = []
        self.temp_dirs: List[str] = []
        
    async def deploy_agent(
        self,
        agent_definition: Dict[str, Any],
        config: Optional[DeploymentConfig] = None
    ) -> DeploymentStatus:
        """Deploy an agent with automatic configuration."""
        
        if not config:
            config = DeploymentConfig(agent_id=agent_definition.get("id", str(uuid.uuid4())))
        
        deployment_status = DeploymentStatus(
            deployment_id=config.id,
            agent_id=config.agent_id,
            status=DeploymentStatus.PENDING,
            environment=config.environment,
            ports=[],
            start_time=datetime.now()
        )
        
        self.active_deployments[config.id] = deployment_status
        
        self.log_operation(
            "deployment_started",
            deployment_id=config.id,
            agent_id=config.agent_id,
            environment=config.environment
        )
        
        try:
            # Stage 1: Prepare deployment
            deployment_status.status = DeploymentStatus.PREPARING
            await self._prepare_deployment(agent_definition, config, deployment_status)
            
            # Stage 2: Build container image
            deployment_status.status = DeploymentStatus.BUILDING
            image_tag = await self._build_agent_image(agent_definition, config, deployment_status)
            
            # Stage 3: Run tests
            deployment_status.status = DeploymentStatus.TESTING
            await self._run_deployment_tests(agent_definition, config, deployment_status, image_tag)
            
            # Stage 4: Deploy to Kubernetes
            deployment_status.status = DeploymentStatus.DEPLOYING
            await self._deploy_to_kubernetes(agent_definition, config, deployment_status, image_tag)
            
            # Stage 5: Verify deployment
            await self._verify_deployment(config, deployment_status)
            
            deployment_status.status = DeploymentStatus.RUNNING
            deployment_status.end_time = datetime.now()
            
            self.log_operation(
                "deployment_completed",
                deployment_id=config.id,
                duration=(deployment_status.end_time - deployment_status.start_time).total_seconds()
            )
            
            return deployment_status
            
        except Exception as e:
            self.log_error("deployment_failed", e, deployment_id=config.id)
            deployment_status.status = DeploymentStatus.FAILED
            deployment_status.error = str(e)
            deployment_status.end_time = datetime.now()
            
            # Attempt rollback
            await self._rollback_deployment(config, deployment_status)
            
            return deployment_status
        finally:
            # Cleanup temporary directories
            await self._cleanup_temp_dirs()
    
    async def _prepare_deployment(
        self,
        agent_definition: Dict[str, Any],
        config: DeploymentConfig,
        status: DeploymentStatus
    ) -> None:
        """Prepare deployment artifacts."""
        
        # Create temporary directory
        temp_dir = tempfile.mkdtemp(prefix=f"agent-{config.agent_id}-")
        self.temp_dirs.append(temp_dir)
        
        # Allocate ports
        main_port = self.port_manager.allocate_port(
            service_name=f"{config.agent_id}-main",
            protocol="http",
            is_external=True
        )
        
        if not main_port:
            raise AgentError("Could not allocate main service port")
        
        config.ports.append(main_port)
        status.ports.append(main_port)
        
        # Allocate additional ports if needed
        metrics_port = self.port_manager.allocate_port(
            service_name=f"{config.agent_id}-metrics",
            protocol="http",
            is_external=False
        )
        
        if metrics_port:
            config.ports.append(metrics_port)
            status.ports.append(metrics_port)
        
        # Generate deployment artifacts
        await self._generate_deployment_artifacts(agent_definition, config, temp_dir)
        
        status.logs.append(f"Deployment prepared in {temp_dir}")
        status.logs.append(f"Allocated ports: {[p.port for p in config.ports]}")
    
    async def _generate_deployment_artifacts(
        self,
        agent_definition: Dict[str, Any],
        config: DeploymentConfig,
        temp_dir: str
    ) -> None:
        """Generate Docker and Kubernetes deployment artifacts."""
        
        # Create directory structure
        os.makedirs(f"{temp_dir}/src", exist_ok=True)
        os.makedirs(f"{temp_dir}/k8s", exist_ok=True)
        os.makedirs(f"{temp_dir}/tests", exist_ok=True)
        
        # Generate agent source code
        implementation = agent_definition.get("implementation", {})
        if implementation:
            await self._write_source_code(implementation, f"{temp_dir}/src")
        
        # Generate Dockerfile
        dockerfile_content = self._generate_dockerfile(agent_definition, config)
        async with aiofiles.open(f"{temp_dir}/Dockerfile", "w") as f:
            await f.write(dockerfile_content)
        
        # Generate requirements.txt
        dependencies = agent_definition.get("dependencies", [])
        if dependencies:
            async with aiofiles.open(f"{temp_dir}/requirements.txt", "w") as f:
                await f.write("\n".join(dependencies))
        
        # Generate Kubernetes manifests
        k8s_manifests = self._generate_k8s_manifests(agent_definition, config)
        for filename, content in k8s_manifests.items():
            async with aiofiles.open(f"{temp_dir}/k8s/{filename}", "w") as f:
                await f.write(yaml.dump(content, default_flow_style=False))
        
        # Generate test files
        tests = agent_definition.get("tests", {})
        if tests:
            await self._write_test_files(tests, f"{temp_dir}/tests")
    
    async def _write_source_code(self, implementation: Dict[str, Any], src_dir: str) -> None:
        """Write agent source code files."""
        
        if isinstance(implementation, dict):
            for filename, content in implementation.items():
                if isinstance(content, str) and content.strip():
                    file_path = f"{src_dir}/{filename}"
                    os.makedirs(os.path.dirname(file_path), exist_ok=True)
                    async with aiofiles.open(file_path, "w") as f:
                        await f.write(content)
    
    async def _write_test_files(self, tests: Dict[str, Any], test_dir: str) -> None:
        """Write test files."""
        
        if isinstance(tests, dict):
            for filename, content in tests.items():
                if isinstance(content, str) and content.strip():
                    file_path = f"{test_dir}/{filename}"
                    os.makedirs(os.path.dirname(file_path), exist_ok=True)
                    async with aiofiles.open(file_path, "w") as f:
                        await f.write(content)
    
    def _generate_dockerfile(self, agent_definition: Dict[str, Any], config: DeploymentConfig) -> str:
        """Generate Dockerfile for the agent."""
        
        python_version = agent_definition.get("metadata", {}).get("python_version", "3.11")
        main_port = config.ports[0].port if config.ports else 8000
        
        return f"""FROM python:{python_version}-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY src/ ./src/

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=30s --retries=3 \\
    CMD curl -f http://localhost:{main_port}/health || exit 1

# Expose port
EXPOSE {main_port}

# Start application
CMD ["python", "-m", "src.main"]
"""
    
    def _generate_k8s_manifests(
        self,
        agent_definition: Dict[str, Any],
        config: DeploymentConfig
    ) -> Dict[str, Dict[str, Any]]:
        """Generate Kubernetes deployment manifests."""
        
        agent_name = agent_definition.get("name", f"agent-{config.agent_id}")
        labels = {
            "app": agent_name,
            "agent-id": config.agent_id,
            "environment": config.environment,
            "managed-by": "auto-deployment-system"
        }
        
        # Deployment manifest
        deployment = {
            "apiVersion": "apps/v1",
            "kind": "Deployment",
            "metadata": {
                "name": agent_name,
                "namespace": self.k8s_namespace,
                "labels": labels
            },
            "spec": {
                "replicas": config.scaling["min_replicas"],
                "selector": {"matchLabels": labels},
                "template": {
                    "metadata": {"labels": labels},
                    "spec": {
                        "containers": [{
                            "name": agent_name,
                            "image": f"{self.docker_registry}/{agent_name}:latest",
                            "ports": [
                                {"containerPort": port.port, "protocol": "TCP"}
                                for port in config.ports
                            ],
                            "env": [
                                {"name": k, "value": v}
                                for k, v in config.env_vars.items()
                            ],
                            "resources": {
                                "requests": {
                                    "cpu": config.resources["cpu"],
                                    "memory": config.resources["memory"]
                                },
                                "limits": {
                                    "cpu": config.resources["cpu_limit"],
                                    "memory": config.resources["memory_limit"]
                                }
                            },
                            "livenessProbe": {
                                "httpGet": {
                                    "path": config.health_check["endpoint"],
                                    "port": config.ports[0].port
                                },
                                "initialDelaySeconds": config.health_check["initial_delay"],
                                "periodSeconds": config.health_check["period"],
                                "timeoutSeconds": config.health_check["timeout"],
                                "failureThreshold": config.health_check["failure_threshold"]
                            },
                            "readinessProbe": {
                                "httpGet": {
                                    "path": config.health_check["endpoint"],
                                    "port": config.ports[0].port
                                },
                                "initialDelaySeconds": 5,
                                "periodSeconds": 10
                            }
                        }]
                    }
                }
            }
        }
        
        # Service manifest
        service = {
            "apiVersion": "v1",
            "kind": "Service",
            "metadata": {
                "name": agent_name,
                "namespace": self.k8s_namespace,
                "labels": labels
            },
            "spec": {
                "selector": labels,
                "ports": [
                    {
                        "name": f"port-{port.port}",
                        "port": port.port,
                        "targetPort": port.port,
                        "protocol": "TCP"
                    }
                    for port in config.ports if port.is_external
                ],
                "type": "ClusterIP"
            }
        }
        
        # HorizontalPodAutoscaler manifest
        hpa = {
            "apiVersion": "autoscaling/v2",
            "kind": "HorizontalPodAutoscaler",
            "metadata": {
                "name": agent_name,
                "namespace": self.k8s_namespace,
                "labels": labels
            },
            "spec": {
                "scaleTargetRef": {
                    "apiVersion": "apps/v1",
                    "kind": "Deployment",
                    "name": agent_name
                },
                "minReplicas": config.scaling["min_replicas"],
                "maxReplicas": config.scaling["max_replicas"],
                "metrics": [{
                    "type": "Resource",
                    "resource": {
                        "name": "cpu",
                        "target": {
                            "type": "Utilization",
                            "averageUtilization": config.scaling["target_cpu"]
                        }
                    }
                }]
            }
        }
        
        return {
            "deployment.yaml": deployment,
            "service.yaml": service,
            "hpa.yaml": hpa
        }
    
    async def _build_agent_image(
        self,
        agent_definition: Dict[str, Any],
        config: DeploymentConfig,
        status: DeploymentStatus
    ) -> str:
        """Build Docker image for the agent."""
        
        temp_dir = self.temp_dirs[-1]  # Get latest temp dir
        agent_name = agent_definition.get("name", f"agent-{config.agent_id}")
        image_tag = f"{self.docker_registry}/{agent_name}:latest"
        
        # Build Docker image
        build_process = await asyncio.create_subprocess_exec(
            "docker", "build", "-t", image_tag, temp_dir,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await build_process.communicate()
        
        if build_process.returncode != 0:
            error_msg = f"Docker build failed: {stderr.decode()}"
            status.logs.append(error_msg)
            raise AgentError(error_msg)
        
        status.logs.append(f"Docker image built: {image_tag}")
        
        # Push image to registry if configured
        if self.docker_registry != "localhost:5000":
            push_process = await asyncio.create_subprocess_exec(
                "docker", "push", image_tag,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await push_process.communicate()
            
            if push_process.returncode != 0:
                error_msg = f"Docker push failed: {stderr.decode()}"
                status.logs.append(error_msg)
                raise AgentError(error_msg)
            
            status.logs.append(f"Docker image pushed: {image_tag}")
        
        return image_tag
    
    async def _run_deployment_tests(
        self,
        agent_definition: Dict[str, Any],
        config: DeploymentConfig,
        status: DeploymentStatus,
        image_tag: str
    ) -> None:
        """Run tests before deployment."""
        
        temp_dir = self.temp_dirs[-1]
        test_dir = f"{temp_dir}/tests"
        
        if not os.path.exists(test_dir) or not os.listdir(test_dir):
            status.logs.append("No tests found, skipping test phase")
            return
        
        # Run tests in container
        test_process = await asyncio.create_subprocess_exec(
            "docker", "run", "--rm", "-v", f"{test_dir}:/app/tests", image_tag,
            "python", "-m", "pytest", "tests/", "-v",
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await test_process.communicate()
        
        if test_process.returncode != 0:
            error_msg = f"Tests failed: {stderr.decode()}"
            status.logs.append(error_msg)
            raise AgentError(error_msg)
        
        status.logs.append("All tests passed successfully")
    
    async def _deploy_to_kubernetes(
        self,
        agent_definition: Dict[str, Any],
        config: DeploymentConfig,
        status: DeploymentStatus,
        image_tag: str
    ) -> None:
        """Deploy agent to Kubernetes."""
        
        temp_dir = self.temp_dirs[-1]
        k8s_dir = f"{temp_dir}/k8s"
        
        # Apply Kubernetes manifests
        for filename in os.listdir(k8s_dir):
            if filename.endswith('.yaml'):
                manifest_path = f"{k8s_dir}/{filename}"
                
                apply_process = await asyncio.create_subprocess_exec(
                    "kubectl", "apply", "-f", manifest_path,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await apply_process.communicate()
                
                if apply_process.returncode != 0:
                    error_msg = f"Kubernetes apply failed for {filename}: {stderr.decode()}"
                    status.logs.append(error_msg)
                    raise AgentError(error_msg)
                
                status.logs.append(f"Applied {filename}: {stdout.decode().strip()}")
    
    async def _verify_deployment(self, config: DeploymentConfig, status: DeploymentStatus) -> None:
        """Verify deployment is healthy."""
        
        agent_name = f"agent-{config.agent_id}"
        
        # Wait for deployment to be ready
        max_wait_time = 300  # 5 minutes
        wait_interval = 10
        elapsed_time = 0
        
        while elapsed_time < max_wait_time:
            # Check deployment status
            check_process = await asyncio.create_subprocess_exec(
                "kubectl", "get", "deployment", agent_name,
                "-n", self.k8s_namespace, "-o", "json",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await check_process.communicate()
            
            if check_process.returncode == 0:
                deployment_info = json.loads(stdout.decode())
                replicas_status = deployment_info.get("status", {})
                
                desired = replicas_status.get("replicas", 0)
                ready = replicas_status.get("readyReplicas", 0)
                available = replicas_status.get("availableReplicas", 0)
                
                status.replicas = {
                    "desired": desired,
                    "ready": ready,
                    "available": available
                }
                
                if ready == desired and available == desired and desired > 0:
                    status.logs.append("Deployment is ready and healthy")
                    return
            
            await asyncio.sleep(wait_interval)
            elapsed_time += wait_interval
            status.logs.append(f"Waiting for deployment to be ready... ({elapsed_time}s)")
        
        raise AgentError("Deployment did not become healthy within timeout period")
    
    async def _rollback_deployment(
        self,
        config: DeploymentConfig,
        status: DeploymentStatus
    ) -> None:
        """Rollback failed deployment."""
        
        status.status = DeploymentStatus.ROLLBACK
        status.logs.append("Starting rollback procedure")
        
        try:
            agent_name = f"agent-{config.agent_id}"
            
            # Delete Kubernetes resources
            delete_process = await asyncio.create_subprocess_exec(
                "kubectl", "delete", "deployment,service,hpa", agent_name,
                "-n", self.k8s_namespace, "--ignore-not-found=true",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await delete_process.communicate()
            status.logs.append(f"Cleaned up Kubernetes resources: {stdout.decode().strip()}")
            
            # Release allocated ports
            for port_assignment in config.ports:
                self.port_manager.release_port(port_assignment.port)
            
            status.logs.append("Released allocated ports")
            
        except Exception as e:
            status.logs.append(f"Rollback encountered error: {str(e)}")
    
    async def _cleanup_temp_dirs(self) -> None:
        """Clean up temporary directories."""
        
        for temp_dir in self.temp_dirs:
            try:
                shutil.rmtree(temp_dir)
            except Exception as e:
                logger.warning(f"Failed to cleanup temp dir {temp_dir}: {e}")
        
        self.temp_dirs.clear()
    
    def get_deployment_status(self, deployment_id: str) -> Optional[DeploymentStatus]:
        """Get deployment status."""
        return self.active_deployments.get(deployment_id)
    
    def list_active_deployments(self) -> List[DeploymentStatus]:
        """List all active deployments."""
        return list(self.active_deployments.values())
    
    async def stop_deployment(self, deployment_id: str) -> bool:
        """Stop a running deployment."""
        
        deployment = self.active_deployments.get(deployment_id)
        if not deployment:
            return False
        
        try:
            deployment.status = DeploymentStatus.STOPPING
            
            agent_name = f"agent-{deployment.agent_id}"
            
            # Scale down to 0 replicas
            scale_process = await asyncio.create_subprocess_exec(
                "kubectl", "scale", "deployment", agent_name,
                "-n", self.k8s_namespace, "--replicas=0",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            await scale_process.communicate()
            
            deployment.status = DeploymentStatus.STOPPED
            deployment.end_time = datetime.now()
            
            # Move to history
            self.deployment_history.append(deployment)
            del self.active_deployments[deployment_id]
            
            return True
            
        except Exception as e:
            self.log_error("deployment_stop_failed", e, deployment_id=deployment_id)
            return False
    
    def get_port_allocations(self) -> Dict[str, List[PortAssignment]]:
        """Get current port allocations grouped by service."""
        
        allocations = {}
        for port, assignment in self.port_manager.allocated_ports.items():
            service = assignment.service_name
            if service not in allocations:
                allocations[service] = []
            allocations[service].append(assignment)
        
        return allocations