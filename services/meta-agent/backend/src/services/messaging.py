"""
AI Agent Platform - Message Queue Service (Kafka Integration)
"""

import asyncio
import json
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime
import structlog

from kafka import KafkaProducer, KafkaConsumer
from kafka.errors import KafkaError
from config.settings import settings

logger = structlog.get_logger()


class MessageQueueService:
    """Service for handling message queue operations with Kafka"""
    
    def __init__(self):
        self.bootstrap_servers = settings.message_queue.kafka_bootstrap_servers
        self.producer = None
        self.consumers = {}
        self.message_handlers = {}
        self.is_connected = False
        
        # Topic configurations
        self.topics = {
            'agent_events': settings.message_queue.agent_events_topic,
            'orchestration': settings.message_queue.orchestration_topic,
            'intelligence': settings.message_queue.intelligence_topic
        }
        
        logger.info("Message queue service initialized", topics=self.topics)
    
    async def connect(self) -> bool:
        """Connect to Kafka"""
        try:
            # Initialize producer
            self.producer = KafkaProducer(
                bootstrap_servers=self.bootstrap_servers,
                value_serializer=lambda x: json.dumps(x, default=str).encode('utf-8'),
                key_serializer=lambda x: x.encode('utf-8') if isinstance(x, str) else x,
                acks='all',
                retries=3,
                retry_backoff_ms=1000,
                max_in_flight_requests_per_connection=1
            )
            
            self.is_connected = True
            logger.info("Connected to Kafka successfully")
            return True
            
        except Exception as e:
            logger.error("Failed to connect to Kafka", error=str(e))
            self.is_connected = False
            return False
    
    async def disconnect(self):
        """Disconnect from Kafka"""
        try:
            if self.producer:
                self.producer.close()
                self.producer = None
            
            # Close all consumers
            for consumer in self.consumers.values():
                consumer.close()
            self.consumers.clear()
            
            self.is_connected = False
            logger.info("Disconnected from Kafka")
            
        except Exception as e:
            logger.error("Error during Kafka disconnect", error=str(e))
    
    async def publish_message(
        self,
        topic: str,
        message: Dict[str, Any],
        key: Optional[str] = None,
        partition: Optional[int] = None
    ) -> bool:
        """Publish a message to a topic"""
        try:
            if not self.is_connected or not self.producer:
                logger.error("Cannot publish - not connected to Kafka")
                return False
            
            # Add metadata to message
            message_with_meta = {
                **message,
                'timestamp': datetime.utcnow().isoformat(),
                'message_id': f"{datetime.utcnow().timestamp()}_{key or 'no_key'}"
            }
            
            # Send message
            future = self.producer.send(
                topic=topic,
                value=message_with_meta,
                key=key,
                partition=partition
            )
            
            # Wait for send completion
            record_metadata = future.get(timeout=10)
            
            logger.info(
                "Message published successfully",
                topic=topic,
                partition=record_metadata.partition,
                offset=record_metadata.offset,
                key=key
            )
            
            return True
            
        except Exception as e:
            logger.error(
                "Failed to publish message",
                topic=topic,
                key=key,
                error=str(e)
            )
            return False
    
    async def publish_agent_event(
        self,
        agent_id: str,
        event_type: str,
        event_data: Dict[str, Any]
    ) -> bool:
        """Publish agent event"""
        message = {
            'agent_id': agent_id,
            'event_type': event_type,
            'event_data': event_data,
            'source': 'agent_runtime'
        }
        
        return await self.publish_message(
            topic=self.topics['agent_events'],
            message=message,
            key=agent_id
        )
    
    async def publish_orchestration_command(
        self,
        orchestration_id: str,
        command: str,
        command_data: Dict[str, Any],
        target_agents: Optional[List[str]] = None
    ) -> bool:
        """Publish orchestration command"""
        message = {
            'orchestration_id': orchestration_id,
            'command': command,
            'command_data': command_data,
            'target_agents': target_agents or [],
            'source': 'orchestration_service'
        }
        
        return await self.publish_message(
            topic=self.topics['orchestration'],
            message=message,
            key=orchestration_id
        )
    
    async def publish_intelligence_request(
        self,
        request_id: str,
        request_type: str,
        request_data: Dict[str, Any],
        requester_id: str
    ) -> bool:
        """Publish intelligence service request"""
        message = {
            'request_id': request_id,
            'request_type': request_type,
            'request_data': request_data,
            'requester_id': requester_id,
            'source': 'intelligence_service'
        }
        
        return await self.publish_message(
            topic=self.topics['intelligence'],
            message=message,
            key=request_id
        )
    
    def subscribe_to_topic(
        self,
        topic: str,
        group_id: str,
        message_handler: Callable[[Dict[str, Any]], None]
    ) -> bool:
        """Subscribe to a topic with a message handler"""
        try:
            if topic in self.consumers:
                logger.warning("Already subscribed to topic", topic=topic)
                return True
            
            # Create consumer
            consumer = KafkaConsumer(
                topic,
                bootstrap_servers=self.bootstrap_servers,
                group_id=group_id,
                auto_offset_reset=settings.message_queue.kafka_auto_offset_reset,
                value_deserializer=lambda x: json.loads(x.decode('utf-8')),
                key_deserializer=lambda x: x.decode('utf-8') if x else None,
                enable_auto_commit=True,
                auto_commit_interval_ms=1000
            )
            
            self.consumers[topic] = consumer
            self.message_handlers[topic] = message_handler
            
            # Start consumer task
            asyncio.create_task(self._consume_messages(topic))
            
            logger.info("Subscribed to topic successfully", topic=topic, group_id=group_id)
            return True
            
        except Exception as e:
            logger.error("Failed to subscribe to topic", topic=topic, error=str(e))
            return False
    
    def unsubscribe_from_topic(self, topic: str) -> bool:
        """Unsubscribe from a topic"""
        try:
            if topic not in self.consumers:
                logger.warning("Not subscribed to topic", topic=topic)
                return True
            
            # Close consumer
            self.consumers[topic].close()
            del self.consumers[topic]
            del self.message_handlers[topic]
            
            logger.info("Unsubscribed from topic", topic=topic)
            return True
            
        except Exception as e:
            logger.error("Failed to unsubscribe from topic", topic=topic, error=str(e))
            return False
    
    async def get_topic_info(self, topic: str) -> Optional[Dict[str, Any]]:
        """Get information about a topic"""
        try:
            if not self.producer:
                return None
            
            # Get topic metadata
            metadata = self.producer.partitions_for(topic)
            
            if metadata:
                return {
                    'topic': topic,
                    'partitions': list(metadata),
                    'partition_count': len(metadata)
                }
            
            return None
            
        except Exception as e:
            logger.error("Failed to get topic info", topic=topic, error=str(e))
            return None
    
    async def get_consumer_info(self) -> Dict[str, Any]:
        """Get information about active consumers"""
        return {
            'active_subscriptions': list(self.consumers.keys()),
            'subscription_count': len(self.consumers),
            'topics': self.topics
        }
    
    # Private methods
    
    async def _consume_messages(self, topic: str):
        """Consume messages from a topic"""
        consumer = self.consumers.get(topic)
        handler = self.message_handlers.get(topic)
        
        if not consumer or not handler:
            logger.error("Consumer or handler not found", topic=topic)
            return
        
        logger.info("Starting message consumption", topic=topic)
        
        try:
            while topic in self.consumers:
                # Poll for messages
                message_batch = consumer.poll(timeout_ms=1000)
                
                for topic_partition, messages in message_batch.items():
                    for message in messages:
                        try:
                            # Process message
                            await self._process_message(message, handler)
                            
                        except Exception as e:
                            logger.error(
                                "Error processing message",
                                topic=topic,
                                partition=message.partition,
                                offset=message.offset,
                                error=str(e)
                            )
                
                # Small delay to prevent tight loop
                await asyncio.sleep(0.1)
                
        except Exception as e:
            logger.error("Error in message consumption loop", topic=topic, error=str(e))
        finally:
            logger.info("Message consumption ended", topic=topic)
    
    async def _process_message(self, message, handler: Callable[[Dict[str, Any]], None]):
        """Process a single message"""
        try:
            message_data = {
                'topic': message.topic,
                'partition': message.partition,
                'offset': message.offset,
                'key': message.key,
                'value': message.value,
                'timestamp': datetime.fromtimestamp(message.timestamp / 1000) if message.timestamp else None
            }
            
            # Call handler
            if asyncio.iscoroutinefunction(handler):
                await handler(message_data)
            else:
                handler(message_data)
                
        except Exception as e:
            logger.error("Handler execution failed", error=str(e))
            raise


# Message handler examples
class MessageHandlers:
    """Example message handlers for different topics"""
    
    @staticmethod
    async def agent_event_handler(message_data: Dict[str, Any]):
        """Handle agent events"""
        value = message_data['value']
        agent_id = value.get('agent_id')
        event_type = value.get('event_type')
        
        logger.info(
            "Agent event received",
            agent_id=agent_id,
            event_type=event_type
        )
        
        # Process based on event type
        if event_type == 'heartbeat':
            await MessageHandlers._handle_agent_heartbeat(value)
        elif event_type == 'status_change':
            await MessageHandlers._handle_agent_status_change(value)
        elif event_type == 'task_completed':
            await MessageHandlers._handle_task_completed(value)
    
    @staticmethod
    async def orchestration_command_handler(message_data: Dict[str, Any]):
        """Handle orchestration commands"""
        value = message_data['value']
        orchestration_id = value.get('orchestration_id')
        command = value.get('command')
        
        logger.info(
            "Orchestration command received",
            orchestration_id=orchestration_id,
            command=command
        )
        
        # Process command
        # TODO: Integrate with orchestration service
    
    @staticmethod
    async def intelligence_request_handler(message_data: Dict[str, Any]):
        """Handle intelligence requests"""
        value = message_data['value']
        request_id = value.get('request_id')
        request_type = value.get('request_type')
        
        logger.info(
            "Intelligence request received",
            request_id=request_id,
            request_type=request_type
        )
        
        # Process request
        # TODO: Integrate with intelligence service
    
    @staticmethod
    async def _handle_agent_heartbeat(event_data: Dict[str, Any]):
        """Handle agent heartbeat event"""
        # Update agent last_heartbeat in database
        # TODO: Implement database update
        pass
    
    @staticmethod
    async def _handle_agent_status_change(event_data: Dict[str, Any]):
        """Handle agent status change event"""
        # Update agent status in database
        # TODO: Implement database update
        pass
    
    @staticmethod
    async def _handle_task_completed(event_data: Dict[str, Any]):
        """Handle task completion event"""
        # Update task status in database
        # TODO: Implement database update
        pass


# Global message queue service instance
message_queue_service = MessageQueueService()