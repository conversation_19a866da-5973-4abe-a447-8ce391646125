"""Template processor for generating frontend code from templates."""
import json
import re
from pathlib import Path
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)


class TemplateProcessor:
    """Process frontend templates with agent-specific replacements."""
    
    def __init__(self, template_base_path: Path):
        self.template_base_path = template_base_path
        self.placeholder_pattern = re.compile(r'\{\{(\w+)\}\}')
    
    def process_template(self, template_path: Path, replacements: Dict[str, str]) -> str:
        """Process a single template file with replacements."""
        try:
            with open(template_path, 'r') as f:
                content = f.read()
            
            # Replace all placeholders
            def replace_placeholder(match):
                key = match.group(1)
                if key in replacements:
                    return replacements[key]
                logger.warning(f"No replacement found for placeholder: {key}")
                return match.group(0)  # Keep original if no replacement
            
            return self.placeholder_pattern.sub(replace_placeholder, content)
        except Exception as e:
            logger.error(f"Error processing template {template_path}: {e}")
            raise
    
    def generate_frontend_structure(
        self,
        agent_name: str,
        agent_id: str,
        api_endpoints: List[Dict[str, Any]],
        custom_components: Dict[str, str],
        output_path: Path
    ) -> Dict[str, str]:
        """Generate complete frontend structure from templates."""
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Base replacements used across all templates
        base_replacements = {
            "AGENT_NAME": agent_name,
            "AGENT_ID": agent_id,
            "VERSION": "1.0.0",
            "BACKEND_HOST": "backend",  # Docker service name
            "BACKEND_PORT": "8000"
        }
        
        # Generate API types and methods from endpoints
        api_types = self._generate_api_types(api_endpoints)
        api_methods = self._generate_api_methods(api_endpoints)
        convenience_functions = self._generate_convenience_functions(api_endpoints)
        
        # Generate routes and nav links
        routes = self._generate_routes(custom_components)
        nav_links = self._generate_nav_links(custom_components)
        component_imports = self._generate_component_imports(custom_components)
        
        # Template-specific replacements
        template_configs = {
            "package.json.template": {
                **base_replacements,
                "AGENT_NAME_KEBAB": self._to_kebab_case(agent_name)
            },
            "vite.config.ts.template": base_replacements,
            "tsconfig.json.template": base_replacements,
            "tsconfig.node.json.template": base_replacements,
            "src/App.tsx.template": {
                **base_replacements,
                "ROUTES": routes,
                "COMPONENT_IMPORTS": component_imports
            },
            "src/services/api.ts.template": {
                **base_replacements,
                "API_TYPES": api_types,
                "API_METHODS": api_methods,
                "CONVENIENCE_FUNCTIONS": convenience_functions
            },
            "src/components/Header.tsx.template": {
                **base_replacements,
                "NAV_LINKS": nav_links
            },
            "Dockerfile.template": base_replacements,
            "nginx.conf.template": base_replacements
        }
        
        generated_files = {}
        
        # Process each template
        for template_name, replacements in template_configs.items():
            template_path = self.template_base_path / template_name
            if not template_path.exists():
                logger.warning(f"Template not found: {template_path}")
                continue
            
            # Determine output file path
            output_file_path = output_path / template_name.replace('.template', '')
            output_file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Process and save
            processed_content = self.process_template(template_path, replacements)
            with open(output_file_path, 'w') as f:
                f.write(processed_content)
            
            generated_files[str(output_file_path)] = processed_content
        
        # Generate additional custom component files
        for component_name, component_code in custom_components.items():
            component_path = output_path / f"src/components/{component_name}.tsx"
            component_path.parent.mkdir(parents=True, exist_ok=True)
            with open(component_path, 'w') as f:
                f.write(component_code)
            generated_files[str(component_path)] = component_code
        
        # Generate default pages if not provided
        self._generate_default_pages(output_path, agent_name, agent_id)
        
        # Generate index.html
        self._generate_index_html(output_path, agent_name)
        
        # Generate main.tsx
        self._generate_main_tsx(output_path)
        
        # Generate styles
        self._generate_styles(output_path)
        
        return generated_files
    
    def _generate_api_types(self, endpoints: List[Dict[str, Any]]) -> str:
        """Generate TypeScript types from API endpoints."""
        types = []
        
        for endpoint in endpoints:
            operation_id = endpoint.get('operation_id', self._path_to_operation_id(endpoint['path']))
            
            # Extract request/response types from endpoint info
            if endpoint.get('request_body'):
                types.append(f"export interface {operation_id}Request {endpoint['request_body']}")
            
            if endpoint.get('response_type'):
                types.append(f"export interface {operation_id}Response {endpoint['response_type']}")
            elif endpoint.get('response_schema'):
                # Use provided response schema
                types.append(f"export interface {operation_id}Response {endpoint['response_schema']}")
            else:
                # Generate basic response type
                types.append(f"""
export interface {operation_id}Response {{
  status: string
  data?: any
  message?: string
}}""")
        
        return '\n'.join(types) if types else "// No additional types needed"
    
    def _generate_api_methods(self, endpoints: List[Dict[str, Any]]) -> str:
        """Generate API client methods from endpoints."""
        methods = []
        
        for endpoint in endpoints:
            method = endpoint.get('method', 'GET').lower()
            path = endpoint['path']
            operation_id = endpoint.get('operation_id', self._path_to_operation_id(path))
            
            # Generate method signature based on HTTP method
            if method in ['post', 'put', 'patch']:
                if operation_id == 'root':
                    continue  # Skip root for POST methods
                
                # Check if endpoint has specific parameter schema
                if endpoint.get('request_schema'):
                    param_type = f"{operation_id}Request"
                else:
                    param_type = "any"
                
                method_signature = f"async {operation_id}(data: {param_type}): Promise<{operation_id}Response>"
                method_body = f"const response = await this.client.{method}('{path}', data)"
            else:
                method_signature = f"async {operation_id}(): Promise<{operation_id}Response>"
                method_body = f"const response = await this.client.{method}('{path}')"
            
            methods.append(f"""
  // {endpoint.get('description', f'{operation_id} endpoint')}
  {method_signature} {{
    {method_body}
    return response.data
  }}""")
        
        return '\n'.join(methods) if methods else "// No additional API methods"
    
    def _generate_convenience_functions(self, endpoints: List[Dict[str, Any]]) -> str:
        """Generate convenience functions for easier API usage."""
        functions = []
        
        for endpoint in endpoints:
            method = endpoint.get('method', 'GET').lower()
            path = endpoint['path']
            operation_id = endpoint.get('operation_id', self._path_to_operation_id(path))
            
            # Skip root endpoints and health checks
            if operation_id in ['root', 'health', 'healthCheck']:
                continue
            
            # Generate convenience function
            if method in ['post', 'put', 'patch']:
                function_code = f"export const {operation_id} = (data: any) => apiClient.{operation_id}(data)"
            else:
                function_code = f"export const {operation_id} = () => apiClient.{operation_id}()"
            
            functions.append(function_code)
        
        return '\n'.join(functions) if functions else "// No convenience functions needed"
    
    def _generate_routes(self, components: Dict[str, str]) -> str:
        """Generate routes from custom components."""
        routes = []
        for component_name in components:
            # Convert component name to path (e.g., CalculatorPage -> /calculator)
            path = '/' + self._to_kebab_case(component_name.replace('Page', ''))
            routes.append(f'<Route path="{path}" component={{{component_name}}} />')
        
        return '\n          '.join(routes) if routes else "// No additional routes"
    
    def _generate_nav_links(self, components: Dict[str, str]) -> str:
        """Generate navigation links for custom components."""
        links = []
        for component_name in components:
            if 'Page' in component_name:
                path = '/' + self._to_kebab_case(component_name.replace('Page', ''))
                label = self._to_kebab_case(component_name.replace('Page', '')).replace('-', ' ').title()
                links.append(f"""
              <Link 
                href="{path}" 
                class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                activeClassName="bg-gray-100 text-gray-900"
              >
                {label}
              </Link>""")
        
        return '\n'.join(links) if links else ""
    
    def _generate_component_imports(self, components: Dict[str, str]) -> str:
        """Generate imports for custom components."""
        imports = []
        for component_name in components:
            imports.append(f"import {{ {component_name} }} from '@/components/{component_name}'")
        
        return '\n'.join(imports) if imports else ""
    
    def _generate_default_pages(self, output_path: Path, agent_name: str, agent_id: str):
        """Generate default page components."""
        pages_dir = output_path / "src/pages"
        pages_dir.mkdir(parents=True, exist_ok=True)
        
        # HomePage
        home_page = f"""import {{ useEffect, useState }} from 'preact/hooks'
import {{ apiClient, AgentInfo }} from '@/services/api'

export function HomePage() {{
  const [agentInfo, setAgentInfo] = useState<AgentInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {{
    fetchAgentInfo()
  }}, [])

  const fetchAgentInfo = async () => {{
    try {{
      const info = await apiClient.getAgentInfo()
      setAgentInfo(info)
    }} catch (err) {{
      setError(err.message || 'Failed to fetch agent info')
    }} finally {{
      setLoading(false)
    }}
  }}

  if (loading) return <div class="text-center py-8">Loading...</div>
  if (error) return <div class="text-red-600 text-center py-8">Error: {{error}}</div>

  return (
    <div class="max-w-4xl mx-auto">
      <div class="bg-white shadow rounded-lg p-6">
        <h1 class="text-2xl font-bold mb-4">{{agentInfo?.name || '{agent_name}'}}</h1>
        <p class="text-gray-600 mb-6">{{agentInfo?.description}}</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h2 class="text-lg font-semibold mb-2">Agent Details</h2>
            <dl class="space-y-1">
              <div class="flex justify-between">
                <dt class="text-gray-500">ID:</dt>
                <dd class="font-mono text-sm">{{agentInfo?.agent_id}}</dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-gray-500">Type:</dt>
                <dd>{{agentInfo?.type}}</dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-gray-500">Version:</dt>
                <dd>{{agentInfo?.version}}</dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-gray-500">Status:</dt>
                <dd class="text-green-600">{{agentInfo?.status}}</dd>
              </div>
            </dl>
          </div>
          
          <div>
            <h2 class="text-lg font-semibold mb-2">Capabilities</h2>
            <ul class="space-y-1">
              {{agentInfo?.capabilities.map(cap => (
                <li key={{cap}} class="text-gray-600">• {{cap}}</li>
              ))}}
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}}"""
        
        with open(pages_dir / "HomePage.tsx", 'w') as f:
            f.write(home_page)
        
        # ApiInfoPage
        api_info_page = f"""import {{ useEffect, useState }} from 'preact/hooks'
import {{ apiClient, AgentInfo }} from '@/services/api'

export function ApiInfoPage() {{
  const [agentInfo, setAgentInfo] = useState<AgentInfo | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {{
    fetchAgentInfo()
  }}, [])

  const fetchAgentInfo = async () => {{
    try {{
      const info = await apiClient.getAgentInfo()
      setAgentInfo(info)
    }} catch (err) {{
      console.error('Failed to fetch agent info:', err)
    }} finally {{
      setLoading(false)
    }}
  }}

  if (loading) return <div class="text-center py-8">Loading...</div>

  return (
    <div class="max-w-4xl mx-auto">
      <div class="bg-white shadow rounded-lg p-6">
        <h1 class="text-2xl font-bold mb-4">API Information</h1>
        
        <div class="mb-6">
          <h2 class="text-lg font-semibold mb-2">A2A Protocol</h2>
          <p class="text-gray-600">{{agentInfo?.a2a_protocol}}</p>
        </div>
        
        <div>
          <h2 class="text-lg font-semibold mb-2">Available Endpoints</h2>
          <div class="space-y-2">
            {{agentInfo?.endpoints.map(endpoint => (
              <div key={{endpoint.path}} class="border rounded p-3">
                <code class="text-sm font-mono text-blue-600">{{endpoint.path}}</code>
                <p class="text-gray-600 text-sm mt-1">{{endpoint.description}}</p>
              </div>
            ))}}
          </div>
        </div>
      </div>
    </div>
  )
}}"""
        
        with open(pages_dir / "ApiInfoPage.tsx", 'w') as f:
            f.write(api_info_page)
    
    def _generate_index_html(self, output_path: Path, agent_name: str):
        """Generate index.html file."""
        index_html = f"""<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{agent_name}</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>"""
        
        with open(output_path / "index.html", 'w') as f:
            f.write(index_html)
    
    def _generate_main_tsx(self, output_path: Path):
        """Generate main.tsx entry point."""
        main_tsx = """import { render } from 'preact'
import { App } from './App'
import './index.css'

render(<App />, document.getElementById('app')!)"""
        
        src_dir = output_path / "src"
        src_dir.mkdir(parents=True, exist_ok=True)
        with open(src_dir / "main.tsx", 'w') as f:
            f.write(main_tsx)
    
    def _generate_styles(self, output_path: Path):
        """Generate CSS files."""
        index_css = """@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light;
  color: rgba(0, 0, 0, 0.87);
  background-color: #f9fafb;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}"""
        
        src_dir = output_path / "src"
        with open(src_dir / "index.css", 'w') as f:
            f.write(index_css)
        
        # Also create tailwind config
        tailwind_config = """/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}"""
        
        with open(output_path / "tailwind.config.js", 'w') as f:
            f.write(tailwind_config)
        
        # Create postcss config
        postcss_config = """export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}"""
        
        with open(output_path / "postcss.config.js", 'w') as f:
            f.write(postcss_config)
    
    def _to_kebab_case(self, text: str) -> str:
        """Convert text to kebab-case."""
        # Insert hyphens before uppercase letters
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1-\2', text)
        # Insert hyphens before uppercase letters followed by lowercase
        s2 = re.sub('([a-z0-9])([A-Z])', r'\1-\2', s1)
        # Convert to lowercase and replace spaces/underscores with hyphens
        return s2.lower().replace(' ', '-').replace('_', '-')
    
    def _path_to_operation_id(self, path: str) -> str:
        """Convert API path to operation ID."""
        # Remove leading slash and convert to camelCase
        parts = path.strip('/').split('/')
        if not parts:
            return 'root'
        
        # First part is lowercase, rest are title case
        operation_id = parts[0]
        for part in parts[1:]:
            # Handle path parameters like {id}
            if part.startswith('{') and part.endswith('}'):
                operation_id += 'By' + part[1:-1].title()
            else:
                operation_id += part.title()
        
        return operation_id