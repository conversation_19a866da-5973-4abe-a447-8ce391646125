"""Visual Workflow Engine.

This module implements a visual workflow engine providing drag-and-drop workflow
building capabilities with nodes, connections, and execution.
"""

from typing import Dict, Any, List, Optional, Set, Callable, AsyncIterator
from dataclasses import dataclass, field
from enum import Enum
import asyncio
import json
import uuid
from datetime import datetime, timedelta
import aiohttp
from intelligence.gateway import AIGateway
from intelligence.providers.base import AIMessage
from utils.logging import get_logger, LoggerMixin
from utils.exceptions import AgentError, ValidationError
from orchestration.compound_agents import CompoundAgentSystem, ExecutionContext, CompoundAgentStatus
from services.mcp_integration import MCPIntegrationManager

logger = get_logger(__name__)

class WorkflowTriggerType(str, Enum):
    """Types of workflow triggers."""
    MANUAL = "manual"
    WEBHOOK = "webhook" 
    SCHEDULE = "schedule"
    EVENT = "event"
    API_CALL = "api_call"
    FILE_WATCH = "file_watch"
    DATABASE_CHANGE = "database_change"
    EMAIL = "email"

class NodeCategory(str, Enum):
    """Categories of workflow nodes."""
    TRIGGER = "trigger"
    ACTION = "action"
    TRANSFORM = "transform"
    CONDITION = "condition"
    AI = "ai"
    DATABASE = "database"
    API = "api"
    FILE = "file"
    EMAIL = "email"
    UTILITY = "utility"

@dataclass
class WorkflowNode:
    """Visual workflow node definition."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    type: str = ""
    category: NodeCategory = NodeCategory.ACTION
    position: Dict[str, float] = field(default_factory=dict)
    parameters: Dict[str, Any] = field(default_factory=dict)
    credentials: Dict[str, str] = field(default_factory=dict)
    webhookId: Optional[str] = None
    continueOnFail: bool = False
    retryOnFail: bool = False
    maxTries: int = 3
    waitBetweenTries: int = 1000
    alwaysOutputData: bool = False
    executeOnce: bool = False
    disabled: bool = False
    notes: str = ""
    notesInFlow: bool = False

@dataclass
class WorkflowConnection:
    """Connection between workflow nodes."""
    node: str = ""
    type: str = "main"
    index: int = 0

@dataclass
class WorkflowNodeConnection:
    """Node connection configuration."""
    main: List[List[WorkflowConnection]] = field(default_factory=list)

@dataclass
class WorkflowDefinition:
    """Complete N8N-style workflow definition."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    nodes: List[WorkflowNode] = field(default_factory=list)
    connections: Dict[str, WorkflowNodeConnection] = field(default_factory=dict)
    active: bool = False
    settings: Dict[str, Any] = field(default_factory=dict)
    staticData: Dict[str, Any] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)
    triggerCount: int = 0
    updatedAt: datetime = field(default_factory=datetime.now)
    createdAt: datetime = field(default_factory=datetime.now)
    versionId: str = field(default_factory=lambda: str(uuid.uuid4()))
    meta: Dict[str, Any] = field(default_factory=dict)

@dataclass
class WorkflowExecution:
    """Workflow execution instance."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    workflowId: str = ""
    mode: str = "manual"
    retryOf: Optional[str] = None
    startedAt: datetime = field(default_factory=datetime.now)
    stoppedAt: Optional[datetime] = None
    finished: bool = False
    status: str = "running"
    data: Dict[str, Any] = field(default_factory=dict)
    waitTill: Optional[datetime] = None

@dataclass
class WorkflowExecutionResult:
    """Result of workflow execution."""
    execution_id: str
    workflow_id: str
    status: str
    data: Dict[str, Any] = field(default_factory=dict)
    error: Optional[str] = None
    started_at: datetime = field(default_factory=datetime.now)
    finished_at: Optional[datetime] = None
    duration: Optional[float] = None

class NodeType(str, Enum):
    """Available node types."""
    TRIGGER = "trigger"
    HTTP_REQUEST = "http_request"
    WEBHOOK = "webhook"
    AI_CHAT = "ai_chat"
    CODE = "code"
    SET = "set"
    IF = "if"
    MERGE = "merge"
    SPLIT = "split"
    EMAIL = "email"
    DATABASE = "database"
    FILE = "file"

class WorkflowEngine(LoggerMixin):
    """N8N-style workflow execution engine."""
    
    def __init__(
        self,
        ai_gateway: AIGateway,
        mcp_manager: Optional[MCPIntegrationManager] = None,
        compound_agent_system: Optional[CompoundAgentSystem] = None
    ):
        self.ai_gateway = ai_gateway
        self.mcp_manager = mcp_manager
        self.compound_agent_system = compound_agent_system
        
        self.workflows: Dict[str, WorkflowDefinition] = {}
        self.executions: Dict[str, WorkflowExecution] = {}
        self.node_types = self._initialize_node_types()
        self.webhook_endpoints: Dict[str, str] = {}  # webhook_id -> workflow_id
        self.scheduled_workflows: Dict[str, asyncio.Task] = {}
        
        # Active execution tracking
        self.active_executions: Set[str] = set()
        
    def _initialize_node_types(self) -> Dict[str, Dict[str, Any]]:
        """Initialize available node types for visual workflows."""
        
        return {
            # Trigger Nodes
            "webhook": {
                "displayName": "Webhook",
                "name": "webhook",
                "category": NodeCategory.TRIGGER,
                "description": "Receives HTTP requests",
                "properties": [
                    {
                        "displayName": "HTTP Method",
                        "name": "httpMethod",
                        "type": "options",
                        "options": [
                            {"name": "GET", "value": "GET"},
                            {"name": "POST", "value": "POST"},
                            {"name": "PUT", "value": "PUT"},
                            {"name": "DELETE", "value": "DELETE"}
                        ],
                        "default": "POST"
                    },
                    {
                        "displayName": "Path",
                        "name": "path",
                        "type": "string",
                        "default": ""
                    }
                ]
            },
            "schedule": {
                "displayName": "Schedule Trigger",
                "name": "schedule",
                "category": NodeCategory.TRIGGER,
                "description": "Triggers workflow on schedule",
                "properties": [
                    {
                        "displayName": "Trigger Interval",
                        "name": "trigger",
                        "type": "options",
                        "options": [
                            {"name": "Seconds", "value": "seconds"},
                            {"name": "Minutes", "value": "minutes"},
                            {"name": "Hours", "value": "hours"},
                            {"name": "Days", "value": "days"}
                        ],
                        "default": "minutes"
                    },
                    {
                        "displayName": "Seconds Between Triggers",
                        "name": "seconds",
                        "type": "number",
                        "default": 60
                    }
                ]
            },
            
            # HTTP Nodes
            "httpRequest": {
                "displayName": "HTTP Request",
                "name": "httpRequest",
                "category": NodeCategory.API,
                "description": "Makes HTTP requests",
                "properties": [
                    {
                        "displayName": "Request Method",
                        "name": "requestMethod",
                        "type": "options",
                        "options": [
                            {"name": "GET", "value": "GET"},
                            {"name": "POST", "value": "POST"},
                            {"name": "PUT", "value": "PUT"},
                            {"name": "DELETE", "value": "DELETE"},
                            {"name": "PATCH", "value": "PATCH"}
                        ],
                        "default": "GET"
                    },
                    {
                        "displayName": "URL",
                        "name": "url",
                        "type": "string",
                        "required": True,
                        "default": ""
                    }
                ]
            },
            
            # AI Nodes
            "openAI": {
                "displayName": "OpenAI",
                "name": "openAI",
                "category": NodeCategory.AI,
                "description": "Interact with OpenAI API",
                "properties": [
                    {
                        "displayName": "Operation",
                        "name": "operation",
                        "type": "options",
                        "options": [
                            {"name": "Chat", "value": "chat"},
                            {"name": "Complete", "value": "complete"},
                            {"name": "Edit", "value": "edit"},
                            {"name": "Embedding", "value": "embedding"}
                        ],
                        "default": "chat"
                    },
                    {
                        "displayName": "Model",
                        "name": "model",
                        "type": "options",
                        "options": [
                            {"name": "GPT-4o", "value": "gpt-4o"},
                            {"name": "GPT-4o mini", "value": "gpt-4o-mini"},
                            {"name": "GPT-3.5 Turbo", "value": "gpt-3.5-turbo"}
                        ],
                        "default": "gpt-4o"
                    }
                ]
            },
            
            # Data Transformation
            "set": {
                "displayName": "Set",
                "name": "set",
                "category": NodeCategory.TRANSFORM,
                "description": "Sets values in data",
                "properties": [
                    {
                        "displayName": "Values to Set",
                        "name": "values",
                        "type": "fixedCollection",
                        "typeOptions": {"multipleValues": True}
                    }
                ]
            },
            "code": {
                "displayName": "Code",
                "name": "code",
                "category": NodeCategory.TRANSFORM,
                "description": "Run JavaScript code",
                "properties": [
                    {
                        "displayName": "Mode",
                        "name": "mode",
                        "type": "options",
                        "options": [
                            {"name": "Run Once for All Items", "value": "runOnceForAllItems"},
                            {"name": "Run Once for Each Item", "value": "runOnceForEachItem"}
                        ],
                        "default": "runOnceForAllItems"
                    },
                    {
                        "displayName": "JavaScript Code",
                        "name": "jsCode",
                        "type": "string",
                        "typeOptions": {"editor": "code"},
                        "default": "// items contains the data from previous steps\nreturn items;"
                    }
                ]
            },
            
            # Conditional Logic
            "if": {
                "displayName": "IF",
                "name": "if",
                "category": NodeCategory.CONDITION,
                "description": "Conditional branching",
                "properties": [
                    {
                        "displayName": "Conditions",
                        "name": "conditions",
                        "type": "filter",
                        "typeOptions": {"filter": {"caseSensitive": True}}
                    }
                ]
            },
            
            # Database Nodes
            "postgres": {
                "displayName": "Postgres",
                "name": "postgres",
                "category": NodeCategory.DATABASE,
                "description": "Get, add and update data in Postgres",
                "properties": [
                    {
                        "displayName": "Operation",
                        "name": "operation",
                        "type": "options",
                        "options": [
                            {"name": "Execute Query", "value": "executeQuery"},
                            {"name": "Insert", "value": "insert"},
                            {"name": "Update", "value": "update"},
                            {"name": "Delete", "value": "delete"}
                        ],
                        "default": "executeQuery"
                    }
                ]
            },
            
            # File Operations
            "readFile": {
                "displayName": "Read Binary File",
                "name": "readFile",
                "category": NodeCategory.FILE,
                "description": "Reads a file from disk",
                "properties": [
                    {
                        "displayName": "File Path",
                        "name": "filePath",
                        "type": "string",
                        "required": True
                    }
                ]
            },
            "writeFile": {
                "displayName": "Write Binary File",
                "name": "writeFile", 
                "category": NodeCategory.FILE,
                "description": "Writes a file to disk",
                "properties": [
                    {
                        "displayName": "File Name",
                        "name": "fileName",
                        "type": "string",
                        "required": True
                    },
                    {
                        "displayName": "File Path",
                        "name": "filePath",
                        "type": "string",
                        "required": True
                    }
                ]
            },
            
            # Email
            "gmail": {
                "displayName": "Gmail",
                "name": "gmail",
                "category": NodeCategory.EMAIL,
                "description": "Consume Gmail API",
                "properties": [
                    {
                        "displayName": "Operation",
                        "name": "operation",
                        "type": "options",
                        "options": [
                            {"name": "Send", "value": "send"},
                            {"name": "Get", "value": "get"},
                            {"name": "Get All", "value": "getAll"},
                            {"name": "Reply", "value": "reply"}
                        ],
                        "default": "send"
                    }
                ]
            },
            
            # Utilities
            "wait": {
                "displayName": "Wait",
                "name": "wait",
                "category": NodeCategory.UTILITY,
                "description": "Wait before continuing",
                "properties": [
                    {
                        "displayName": "Amount",
                        "name": "amount",
                        "type": "number",
                        "default": 1
                    },
                    {
                        "displayName": "Unit",
                        "name": "unit",
                        "type": "options",
                        "options": [
                            {"name": "Seconds", "value": "seconds"},
                            {"name": "Minutes", "value": "minutes"},
                            {"name": "Hours", "value": "hours"}
                        ],
                        "default": "seconds"
                    }
                ]
            },
            "noOp": {
                "displayName": "No Operation",
                "name": "noOp",
                "category": NodeCategory.UTILITY,
                "description": "No operation node (passthrough)",
                "properties": []
            }
        }
    
    async def create_workflow(self, workflow_data: Dict[str, Any]) -> str:
        """Create a new workflow."""
        
        try:
            # Convert to workflow definition
            workflow = self._dict_to_workflow(workflow_data)
            
            # Validate workflow
            self._validate_workflow(workflow)
            
            # Store workflow
            self.workflows[workflow.id] = workflow
            
            # Setup triggers if workflow is active
            if workflow.active:
                await self._setup_workflow_triggers(workflow)
            
            self.log_operation(
                "workflow_created",
                workflow_id=workflow.id,
                name=workflow.name,
                nodes=len(workflow.nodes),
                active=workflow.active
            )
            
            return workflow.id
            
        except Exception as e:
            raise AgentError(f"Failed to create workflow: {e}")
    
    def _dict_to_workflow(self, data: Dict[str, Any]) -> WorkflowDefinition:
        """Convert dictionary to workflow definition."""
        
        # Parse nodes
        nodes = []
        for node_data in data.get("nodes", []):
            node = WorkflowNode(
                id=node_data.get("id", str(uuid.uuid4())),
                name=node_data.get("name", ""),
                type=node_data.get("type", ""),
                category=NodeCategory(node_data.get("category", "action")),
                position=node_data.get("position", {}),
                parameters=node_data.get("parameters", {}),
                credentials=node_data.get("credentials", {}),
                webhookId=node_data.get("webhookId"),
                continueOnFail=node_data.get("continueOnFail", False),
                retryOnFail=node_data.get("retryOnFail", False),
                maxTries=node_data.get("maxTries", 3),
                waitBetweenTries=node_data.get("waitBetweenTries", 1000),
                disabled=node_data.get("disabled", False)
            )
            nodes.append(node)
        
        # Parse connections
        connections = {}
        for node_id, node_connections in data.get("connections", {}).items():
            main_connections = []
            for connection_group in node_connections.get("main", []):
                connection_list = []
                for conn in connection_group:
                    connection = WorkflowConnection(
                        node=conn.get("node", ""),
                        type=conn.get("type", "main"),
                        index=conn.get("index", 0)
                    )
                    connection_list.append(connection)
                main_connections.append(connection_list)
            
            connections[node_id] = WorkflowNodeConnection(main=main_connections)
        
        # Create workflow
        workflow = WorkflowDefinition(
            id=data.get("id", str(uuid.uuid4())),
            name=data.get("name", ""),
            description=data.get("description", ""),
            nodes=nodes,
            connections=connections,
            active=data.get("active", False),
            settings=data.get("settings", {}),
            staticData=data.get("staticData", {}),
            tags=data.get("tags", []),
            meta=data.get("meta", {})
        )
        
        return workflow
    
    def _validate_workflow(self, workflow: WorkflowDefinition) -> None:
        """Validate workflow definition."""
        
        if not workflow.name:
            raise ValidationError("Workflow name is required")
        
        if not workflow.nodes:
            raise ValidationError("Workflow must have at least one node")
        
        # Check node types exist
        node_ids = {node.id for node in workflow.nodes}
        for node in workflow.nodes:
            if node.type not in self.node_types:
                raise ValidationError(f"Unknown node type: {node.type}")
        
        # Validate connections
        for source_id, connection in workflow.connections.items():
            if source_id not in node_ids:
                raise ValidationError(f"Connection source node {source_id} not found")
            
            for connection_group in connection.main:
                for conn in connection_group:
                    if conn.node not in node_ids:
                        raise ValidationError(f"Connection target node {conn.node} not found")
    
    async def _setup_workflow_triggers(self, workflow: WorkflowDefinition) -> None:
        """Setup triggers for active workflow."""
        
        for node in workflow.nodes:
            if node.category == NodeCategory.TRIGGER:
                if node.type == "webhook":
                    await self._setup_webhook_trigger(workflow.id, node)
                elif node.type == "schedule":
                    await self._setup_schedule_trigger(workflow.id, node)
    
    async def _setup_webhook_trigger(self, workflow_id: str, node: WorkflowNode) -> None:
        """Setup webhook trigger for workflow."""
        
        webhook_id = node.webhookId or str(uuid.uuid4())
        path = node.parameters.get("path", f"/webhook/{webhook_id}")
        
        # Register webhook endpoint
        self.webhook_endpoints[webhook_id] = workflow_id
        
        # Store webhook ID back to node
        node.webhookId = webhook_id
        
        self.log_operation("webhook_trigger_setup", workflow_id=workflow_id, webhook_id=webhook_id, path=path)
    
    async def _setup_schedule_trigger(self, workflow_id: str, node: WorkflowNode) -> None:
        """Setup scheduled trigger for workflow."""
        
        trigger_type = node.parameters.get("trigger", "minutes")
        interval = node.parameters.get("seconds", 60)
        
        # Convert to seconds
        if trigger_type == "minutes":
            interval *= 60
        elif trigger_type == "hours":
            interval *= 3600
        elif trigger_type == "days":
            interval *= 86400
        
        # Create scheduled task
        async def scheduled_execution():
            while workflow_id in self.workflows and self.workflows[workflow_id].active:
                await self.execute_workflow(workflow_id, trigger_data={"trigger": "schedule"})
                await asyncio.sleep(interval)
        
        task = asyncio.create_task(scheduled_execution())
        self.scheduled_workflows[workflow_id] = task
        
        self.log_operation("schedule_trigger_setup", workflow_id=workflow_id, interval=interval)
    
    async def execute_workflow(
        self,
        workflow_id: str,
        trigger_data: Optional[Dict[str, Any]] = None,
        mode: str = "manual"
    ) -> str:
        """Execute a workflow."""
        
        workflow = self.workflows.get(workflow_id)
        if not workflow:
            raise AgentError(f"Workflow {workflow_id} not found")
        
        # Create execution record
        execution = WorkflowExecution(
            workflowId=workflow_id,
            mode=mode,
            data=trigger_data or {}
        )
        
        self.executions[execution.id] = execution
        self.active_executions.add(execution.id)
        
        # Start execution
        asyncio.create_task(self._execute_workflow_impl(workflow, execution))
        
        self.log_operation("workflow_execution_started", workflow_id=workflow_id, execution_id=execution.id)
        
        return execution.id
    
    async def _execute_workflow_impl(self, workflow: WorkflowDefinition, execution: WorkflowExecution) -> None:
        """Implementation of workflow execution."""
        
        try:
            # Find trigger nodes
            trigger_nodes = [node for node in workflow.nodes if node.category == NodeCategory.TRIGGER]
            
            # Start execution data
            execution_data = {
                "trigger": execution.data
            }
            
            # Execute workflow starting from trigger nodes
            if trigger_nodes:
                # Start from trigger nodes
                for trigger_node in trigger_nodes:
                    if not trigger_node.disabled:
                        await self._execute_node_chain(workflow, trigger_node, execution_data, execution)
            else:
                # Find nodes with no incoming connections
                entry_nodes = self._find_entry_nodes(workflow)
                for entry_node in entry_nodes:
                    if not entry_node.disabled:
                        await self._execute_node_chain(workflow, entry_node, execution_data, execution)
            
            execution.finished = True
            execution.status = "completed"
            execution.stoppedAt = datetime.now()
            
        except Exception as e:
            execution.finished = True
            execution.status = "error"
            execution.stoppedAt = datetime.now()
            execution.data["error"] = str(e)
            
            self.log_error("workflow_execution_failed", e, workflow_id=workflow.id, execution_id=execution.id)
        
        finally:
            self.active_executions.discard(execution.id)
    
    def _find_entry_nodes(self, workflow: WorkflowDefinition) -> List[WorkflowNode]:
        """Find nodes with no incoming connections."""
        
        # Get all target nodes from connections
        target_nodes = set()
        for connection in workflow.connections.values():
            for connection_group in connection.main:
                for conn in connection_group:
                    target_nodes.add(conn.node)
        
        # Find nodes not in targets
        entry_nodes = []
        for node in workflow.nodes:
            if node.id not in target_nodes and node.category != NodeCategory.TRIGGER:
                entry_nodes.append(node)
        
        return entry_nodes
    
    async def _execute_node_chain(
        self,
        workflow: WorkflowDefinition,
        start_node: WorkflowNode,
        execution_data: Dict[str, Any],
        execution: WorkflowExecution
    ) -> None:
        """Execute a chain of nodes starting from given node."""
        
        current_nodes = [start_node]
        node_data = execution_data.copy()
        
        while current_nodes:
            next_nodes = []
            
            # Execute current level nodes
            for node in current_nodes:
                if node.disabled:
                    continue
                
                try:
                    # Execute node
                    result = await self._execute_single_node(node, node_data, execution)
                    
                    # Store result
                    execution_data[node.id] = result
                    
                    # Find next nodes to execute
                    if node.id in workflow.connections:
                        for connection_group in workflow.connections[node.id].main:
                            for conn in connection_group:
                                next_node = self._find_node_by_id(workflow, conn.node)
                                if next_node and next_node not in next_nodes:
                                    next_nodes.append(next_node)
                    
                except Exception as e:
                    if not node.continueOnFail:
                        raise
                    
                    # Log error but continue
                    execution_data[node.id] = {"error": str(e)}
                    self.log_error("node_execution_error", e, node_id=node.id, execution_id=execution.id)
            
            current_nodes = next_nodes
            
            # Update node data for next iteration
            node_data = execution_data.copy()
    
    def _find_node_by_id(self, workflow: WorkflowDefinition, node_id: str) -> Optional[WorkflowNode]:
        """Find node by ID."""
        for node in workflow.nodes:
            if node.id == node_id:
                return node
        return None
    
    async def _execute_single_node(
        self,
        node: WorkflowNode,
        input_data: Dict[str, Any],
        execution: WorkflowExecution
    ) -> Dict[str, Any]:
        """Execute a single workflow node."""
        
        node_type = node.type
        parameters = node.parameters
        
        # Execute based on node type
        if node_type == "webhook":
            return await self._execute_webhook_node(node, input_data)
        elif node_type == "httpRequest":
            return await self._execute_http_request_node(node, input_data)
        elif node_type == "openAI":
            return await self._execute_openai_node(node, input_data)
        elif node_type == "set":
            return await self._execute_set_node(node, input_data)
        elif node_type == "code":
            return await self._execute_code_node(node, input_data)
        elif node_type == "if":
            return await self._execute_if_node(node, input_data)
        elif node_type == "wait":
            return await self._execute_wait_node(node, input_data)
        elif node_type == "noOp":
            return input_data
        else:
            # Try to execute as custom node through compound agent system
            if self.compound_agent_system:
                return await self._execute_custom_node(node, input_data)
            else:
                return {"error": f"Unknown node type: {node_type}"}
    
    async def _execute_webhook_node(self, node: WorkflowNode, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute webhook trigger node."""
        return input_data.get("trigger", {})
    
    async def _execute_http_request_node(self, node: WorkflowNode, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute HTTP request node."""
        
        url = node.parameters.get("url", "")
        method = node.parameters.get("requestMethod", "GET")
        headers = node.parameters.get("headers", {})
        body = node.parameters.get("body", {})
        
        # Support dynamic values from input
        if "{{" in url:
            url = self._resolve_expressions(url, input_data)
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.request(method, url, headers=headers, json=body) as response:
                    response_data = await response.text()
                    try:
                        response_json = await response.json()
                    except:
                        response_json = response_data
                    
                    return {
                        "statusCode": response.status,
                        "body": response_json,
                        "headers": dict(response.headers)
                    }
        
        except Exception as e:
            return {"error": str(e)}
    
    async def _execute_openai_node(self, node: WorkflowNode, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute OpenAI node."""
        
        operation = node.parameters.get("operation", "chat")
        model = node.parameters.get("model", "gpt-4o")
        prompt = node.parameters.get("prompt", "")
        
        # Resolve prompt from input data if needed
        if "{{" in prompt:
            prompt = self._resolve_expressions(prompt, input_data)
        
        try:
            if operation == "chat":
                messages = [{"role": "user", "content": prompt}]
                response = await self.ai_gateway.chat_completion(
                    messages=[AIMessage(role="user", content=prompt)],
                    model=model
                )
                
                return {
                    "choices": [{"message": {"content": response.content}}],
                    "usage": response.usage
                }
            
            elif operation == "embedding":
                embedding = await self.ai_gateway.generate_embedding(prompt)
                return {"data": [{"embedding": embedding}]}
            
            else:
                return {"error": f"Unsupported operation: {operation}"}
        
        except Exception as e:
            return {"error": str(e)}
    
    async def _execute_set_node(self, node: WorkflowNode, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute set node to modify data."""
        
        values = node.parameters.get("values", {})
        result = input_data.copy()
        
        # Set values
        for key, value in values.items():
            if isinstance(value, str) and "{{" in value:
                value = self._resolve_expressions(value, input_data)
            result[key] = value
        
        return result
    
    async def _execute_code_node(self, node: WorkflowNode, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute JavaScript code node (simplified)."""
        
        mode = node.parameters.get("mode", "runOnceForAllItems")
        js_code = node.parameters.get("jsCode", "return items;")
        
        # Simple JavaScript-like transformations (security risk in production)
        # In real implementation, would use a sandboxed JS engine
        
        try:
            # Very basic JS simulation
            if "return items" in js_code:
                return input_data
            elif "JSON.stringify" in js_code:
                return {"result": json.dumps(input_data)}
            elif "JSON.parse" in js_code:
                if isinstance(input_data.get("json"), str):
                    return {"result": json.loads(input_data["json"])}
            
            # Default passthrough
            return input_data
            
        except Exception as e:
            return {"error": str(e)}
    
    async def _execute_if_node(self, node: WorkflowNode, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute conditional IF node."""
        
        conditions = node.parameters.get("conditions", {})
        
        # Simple condition evaluation
        # In production, would use a proper expression evaluator
        
        try:
            # Simplified condition checking
            condition_met = True  # Default true for now
            
            return {
                "condition": condition_met,
                "true": input_data if condition_met else {},
                "false": {} if condition_met else input_data
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    async def _execute_wait_node(self, node: WorkflowNode, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute wait/delay node."""
        
        amount = node.parameters.get("amount", 1)
        unit = node.parameters.get("unit", "seconds")
        
        # Convert to seconds
        if unit == "minutes":
            amount *= 60
        elif unit == "hours":
            amount *= 3600
        
        await asyncio.sleep(amount)
        
        return input_data
    
    async def _execute_custom_node(self, node: WorkflowNode, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute custom node through compound agent system."""
        
        # This would integrate with the compound agent system
        return {"result": f"Custom node {node.type} executed", "input": input_data}
    
    def _resolve_expressions(self, text: str, data: Dict[str, Any]) -> str:
        """Resolve expressions in text (simplified n8n-style)."""
        
        # Simple expression resolution
        # In production, would use a proper expression engine
        
        import re
        
        # Find expressions like {{$json.field}}
        expressions = re.findall(r'\{\{(.+?)\}\}', text)
        
        for expr in expressions:
            try:
                # Very simple path resolution
                if expr.startswith("$json."):
                    path = expr[6:]  # Remove "$json."
                    value = data
                    for key in path.split("."):
                        value = value.get(key, "")
                    text = text.replace("{{" + expr + "}}", str(value))
                elif expr in data:
                    text = text.replace("{{" + expr + "}}", str(data[expr]))
            except:
                continue
        
        return text
    
    async def trigger_webhook(self, webhook_id: str, request_data: Dict[str, Any]) -> Optional[str]:
        """Trigger workflow via webhook."""
        
        workflow_id = self.webhook_endpoints.get(webhook_id)
        if not workflow_id:
            return None
        
        # Execute workflow with webhook data
        execution_id = await self.execute_workflow(
            workflow_id,
            trigger_data={
                "headers": request_data.get("headers", {}),
                "body": request_data.get("body", {}),
                "query": request_data.get("query", {}),
                "method": request_data.get("method", "POST")
            },
            mode="webhook"
        )
        
        return execution_id
    
    async def update_workflow(self, workflow_id: str, workflow_data: Dict[str, Any]) -> bool:
        """Update existing workflow."""
        
        try:
            workflow = self.workflows.get(workflow_id)
            if not workflow:
                return False
            
            # Convert and validate new data
            updated_workflow = self._dict_to_workflow(workflow_data)
            updated_workflow.id = workflow_id  # Preserve ID
            updated_workflow.updatedAt = datetime.now()
            
            self._validate_workflow(updated_workflow)
            
            # Remove old triggers
            await self._cleanup_workflow_triggers(workflow_id)
            
            # Store updated workflow
            self.workflows[workflow_id] = updated_workflow
            
            # Setup new triggers if active
            if updated_workflow.active:
                await self._setup_workflow_triggers(updated_workflow)
            
            self.log_operation("workflow_updated", workflow_id=workflow_id)
            return True
            
        except Exception as e:
            self.log_error("workflow_update_failed", e, workflow_id=workflow_id)
            return False
    
    async def _cleanup_workflow_triggers(self, workflow_id: str) -> None:
        """Cleanup triggers for workflow."""
        
        # Remove scheduled tasks
        if workflow_id in self.scheduled_workflows:
            self.scheduled_workflows[workflow_id].cancel()
            del self.scheduled_workflows[workflow_id]
        
        # Remove webhook endpoints
        webhooks_to_remove = [
            webhook_id for webhook_id, wf_id in self.webhook_endpoints.items()
            if wf_id == workflow_id
        ]
        for webhook_id in webhooks_to_remove:
            del self.webhook_endpoints[webhook_id]
    
    async def delete_workflow(self, workflow_id: str) -> bool:
        """Delete workflow."""
        
        try:
            await self._cleanup_workflow_triggers(workflow_id)
            
            if workflow_id in self.workflows:
                del self.workflows[workflow_id]
                self.log_operation("workflow_deleted", workflow_id=workflow_id)
                return True
            
            return False
            
        except Exception as e:
            self.log_error("workflow_delete_failed", e, workflow_id=workflow_id)
            return False
    
    async def activate_workflow(self, workflow_id: str) -> bool:
        """Activate workflow."""
        
        workflow = self.workflows.get(workflow_id)
        if not workflow:
            return False
        
        workflow.active = True
        await self._setup_workflow_triggers(workflow)
        
        self.log_operation("workflow_activated", workflow_id=workflow_id)
        return True
    
    async def deactivate_workflow(self, workflow_id: str) -> bool:
        """Deactivate workflow."""
        
        workflow = self.workflows.get(workflow_id)
        if not workflow:
            return False
        
        workflow.active = False
        await self._cleanup_workflow_triggers(workflow_id)
        
        self.log_operation("workflow_deactivated", workflow_id=workflow_id)
        return True
    
    def get_workflow(self, workflow_id: str) -> Optional[WorkflowDefinition]:
        """Get workflow definition."""
        return self.workflows.get(workflow_id)
    
    def list_workflows(self) -> List[WorkflowDefinition]:
        """List all workflows."""
        return list(self.workflows.values())
    
    def get_execution(self, execution_id: str) -> Optional[WorkflowExecution]:
        """Get execution details."""
        return self.executions.get(execution_id)
    
    def list_executions(self, workflow_id: Optional[str] = None) -> List[WorkflowExecution]:
        """List workflow executions."""
        executions = list(self.executions.values())
        
        if workflow_id:
            executions = [ex for ex in executions if ex.workflowId == workflow_id]
        
        return sorted(executions, key=lambda x: x.startedAt, reverse=True)
    
    def get_node_types(self) -> Dict[str, Dict[str, Any]]:
        """Get available node types."""
        return self.node_types.copy()
    
    def get_webhook_endpoints(self) -> Dict[str, str]:
        """Get webhook endpoints."""
        return self.webhook_endpoints.copy()
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get system statistics."""
        
        active_workflows = sum(1 for w in self.workflows.values() if w.active)
        
        return {
            "total_workflows": len(self.workflows),
            "active_workflows": active_workflows,
            "total_executions": len(self.executions),
            "active_executions": len(self.active_executions),
            "webhook_endpoints": len(self.webhook_endpoints),
            "scheduled_workflows": len(self.scheduled_workflows),
            "node_types": len(self.node_types)
        }