"""
Dynamic Docker Deployment Service
Handles creation, deployment, and management of Docker containers for generated agents
"""

import os
import asyncio
import tempfile
import shutil
import socket
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import structlog
import docker
from docker.errors import DockerException, APIError
import uuid
import json

logger = structlog.get_logger()

class DockerDeploymentService:
    """Manages Docker deployment of generated agents"""
    
    def __init__(self):
        try:
            self.docker_client = docker.from_env()
            self.docker_client.ping()  # Test connection
            logger.info("Docker client initialized successfully")
        except DockerException as e:
            logger.error("Failed to initialize Docker client", error=str(e))
            self.docker_client = None
        
        self.deployments_dir = Path("/tmp/agent_deployments")
        self.deployments_dir.mkdir(exist_ok=True)
        
        # Track deployed agents
        self.deployed_agents = {}
        
        # Port range for agent deployments
        self.port_range_start = 8100
        self.port_range_end = 8999
    
    def _find_free_port(self, exclude_ports: set = None) -> int:
        """Find a free port in the allowed range, excluding specified ports"""
        exclude_ports = exclude_ports or set()

        for port in range(self.port_range_start, self.port_range_end + 1):
            if port in exclude_ports:
                continue

            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                try:
                    s.bind(('localhost', port))
                    return port
                except OSError:
                    continue
        raise RuntimeError("No free ports available in range")
    
    async def deploy_agent(self, agent_id: str, generated_code: Dict[str, Any]) -> Dict[str, Any]:
        """Deploy a generated agent as a Docker container"""
        
        if not self.docker_client:
            raise RuntimeError("Docker client not available")
        
        logger.info("Starting agent deployment", 
                   agent_id=agent_id,
                   generated_code_keys=list(generated_code.keys()) if generated_code else None)
        
        try:
            # 1. Create deployment directory
            deployment_dir = self.deployments_dir / f"agent_{agent_id}"
            deployment_dir.mkdir(exist_ok=True)
            logger.info("Created deployment directory", 
                       agent_id=agent_id,
                       deployment_dir=str(deployment_dir))
            
            # 2. Write generated code to files (with agent_id context)
            self._current_agent_id = agent_id  # Set context for file writing
            await self._write_code_files(deployment_dir, generated_code)
            
            # 3. Verify files were written
            deployed_files = list(deployment_dir.rglob("*"))
            logger.info("Files written to deployment directory",
                       agent_id=agent_id,
                       files_count=len(deployed_files),
                       files=[str(f.relative_to(deployment_dir)) for f in deployed_files if f.is_file()])
            
            # 3. Find free port
            port = self._find_free_port()
            
            # 4. Build Docker image
            image_name = f"agent_{agent_id}:latest"
            await self._build_docker_image(deployment_dir, image_name)
            
            # 5. Run Docker container
            container_info = await self._run_docker_container(
                agent_id, image_name, port, generated_code.get("deployment_config", {})
            )
            
            # 6. Wait for container to be ready
            await self._wait_for_container_ready(container_info["container_id"], port)
            
            # 7. Store deployment info
            deployment_info = {
                "agent_id": agent_id,
                "container_id": container_info["container_id"],
                "container_name": container_info["container_name"],
                "image_name": image_name,
                "port": port,
                "url": f"http://localhost:{port}",
                "status": "running",
                "deployment_dir": str(deployment_dir),
                "deployed_at": "2024-01-15T12:00:00Z",
                "health_check_url": f"http://localhost:{port}/health"
            }
            
            self.deployed_agents[agent_id] = deployment_info
            
            logger.info("Agent deployed successfully", 
                       agent_id=agent_id, 
                       port=port, 
                       container_id=container_info["container_id"])
            
            return deployment_info
            
        except Exception as e:
            logger.error("Failed to deploy agent", agent_id=agent_id, error=str(e))
            # Cleanup on failure
            await self._cleanup_failed_deployment(agent_id)
            raise

    async def deploy_fullstack_agent(self, agent_id: str, generated_code: Dict[str, Any]) -> Dict[str, Any]:
        """Deploy a full-stack agent (backend + frontend) using docker-compose"""
        
        if not self.docker_client:
            raise RuntimeError("Docker client not available")
        
        logger.info("Starting full-stack agent deployment", agent_id=agent_id)
        
        try:
            # 1. Create deployment directory
            deployment_dir = self.deployments_dir / f"fullstack_agent_{agent_id}"
            deployment_dir.mkdir(exist_ok=True)
            
            # 2. Copy files from storage
            from services.agent_storage import agent_storage
            self._current_agent_id = agent_id
            
            if agent_storage.agent_exists(agent_id):
                success = agent_storage.copy_agent_files_for_deployment(agent_id, deployment_dir)
                if not success:
                    raise RuntimeError("Failed to copy agent files for deployment")
            else:
                raise RuntimeError(f"Agent files not found in storage for agent {agent_id}")
            
            # 3. Find free ports for backend and frontend (ensure they're different)
            backend_port = self._find_free_port()
            frontend_port = self._find_free_port(exclude_ports={backend_port})

            logger.info("Port allocation for full-stack deployment",
                       agent_id=agent_id,
                       backend_port=backend_port,
                       frontend_port=frontend_port)
            
            # 4. Create docker-compose.yml for full-stack deployment
            await self._create_docker_compose(deployment_dir, backend_port, frontend_port, agent_id)
            
            # 5. Build and run with docker-compose
            compose_info = await self._run_docker_compose(deployment_dir, agent_id)
            
            # 6. Wait for services to be ready
            await self._wait_for_container_ready(f"backend_{agent_id}", backend_port)
            await self._wait_for_container_ready(f"frontend_{agent_id}", frontend_port)
            
            # 7. Store deployment info
            deployment_info = {
                "agent_id": agent_id,
                "type": "fullstack",
                "backend_port": backend_port,
                "frontend_port": frontend_port,
                "backend_url": f"http://localhost:{backend_port}",
                "frontend_url": f"http://localhost:{frontend_port}",
                "status": "running",
                "deployment_dir": str(deployment_dir),
                "deployed_at": "2024-01-15T12:00:00Z",
                "services": compose_info["services"]
            }
            
            self.deployed_agents[agent_id] = deployment_info
            
            logger.info("Full-stack agent deployed successfully", 
                       agent_id=agent_id, 
                       backend_port=backend_port,
                       frontend_port=frontend_port)
            
            return deployment_info
            
        except Exception as e:
            logger.error("Failed to deploy full-stack agent", agent_id=agent_id, error=str(e))
            await self._cleanup_failed_deployment(agent_id)
            raise
    
    async def _write_code_files(self, deployment_dir: Path, generated_code: Dict[str, Any]):
        """Write generated code to deployment directory"""
        
        logger.info("Starting file writing process",
                   deployment_dir=str(deployment_dir),
                   has_current_agent_id=hasattr(self, '_current_agent_id'),
                   current_agent_id=getattr(self, '_current_agent_id', None))
        
        # First try to use the agent storage system
        from services.agent_storage import agent_storage
        
        # If we have agent_id and files exist in storage, copy them directly
        if hasattr(self, '_current_agent_id') and agent_storage.agent_exists(self._current_agent_id):
            logger.info("Agent files exist in storage, attempting to copy",
                       agent_id=self._current_agent_id)
            success = agent_storage.copy_agent_files_for_deployment(
                self._current_agent_id, 
                deployment_dir
            )
            if success:
                logger.info("Successfully copied agent files from storage for deployment", 
                           agent_id=self._current_agent_id,
                           deployment_dir=str(deployment_dir))
                return
            else:
                logger.error("Failed to copy from storage, falling back to component writing",
                            agent_id=self._current_agent_id)
        else:
            logger.info("Using component-based file writing",
                       reasons={
                           "has_agent_id": hasattr(self, '_current_agent_id'),
                           "storage_exists": agent_storage.agent_exists(getattr(self, '_current_agent_id', '')) if hasattr(self, '_current_agent_id') else False
                       })
        
        # Fallback: write components structure (legacy method)
        components = generated_code.get("components", {})
        logger.info("Writing components", component_types=list(components.keys()))
        
        # Write API files (main application files)
        if "api" in components:
            for filename, content in components["api"].items():
                # Write files directly to deployment directory root
                file_path = deployment_dir / filename
                file_path.parent.mkdir(parents=True, exist_ok=True)
                
                with open(file_path, "w") as f:
                    f.write(content)
                
                logger.info(f"Written file: {filename}", path=str(file_path))
        
        # Write model files
        if "models" in components:
            models_dir = deployment_dir / "models"
            models_dir.mkdir(exist_ok=True)
            
            for filename, content in components["models"].items():
                with open(models_dir / filename, "w") as f:
                    f.write(content)
        
        # Write service files
        if "services" in components:
            services_dir = deployment_dir / "services"
            services_dir.mkdir(exist_ok=True)
            
            for filename, content in components["services"].items():
                with open(services_dir / filename, "w") as f:
                    f.write(content)
        
        # Write config files
        if "config" in components:
            for filename, content in components["config"].items():
                with open(deployment_dir / filename, "w") as f:
                    f.write(content)
        
        # Write Docker files
        if "docker" in components:
            for filename, content in components["docker"].items():
                with open(deployment_dir / filename, "w") as f:
                    f.write(content)
        
        # Write test files
        if "tests" in components:
            tests_dir = deployment_dir / "tests"
            tests_dir.mkdir(exist_ok=True)
            
            for filename, content in components["tests"].items():
                with open(tests_dir / filename, "w") as f:
                    f.write(content)
        
        # Write documentation
        if "docs" in components:
            for filename, content in components["docs"].items():
                with open(deployment_dir / filename, "w") as f:
                    f.write(content)
    
    async def _build_docker_image(self, deployment_dir: Path, image_name: str):
        """Build Docker image from generated code"""
        
        logger.info("Building Docker image", image_name=image_name)
        
        try:
            # Build image
            image, build_logs = self.docker_client.images.build(
                path=str(deployment_dir),
                tag=image_name,
                rm=True,
                forcerm=True
            )
            
            # Log build output
            for log in build_logs:
                if 'stream' in log:
                    logger.debug("Docker build", output=log['stream'].strip())
            
            logger.info("Docker image built successfully", image_name=image_name)
            
        except APIError as e:
            logger.error("Failed to build Docker image", image_name=image_name, error=str(e))
            raise
    
    async def _run_docker_container(self, agent_id: str, image_name: str, port: int, deployment_config: Dict[str, Any]) -> Dict[str, str]:
        """Run Docker container from built image"""
        
        container_name = f"agent_{agent_id}_{uuid.uuid4().hex[:8]}"
        
        logger.info("Starting Docker container", 
                   container_name=container_name, 
                   image_name=image_name, 
                   port=port)
        
        try:
            # Container configuration
            container_config = {
                "image": image_name,
                "name": container_name,
                "ports": {f"{deployment_config.get('port', 8000)}/tcp": port},
                "environment": {
                    "PORT": str(deployment_config.get('port', 8000)),
                    "AGENT_ID": agent_id,
                    "DEPLOYMENT_MODE": "docker"
                },
                "detach": True,
                "remove": False,  # Keep container for debugging
                "restart_policy": {"Name": "unless-stopped"}
            }
            
            # Add resource limits if specified
            resources = deployment_config.get("resources", {})
            if resources:
                memory_limit = self._parse_memory_limit(resources.get("memory_limit", "512m"))
                container_config["mem_limit"] = memory_limit
                container_config["cpu_quota"] = self._parse_cpu_limit(resources.get("cpu_limit", "500m"))
            
            # Run container
            container = self.docker_client.containers.run(**container_config)
            
            logger.info("Docker container started", 
                       container_id=container.id, 
                       container_name=container_name)
            
            return {
                "container_id": container.id,
                "container_name": container_name
            }
            
        except APIError as e:
            logger.error("Failed to start Docker container", 
                        container_name=container_name, 
                        error=str(e))
            raise
    
    def _parse_cpu_limit(self, cpu_limit: str) -> int:
        """Parse CPU limit string to quota value"""
        if cpu_limit.endswith('m'):
            # Convert millicores to quota (1000m = 100000 quota)
            millicores = int(cpu_limit[:-1])
            return int(millicores * 100)
        else:
            # Assume it's cores
            cores = float(cpu_limit)
            return int(cores * 100000)
    
    def _parse_memory_limit(self, memory_limit: str) -> str:
        """Parse memory limit to Docker format (convert Kubernetes Mi/Gi to Docker m/g)"""
        if not memory_limit:
            return "512m"
        
        memory_limit = memory_limit.strip()
        
        # Convert Kubernetes format to Docker format
        if memory_limit.endswith('Mi'):
            # Convert Mi to m (both are megabytes)
            value = memory_limit[:-2]
            return f"{value}m"
        elif memory_limit.endswith('Gi'):
            # Convert Gi to g (both are gigabytes)
            value = memory_limit[:-2]
            return f"{value}g"
        elif memory_limit.endswith('Ki'):
            # Convert Ki to k (both are kilobytes)
            value = memory_limit[:-2]
            return f"{value}k"
        elif memory_limit.endswith('Bi'):
            # Convert Bi to b (both are bytes)
            value = memory_limit[:-2]
            return f"{value}b"
        else:
            # Already in Docker format or plain number
            return memory_limit
    
    async def _wait_for_container_ready(self, container_id: str, port: int, timeout: int = 60):
        """Wait for container to be ready and responding"""
        
        logger.info("Waiting for container to be ready", container_id=container_id, port=port)
        
        import aiohttp
        
        health_url = f"http://localhost:{port}/health"
        
        for attempt in range(timeout):
            try:
                # Check if container is still running
                container = self.docker_client.containers.get(container_id)
                if container.status != 'running':
                    raise RuntimeError(f"Container stopped unexpectedly: {container.status}")
                
                # Try to connect to health endpoint
                async with aiohttp.ClientSession() as session:
                    async with session.get(health_url, timeout=aiohttp.ClientTimeout(total=2)) as response:
                        if response.status == 200:
                            logger.info("Container is ready", container_id=container_id)
                            return
                
            except Exception as e:
                if attempt == timeout - 1:
                    logger.error("Container failed to become ready", 
                                container_id=container_id, 
                                error=str(e))
                    raise RuntimeError(f"Container failed to become ready after {timeout} seconds")
                
                await asyncio.sleep(1)
    
    async def stop_agent(self, agent_id: str) -> bool:
        """Stop and remove deployed agent"""
        
        if agent_id not in self.deployed_agents:
            logger.warning("Agent not found in deployments", agent_id=agent_id)
            return False
        
        deployment_info = self.deployed_agents[agent_id]
        
        try:
            # Stop and remove container
            container = self.docker_client.containers.get(deployment_info["container_id"])
            container.stop(timeout=10)
            container.remove()
            
            # Remove image
            try:
                self.docker_client.images.remove(deployment_info["image_name"], force=True)
            except Exception as e:
                logger.warning("Failed to remove image", image_name=deployment_info["image_name"], error=str(e))
            
            # Cleanup deployment directory
            deployment_dir = Path(deployment_info["deployment_dir"])
            if deployment_dir.exists():
                shutil.rmtree(deployment_dir)
            
            # Remove from tracking
            del self.deployed_agents[agent_id]
            
            logger.info("Agent stopped and cleaned up", agent_id=agent_id)
            return True
            
        except Exception as e:
            logger.error("Failed to stop agent", agent_id=agent_id, error=str(e))
            return False
    
    async def get_agent_status(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Get status of deployed agent"""

        if agent_id not in self.deployed_agents:
            return None

        deployment_info = self.deployed_agents[agent_id].copy()

        try:
            # Handle different deployment types
            if deployment_info.get("type") == "fullstack":
                # For full-stack deployments, check both containers
                backend_container_name = f"backend_{agent_id}"
                frontend_container_name = f"frontend_{agent_id}"

                try:
                    backend_container = self.docker_client.containers.get(backend_container_name)
                    frontend_container = self.docker_client.containers.get(frontend_container_name)

                    # Both containers should be running
                    if backend_container.status == "running" and frontend_container.status == "running":
                        deployment_info["status"] = "running"
                    else:
                        deployment_info["status"] = f"backend: {backend_container.status}, frontend: {frontend_container.status}"

                except Exception as container_error:
                    deployment_info["status"] = "error"
                    deployment_info["error"] = f"Container check failed: {str(container_error)}"

            else:
                # For single container deployments
                container = self.docker_client.containers.get(deployment_info["container_id"])
                deployment_info["status"] = container.status
                deployment_info["container_logs"] = container.logs(tail=10).decode('utf-8')

            # Check health endpoint if available
            health_url = deployment_info.get("frontend_url") or deployment_info.get("backend_url")
            if health_url:
                import aiohttp
                try:
                    async with aiohttp.ClientSession() as session:
                        health_check_url = f"{health_url}/health" if not health_url.endswith('/health') else health_url
                        async with session.get(health_check_url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                            deployment_info["health_status"] = "healthy" if response.status == 200 else "unhealthy"
                except Exception:
                    deployment_info["health_status"] = "unreachable"

        except Exception as e:
            deployment_info["status"] = "error"
            deployment_info["error"] = str(e)

        return deployment_info
    
    async def list_deployed_agents(self) -> List[Dict[str, Any]]:
        """List all deployed agents"""
        
        agents = []
        for agent_id in list(self.deployed_agents.keys()):
            status = await self.get_agent_status(agent_id)
            if status:
                agents.append(status)
        
        return agents
    
    async def _cleanup_failed_deployment(self, agent_id: str):
        """Cleanup resources after failed deployment"""
        
        try:
            # Remove from tracking if exists
            if agent_id in self.deployed_agents:
                deployment_info = self.deployed_agents[agent_id]
                
                # Try to stop container
                try:
                    container = self.docker_client.containers.get(deployment_info["container_id"])
                    container.stop(timeout=5)
                    container.remove()
                except Exception:
                    pass
                
                # Try to remove image
                try:
                    self.docker_client.images.remove(deployment_info["image_name"], force=True)
                except Exception:
                    pass
                
                del self.deployed_agents[agent_id]
            
            # Remove deployment directory
            deployment_dir = self.deployments_dir / f"agent_{agent_id}"
            if deployment_dir.exists():
                shutil.rmtree(deployment_dir)
                
        except Exception as e:
            logger.error("Failed to cleanup deployment", agent_id=agent_id, error=str(e))

    async def _create_docker_compose(self, deployment_dir: Path, backend_port: int, frontend_port: int, agent_id: str):
        """Create docker-compose.yml for full-stack deployment"""
        
        # Check if frontend files exist
        frontend_dir = deployment_dir / "frontend"
        has_frontend = frontend_dir.exists() and (frontend_dir / "package.json").exists()
        
        if has_frontend:
            # Full-stack compose with backend and frontend
            compose_content = f"""version: '3.8'
services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: backend_{agent_id}
    ports:
      - "{backend_port}:8000"
    environment:
      - NODE_ENV=production
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - agent_network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: frontend_{agent_id}
    ports:
      - "{frontend_port}:80"
    environment:
      - BACKEND_HOST=backend
      - BACKEND_PORT=8000
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - agent_network

networks:
  agent_network:
    driver: bridge
"""
        else:
            # Backend-only compose
            compose_content = f"""version: '3.8'
services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: backend_{agent_id}
    ports:
      - "{backend_port}:8000"
    environment:
      - NODE_ENV=production
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - agent_network

networks:
  agent_network:
    driver: bridge
"""
        
        compose_file = deployment_dir / "docker-compose.yml"
        with open(compose_file, 'w') as f:
            f.write(compose_content)
        
        logger.info("Created docker-compose.yml", 
                   agent_id=agent_id, 
                   has_frontend=has_frontend,
                   backend_port=backend_port,
                   frontend_port=frontend_port if has_frontend else None)

    async def _run_docker_compose(self, deployment_dir: Path, agent_id: str) -> Dict[str, Any]:
        """Build and run docker-compose services"""
        
        try:
            # Change to deployment directory for docker-compose commands
            original_cwd = os.getcwd()
            os.chdir(deployment_dir)
            
            # Stop any existing containers with same name
            stop_cmd = ["docker-compose", "down", "--remove-orphans"]
            process = await asyncio.create_subprocess_exec(
                *stop_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await process.communicate()
            
            # Build and start services
            build_cmd = ["docker-compose", "up", "-d", "--build"]
            process = await asyncio.create_subprocess_exec(
                *build_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                raise RuntimeError(f"Docker compose build failed: {stderr.decode()}")
            
            # Get service info
            ps_cmd = ["docker-compose", "ps", "--format", "json"]
            process = await asyncio.create_subprocess_exec(
                *ps_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            services = []
            if stdout:
                import json
                try:
                    services = json.loads(stdout.decode())
                    if not isinstance(services, list):
                        services = [services]
                except json.JSONDecodeError:
                    # Fallback for older docker-compose versions
                    services = []
            
            return {
                "status": "success",
                "services": services,
                "build_output": stdout.decode()
            }
            
        except Exception as e:
            logger.error("Docker compose deployment failed", agent_id=agent_id, error=str(e))
            raise
        finally:
            # Restore original working directory
            os.chdir(original_cwd)

# Global instance
docker_deployment = DockerDeploymentService()
