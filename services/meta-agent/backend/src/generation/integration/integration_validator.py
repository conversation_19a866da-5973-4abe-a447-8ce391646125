"""
Integration Validation System
Validates integration points during agent generation to prevent deployment issues
"""

import json
import re
import ast
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
import logging

@dataclass
class IntegrationIssue:
    """Represents an integration issue found during validation"""
    severity: str  # 'error', 'warning', 'info'
    category: str  # 'api', 'imports', 'config', 'types'
    message: str
    file_path: Optional[str] = None
    line_number: Optional[int] = None
    suggestion: Optional[str] = None

@dataclass
class IntegrationReport:
    """Report of integration validation results"""
    passed: bool
    issues: List[IntegrationIssue] = field(default_factory=list)
    summary: Dict[str, int] = field(default_factory=dict)
    
    def add_issue(self, issue: IntegrationIssue):
        """Add an issue to the report"""
        self.issues.append(issue)
        
        # Update summary
        if issue.severity not in self.summary:
            self.summary[issue.severity] = 0
        self.summary[issue.severity] += 1
        
        # Update passed status
        if issue.severity == 'error':
            self.passed = False

class IntegrationValidator:
    """
    Validates integration points between components in generated agents
    """
    
    def __init__(self, agent_path: Path):
        self.agent_path = Path(agent_path)
        self.logger = logging.getLogger(__name__)
        
        # Track discovered components
        self.backend_routes: Set[str] = set()
        self.frontend_api_calls: Set[str] = set()
        self.exports: Dict[str, Set[str]] = {}
        self.imports: Dict[str, Set[str]] = {}
        self.types: Dict[str, Dict[str, Any]] = {}
    
    def validate_all_integrations(self) -> IntegrationReport:
        """Run all integration validations"""
        report = IntegrationReport(passed=True)
        
        self.logger.info("Starting integration validation...")
        
        try:
            # Discover components
            self._discover_backend_routes()
            self._discover_frontend_api_calls()
            self._discover_imports_exports()
            self._discover_types()
            
            # Run validations
            self._validate_api_contracts(report)
            self._validate_import_export_consistency(report)
            self._validate_type_safety(report)
            self._validate_configuration_consistency(report)
            self._validate_cors_configuration(report)
            
        except Exception as e:
            report.add_issue(IntegrationIssue(
                severity='error',
                category='system',
                message=f"Integration validation failed: {str(e)}"
            ))
        
        self.logger.info(f"Integration validation completed. Passed: {report.passed}")
        return report
    
    def _discover_backend_routes(self):
        """Discover all routes defined in the backend"""
        app_py = self.agent_path / 'app.py'
        if not app_py.exists():
            return
        
        try:
            with open(app_py, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find all Flask routes
            route_pattern = r'@app\.route\(["\']([^"\']+)["\']'
            routes = re.findall(route_pattern, content)
            self.backend_routes.update(routes)
            
            self.logger.debug(f"Discovered backend routes: {self.backend_routes}")
            
        except Exception as e:
            self.logger.warning(f"Could not discover backend routes: {e}")
    
    def _discover_frontend_api_calls(self):
        """Discover all API calls made by the frontend"""
        frontend_src = self.agent_path / 'frontend' / 'src'
        if not frontend_src.exists():
            return
        
        try:
            # Scan all TypeScript/TSX files
            for file_path in frontend_src.rglob('*.ts*'):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Find API calls
                api_call_patterns = [
                    r'\.get\(["\']([^"\']+)["\']',
                    r'\.post\(["\']([^"\']+)["\']',
                    r'\.put\(["\']([^"\']+)["\']',
                    r'\.delete\(["\']([^"\']+)["\']'
                ]
                
                for pattern in api_call_patterns:
                    calls = re.findall(pattern, content)
                    self.frontend_api_calls.update(calls)
            
            self.logger.debug(f"Discovered frontend API calls: {self.frontend_api_calls}")
            
        except Exception as e:
            self.logger.warning(f"Could not discover frontend API calls: {e}")
    
    def _discover_imports_exports(self):
        """Discover all imports and exports in the frontend"""
        frontend_src = self.agent_path / 'frontend' / 'src'
        if not frontend_src.exists():
            return
        
        try:
            for file_path in frontend_src.rglob('*.ts*'):
                relative_path = str(file_path.relative_to(self.agent_path))
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Find exports
                exports = set()
                export_patterns = [
                    r'export\s+function\s+(\w+)',
                    r'export\s+const\s+(\w+)',
                    r'export\s+class\s+(\w+)',
                    r'export\s+interface\s+(\w+)',
                    r'export\s+{\s*([^}]+)\s*}',
                    r'export\s+default\s+(\w+)'
                ]
                
                for pattern in export_patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        if ',' in match:  # Handle multiple exports in braces
                            exports.update(name.strip() for name in match.split(','))
                        else:
                            exports.add(match)
                
                self.exports[relative_path] = exports
                
                # Find imports
                imports = set()
                import_patterns = [
                    r'import\s+{\s*([^}]+)\s*}\s+from',
                    r'import\s+(\w+)\s+from',
                    r'import\s+\*\s+as\s+(\w+)\s+from'
                ]
                
                for pattern in import_patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        if ',' in match:  # Handle multiple imports in braces
                            imports.update(name.strip() for name in match.split(','))
                        else:
                            imports.add(match)
                
                self.imports[relative_path] = imports
            
        except Exception as e:
            self.logger.warning(f"Could not discover imports/exports: {e}")
    
    def _discover_types(self):
        """Discover TypeScript type definitions"""
        api_ts = self.agent_path / 'frontend' / 'src' / 'services' / 'api.ts'
        if not api_ts.exists():
            return
        
        try:
            with open(api_ts, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find interface definitions
            interface_pattern = r'interface\s+(\w+)\s*{([^}]+)}'
            interfaces = re.findall(interface_pattern, content, re.DOTALL)
            
            for name, body in interfaces:
                # Parse interface properties (simplified)
                properties = {}
                prop_pattern = r'(\w+):\s*([^;]+)'
                props = re.findall(prop_pattern, body)
                
                for prop_name, prop_type in props:
                    properties[prop_name.strip()] = prop_type.strip()
                
                self.types[name] = properties
            
            self.logger.debug(f"Discovered types: {list(self.types.keys())}")
            
        except Exception as e:
            self.logger.warning(f"Could not discover types: {e}")
    
    def _validate_api_contracts(self, report: IntegrationReport):
        """Validate that frontend API calls have matching backend endpoints"""
        self.logger.debug("Validating API contracts...")
        
        # Check each frontend API call
        for api_call in self.frontend_api_calls:
            # Normalize the API call path
            normalized_call = api_call
            if not normalized_call.startswith('/'):
                normalized_call = '/' + normalized_call
            
            # Check if backend has a matching route
            route_found = False
            for route in self.backend_routes:
                if route == normalized_call or self._routes_match(route, normalized_call):
                    route_found = True
                    break
            
            if not route_found:
                report.add_issue(IntegrationIssue(
                    severity='error',
                    category='api',
                    message=f"Frontend API call '{api_call}' has no matching backend route",
                    suggestion=f"Add route @app.route('{normalized_call}') to backend"
                ))
        
        # Check for common required endpoints
        required_endpoints = ['/', '/health']
        for endpoint in required_endpoints:
            if endpoint not in self.backend_routes:
                report.add_issue(IntegrationIssue(
                    severity='error',
                    category='api',
                    message=f"Required endpoint '{endpoint}' is missing from backend",
                    suggestion=f"Add route @app.route('{endpoint}') to backend"
                ))
    
    def _validate_import_export_consistency(self, report: IntegrationReport):
        """Validate that all imports have corresponding exports"""
        self.logger.debug("Validating import/export consistency...")
        
        # Check critical files have proper exports
        critical_files = {
            'frontend/src/services/api.ts': ['ApiClient', 'apiClient', 'api'],
            'frontend/src/components/Header.tsx': ['Header'],
            'frontend/src/pages/HomePage.tsx': ['HomePage']
        }
        
        for file_path, required_exports in critical_files.items():
            file_exports = self.exports.get(file_path, set())
            
            for required_export in required_exports:
                if required_export not in file_exports:
                    report.add_issue(IntegrationIssue(
                        severity='error',
                        category='imports',
                        message=f"Required export '{required_export}' missing from {file_path}",
                        file_path=file_path,
                        suggestion=f"Add 'export {{ {required_export} }}' or 'export const {required_export}'"
                    ))
        
        # Check that API client has both default and named exports
        api_exports = self.exports.get('frontend/src/services/api.ts', set())
        if 'apiClient' in api_exports and 'default' not in str(api_exports):
            report.add_issue(IntegrationIssue(
                severity='warning',
                category='imports',
                message="API client should have both default and named exports for compatibility",
                file_path='frontend/src/services/api.ts',
                suggestion="Add 'export default apiClient'"
            ))
    
    def _validate_type_safety(self, report: IntegrationReport):
        """Validate TypeScript type definitions"""
        self.logger.debug("Validating type safety...")
        
        # Check that AgentInfo interface exists and has required properties
        if 'AgentInfo' in self.types:
            agent_info = self.types['AgentInfo']
            required_props = ['agent_id', 'name', 'type', 'status', 'capabilities']
            
            for prop in required_props:
                if prop not in agent_info:
                    report.add_issue(IntegrationIssue(
                        severity='warning',
                        category='types',
                        message=f"AgentInfo interface missing property '{prop}'",
                        file_path='frontend/src/services/api.ts',
                        suggestion=f"Add '{prop}: string' to AgentInfo interface"
                    ))
        else:
            report.add_issue(IntegrationIssue(
                severity='error',
                category='types',
                message="AgentInfo interface not found",
                file_path='frontend/src/services/api.ts',
                suggestion="Define AgentInfo interface with required properties"
            ))
    
    def _validate_configuration_consistency(self, report: IntegrationReport):
        """Validate configuration consistency between components"""
        self.logger.debug("Validating configuration consistency...")
        
        # Check Docker compose configuration
        compose_file = self.agent_path / 'docker-compose.yml'
        if compose_file.exists():
            try:
                import yaml
                with open(compose_file, 'r') as f:
                    compose_config = yaml.safe_load(f)
                
                services = compose_config.get('services', {})
                
                # Check that backend and frontend services exist
                required_services = ['backend', 'frontend']
                for service in required_services:
                    if service not in services:
                        report.add_issue(IntegrationIssue(
                            severity='error',
                            category='config',
                            message=f"Missing service '{service}' in docker-compose.yml",
                            file_path='docker-compose.yml',
                            suggestion=f"Add {service} service definition"
                        ))
                
                # Check port configurations
                if 'backend' in services and 'ports' in services['backend']:
                    backend_ports = services['backend']['ports']
                    if isinstance(backend_ports, list) and backend_ports:
                        port_mapping = backend_ports[0]
                        if ':8000' not in str(port_mapping):
                            report.add_issue(IntegrationIssue(
                                severity='warning',
                                category='config',
                                message="Backend port mapping may not match application configuration",
                                file_path='docker-compose.yml'
                            ))
                
            except Exception as e:
                report.add_issue(IntegrationIssue(
                    severity='warning',
                    category='config',
                    message=f"Could not validate docker-compose.yml: {e}",
                    file_path='docker-compose.yml'
                ))
    
    def _validate_cors_configuration(self, report: IntegrationReport):
        """Validate CORS configuration"""
        self.logger.debug("Validating CORS configuration...")
        
        app_py = self.agent_path / 'app.py'
        if app_py.exists():
            try:
                with open(app_py, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for CORS import
                if 'from flask_cors import CORS' not in content:
                    report.add_issue(IntegrationIssue(
                        severity='error',
                        category='config',
                        message="Missing CORS import in backend",
                        file_path='app.py',
                        suggestion="Add 'from flask_cors import CORS'"
                    ))
                
                # Check for CORS initialization
                if 'CORS(app' not in content:
                    report.add_issue(IntegrationIssue(
                        severity='error',
                        category='config',
                        message="CORS not initialized in backend",
                        file_path='app.py',
                        suggestion="Add 'CORS(app, origins=[\"http://localhost:3000\"])'"
                    ))
                
                # Check for frontend origin in CORS config
                if 'localhost:3000' not in content and 'localhost:${' not in content:
                    report.add_issue(IntegrationIssue(
                        severity='warning',
                        category='config',
                        message="CORS configuration may not allow frontend origin",
                        file_path='app.py',
                        suggestion="Ensure CORS allows frontend origin (typically localhost:3000)"
                    ))
                
            except Exception as e:
                report.add_issue(IntegrationIssue(
                    severity='warning',
                    category='config',
                    message=f"Could not validate CORS configuration: {e}",
                    file_path='app.py'
                ))
    
    def _routes_match(self, backend_route: str, frontend_call: str) -> bool:
        """Check if a backend route matches a frontend API call"""
        # Simple matching for now - could be enhanced with parameter matching
        return backend_route == frontend_call
    
    def generate_report_text(self, report: IntegrationReport) -> str:
        """Generate a human-readable report"""
        lines = []
        lines.append("="*60)
        lines.append("INTEGRATION VALIDATION REPORT")
        lines.append("="*60)
        lines.append("")
        
        # Summary
        lines.append(f"Overall Status: {'✅ PASSED' if report.passed else '❌ FAILED'}")
        lines.append(f"Total Issues: {len(report.issues)}")
        
        if report.summary:
            lines.append("")
            lines.append("Issue Summary:")
            for severity, count in report.summary.items():
                emoji = {'error': '❌', 'warning': '⚠️', 'info': 'ℹ️'}.get(severity, '•')
                lines.append(f"  {emoji} {severity.title()}: {count}")
        
        # Detailed issues
        if report.issues:
            lines.append("")
            lines.append("Detailed Issues:")
            lines.append("-" * 40)
            
            for i, issue in enumerate(report.issues, 1):
                emoji = {'error': '❌', 'warning': '⚠️', 'info': 'ℹ️'}.get(issue.severity, '•')
                lines.append(f"{i:2d}. {emoji} [{issue.category.upper()}] {issue.message}")
                
                if issue.file_path:
                    lines.append(f"     File: {issue.file_path}")
                if issue.line_number:
                    lines.append(f"     Line: {issue.line_number}")
                if issue.suggestion:
                    lines.append(f"     💡 Suggestion: {issue.suggestion}")
                lines.append("")
        
        return "\n".join(lines)

def validate_agent_integration(agent_path: str) -> Tuple[bool, str]:
    """
    Convenience function to validate agent integration and return results
    
    Returns:
        Tuple of (success: bool, report: str)
    """
    validator = IntegrationValidator(Path(agent_path))
    report = validator.validate_all_integrations()
    report_text = validator.generate_report_text(report)
    
    return report.passed, report_text

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python integration_validator.py <agent_path>")
        sys.exit(1)
    
    agent_path = sys.argv[1]
    success, report = validate_agent_integration(agent_path)
    
    print(report)
    sys.exit(0 if success else 1)