"""AI-powered agent generation workflow system.

This module implements an intelligent agent generation system where
multiple specialized agents collaborate to create, test, and deploy
new agents automatically.
"""

from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import asyncio
import json
import uuid
from datetime import datetime
from intelligence.gateway import AIGateway
from intelligence.providers.base import AIMessage
from utils.logging import get_logger, LoggerMixin
from utils.exceptions import AgentError, ValidationError

logger = get_logger(__name__)

class WorkflowStage(str, Enum):
    """Agent generation workflow stages."""
    REQUIREMENT_ANALYSIS = "requirement_analysis"
    ARCHITECTURE_DESIGN = "architecture_design"
    CODE_GENERATION = "code_generation"
    TESTING = "testing"
    INTEGRATION = "integration"
    DEPLOYMENT = "deployment"
    VALIDATION = "validation"
    COMPLETION = "completion"

@dataclass
class GenerationRequest:
    """Agent generation request."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    description: str = ""
    requirements: Dict[str, Any] = field(default_factory=dict)
    constraints: Dict[str, Any] = field(default_factory=dict)
    target_platform: str = "python"
    deployment_target: str = "kubernetes"
    created_at: datetime = field(default_factory=datetime.now)
    priority: str = "medium"

@dataclass
class WorkflowStep:
    """Workflow step definition."""
    stage: WorkflowStage
    agent_type: str
    prompt_template: str
    inputs: Dict[str, Any] = field(default_factory=dict)
    outputs: Dict[str, Any] = field(default_factory=dict)
    success_criteria: List[str] = field(default_factory=list)
    retry_count: int = 0
    max_retries: int = 3

@dataclass
class GenerationResult:
    """Agent generation result."""
    request_id: str
    success: bool
    generated_agent: Optional[Dict[str, Any]] = None
    artifacts: Dict[str, Any] = field(default_factory=dict)
    workflow_log: List[Dict[str, Any]] = field(default_factory=list)
    error: Optional[str] = None
    duration: float = 0.0

class AgentWorkflowGenerator(LoggerMixin):
    """AI-powered agent generation workflow orchestrator."""
    
    def __init__(self, ai_gateway: AIGateway):
        self.ai_gateway = ai_gateway
        self.active_workflows: Dict[str, Dict[str, Any]] = {}
        self.workflow_templates = self._initialize_workflow_templates()
        self.specialized_agents = self._initialize_specialized_agents()
    
    def _initialize_workflow_templates(self) -> Dict[str, List[WorkflowStep]]:
        """Initialize workflow templates for different agent types."""
        
        standard_workflow = [
            WorkflowStep(
                stage=WorkflowStage.REQUIREMENT_ANALYSIS,
                agent_type="requirement_analyst",
                prompt_template="""You are a Requirements Analyst AI specializing in agent specification analysis.

Task: Analyze the following agent request and create detailed, technical requirements.

Request: {description}
Additional Context: {requirements}

Please provide:
1. Functional Requirements (what the agent should do)
2. Non-Functional Requirements (performance, scalability, etc.)
3. Technical Specifications (APIs, data structures, dependencies)
4. Interface Definitions (inputs, outputs, protocols)
5. Success Criteria (how to measure completion)

Format your response as structured JSON with clear categorization.""",
                success_criteria=[
                    "functional_requirements_defined",
                    "technical_specs_complete",
                    "interfaces_specified"
                ]
            ),
            
            WorkflowStep(
                stage=WorkflowStage.ARCHITECTURE_DESIGN,
                agent_type="system_architect",
                prompt_template="""You are a System Architect AI specializing in scalable agent architecture design.

Based on the requirements analysis, design a comprehensive architecture.

Requirements: {requirements_analysis}
Target Platform: {target_platform}
Deployment: {deployment_target}

Please provide:
1. High-level Architecture (components, modules, layers)
2. Data Flow Diagrams (how information moves through the system)
3. API Design (endpoints, request/response formats)
4. Database Schema (if applicable)
5. Integration Points (external systems, protocols)
6. Scalability Considerations
7. Security Architecture

Format as structured JSON with detailed specifications.""",
                success_criteria=[
                    "architecture_defined",
                    "api_design_complete",
                    "data_flow_specified"
                ]
            ),
            
            WorkflowStep(
                stage=WorkflowStage.CODE_GENERATION,
                agent_type="code_generator",
                prompt_template="""You are a Senior Software Engineer AI specializing in production-ready code generation.

Generate complete, production-quality code based on the architecture design.

Architecture: {architecture_design}
Requirements: {requirements_analysis}
Platform: {target_platform}

Please generate:
1. Core Agent Implementation
2. API Handlers and Routes
3. Data Models and Schemas
4. Configuration Management
5. Error Handling and Logging
6. Input Validation and Security
7. Documentation and Comments

Requirements:
- Follow best practices and design patterns
- Include comprehensive error handling
- Add proper logging and monitoring hooks
- Ensure security and input validation
- Make code maintainable and testable

Format as structured JSON with files and complete code.""",
                success_criteria=[
                    "core_implementation_complete",
                    "api_handlers_generated",
                    "error_handling_implemented"
                ]
            ),
            
            WorkflowStep(
                stage=WorkflowStage.TESTING,
                agent_type="test_engineer",
                prompt_template="""You are a Test Engineer AI specializing in comprehensive test suite generation.

Create a complete test suite for the generated agent code.

Generated Code: {generated_code}
Requirements: {requirements_analysis}
Architecture: {architecture_design}

Please generate:
1. Unit Tests (individual functions/methods)
2. Integration Tests (component interactions)
3. API Tests (endpoint validation)
4. Performance Tests (load and stress)
5. Security Tests (input validation, auth)
6. End-to-End Tests (complete workflows)
7. Test Data and Fixtures
8. Test Configuration and Setup

Requirements:
- Achieve >90% code coverage
- Test both happy path and error cases
- Include performance benchmarks
- Validate security measures
- Ensure tests are maintainable

Format as structured JSON with test files and configurations.""",
                success_criteria=[
                    "unit_tests_complete",
                    "integration_tests_generated",
                    "coverage_target_met"
                ]
            ),
            
            WorkflowStep(
                stage=WorkflowStage.INTEGRATION,
                agent_type="integration_specialist",
                prompt_template="""You are an Integration Specialist AI focusing on system integration and compatibility.

Create integration configurations and ensure platform compatibility.

Generated Code: {generated_code}
Tests: {test_suite}
Platform Requirements: {target_platform}
Deployment Target: {deployment_target}

Please generate:
1. Docker Configuration (Dockerfile, docker-compose)
2. Kubernetes Manifests (deployment, service, configmap)
3. CI/CD Pipeline Configuration
4. Environment Configuration (dev, staging, prod)
5. Monitoring and Observability Setup
6. Service Discovery Configuration
7. Load Balancing Configuration
8. Security Configurations (RBAC, network policies)

Requirements:
- Ensure scalability and high availability
- Include comprehensive monitoring
- Configure proper security measures
- Enable easy deployment and rollback
- Support multiple environments

Format as structured JSON with configuration files.""",
                success_criteria=[
                    "docker_config_complete",
                    "k8s_manifests_generated",
                    "ci_cd_configured"
                ]
            ),
            
            WorkflowStep(
                stage=WorkflowStage.DEPLOYMENT,
                agent_type="deployment_engineer",
                prompt_template="""You are a Deployment Engineer AI specializing in automated deployment strategies.

Create deployment automation and validation procedures.

Integration Config: {integration_config}
All Artifacts: {all_artifacts}
Target Environment: {deployment_target}

Please generate:
1. Deployment Scripts (automated deployment)
2. Health Check Procedures
3. Rollback Strategies
4. Monitoring Alerts Configuration
5. Post-Deployment Validation
6. Performance Benchmarking
7. Security Scanning Configuration
8. Documentation and Runbooks

Requirements:
- Zero-downtime deployment capability
- Automated health checks and validation
- Comprehensive monitoring and alerting
- Easy rollback procedures
- Performance validation
- Security compliance verification

Format as structured JSON with scripts and procedures.""",
                success_criteria=[
                    "deployment_scripts_complete",
                    "health_checks_configured",
                    "monitoring_setup"
                ]
            )
        ]
        
        return {
            "standard": standard_workflow,
            "microservice": standard_workflow,  # Can be customized
            "data_processor": standard_workflow,  # Can be customized
            "api_gateway": standard_workflow,  # Can be customized
        }
    
    def _initialize_specialized_agents(self) -> Dict[str, Dict[str, Any]]:
        """Initialize configurations for specialized generation agents."""
        return {
            "requirement_analyst": {
                "model": "claude-3-5-sonnet-20241022",
                "temperature": 0.3,
                "max_tokens": 4000,
                "system_prompt": "You are an expert requirements analyst with deep experience in software agent design."
            },
            "system_architect": {
                "model": "gpt-4o",
                "temperature": 0.4,
                "max_tokens": 6000,
                "system_prompt": "You are a senior system architect with expertise in scalable, distributed systems."
            },
            "code_generator": {
                "model": "claude-3-5-sonnet-20241022",
                "temperature": 0.2,
                "max_tokens": 8000,
                "system_prompt": "You are a senior software engineer who writes clean, production-ready code."
            },
            "test_engineer": {
                "model": "gpt-4o",
                "temperature": 0.3,
                "max_tokens": 6000,
                "system_prompt": "You are a test engineering expert who creates comprehensive test suites."
            },
            "integration_specialist": {
                "model": "gpt-4o",
                "temperature": 0.3,
                "max_tokens": 4000,
                "system_prompt": "You are a DevOps expert specializing in containerization and orchestration."
            },
            "deployment_engineer": {
                "model": "claude-3-5-sonnet-20241022",
                "temperature": 0.2,
                "max_tokens": 4000,
                "system_prompt": "You are a deployment engineering expert focused on reliable, automated deployments."
            }
        }
    
    async def generate_agent(self, request: GenerationRequest) -> GenerationResult:
        """Generate an agent using AI-powered workflow."""
        
        self.log_operation(
            "agent_generation_started",
            request_id=request.id,
            description=request.description[:100]
        )
        
        start_time = datetime.now()
        workflow_log = []
        artifacts = {}
        
        try:
            # Select workflow template
            workflow_template = self.workflow_templates.get("standard")
            if not workflow_template:
                raise AgentError("No workflow template available")
            
            # Track workflow
            self.active_workflows[request.id] = {
                "request": request,
                "current_stage": WorkflowStage.REQUIREMENT_ANALYSIS,
                "artifacts": artifacts,
                "start_time": start_time
            }
            
            # Execute workflow stages
            context = {"request": request}
            
            for step in workflow_template:
                self.logger.info(f"Executing stage: {step.stage.value}")
                
                step_result = await self._execute_workflow_step(step, context, artifacts)
                
                workflow_log.append({
                    "stage": step.stage.value,
                    "timestamp": datetime.now().isoformat(),
                    "success": step_result["success"],
                    "output_size": len(str(step_result.get("output", ""))),
                    "duration": step_result.get("duration", 0)
                })
                
                if not step_result["success"]:
                    error_msg = f"Stage {step.stage.value} failed: {step_result.get('error', 'Unknown error')}"
                    self.log_error("workflow_stage_failed", Exception(error_msg))
                    
                    return GenerationResult(
                        request_id=request.id,
                        success=False,
                        error=error_msg,
                        workflow_log=workflow_log,
                        duration=(datetime.now() - start_time).total_seconds()
                    )
                
                # Update context with stage output
                context[step.stage.value] = step_result["output"]
                artifacts[step.stage.value] = step_result["output"]
                
                # Update workflow status
                self.active_workflows[request.id]["current_stage"] = step.stage
                self.active_workflows[request.id]["artifacts"] = artifacts
            
            # Create final agent definition
            generated_agent = await self._create_agent_definition(context, artifacts)
            
            # Complete workflow
            duration = (datetime.now() - start_time).total_seconds()
            
            result = GenerationResult(
                request_id=request.id,
                success=True,
                generated_agent=generated_agent,
                artifacts=artifacts,
                workflow_log=workflow_log,
                duration=duration
            )
            
            # Cleanup
            if request.id in self.active_workflows:
                del self.active_workflows[request.id]
            
            self.log_operation(
                "agent_generation_completed",
                request_id=request.id,
                duration=duration,
                stages_completed=len(workflow_log)
            )
            
            return result
            
        except Exception as e:
            self.log_error("agent_generation_failed", e, request_id=request.id)
            
            if request.id in self.active_workflows:
                del self.active_workflows[request.id]
            
            return GenerationResult(
                request_id=request.id,
                success=False,
                error=str(e),
                workflow_log=workflow_log,
                duration=(datetime.now() - start_time).total_seconds()
            )
    
    async def _execute_workflow_step(
        self,
        step: WorkflowStep,
        context: Dict[str, Any],
        artifacts: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute a single workflow step using specialized AI agent."""
        
        step_start = datetime.now()
        
        try:
            # Get agent configuration
            agent_config = self.specialized_agents.get(step.agent_type, {})
            if not agent_config:
                raise AgentError(f"Unknown agent type: {step.agent_type}")
            
            # Prepare prompt with context
            prompt_vars = {
                **context.get("request", {}).__dict__,
                **{k: v for k, v in context.items() if k != "request"}
            }
            
            prompt = step.prompt_template.format(**prompt_vars)
            
            # Create messages
            messages = [
                AIMessage(role="system", content=agent_config["system_prompt"]),
                AIMessage(role="user", content=prompt)
            ]
            
            # Execute with AI
            response = await self.ai_gateway.chat_completion(
                messages=messages,
                model=agent_config["model"],
                temperature=agent_config["temperature"],
                max_tokens=agent_config["max_tokens"]
            )
            
            # Validate response
            try:
                output = json.loads(response.content)
            except json.JSONDecodeError:
                # If not JSON, keep as text
                output = response.content
            
            # Check success criteria
            success = self._validate_step_output(step, output)
            
            duration = (datetime.now() - step_start).total_seconds()
            
            return {
                "success": success,
                "output": output,
                "duration": duration,
                "token_usage": response.usage
            }
            
        except Exception as e:
            duration = (datetime.now() - step_start).total_seconds()
            return {
                "success": False,
                "error": str(e),
                "duration": duration
            }
    
    def _validate_step_output(self, step: WorkflowStep, output: Any) -> bool:
        """Validate workflow step output against success criteria."""
        
        if not step.success_criteria:
            return True
        
        # Basic validation - check if output contains expected elements
        output_str = str(output).lower()
        
        for criterion in step.success_criteria:
            criterion_key = criterion.replace("_", " ")
            if criterion_key not in output_str and criterion not in output_str:
                return False
        
        return True
    
    async def _create_agent_definition(
        self,
        context: Dict[str, Any],
        artifacts: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create final agent definition from workflow artifacts."""
        
        requirements = artifacts.get(WorkflowStage.REQUIREMENT_ANALYSIS.value, {})
        architecture = artifacts.get(WorkflowStage.ARCHITECTURE_DESIGN.value, {})
        code = artifacts.get(WorkflowStage.CODE_GENERATION.value, {})
        tests = artifacts.get(WorkflowStage.TESTING.value, {})
        integration = artifacts.get(WorkflowStage.INTEGRATION.value, {})
        deployment = artifacts.get(WorkflowStage.DEPLOYMENT.value, {})
        
        agent_definition = {
            "id": str(uuid.uuid4()),
            "name": f"Generated Agent {context['request'].id[:8]}",
            "description": context["request"].description,
            "version": "1.0.0",
            "created_at": datetime.now().isoformat(),
            "metadata": {
                "generated_by": "ai_workflow_generator",
                "generation_request_id": context["request"].id,
                "target_platform": context["request"].target_platform,
                "deployment_target": context["request"].deployment_target
            },
            "requirements": requirements,
            "architecture": architecture,
            "implementation": code,
            "tests": tests,
            "integration": integration,
            "deployment": deployment,
            "capabilities": self._extract_capabilities(artifacts),
            "interfaces": self._extract_interfaces(artifacts),
            "dependencies": self._extract_dependencies(artifacts)
        }
        
        return agent_definition
    
    def _extract_capabilities(self, artifacts: Dict[str, Any]) -> List[str]:
        """Extract agent capabilities from artifacts."""
        capabilities = []
        
        # Extract from requirements
        requirements = artifacts.get(WorkflowStage.REQUIREMENT_ANALYSIS.value, {})
        if isinstance(requirements, dict):
            functional_reqs = requirements.get("functional_requirements", [])
            if isinstance(functional_reqs, list):
                capabilities.extend(functional_reqs)
        
        return capabilities[:10]  # Limit to top 10
    
    def _extract_interfaces(self, artifacts: Dict[str, Any]) -> Dict[str, Any]:
        """Extract agent interfaces from artifacts."""
        interfaces = {}
        
        # Extract from architecture
        architecture = artifacts.get(WorkflowStage.ARCHITECTURE_DESIGN.value, {})
        if isinstance(architecture, dict):
            api_design = architecture.get("api_design", {})
            if isinstance(api_design, dict):
                interfaces["api"] = api_design
        
        return interfaces
    
    def _extract_dependencies(self, artifacts: Dict[str, Any]) -> List[str]:
        """Extract agent dependencies from artifacts."""
        dependencies = []
        
        # Extract from code generation
        code = artifacts.get(WorkflowStage.CODE_GENERATION.value, {})
        if isinstance(code, dict):
            deps = code.get("dependencies", [])
            if isinstance(deps, list):
                dependencies.extend(deps)
        
        return dependencies
    
    def get_workflow_status(self, request_id: str) -> Optional[Dict[str, Any]]:
        """Get current workflow status."""
        workflow = self.active_workflows.get(request_id)
        if not workflow:
            return None
        
        return {
            "request_id": request_id,
            "current_stage": workflow["current_stage"].value,
            "start_time": workflow["start_time"].isoformat(),
            "duration": (datetime.now() - workflow["start_time"]).total_seconds(),
            "artifacts_count": len(workflow["artifacts"])
        }
    
    def list_active_workflows(self) -> List[Dict[str, Any]]:
        """List all active workflows."""
        return [
            self.get_workflow_status(request_id)
            for request_id in self.active_workflows.keys()
        ]