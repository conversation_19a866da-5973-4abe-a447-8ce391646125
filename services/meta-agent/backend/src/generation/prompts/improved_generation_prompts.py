"""
Improved Code Generation Prompts
Addresses root causes of agent generation issues through better prompts
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass

@dataclass
class PromptTemplate:
    """Template for generation prompts with validation"""
    name: str
    description: str
    template: str
    validation_rules: List[str]
    examples: List[Dict[str, Any]]

class ImprovedGenerationPrompts:
    """
    Improved prompts that prevent common generation issues:
    - Missing files
    - Incorrect dependencies
    - Import/export mismatches
    - Build failures
    """
    
    @staticmethod
    def get_fullstack_web_agent_prompt() -> PromptTemplate:
        """
        Enhanced prompt for fullstack web agent generation
        Addresses all identified issues from previous deployments
        """
        return PromptTemplate(
            name="fullstack_web_agent_enhanced",
            description="Enhanced fullstack web agent generation with comprehensive validation",
            template="""
# Enhanced Full-Stack Web Agent Generation

You are generating a production-ready full-stack web agent. Follow these critical requirements:

## CRITICAL REQUIREMENTS (MUST FOLLOW):

### File Structure Requirements:
1. **Backend Files (Required)**:
   - `app.py` - Flask application with proper routes
   - `requirements.txt` - Pinned Python dependencies
   - `Dockerfile` - Multi-stage Docker build
   - `.env` - Environment configuration

2. **Frontend Files (Required)**:
   - `frontend/package.json` - NPM dependencies (must include preact, preact-router, axios)
   - `frontend/vite.config.ts` - Vite configuration with proxy
   - `frontend/tsconfig.json` - TypeScript config with jsx: react-jsx and jsxImportSource: preact
   - `frontend/src/main.tsx` - Entry point
   - `frontend/src/App.tsx` - Main application component
   - `frontend/src/services/api.ts` - API client with BOTH default and named exports
   - `frontend/src/components/Header.tsx` - Navigation component
   - `frontend/src/pages/HomePage.tsx` - Main dashboard page
   - `frontend/index.html` - HTML template
   - `frontend/tailwind.config.js` - Tailwind configuration
   - `frontend/postcss.config.js` - PostCSS configuration

3. **Docker Files (Required)**:
   - `docker-compose.yml` - Multi-service orchestration
   - `frontend/Dockerfile` - Frontend build and nginx serving
   - `frontend/nginx.conf` - Nginx configuration with API proxy

### Code Quality Requirements:

#### Backend (Flask):
```python
# app.py MUST include:
from flask import Flask, request, jsonify
from flask_cors import CORS
import os
from datetime import datetime

app = Flask(__name__)
CORS(app, origins=["http://localhost:3000"])  # CORS is CRITICAL

@app.route("/", methods=["GET"])  # Root endpoint REQUIRED
def get_agent_info():
    return jsonify({{
        "agent_id": "{agent_id}",
        "name": "{agent_name}",
        "type": "{agent_type}",
        "status": "running",
        "capabilities": {capabilities}
    }})

@app.route("/health", methods=["GET"])  # Health check REQUIRED
def health():
    return jsonify({{"status": "healthy", "timestamp": datetime.now().isoformat()}})

if __name__ == "__main__":
    port = int(os.environ.get("PORT", 8000))
    app.run(host="0.0.0.0", port=port)
```

#### Frontend API Client:
```typescript
// frontend/src/services/api.ts MUST include BOTH exports:
export class ApiClient {{
    // ... implementation
}}

export const apiClient = new ApiClient()
export default apiClient  // DEFAULT export
export const api = apiClient  // NAMED export alias
```

#### Frontend Components:
```typescript
// EVERY component MUST have BOTH exports:
export function ComponentName() {{
    // ... implementation
}}

export {{ ComponentName }}  // NAMED export
export default ComponentName  // DEFAULT export
```

### Dependencies Requirements:

#### Python (requirements.txt):
```
Flask==2.3.3
flask-cors==4.0.0
python-dotenv==1.0.0
gunicorn==21.2.0
```

#### Frontend (package.json dependencies):
```json
{{
  "dependencies": {{
    "preact": "10.19.3",
    "preact-router": "4.1.2",
    "axios": "1.6.7"
  }}
}}
```

### Build Validation Requirements:
1. All files must be syntactically correct
2. All imports must resolve to existing exports
3. TypeScript config must be compatible with Preact
4. Docker files must be valid
5. All required routes and endpoints must exist

## Agent Specification:
- Agent ID: {agent_id}
- Agent Name: {agent_name}
- Agent Type: {agent_type}
- Description: {description}
- Capabilities: {capabilities}
- Backend Port: {backend_port}
- Frontend Port: {frontend_port}

## Custom Configuration:
{custom_config}

## VALIDATION CHECKLIST (Verify each item):
□ All required files are generated
□ Flask app has CORS enabled
□ Root endpoint (/) returns agent info
□ Health endpoint (/health) exists
□ API client has both default and named exports
□ All components have both export types
□ TypeScript config has correct jsx settings
□ Package.json has all required dependencies
□ Docker compose file defines both services
□ All imports resolve to existing exports

## CRITICAL SUCCESS FACTORS:
1. **NO MISSING FILES** - Every file in the requirements must be generated
2. **CORRECT EXPORTS** - Use both default and named exports for compatibility
3. **PROPER CORS** - Backend must allow frontend origin
4. **VALID SYNTAX** - All generated code must be syntactically correct
5. **COMPLETE DEPENDENCIES** - All required packages must be included

Generate the complete agent following these requirements exactly.
""",
            validation_rules=[
                "All required files must be present",
                "Flask app must have CORS configuration",
                "API client must have both default and named exports",
                "Components must have both export types",
                "TypeScript config must be Preact-compatible",
                "All dependencies must be properly pinned",
                "Docker files must be valid",
                "All imports must resolve"
            ],
            examples=[
                {
                    "name": "Customer Service Agent",
                    "agent_type": "customer_service",
                    "capabilities": ["conversation", "ticket_management", "api_integration"],
                    "custom_config": {
                        "api_endpoints": [
                            {"path": "/api/tickets", "methods": ["GET", "POST"]},
                            {"path": "/api/customers", "methods": ["GET"]}
                        ],
                        "frontend_pages": [
                            {"name": "Tickets", "path": "/tickets", "component": "TicketsPage"},
                            {"name": "Customers", "path": "/customers", "component": "CustomersPage"}
                        ]
                    }
                }
            ]
        )
    
    @staticmethod
    def get_dependency_resolution_prompt() -> PromptTemplate:
        """Prompt for resolving dependency conflicts during generation"""
        return PromptTemplate(
            name="dependency_resolution",
            description="Resolves dependency conflicts and ensures compatibility",
            template="""
# Dependency Resolution and Compatibility Check

Analyze the agent requirements and resolve any dependency conflicts:

## Agent Requirements:
- Language: {language}
- Framework: {framework}
- Capabilities: {capabilities}
- Additional Dependencies: {additional_deps}

## Compatibility Rules:
1. **Python Flask Projects**:
   - Flask >= 2.3.0
   - flask-cors for CORS handling
   - gunicorn for production serving
   - python-dotenv for environment management

2. **Frontend Preact Projects**:
   - Preact 10.x for modern features
   - preact-router for routing
   - axios for HTTP requests
   - Vite 5.x for building
   - TypeScript 5.x for type safety

3. **Version Compatibility**:
   - Node.js 18+ required for Vite 5.x
   - Python 3.9+ required for Flask 2.3+
   - Use exact versions to prevent conflicts

## Conflict Resolution Strategy:
1. Identify conflicting dependencies
2. Find compatible versions
3. Update to latest stable versions when possible
4. Pin exact versions to prevent drift

Provide the resolved dependency list with justification for any changes.
""",
            validation_rules=[
                "No conflicting dependency versions",
                "All dependencies are compatible with target runtime",
                "Versions are pinned to prevent drift",
                "Security vulnerabilities are addressed"
            ],
            examples=[]
        )
    
    @staticmethod
    def get_integration_validation_prompt() -> PromptTemplate:
        """Prompt for validating integration between components"""
        return PromptTemplate(
            name="integration_validation",
            description="Validates integration points between components",
            template="""
# Integration Validation and Contract Verification

Validate all integration points in the generated agent:

## Integration Points to Validate:

### 1. Frontend-Backend API Contract:
- Verify API endpoints match frontend expectations
- Check request/response data structures
- Validate error handling consistency
- Ensure CORS configuration allows frontend origin

### 2. Import/Export Consistency:
- All imports resolve to existing exports
- Both default and named exports are available where needed
- Module paths are correct and consistent
- No circular dependencies exist

### 3. Configuration Alignment:
- Port configurations match between services
- Environment variables are consistently used
- Docker networking is properly configured
- Health check endpoints are accessible

### 4. Type Safety:
- TypeScript interfaces match API responses
- Component props are properly typed
- No 'any' types unless necessary
- Import paths use correct type exports

## Validation Checklist:
□ API endpoints exist in backend for all frontend calls
□ Response types match TypeScript interfaces
□ All imports can be resolved
□ CORS allows frontend origin
□ Docker services can communicate
□ Health checks are properly configured
□ Environment variables are consistent
□ No type errors exist

## Agent Components:
{agent_components}

Verify all integration points and report any issues found.
""",
            validation_rules=[
                "All API calls have matching backend endpoints",
                "Type definitions match API responses",
                "All imports resolve successfully",
                "CORS configuration allows required origins",
                "Docker services can communicate",
                "No circular dependencies exist"
            ],
            examples=[]
        )
    
    @staticmethod
    def get_error_prevention_prompt() -> PromptTemplate:
        """Prompt specifically designed to prevent common errors"""
        return PromptTemplate(
            name="error_prevention",
            description="Prevents common code generation errors",
            template="""
# Error Prevention and Quality Assurance

Follow these rules to prevent common generation errors:

## CRITICAL ERROR PREVENTION:

### 1. Missing File Prevention:
- Generate ALL files in the requirements list
- Use file separators correctly in templates
- Verify directory structure is complete
- Include all configuration files

### 2. Import/Export Error Prevention:
```typescript
// CORRECT - Provide both export types:
export function MyComponent() {{ ... }}
export {{ MyComponent }}
export default MyComponent

// INCORRECT - Only one export type:
export default function MyComponent() {{ ... }}  // Missing named export
```

### 3. CORS Error Prevention:
```python
# CORRECT - Properly configured CORS:
from flask_cors import CORS
app = Flask(__name__)
CORS(app, origins=["http://localhost:3000"])

# INCORRECT - Missing or misconfigured CORS:
app = Flask(__name__)  # Missing CORS entirely
```

### 4. Dependency Error Prevention:
- Pin ALL dependency versions
- Include ALL required dependencies
- Check compatibility between packages
- Use tested version combinations

### 5. TypeScript Configuration Prevention:
```json
// CORRECT - Preact-compatible config:
{{
  "compilerOptions": {{
    "jsx": "react-jsx",
    "jsxImportSource": "preact"
  }}
}}

// INCORRECT - React config for Preact:
{{
  "compilerOptions": {{
    "jsx": "react-jsx"  // Missing jsxImportSource
  }}
}}
```

### 6. Build Error Prevention:
- Validate all file syntax
- Ensure all imports resolve
- Check that build tools are configured correctly
- Verify Docker files are valid

## Common Error Patterns to Avoid:
1. Missing requirements.txt or package.json
2. Incorrect import statements
3. Missing CORS configuration
4. Incompatible TypeScript settings
5. Broken Docker configurations
6. Missing health check endpoints
7. Incorrect port configurations
8. Circular dependencies

## Quality Gates:
Before completing generation, verify:
□ All files compile/parse successfully
□ All imports resolve to existing exports
□ CORS is properly configured
□ All required endpoints exist
□ TypeScript config is correct
□ Dependencies are complete and compatible
□ Docker files are valid

Generate code that passes all these quality gates.
""",
            validation_rules=[
                "No syntax errors in generated code",
                "All imports resolve successfully",
                "CORS is properly configured",
                "All dependencies are included",
                "TypeScript configuration is correct",
                "Docker files are valid",
                "No circular dependencies"
            ],
            examples=[]
        )

def get_prompt_for_agent_type(agent_type: str, **kwargs) -> str:
    """
    Get the appropriate generation prompt for the specified agent type
    """
    prompts = ImprovedGenerationPrompts()
    
    if agent_type in ["fullstack_web", "fullstack"]:
        template = prompts.get_fullstack_web_agent_prompt()
        return template.template.format(**kwargs)
    
    # Default to fullstack web agent prompt for now
    template = prompts.get_fullstack_web_agent_prompt()
    return template.template.format(**kwargs)

def validate_generation_requirements(agent_config: Dict[str, Any]) -> List[str]:
    """
    Validate that the agent configuration has all required fields
    """
    required_fields = [
        "agent_id", "agent_name", "agent_type", "description", 
        "capabilities", "backend_port", "frontend_port"
    ]
    
    missing_fields = []
    for field in required_fields:
        if field not in agent_config:
            missing_fields.append(field)
    
    return missing_fields

def get_validation_checklist(agent_type: str) -> List[str]:
    """
    Get the validation checklist for the specified agent type
    """
    prompts = ImprovedGenerationPrompts()
    
    if agent_type in ["fullstack_web", "fullstack"]:
        template = prompts.get_fullstack_web_agent_prompt()
        return template.validation_rules
    
    return [
        "All required files are present",
        "All imports resolve successfully", 
        "Configuration is valid",
        "No syntax errors exist"
    ]