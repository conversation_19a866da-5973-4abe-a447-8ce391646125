#!/usr/bin/env python3
"""
Comprehensive Agent Generation Validation System
Validates generated agents before deployment to prevent common issues
"""

import os
import json
import subprocess
import sys
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import logging
import yaml

class AgentValidationError(Exception):
    """Custom exception for agent validation errors"""
    pass

class ComprehensiveAgentValidator:
    """
    Validates generated agents across multiple dimensions:
    - File structure completeness
    - Dependency consistency
    - Build compatibility 
    - Import/export correctness
    - Configuration validity
    """
    
    def __init__(self, agent_path: str, config: Optional[Dict] = None):
        self.agent_path = Path(agent_path)
        self.config = config or {}
        self.errors = []
        self.warnings = []
        self.test_results = {}
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def validate_all(self) -> bool:
        """Run complete validation suite"""
        self.logger.info(f"🔍 Starting comprehensive validation for: {self.agent_path}")
        
        try:
            # Core validations
            self._validate_file_structure()
            self._validate_backend()
            self._validate_frontend()
            self._validate_dependencies()
            self._validate_configuration()
            
            # Advanced validations
            self._validate_build_process()
            self._validate_import_exports()
            self._validate_api_contracts()
            
            # Integration tests
            self._run_integration_tests()
            
        except Exception as e:
            self.errors.append(f"Validation framework error: {str(e)}")
        
        return self._report_results()
    
    def _validate_file_structure(self):
        """Validate all required files exist with correct structure"""
        self.logger.info("📁 Validating file structure...")
        
        # Backend files
        backend_files = [
            'app.py', 'requirements.txt', 'Dockerfile', '.env'
        ]
        
        # Frontend files
        frontend_files = [
            'frontend/package.json',
            'frontend/vite.config.ts',
            'frontend/tsconfig.json',
            'frontend/src/main.tsx',
            'frontend/src/App.tsx',
            'frontend/src/services/api.ts',
            'frontend/src/components/Header.tsx',
            'frontend/src/pages/HomePage.tsx',
            'frontend/index.html',
            'frontend/tailwind.config.js',
            'frontend/postcss.config.js'
        ]
        
        # Docker files
        docker_files = [
            'docker-compose.yml',
            'frontend/Dockerfile',
            'frontend/nginx.conf'
        ]
        
        all_files = backend_files + frontend_files + docker_files
        
        for file_path in all_files:
            full_path = self.agent_path / file_path
            if not full_path.exists():
                self.errors.append(f"Missing required file: {file_path}")
            else:
                self.logger.debug(f"✅ Found: {file_path}")
    
    def _validate_backend(self):
        """Validate Python backend configuration and syntax"""
        self.logger.info("🐍 Validating Python backend...")
        
        # Validate app.py
        app_py = self.agent_path / 'app.py'
        if app_py.exists():
            try:
                with open(app_py, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check required imports
                required_imports = ['Flask', 'CORS', 'jsonify', 'request']
                for imp in required_imports:
                    if imp not in content:
                        self.errors.append(f"Missing import in app.py: {imp}")
                
                # Check required routes
                required_routes = [
                    '@app.route("/", methods=["GET"])',
                    '@app.route("/health", methods=["GET"])'
                ]
                for route in required_routes:
                    if route not in content:
                        self.errors.append(f"Missing route in app.py: {route}")
                
                # Check CORS configuration
                if 'CORS(app' not in content:
                    self.errors.append("Missing CORS configuration in app.py")
                
                # Check if main execution block exists
                if 'if __name__ == "__main__"' not in content:
                    self.warnings.append("Missing main execution block in app.py")
                
            except Exception as e:
                self.errors.append(f"Error reading app.py: {e}")
        
        # Validate requirements.txt
        req_file = self.agent_path / 'requirements.txt'
        if req_file.exists():
            try:
                with open(req_file, 'r', encoding='utf-8') as f:
                    deps = f.read()
                
                required_deps = ['Flask', 'flask-cors', 'python-dotenv', 'gunicorn']
                for dep in required_deps:
                    if dep not in deps:
                        self.errors.append(f"Missing Python dependency: {dep}")
                
                # Check for version pinning
                lines = deps.strip().split('\n')
                for line in lines:
                    if line and '==' not in line and '>' not in line and '<' not in line:
                        self.warnings.append(f"Unpinned dependency: {line}")
                        
            except Exception as e:
                self.errors.append(f"Error reading requirements.txt: {e}")
    
    def _validate_frontend(self):
        """Validate frontend configuration and dependencies"""
        self.logger.info("⚛️ Validating frontend configuration...")
        
        # Validate package.json
        package_json = self.agent_path / 'frontend' / 'package.json'
        if package_json.exists():
            try:
                with open(package_json, 'r', encoding='utf-8') as f:
                    pkg = json.load(f)
                
                # Check required dependencies
                deps = pkg.get('dependencies', {})
                required_deps = ['preact', 'preact-router', 'axios']
                for dep in required_deps:
                    if dep not in deps:
                        self.errors.append(f"Missing frontend dependency: {dep}")
                
                # Check required scripts
                scripts = pkg.get('scripts', {})
                required_scripts = ['dev', 'build', 'preview']
                for script in required_scripts:
                    if script not in scripts:
                        self.errors.append(f"Missing npm script: {script}")
                
                # Check if name follows convention
                name = pkg.get('name', '')
                if not name or ' ' in name:
                    self.warnings.append("Package name should follow npm conventions")
                
            except json.JSONDecodeError as e:
                self.errors.append(f"Invalid JSON in package.json: {e}")
            except Exception as e:
                self.errors.append(f"Error reading package.json: {e}")
        
        # Validate TypeScript configuration
        tsconfig = self.agent_path / 'frontend' / 'tsconfig.json'
        if tsconfig.exists():
            try:
                with open(tsconfig, 'r', encoding='utf-8') as f:
                    ts_config = json.load(f)
                
                compiler_options = ts_config.get('compilerOptions', {})
                
                # Check critical TypeScript settings
                critical_settings = {
                    'jsx': 'react-jsx',
                    'jsxImportSource': 'preact',
                    'moduleResolution': 'bundler'
                }
                
                for setting, expected in critical_settings.items():
                    if compiler_options.get(setting) != expected:
                        self.errors.append(f"TypeScript config: {setting} should be '{expected}'")
                
                # Check path aliases
                paths = compiler_options.get('paths', {})
                if '@/*' not in paths:
                    self.warnings.append("Missing path alias '@/*' in TypeScript config")
                
            except json.JSONDecodeError as e:
                self.errors.append(f"Invalid JSON in tsconfig.json: {e}")
            except Exception as e:
                self.errors.append(f"Error reading tsconfig.json: {e}")
    
    def _validate_dependencies(self):
        """Validate import/export consistency and dependency compatibility"""
        self.logger.info("🔗 Validating dependencies and imports...")
        
        # Validate API service exports
        api_ts = self.agent_path / 'frontend' / 'src' / 'services' / 'api.ts'
        if api_ts.exists():
            try:
                with open(api_ts, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for proper exports
                exports_to_check = [
                    'export default apiClient',
                    'export const apiClient',
                    'export class ApiClient'
                ]
                
                for export in exports_to_check:
                    if export not in content:
                        self.errors.append(f"Missing export in api.ts: {export}")
                
                # Check for required imports
                required_imports = ['axios', 'AxiosInstance']
                for imp in required_imports:
                    if imp not in content:
                        self.errors.append(f"Missing import in api.ts: {imp}")
                
            except Exception as e:
                self.errors.append(f"Error reading api.ts: {e}")
        
        # Validate component exports
        header_tsx = self.agent_path / 'frontend' / 'src' / 'components' / 'Header.tsx'
        if header_tsx.exists():
            try:
                with open(header_tsx, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for both named and default exports
                if 'export function Header' not in content and 'export const Header' not in content:
                    self.errors.append("Missing named export for Header component")
                
                if 'export default Header' not in content:
                    self.errors.append("Missing default export for Header component")
                
            except Exception as e:
                self.errors.append(f"Error reading Header.tsx: {e}")
    
    def _validate_configuration(self):
        """Validate configuration files and environment setup"""
        self.logger.info("⚙️ Validating configuration...")
        
        # Validate Docker configuration
        dockerfile = self.agent_path / 'Dockerfile'
        if dockerfile.exists():
            try:
                with open(dockerfile, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for required Docker instructions
                required_instructions = ['FROM', 'WORKDIR', 'COPY', 'RUN', 'EXPOSE', 'CMD']
                for instruction in required_instructions:
                    if instruction not in content:
                        self.errors.append(f"Missing Docker instruction: {instruction}")
                
                # Check for security best practices
                if 'USER agent' not in content and 'USER ' not in content:
                    self.warnings.append("Dockerfile should run as non-root user")
                
                # Check for health check
                if 'HEALTHCHECK' not in content:
                    self.warnings.append("Missing health check in Dockerfile")
                
            except Exception as e:
                self.errors.append(f"Error reading Dockerfile: {e}")
        
        # Validate docker-compose.yml
        compose_file = self.agent_path / 'docker-compose.yml'
        if compose_file.exists():
            try:
                with open(compose_file, 'r', encoding='utf-8') as f:
                    compose_config = yaml.safe_load(f)
                
                # Check required services
                services = compose_config.get('services', {})
                required_services = ['backend', 'frontend']
                for service in required_services:
                    if service not in services:
                        self.errors.append(f"Missing service in docker-compose.yml: {service}")
                
                # Check service configuration
                if 'backend' in services:
                    backend = services['backend']
                    if 'healthcheck' not in backend:
                        self.warnings.append("Backend service missing health check")
                    if 'ports' not in backend:
                        self.errors.append("Backend service missing port mapping")
                
            except yaml.YAMLError as e:
                self.errors.append(f"Invalid YAML in docker-compose.yml: {e}")
            except Exception as e:
                self.errors.append(f"Error reading docker-compose.yml: {e}")
    
    def _validate_build_process(self):
        """Test that the agent can be built successfully"""
        self.logger.info("🔨 Validating build process...")
        
        # Test backend build (syntax check)
        try:
            app_py = self.agent_path / 'app.py'
            if app_py.exists():
                result = subprocess.run([
                    sys.executable, '-m', 'py_compile', str(app_py)
                ], capture_output=True, text=True, timeout=30)
                
                if result.returncode != 0:
                    self.errors.append(f"Python syntax error in app.py: {result.stderr}")
                else:
                    self.test_results['backend_syntax'] = 'PASS'
        except subprocess.TimeoutExpired:
            self.warnings.append("Backend syntax check timed out")
        except Exception as e:
            self.warnings.append(f"Could not run backend syntax check: {e}")
        
        # Test frontend build (if npm is available)
        frontend_path = self.agent_path / 'frontend'
        if frontend_path.exists() and (frontend_path / 'package.json').exists():
            try:
                # Check if npm is available
                npm_check = subprocess.run(['npm', '--version'], capture_output=True, timeout=10)
                if npm_check.returncode == 0:
                    # Test npm install (dry run)
                    result = subprocess.run([
                        'npm', 'install', '--dry-run'
                    ], cwd=frontend_path, capture_output=True, text=True, timeout=60)
                    
                    if result.returncode != 0:
                        self.warnings.append(f"Frontend dependency issues: {result.stderr}")
                    else:
                        self.test_results['frontend_deps'] = 'PASS'
                else:
                    self.warnings.append("npm not available for frontend validation")
            except (subprocess.TimeoutExpired, FileNotFoundError):
                self.warnings.append("Could not validate frontend build process")
            except Exception as e:
                self.warnings.append(f"Frontend build validation error: {e}")
    
    def _validate_import_exports(self):
        """Validate that all imports and exports are consistent"""
        self.logger.info("📦 Validating import/export consistency...")
        
        frontend_src = self.agent_path / 'frontend' / 'src'
        if not frontend_src.exists():
            return
        
        # Track all exports and imports
        exports = {}
        imports = {}
        
        # Scan all TypeScript/TSX files
        for file_path in frontend_src.rglob('*.ts*'):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Find exports
                if 'export' in content:
                    exports[str(file_path.relative_to(self.agent_path))] = content.count('export')
                
                # Find imports
                if 'import' in content:
                    imports[str(file_path.relative_to(self.agent_path))] = content.count('import')
                
            except Exception as e:
                self.warnings.append(f"Could not scan {file_path}: {e}")
        
        self.test_results['files_scanned'] = len(exports) + len(imports)
        
        # Validate critical files have exports
        critical_files = [
            'frontend/src/services/api.ts',
            'frontend/src/components/Header.tsx',
            'frontend/src/pages/HomePage.tsx'
        ]
        
        for file_path in critical_files:
            if file_path not in exports:
                self.errors.append(f"Critical file missing exports: {file_path}")
    
    def _validate_api_contracts(self):
        """Validate API contracts between frontend and backend"""
        self.logger.info("🔌 Validating API contracts...")
        
        # Check if api.ts defines the expected AgentInfo interface
        api_ts = self.agent_path / 'frontend' / 'src' / 'services' / 'api.ts'
        if api_ts.exists():
            try:
                with open(api_ts, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for AgentInfo interface
                if 'interface AgentInfo' not in content:
                    self.errors.append("Missing AgentInfo interface in api.ts")
                
                # Check for required API methods
                required_methods = ['getAgentInfo', 'checkHealth']
                for method in required_methods:
                    if method not in content:
                        self.errors.append(f"Missing API method: {method}")
                
            except Exception as e:
                self.errors.append(f"Error validating API contracts: {e}")
    
    def _run_integration_tests(self):
        """Run basic integration tests if possible"""
        self.logger.info("🧪 Running integration tests...")
        
        # Test if Docker files are valid
        compose_file = self.agent_path / 'docker-compose.yml'
        if compose_file.exists():
            try:
                # Validate docker-compose syntax
                result = subprocess.run([
                    'docker-compose', '-f', str(compose_file), 'config'
                ], capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0:
                    self.test_results['docker_compose_syntax'] = 'PASS'
                else:
                    self.warnings.append(f"Docker compose validation failed: {result.stderr}")
                    
            except (subprocess.TimeoutExpired, FileNotFoundError):
                self.warnings.append("Docker not available for validation")
            except Exception as e:
                self.warnings.append(f"Docker integration test error: {e}")
    
    def _report_results(self) -> bool:
        """Generate comprehensive validation report"""
        print("\n" + "="*70)
        print("🔍 COMPREHENSIVE AGENT VALIDATION REPORT")
        print("="*70)
        
        # Summary
        total_issues = len(self.errors) + len(self.warnings)
        print(f"\n📊 SUMMARY:")
        print(f"   Agent Path: {self.agent_path}")
        print(f"   Total Issues: {total_issues}")
        print(f"   Errors: {len(self.errors)}")
        print(f"   Warnings: {len(self.warnings)}")
        
        # Test results
        if self.test_results:
            print(f"\n🧪 TEST RESULTS:")
            for test, result in self.test_results.items():
                status = "✅" if result == 'PASS' else "ℹ️"
                print(f"   {status} {test}: {result}")
        
        # Errors
        if self.errors:
            print(f"\n❌ ERRORS ({len(self.errors)}):")
            for i, error in enumerate(self.errors, 1):
                print(f"   {i:2d}. {error}")
        
        # Warnings
        if self.warnings:
            print(f"\n⚠️  WARNINGS ({len(self.warnings)}):")
            for i, warning in enumerate(self.warnings, 1):
                print(f"   {i:2d}. {warning}")
        
        # Final verdict
        print(f"\n{'='*70}")
        if not self.errors and not self.warnings:
            print("🎉 VALIDATION PASSED - Agent is ready for deployment!")
            return True
        elif not self.errors:
            print(f"⚠️  VALIDATION PASSED WITH {len(self.warnings)} WARNINGS")
            print("   Agent can be deployed but consider fixing warnings")
            return True
        else:
            print(f"❌ VALIDATION FAILED - {len(self.errors)} critical errors must be fixed")
            print("   Agent is NOT ready for deployment")
            return False

def main():
    """CLI entry point"""
    if len(sys.argv) < 2:
        print("Usage: python agent_validator.py <agent_directory> [config_file]")
        print("\nExample:")
        print("  python agent_validator.py /path/to/agent")
        print("  python agent_validator.py /path/to/agent config.yaml")
        sys.exit(1)
    
    agent_path = sys.argv[1]
    config_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    # Load configuration if provided
    config = {}
    if config_file and Path(config_file).exists():
        try:
            with open(config_file, 'r') as f:
                config = yaml.safe_load(f)
        except Exception as e:
            print(f"Warning: Could not load config file: {e}")
    
    # Run validation
    validator = ComprehensiveAgentValidator(agent_path, config)
    success = validator.validate_all()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()