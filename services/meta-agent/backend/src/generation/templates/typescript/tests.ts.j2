/**
 * Test suite for {{ agent_name | title }} Agent
 * Generated on: {{ "now" | datetime }}
 *
 * This test suite provides comprehensive testing for the {{ agent_name }} agent
 * including unit tests, integration tests, and performance tests.
 */

import { describe, beforeEach, afterEach, it, expect, jest } from '@jest/globals';
import request from 'supertest';
import { {{ agent_class }}Agent, createAgent, AgentStatus, TaskRequest, TaskResponse, AgentConfig } from './agent';

// Mock external dependencies
jest.mock('axios');
const mockAxios = require('axios');

describe('{{ agent_class }}Agent', () => {
    let agent: {{ agent_class }}Agent;
    let agentConfig: AgentConfig;

    beforeEach(() => {
        agentConfig = {
            name: '{{ agent_name }}',
            port: 0, // Use random port for testing
            corsEnabled: true,
            apiTimeout: 5000,
            logLevel: 'error', // Reduce noise in tests
            {% for key, value in configuration.items() %}
            {{ key }}: {{ value | tojson }},
            {% endfor %}
        };

        agent = createAgent(agentConfig);
    });

    afterEach(async () => {
        if (agent) {
            await agent.shutdown();
        }
        jest.clearAllMocks();
    });

    describe('Agent Creation and Initialization', () => {
        it('should create agent with valid config', () => {
            expect(agent).toBeDefined();
            expect(agent.name || '{{ agent_name }}').toBe('{{ agent_name }}');
        });

        it('should initialize successfully', async () => {
            const result = await agent.initialize();
            expect(result).toBe(true);
        });

        it('should get agent status', async () => {
            await agent.initialize();
            const status = await agent.getStatus();

            expect(status).toBeDefined();
            expect(status.name).toBe('{{ agent_name }}');
            expect(status.type).toBe('{{ agent_type }}');
            expect(status.capabilities).toEqual({{ capabilities | tojson }});
            expect(status.uptime).toBeDefined();
        });
    });

    describe('Task Execution', () => {
        beforeEach(async () => {
            await agent.initialize();
        });

        it('should execute basic task successfully', async () => {
            const task: TaskRequest = {
                taskId: 'test-task-1',
                taskType: 'test',
                inputData: { message: 'Hello, world!' },
                priority: 'medium'
            };

            const result = await agent.executeTask(task);

            expect(result).toBeDefined();
            expect(result.taskId).toBe(task.taskId);
            expect(result.status).toBe(AgentStatus.COMPLETED);
            expect(result.executionTime).toBeGreaterThan(0);
            expect(result.metadata.agentName).toBe('{{ agent_name }}');
        });

        it('should handle task with invalid type', async () => {
            const task: TaskRequest = {
                taskId: 'invalid-task',
                taskType: 'nonexistent_type',
                inputData: {},
                priority: 'low'
            };

            const result = await agent.executeTask(task);

            expect(result).toBeDefined();
            expect(result.taskId).toBe(task.taskId);
            // Should handle gracefully, either complete or error
            expect([AgentStatus.COMPLETED, AgentStatus.ERROR]).toContain(result.status);
        });

        it('should execute multiple tasks concurrently', async () => {
            const tasks: TaskRequest[] = [
                {
                    taskId: 'concurrent-1',
                    taskType: 'test',
                    inputData: { message: 'Task 1' },
                    priority: 'high'
                },
                {
                    taskId: 'concurrent-2',
                    taskType: 'test',
                    inputData: { message: 'Task 2' },
                    priority: 'medium'
                },
                {
                    taskId: 'concurrent-3',
                    taskType: 'test',
                    inputData: { message: 'Task 3' },
                    priority: 'low'
                }
            ];

            const results = await Promise.all(
                tasks.map(task => agent.executeTask(task))
            );

            expect(results).toHaveLength(3);
            results.forEach((result, index) => {
                expect(result.taskId).toBe(tasks[index].taskId);
                expect([AgentStatus.COMPLETED, AgentStatus.ERROR]).toContain(result.status);
            });
        });

        {% if 'conversation' in capabilities %}
        it('should handle conversation tasks', async () => {
            const task: TaskRequest = {
                taskId: 'conversation-test',
                taskType: 'conversation',
                inputData: {
                    message: 'Hello, how are you?',
                    context: { userId: 'test-user' }
                },
                priority: 'medium'
            };

            const result = await agent.executeTask(task);

            expect(result.status).toBe(AgentStatus.COMPLETED);
            expect(result.result).toBeDefined();
            expect(result.result.response).toBeDefined();
            expect(result.result.context).toBeDefined();
        });
        {% endif %}

        {% if 'web_integration' in capabilities %}
        it('should handle web requests', async () => {
            // Mock axios response
            mockAxios.mockResolvedValue({
                status: 200,
                statusText: 'OK',
                headers: { 'content-type': 'application/json' },
                data: { message: 'Success' }
            });

            const task: TaskRequest = {
                taskId: 'web-request-test',
                taskType: 'web_request',
                inputData: {
                    url: 'https://api.example.com/test',
                    method: 'GET'
                },
                priority: 'medium'
            };

            const result = await agent.executeTask(task);

            expect(result.status).toBe(AgentStatus.COMPLETED);
            expect(result.result).toBeDefined();
            expect(result.result.status).toBe(200);
        });
        {% endif %}

        {% if 'api_integration' in capabilities %}
        it('should handle API calls', async () => {
            // Mock API client response
            const mockApiClient = {
                request: jest.fn().mockResolvedValue({
                    status: 200,
                    data: { result: 'API success' },
                    headers: {}
                })
            };

            // Replace the agent's API client
            (agent as any).apiClient = mockApiClient;

            const task: TaskRequest = {
                taskId: 'api-call-test',
                taskType: 'api_call',
                inputData: {
                    endpoint: '/api/test',
                    method: 'POST',
                    data: { key: 'value' }
                },
                priority: 'medium'
            };

            const result = await agent.executeTask(task);

            expect(result.status).toBe(AgentStatus.COMPLETED);
            expect(result.result).toBeDefined();
            expect(mockApiClient.request).toHaveBeenCalled();
        });
        {% endif %}
    });

    describe('HTTP API Endpoints', () => {
        let server: any;

        beforeEach(async () => {
            await agent.initialize();
            server = (agent as any).server;
        });

        it('should respond to health check', async () => {
            const response = await request(server).get('/health');

            expect(response.status).toBe(200);
            expect(response.body.status).toBe('healthy');
            expect(response.body.timestamp).toBeDefined();
        });

        it('should return agent status', async () => {
            const response = await request(server).get('/status');

            expect(response.status).toBe(200);
            expect(response.body.name).toBe('{{ agent_name }}');
            expect(response.body.type).toBe('{{ agent_type }}');
            expect(response.body.capabilities).toEqual({{ capabilities | tojson }});
        });

        it('should execute task via POST /tasks', async () => {
            const taskRequest = {
                taskType: 'test',
                inputData: { message: 'HTTP test' },
                priority: 'medium'
            };

            const response = await request(server)
                .post('/tasks')
                .send(taskRequest);

            expect(response.status).toBe(200);
            expect(response.body.taskId).toBeDefined();
            expect(response.body.status).toBe(AgentStatus.COMPLETED);
            expect(response.body.executionTime).toBeGreaterThan(0);
        });

        it('should handle malformed task requests', async () => {
            const response = await request(server)
                .post('/tasks')
                .send({});

            expect(response.status).toBe(500);
            expect(response.body.error).toBeDefined();
        });

        it('should return 404 for unknown endpoints', async () => {
            const response = await request(server).get('/unknown-endpoint');

            expect(response.status).toBe(404);
            expect(response.body.error).toBe('Endpoint not found');
        });

        {% if 'web_integration' in capabilities %}
        it('should return capabilities info', async () => {
            const response = await request(server).get('/api/capabilities');

            expect(response.status).toBe(200);
            expect(response.body.capabilities).toEqual({{ capabilities | tojson }});
            expect(response.body.agentType).toBe('{{ agent_type }}');
            expect(response.body.version).toBeDefined();
        });
        {% endif %}
    });

    describe('Error Handling', () => {
        beforeEach(async () => {
            await agent.initialize();
        });

        it('should handle task execution errors gracefully', async () => {
            // Mock a method to throw an error
            const originalMethod = (agent as any).handleGenericTask;
            (agent as any).handleGenericTask = jest.fn().mockRejectedValue(new Error('Test error'));

            const task: TaskRequest = {
                taskId: 'error-test',
                taskType: 'test',
                inputData: {},
                priority: 'medium'
            };

            const result = await agent.executeTask(task);

            expect(result.status).toBe(AgentStatus.ERROR);
            expect(result.error).toBe('Test error');
            expect(result.executionTime).toBeGreaterThan(0);

            // Restore original method
            (agent as any).handleGenericTask = originalMethod;
        });

        it('should emit task events', (done) => {
            const task: TaskRequest = {
                taskId: 'event-test',
                taskType: 'test',
                inputData: { message: 'Event test' },
                priority: 'medium'
            };

            let taskStartedEmitted = false;
            let taskCompletedEmitted = false;

            agent.on('taskStarted', (startedTask) => {
                expect(startedTask.taskId).toBe(task.taskId);
                taskStartedEmitted = true;
            });

            agent.on('taskCompleted', (result) => {
                expect(result.taskId).toBe(task.taskId);
                taskCompletedEmitted = true;

                // Check that both events were emitted
                expect(taskStartedEmitted).toBe(true);
                expect(taskCompletedEmitted).toBe(true);
                done();
            });

            agent.executeTask(task);
        });
    });

    describe('Performance Tests', () => {
        beforeEach(async () => {
            await agent.initialize();
        });

        it('should execute tasks within reasonable time', async () => {
            const task: TaskRequest = {
                taskId: 'performance-test',
                taskType: 'test',
                inputData: { message: 'Performance test' },
                priority: 'medium'
            };

            const startTime = Date.now();
            const result = await agent.executeTask(task);
            const executionTime = Date.now() - startTime;

            expect(result.status).toBe(AgentStatus.COMPLETED);
            expect(executionTime).toBeLessThan(5000); // Should complete within 5 seconds
        });

        it('should handle multiple sequential tasks efficiently', async () => {
            const numTasks = 10;
            const tasks: TaskRequest[] = Array.from({ length: numTasks }, (_, i) => ({
                taskId: `sequential-${i}`,
                taskType: 'test',
                inputData: { message: `Sequential task ${i}` },
                priority: 'medium'
            }));

            const startTime = Date.now();
            const results: TaskResponse[] = [];

            for (const task of tasks) {
                const result = await agent.executeTask(task);
                results.push(result);
            }

            const totalTime = Date.now() - startTime;

            expect(results).toHaveLength(numTasks);
            expect(totalTime).toBeLessThan(30000); // Should complete within 30 seconds

            results.forEach(result => {
                expect(result.status).toBe(AgentStatus.COMPLETED);
            });
        });

        it('should maintain performance under concurrent load', async () => {
            const numConcurrentTasks = 20;
            const tasks: TaskRequest[] = Array.from({ length: numConcurrentTasks }, (_, i) => ({
                taskId: `concurrent-load-${i}`,
                taskType: 'test',
                inputData: { message: `Load test task ${i}` },
                priority: 'medium'
            }));

            const startTime = Date.now();
            const results = await Promise.all(
                tasks.map(task => agent.executeTask(task))
            );
            const totalTime = Date.now() - startTime;

            expect(results).toHaveLength(numConcurrentTasks);
            expect(totalTime).toBeLessThan(10000); // Should complete within 10 seconds

            results.forEach(result => {
                expect([AgentStatus.COMPLETED, AgentStatus.ERROR]).toContain(result.status);
            });
        });
    });

    describe('Agent Lifecycle', () => {
        it('should complete full lifecycle successfully', async () => {
            // Initialize
            const initResult = await agent.initialize();
            expect(initResult).toBe(true);

            // Execute task
            const task: TaskRequest = {
                taskId: 'lifecycle-test',
                taskType: 'test',
                inputData: { message: 'Lifecycle test' },
                priority: 'medium'
            };

            const taskResult = await agent.executeTask(task);
            expect(taskResult.status).toBe(AgentStatus.COMPLETED);

            // Get status
            const status = await agent.getStatus();
            expect(status.tasksExecuted).toBeGreaterThan(0);

            // Shutdown
            const shutdownResult = await agent.shutdown();
            expect(shutdownResult).toBe(true);
        });

        it('should handle shutdown gracefully', async () => {
            await agent.initialize();

            const shutdownResult = await agent.shutdown();
            expect(shutdownResult).toBe(true);
        });
    });

    describe('Configuration Handling', () => {
        it('should use default config values', () => {
            const defaultAgent = createAgent({});
            expect(defaultAgent).toBeDefined();
        });

        it('should handle custom configuration', () => {
            const customConfig: AgentConfig = {
                name: 'custom-agent',
                port: 4000,
                corsEnabled: false,
                apiTimeout: 10000,
                customProperty: 'custom-value'
            };

            const customAgent = createAgent(customConfig);
            expect(customAgent).toBeDefined();
        });

        it('should validate configuration constraints', () => {
            // Test would validate config constraints if any exist
            expect(true).toBe(true); // Placeholder
        });
    });

    {% if 'api_integration' in capabilities %}
    describe('API Integration', () => {
        beforeEach(async () => {
            await agent.initialize();
        });

        it('should handle API client errors', async () => {
            // Mock API client to throw error
            const mockApiClient = {
                request: jest.fn().mockRejectedValue(new Error('Network error'))
            };

            (agent as any).apiClient = mockApiClient;

            const task: TaskRequest = {
                taskId: 'api-error-test',
                taskType: 'api_call',
                inputData: {
                    endpoint: '/api/fail',
                    method: 'GET'
                },
                priority: 'medium'
            };

            const result = await agent.executeTask(task);

            expect(result.status).toBe(AgentStatus.ERROR);
            expect(result.error).toContain('API call failed');
        });
    });
    {% endif %}
});

// Utility functions for testing
export function createTestTask(taskType = 'test', inputData = {}, priority: 'low' | 'medium' | 'high' = 'medium'): TaskRequest {
    return {
        taskId: `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        taskType,
        inputData: { message: 'Test message', ...inputData },
        priority
    };
}

export function createTestAgent(overrideConfig: Partial<AgentConfig> = {}): {{ agent_class }}Agent {
    const defaultConfig: AgentConfig = {
        name: '{{ agent_name }}-test',
        port: 0,
        logLevel: 'error',
        {% for key, value in configuration.items() %}
        {{ key }}: {{ value | tojson }},
        {% endfor %}
        ...overrideConfig
    };

    return createAgent(defaultConfig);
}

// Integration test helpers
export class TestHelper {
    static async waitForCondition(
        condition: () => boolean | Promise<boolean>,
        timeout = 5000,
        interval = 100
    ): Promise<void> {
        const startTime = Date.now();

        while (Date.now() - startTime < timeout) {
            if (await condition()) {
                return;
            }
            await new Promise(resolve => setTimeout(resolve, interval));
        }

        throw new Error(`Condition not met within ${timeout}ms`);
    }

    static generateRandomTasks(count: number): TaskRequest[] {
        return Array.from({ length: count }, (_, i) => createTestTask(
            'test',
            { taskNumber: i, timestamp: Date.now() },
            ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any
        ));
    }
}

// Mock data for testing
export const mockResponses = {
    {% if 'api_integration' in capabilities %}
    apiSuccess: {
        status: 200,
        data: { result: 'success', timestamp: new Date().toISOString() },
        headers: { 'content-type': 'application/json' }
    },
    {% endif %}
    {% if 'web_integration' in capabilities %}
    webSuccess: {
        status: 200,
        statusText: 'OK',
        headers: { 'content-type': 'text/html' },
        data: '<html><body>Success</body></html>'
    },
    {% endif %}
};

// Export test configuration
export const testConfig = {
    timeout: 30000,
    retries: 2,
    verbose: process.env.NODE_ENV === 'development'
};