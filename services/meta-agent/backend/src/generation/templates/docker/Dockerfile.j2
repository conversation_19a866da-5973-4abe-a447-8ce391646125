# {{ agent_name | title }} Agent Dockerfile
# Generated on: {{ "now" | datetime }}

FROM python:3.11-slim as base

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive

# Set work directory
WORKDIR /app

# Install system dependencies
{% if 'computer_vision' in capabilities %}
RUN apt-get update && apt-get install -y \
    build-essential \
    libopencv-dev \
    python3-opencv \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*
{% elif 'web_scraping' in capabilities %}
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    curl \
    unzip \
    chromium-browser \
    && rm -rf /var/lib/apt/lists/*
{% else %}
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*
{% endif %}

# Create non-root user
RUN groupadd -r agentuser && useradd -r -g agentuser agentuser

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Change ownership to non-root user
RUN chown -R agentuser:agentuser /app

# Switch to non-root user
USER agentuser

# Create logs directory
RUN mkdir -p /app/logs

# Expose port (if web service)
{% if 'web_integration' in capabilities or 'api_integration' in capabilities %}
EXPOSE 8000
{% endif %}

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD python -c "import asyncio; from agent import create_agent; print('Agent health check OK')" || exit 1

# Default command
{% if 'web_integration' in capabilities %}
CMD ["uvicorn", "agent:app", "--host", "0.0.0.0", "--port", "8000"]
{% else %}
CMD ["python", "agent.py"]
{% endif %}

# Production stage
FROM base as production

# Additional production optimizations
ENV PYTHONOPTIMIZE=1

# Remove development packages and clean up
USER root
RUN apt-get autoremove -y \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

USER agentuser

# Ensure all files are owned by agentuser
RUN find /app -type f -exec chmod 644 {} \; \
    && find /app -type d -exec chmod 755 {} \; \
    && chmod +x /app/agent.py

# Override command for production
{% if 'web_integration' in capabilities %}
CMD ["gunicorn", "agent:app", "-w", "2", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000"]
{% else %}
CMD ["python", "-O", "agent.py"]
{% endif %}