# Generic Full-Stack Web Agent Template
# Generates all required files for any agent type with proper structure
# Supports dynamic configuration based on agent capabilities and requirements

# === Backend Files ===

## app.py
from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import json
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app, origins=["http://localhost:{{ frontend_port | default(3000) }}"])

# Data store for agent state
agent_data = {}

@app.route("/", methods=["GET"])
def get_agent_info():
    return jsonify({
        "agent_id": "{{ agent_id }}",
        "name": "{{ agent_name }}",
        "type": "{{ agent_type }}",
        "capabilities": {{ capabilities | tojson }},
        "status": "running",
        "version": "1.0.0",
        "description": "{{ description }}",
        "created_at": datetime.now().isoformat(),
        "endpoints": [
            {% for endpoint in api_endpoints | default([]) %}
            {"path": "{{ endpoint.path }}", "method": "{{ endpoint.method | default('GET') }}", "description": "{{ endpoint.description }}"},
            {% endfor %}
        ]
    })

@app.route("/health", methods=["GET"])
def health():
    return jsonify({"status": "healthy", "timestamp": datetime.now().isoformat()})

@app.route("/api/status", methods=["GET"])
def get_status():
    return jsonify({
        "agent_id": "{{ agent_id }}",
        "status": "active",
        "uptime": datetime.now().isoformat(),
        "capabilities": {{ capabilities | tojson }}
    })

{% for endpoint in api_endpoints | default([]) %}
@app.route("{{ endpoint.path }}", methods={{ endpoint.methods | default(['GET']) | tojson }})
def {{ endpoint.function_name | default('handle_' + endpoint.path.replace('/', '_').replace('-', '_')) }}():
    """{{ endpoint.description | default('API endpoint') }}"""
    {% if 'GET' in endpoint.methods | default(['GET']) %}
    if request.method == 'GET':
        return jsonify({{ endpoint.get_response | default('{"message": "GET response", "data": []}') }})
    {% endif %}
    {% if 'POST' in endpoint.methods | default([]) %}
    if request.method == 'POST':
        data = request.json or {}
        # Process POST data here
        return jsonify({{ endpoint.post_response | default('{"message": "POST successful", "received": data}') }})
    {% endif %}
    {% if 'PUT' in endpoint.methods | default([]) %}
    if request.method == 'PUT':
        data = request.json or {}
        # Process PUT data here
        return jsonify({{ endpoint.put_response | default('{"message": "PUT successful", "updated": data}') }})
    {% endif %}
    {% if 'DELETE' in endpoint.methods | default([]) %}
    if request.method == 'DELETE':
        # Process DELETE request here
        return jsonify({{ endpoint.delete_response | default('{"message": "DELETE successful"}') }})
    {% endif %}
{% endfor %}

if __name__ == "__main__":
    port = int(os.environ.get("PORT", {{ backend_port | default(8000) }}))
    app.run(host="0.0.0.0", port=port, debug=False)

---FILE_SEPARATOR---

## requirements.txt
Flask=={{ flask_version | default("2.3.3") }}
flask-cors=={{ flask_cors_version | default("4.0.0") }}
python-dotenv=={{ dotenv_version | default("1.0.0") }}
gunicorn=={{ gunicorn_version | default("21.2.0") }}
{% for dep in additional_python_deps | default([]) %}
{{ dep }}
{% endfor %}

---FILE_SEPARATOR---

## Dockerfile
FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 agent
RUN chown -R agent:agent /app
USER agent

# Expose port
EXPOSE {{ backend_port | default(8000) }}

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:{{ backend_port | default(8000) }}/health || exit 1

# Run the application
CMD ["python", "app.py"]

---FILE_SEPARATOR---

## .env
# Agent Configuration
AGENT_NAME={{ agent_name }}
AGENT_TYPE={{ agent_type }}
PORT={{ backend_port | default(8000) }}
LOG_LEVEL=INFO

{% for key, value in env_vars | default({}).items() %}
{{ key }}={{ value }}
{% endfor %}

---FILE_SEPARATOR---

# === Frontend Files ===

## frontend/package.json
{
  "name": "{{ agent_name | lower | replace(' ', '-') }}-frontend",
  "version": "0.1.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "type-check": "tsc"
  },
  "dependencies": {
    "preact": "10.19.3",
    "preact-router": "4.1.2",
    "axios": "1.6.7"
    {% if frontend_dependencies %}
    {% for dep, version in frontend_dependencies.items() %}
    ,"{{ dep }}": "{{ version }}"
    {% endfor %}
    {% endif %}
  },
  "devDependencies": {
    "@preact/preset-vite": "2.8.2",
    "@types/node": "20.11.24",
    "autoprefixer": "10.4.17",
    "postcss": "8.4.35",
    "tailwindcss": "3.4.1",
    "terser": "^5.27.0",
    "typescript": "5.3.3",
    "vite": "5.1.4"
  }
}

---FILE_SEPARATOR---

## frontend/vite.config.ts
import { defineConfig } from 'vite'
import preact from '@preact/preset-vite'

export default defineConfig({
  plugins: [preact()],
  resolve: {
    alias: {
      '@': '/src'
    }
  },
  server: {
    port: {{ frontend_port | default(3000) }},
    proxy: {
      '/api': {
        target: 'http://localhost:{{ backend_port | default(8000) }}',
        changeOrigin: true
      }
    }
  }
})

---FILE_SEPARATOR---

## frontend/src/services/api.ts
import axios, { AxiosInstance } from 'axios'

const API_BASE_URL = import.meta.env.DEV ? '/api' : '/api'

export interface AgentInfo {
  agent_id: string
  name: string
  type: string
  capabilities: string[]
  status: string
  version: string
  description: string
  created_at: string
  endpoints: Array<{
    path: string
    method: string
    description: string
  }>
}

export class ApiClient {
  public client: AxiosInstance

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 30000,
    })

    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('auth_token')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => Promise.reject(error)
    )

    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('auth_token')
          window.location.href = '/login'
        }
        return Promise.reject(error)
      }
    )
  }

  async checkHealth(): Promise<{ status: string }> {
    const response = await this.client.get('/health')
    return response.data
  }

  async getAgentInfo(): Promise<AgentInfo> {
    const response = await this.client.get('/')
    return response.data
  }

  async getStatus(): Promise<any> {
    const response = await this.client.get('/status')
    return response.data
  }
}

// Export both default and named exports for maximum compatibility
export const apiClient = new ApiClient()
export default apiClient
export const api = apiClient

---FILE_SEPARATOR---

## frontend/src/components/Header.tsx
import { Link } from 'preact-router/match'

export function Header() {
  return (
    <header class="bg-white shadow-sm border-b">
      <div class="container mx-auto px-4">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center space-x-8">
            <h1 class="text-xl font-bold text-gray-900">
              {{ agent_name }}
            </h1>
            <nav class="hidden lg:flex space-x-4">
              <Link 
                href="/" 
                class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                activeClassName="bg-gray-100 text-gray-900"
              >
                Dashboard
              </Link>
              {% for page in frontend_pages | default([]) %}
              <Link 
                href="{{ page.path }}" 
                class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                activeClassName="bg-gray-100 text-gray-900"
              >
                {{ page.name }}
              </Link>
              {% endfor %}
            </nav>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-500">v1.0.0</span>
            <div class="w-2 h-2 bg-green-500 rounded-full" title="Connected"></div>
          </div>
        </div>
      </div>
    </header>
  )
}

export { Header }
export default Header

---FILE_SEPARATOR---

## frontend/src/pages/HomePage.tsx
import { useEffect, useState } from 'preact/hooks'
import { apiClient, AgentInfo } from '@/services/api'

export function HomePage() {
  const [agentInfo, setAgentInfo] = useState<AgentInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchAgentInfo()
  }, [])

  const fetchAgentInfo = async () => {
    try {
      const info = await apiClient.getAgentInfo()
      setAgentInfo(info)
    } catch (err: any) {
      setError(err.message || 'Failed to fetch agent info')
    } finally {
      setLoading(false)
    }
  }

  if (loading) return <div class="text-center py-8">Loading...</div>
  if (error) return <div class="text-red-600 text-center py-8">Error: {error}</div>

  return (
    <div class="max-w-6xl mx-auto space-y-6">
      {/* Agent Status */}
      <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-start mb-4">
          <div>
            <h1 class="text-2xl font-bold mb-2">{agentInfo?.name}</h1>
            <p class="text-gray-600">{agentInfo?.description}</p>
          </div>
          <div class="flex items-center space-x-2">
            <div class={`w-3 h-3 rounded-full ${agentInfo?.status === 'running' ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span class="text-sm text-gray-500 capitalize">{agentInfo?.status}</span>
          </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h3 class="font-semibold mb-2">Agent Details</h3>
            <dl class="space-y-1 text-sm">
              <div class="flex justify-between">
                <dt class="text-gray-500">Version:</dt>
                <dd>{agentInfo?.version}</dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-gray-500">Type:</dt>
                <dd class="capitalize">{agentInfo?.type}</dd>
              </div>
            </dl>
          </div>
          
          <div class="md:col-span-2">
            <h3 class="font-semibold mb-2">Capabilities</h3>
            <div class="grid grid-cols-2 gap-2">
              {agentInfo?.capabilities.map(cap => (
                <div key={cap} class="flex items-center space-x-2">
                  <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span class="text-sm text-gray-600 capitalize">{cap.replace('_', ' ')}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* API Endpoints */}
      {agentInfo?.endpoints && agentInfo.endpoints.length > 0 && (
        <div class="bg-white shadow rounded-lg p-6">
          <h2 class="text-lg font-semibold mb-4">Available Endpoints</h2>
          <div class="grid gap-3">
            {agentInfo.endpoints.map((endpoint, index) => (
              <div key={index} class="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div>
                  <span class="font-mono text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">
                    {endpoint.method} {endpoint.path}
                  </span>
                  <p class="text-sm text-gray-600 mt-1">{endpoint.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export { HomePage }
export default HomePage

---FILE_SEPARATOR---

## frontend/src/App.tsx
import { Router, Route } from 'preact-router'
import { Header } from '@/components/Header'
import { HomePage } from '@/pages/HomePage'
{% for page in frontend_pages | default([]) %}
import { {{ page.component }} } from '@/pages/{{ page.component }}'
{% endfor %}

export function App() {
  return (
    <div class="min-h-screen bg-gray-50">
      <Header />
      <main class="container mx-auto px-4 py-8">
        <Router>
          <Route path="/" component={HomePage} />
          {% for page in frontend_pages | default([]) %}
          <Route path="{{ page.path }}" component={{ page.component }} />
          {% endfor %}
        </Router>
      </main>
    </div>
  )
}

export { App }
export default App

---FILE_SEPARATOR---

## frontend/src/main.tsx
import { render } from 'preact'
import { App } from './App'
import './index.css'

render(<App />, document.getElementById('app')!)

---FILE_SEPARATOR---

## frontend/index.html
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{{ agent_name }}</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>

---FILE_SEPARATOR---

## frontend/src/index.css
@tailwind base;
@tailwind components;
@tailwind utilities;

---FILE_SEPARATOR---

## frontend/tailwind.config.js
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}

---FILE_SEPARATOR---

## frontend/postcss.config.js
export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}

---FILE_SEPARATOR---

## frontend/tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "jsxImportSource": "preact",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}

---FILE_SEPARATOR---

## frontend/tsconfig.node.json
{
  "compilerOptions": {
    "composite": true,
    "skipLibCheck": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true
  },
  "include": ["vite.config.ts"]
}

---FILE_SEPARATOR---

## frontend/Dockerfile
# Build stage
FROM node:20-alpine as builder

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm install

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine

# Remove default nginx config
RUN rm /etc/nginx/conf.d/default.conf

# Copy custom nginx config
COPY nginx.conf /etc/nginx/conf.d/

# Copy built application
COPY --from=builder /app/dist /usr/share/nginx/html

# Create non-root user
RUN id -u nginx >/dev/null 2>&1 || adduser -D -H -u 1000 -s /bin/sh nginx
RUN chown -R nginx:nginx /usr/share/nginx/html

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]

---FILE_SEPARATOR---

## frontend/nginx.conf
server {
    listen 80;
    server_name localhost;
    
    root /usr/share/nginx/html;
    index index.html index.htm;
    
    # Handle client-side routing
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API proxy to backend
    location /api/ {
        proxy_pass http://backend:{{ backend_port | default(8000) }}/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Health check proxy
    location /health {
        proxy_pass http://backend:{{ backend_port | default(8000) }}/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # Static assets caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

---FILE_SEPARATOR---

## docker-compose.yml
services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: backend_{{ agent_id }}
    ports:
      - "{{ backend_port | default(8000) }}:{{ backend_port | default(8000) }}"
    environment:
      - NODE_ENV=production
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:{{ backend_port | default(8000) }}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - agent_network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: frontend_{{ agent_id }}
    ports:
      - "{{ frontend_port | default(3000) }}:80"
    environment:
      - BACKEND_HOST=backend
      - BACKEND_PORT={{ backend_port | default(8000) }}
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - agent_network

networks:
  agent_network:
    driver: bridge

---FILE_SEPARATOR---

# === Build Validation Script ===

## validate_build.py
#!/usr/bin/env python3
"""
Generic build validation script for generated fullstack agents.
Validates file structure, dependencies, and configuration.
"""

import os
import json
import sys
from pathlib import Path
from typing import Dict, List, Any

class GenericBuildValidator:
    def __init__(self, agent_path: str):
        self.agent_path = Path(agent_path)
        self.errors = []
        self.warnings = []
        
    def validate(self) -> bool:
        """Run all validation checks"""
        print(f"🔍 Validating agent at: {self.agent_path}")
        
        self.validate_file_structure()
        self.validate_backend()
        self.validate_frontend()
        self.validate_dependencies()
        
        return self.report_results()
    
    def validate_file_structure(self):
        """Validate required files exist"""
        required_files = [
            'app.py', 'requirements.txt', 'Dockerfile', '.env',
            'frontend/package.json', 'frontend/vite.config.ts',
            'frontend/src/main.tsx', 'frontend/src/App.tsx',
            'frontend/src/services/api.ts', 'frontend/src/components/Header.tsx',
            'frontend/src/pages/HomePage.tsx', 'frontend/index.html',
            'frontend/tsconfig.json', 'frontend/tailwind.config.js'
        ]
        
        for file_path in required_files:
            full_path = self.agent_path / file_path
            if not full_path.exists():
                self.errors.append(f"Missing required file: {file_path}")
            else:
                print(f"✅ Found: {file_path}")
    
    def validate_backend(self):
        """Validate Python backend"""
        app_py = self.agent_path / 'app.py'
        if app_py.exists():
            try:
                with open(app_py, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                required_imports = ['Flask', 'CORS', 'jsonify']
                for imp in required_imports:
                    if imp not in content:
                        self.errors.append(f"Missing import in app.py: {imp}")
                
                required_routes = ['@app.route("/", methods=["GET"])', '@app.route("/health"']
                for route in required_routes:
                    if route not in content:
                        self.errors.append(f"Missing route in app.py: {route}")
                        
                print("✅ Backend validation passed")
            except Exception as e:
                self.errors.append(f"Error reading app.py: {e}")
    
    def validate_frontend(self):
        """Validate frontend configuration"""
        package_json = self.agent_path / 'frontend' / 'package.json'
        if package_json.exists():
            try:
                with open(package_json, 'r', encoding='utf-8') as f:
                    pkg = json.load(f)
                    
                deps = pkg.get('dependencies', {})
                required_deps = ['preact', 'preact-router', 'axios']
                for dep in required_deps:
                    if dep not in deps:
                        self.errors.append(f"Missing frontend dependency: {dep}")
                        
                print("✅ Frontend package.json validation passed")
            except Exception as e:
                self.errors.append(f"Error reading package.json: {e}")
    
    def validate_dependencies(self):
        """Validate import/export consistency"""
        api_ts = self.agent_path / 'frontend' / 'src' / 'services' / 'api.ts'
        if api_ts.exists():
            try:
                with open(api_ts, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                if 'export default apiClient' not in content:
                    self.errors.append("api.ts missing default export")
                if 'export const apiClient' not in content:
                    self.errors.append("api.ts missing named export")
                        
                print("✅ API exports validation passed")
            except Exception as e:
                self.errors.append(f"Error reading api.ts: {e}")
    
    def report_results(self) -> bool:
        """Report validation results"""
        print("\n" + "="*50)
        print("🔍 VALIDATION RESULTS")
        print("="*50)
        
        if self.errors:
            print(f"\n❌ ERRORS ({len(self.errors)}):")
            for error in self.errors:
                print(f"  • {error}")
        
        if self.warnings:
            print(f"\n⚠️  WARNINGS ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"  • {warning}")
        
        if not self.errors and not self.warnings:
            print("\n✅ ALL VALIDATIONS PASSED!")
            return True
        elif not self.errors:
            print(f"\n⚠️  VALIDATION PASSED WITH {len(self.warnings)} WARNINGS")
            return True
        else:
            print(f"\n❌ VALIDATION FAILED WITH {len(self.errors)} ERRORS")
            return False

def main():
    if len(sys.argv) != 2:
        print("Usage: python validate_build.py <agent_directory>")
        sys.exit(1)
    
    agent_path = sys.argv[1]
    validator = GenericBuildValidator(agent_path)
    
    if validator.validate():
        print("\n🎉 Agent is ready for deployment!")
        sys.exit(0)
    else:
        print("\n🚨 Agent has validation errors. Fix before deploying.")
        sys.exit(1)

if __name__ == "__main__":
    main()

---FILE_SEPARATOR---

# === Dynamic Page Generator ===

{% for page in frontend_pages | default([]) %}
## frontend/src/pages/{{ page.component }}.tsx
import { useEffect, useState } from 'preact/hooks'
import { apiClient } from '@/services/api'

export function {{ page.component }}() {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<any[]>([])
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    setLoading(true)
    try {
      const response = await apiClient.client.get('{{ page.api_endpoint | default("/api/data") }}')
      setData(response.data || [])
    } catch (err: any) {
      setError(err.message || 'Failed to load data')
    } finally {
      setLoading(false)
    }
  }

  if (loading) return <div class="text-center py-8">Loading {{ page.name | lower }}...</div>
  if (error) return <div class="text-red-600 text-center py-8">Error: {error}</div>

  return (
    <div class="max-w-6xl mx-auto space-y-6">
      <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-2xl font-bold">{{ page.name }}</h1>
          {% if page.actions %}
          <div class="flex space-x-2">
            {% for action in page.actions %}
            <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
              {{ action.label }}
            </button>
            {% endfor %}
          </div>
          {% endif %}
        </div>

        {/* Generic data display */}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {data.map((item, index) => (
            <div key={index} class="border rounded-lg p-4 hover:shadow-md transition-shadow">
              <h3 class="font-medium mb-2">{item.title || item.name || `Item ${index + 1}`}</h3>
              <p class="text-sm text-gray-600">{item.description || item.summary || 'No description available'}</p>
              <div class="mt-3 flex space-x-2">
                <button class="text-blue-600 hover:text-blue-800 text-sm">View</button>
                {% if page.editable %}
                <button class="text-gray-600 hover:text-gray-800 text-sm">Edit</button>
                {% endif %}
              </div>
            </div>
          ))}
        </div>

        {data.length === 0 && !loading && (
          <div class="text-center py-12">
            <p class="text-gray-500">No {{ page.name | lower }} found</p>
            {% if page.empty_action %}
            <button class="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
              {{ page.empty_action }}
            </button>
            {% endif %}
          </div>
        )}
      </div>
    </div>
  )
}

export { {{ page.component }} }
export default {{ page.component }}

{% endfor %}