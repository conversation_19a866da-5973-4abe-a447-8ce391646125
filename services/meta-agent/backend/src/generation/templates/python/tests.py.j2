"""
Test suite for {{ agent_name | title }} Agent
Generated on: {{ "now" | datetime }}

This test suite provides comprehensive testing for the {{ agent_name }} agent
including unit tests, integration tests, and performance tests.
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime
from typing import Dict, Any

{% for import_stmt in imports %}
{{ import_stmt }}
{% endfor %}

from agent import {{ agent_class }}Agent, create_agent, AgentStatus, TaskRequest, TaskResponse


@pytest.fixture
def agent_config():
    """Default agent configuration for testing"""
    return {
        {% for key, value in configuration.items() %}
        "{{ key }}": {{ value | tojson }},
        {% endfor %}
        "test_mode": True
    }


@pytest.fixture
async def agent(agent_config):
    """Create agent instance for testing"""
    agent_instance = create_agent(agent_config)
    await agent_instance.initialize()
    yield agent_instance
    await agent_instance.shutdown()


@pytest.fixture
def sample_task():
    """Sample task for testing"""
    return TaskRequest(
        task_type="test",
        input_data={"message": "Hello, world!"},
        priority="medium"
    )


class Test{{ agent_class }}AgentBasics:
    """Basic agent functionality tests"""
    
    def test_agent_creation(self, agent_config):
        """Test agent can be created with valid config"""
        agent = create_agent(agent_config)
        assert agent.name == "{{ agent_name }}"
        assert agent.agent_type == "{{ agent_type }}"
        assert agent.status == AgentStatus.IDLE
    
    async def test_agent_initialization(self, agent):
        """Test agent initializes correctly"""
        assert agent.status == AgentStatus.IDLE
        assert agent.capabilities == {{ capabilities | tojson }}
        assert agent.agent_id is not None
    
    async def test_agent_status_retrieval(self, agent):
        """Test getting agent status"""
        status = await agent.get_status()
        
        assert status["agent_id"] == agent.agent_id
        assert status["name"] == "{{ agent_name }}"
        assert status["type"] == "{{ agent_type }}"
        assert status["capabilities"] == {{ capabilities | tojson }}
        assert "uptime" in status


class Test{{ agent_class }}AgentTaskExecution:
    """Task execution tests"""
    
    async def test_basic_task_execution(self, agent, sample_task):
        """Test basic task execution"""
        result = await agent.execute_task(sample_task)
        
        assert isinstance(result, TaskResponse)
        assert result.task_id == sample_task.task_id
        assert result.status in [AgentStatus.COMPLETED, AgentStatus.ERROR]
        assert result.execution_time is not None
        assert result.metadata["agent_id"] == agent.agent_id
    
    async def test_task_with_invalid_input(self, agent):
        """Test task execution with invalid input"""
        invalid_task = TaskRequest(
            task_type="invalid_type",
            input_data={}
        )
        
        result = await agent.execute_task(invalid_task)
        
        # Should handle gracefully, either complete or error
        assert isinstance(result, TaskResponse)
        assert result.task_id == invalid_task.task_id
    
    async def test_concurrent_task_execution(self, agent):
        """Test multiple tasks can be executed concurrently"""
        tasks = [
            TaskRequest(
                task_type="test",
                input_data={"message": f"Task {i}"}
            )
            for i in range(3)
        ]
        
        results = await asyncio.gather(*[
            agent.execute_task(task) for task in tasks
        ])
        
        assert len(results) == 3
        for i, result in enumerate(results):
            assert result.task_id == tasks[i].task_id

{% if 'conversation' in capabilities %}

class Test{{ agent_class }}AgentConversation:
    """Conversation capability tests"""
    
    async def test_conversation_handling(self, agent):
        """Test conversation task handling"""
        task = TaskRequest(
            task_type="conversation",
            input_data={
                "message": "Hello, how are you?",
                "context": {"user_id": "test_user"}
            }
        )
        
        result = await agent.execute_task(task)
        
        assert result.status == AgentStatus.COMPLETED
        assert result.result is not None
        assert "response" in result.result
        assert "context" in result.result

{% endif %}

{% if 'data_analysis' in capabilities %}

class Test{{ agent_class }}AgentDataAnalysis:
    """Data analysis capability tests"""
    
    async def test_data_analysis(self, agent):
        """Test data analysis functionality"""
        task = TaskRequest(
            task_type="analyze_data",
            input_data={
                "data_source": "test_dataset.csv",
                "analysis_type": "basic"
            }
        )
        
        result = await agent.execute_task(task)
        
        assert result.status == AgentStatus.COMPLETED
        assert result.result is not None
        assert "analysis_type" in result.result
    
    @patch('pandas.read_csv')
    async def test_data_analysis_with_mock_data(self, mock_read_csv, agent):
        """Test data analysis with mocked data"""
        import pandas as pd
        
        # Mock data
        mock_read_csv.return_value = pd.DataFrame({
            'value': [1, 2, 3, 4, 5],
            'category': ['A', 'B', 'A', 'B', 'A']
        })
        
        task = TaskRequest(
            task_type="analyze_data",
            input_data={
                "data_source": "mock_data.csv",
                "analysis_type": "advanced"
            }
        )
        
        result = await agent.execute_task(task)
        assert result.status == AgentStatus.COMPLETED

{% endif %}

{% if 'data_processing' in capabilities %}

class Test{{ agent_class }}AgentDataProcessing:
    """Data processing capability tests"""
    
    async def test_data_processing(self, agent):
        """Test data processing functionality"""
        test_data = [
            {"name": "john", "age": 25},
            {"name": "jane", "age": 30}
        ]
        
        task = TaskRequest(
            task_type="process_data",
            input_data={
                "data": test_data,
                "operation": "transform"
            }
        )
        
        result = await agent.execute_task(task)
        
        assert result.status == AgentStatus.COMPLETED
        assert result.result is not None
        assert "processed_data" in result.result
        assert "records_processed" in result.result

{% endif %}

{% if 'system_monitoring' in capabilities %}

class Test{{ agent_class }}AgentMonitoring:
    """System monitoring capability tests"""
    
    async def test_system_monitoring(self, agent):
        """Test system monitoring functionality"""
        task = TaskRequest(
            task_type="monitor_system",
            input_data={
                "metrics": ["cpu", "memory"]
            }
        )
        
        result = await agent.execute_task(task)
        
        assert result.status == AgentStatus.COMPLETED
        assert result.result is not None
        assert "system_metrics" in result.result
        assert "timestamp" in result.result
    
    @patch('psutil.cpu_percent')
    @patch('psutil.virtual_memory')
    async def test_monitoring_with_mocked_metrics(self, mock_memory, mock_cpu, agent):
        """Test monitoring with mocked system metrics"""
        # Setup mocks
        mock_cpu.return_value = 45.5
        mock_memory.return_value = Mock(percent=60.0, available=1024**3, total=2*1024**3)
        
        task = TaskRequest(
            task_type="monitor_system",
            input_data={"metrics": ["cpu", "memory"]}
        )
        
        result = await agent.execute_task(task)
        
        assert result.status == AgentStatus.COMPLETED
        assert result.result["system_metrics"]["cpu_usage"] == 45.5
        assert result.result["system_metrics"]["memory_usage"]["percent"] == 60.0

{% endif %}

{% if 'api_integration' in capabilities %}

class Test{{ agent_class }}AgentAPIIntegration:
    """API integration tests"""
    
    @patch('httpx.AsyncClient')
    async def test_api_initialization(self, mock_client, agent_config):
        """Test API client is initialized correctly"""
        agent = create_agent(agent_config)
        await agent.initialize()
        
        # Verify API client was created
        assert hasattr(agent, 'api_client')
        
        await agent.shutdown()
    
    async def test_api_client_cleanup(self, agent):
        """Test API client is properly cleaned up"""
        # Agent fixture handles initialization and shutdown
        # Just verify the pattern works
        assert True  # Placeholder test

{% endif %}

{% if 'database' in capabilities %}

class Test{{ agent_class }}AgentDatabase:
    """Database integration tests"""
    
    @patch('sqlalchemy.create_engine')
    async def test_database_initialization(self, mock_engine, agent_config):
        """Test database connection is initialized"""
        agent_config['database_url'] = 'sqlite:///:memory:'
        
        agent = create_agent(agent_config)
        await agent.initialize()
        
        assert hasattr(agent, 'db_engine')
        mock_engine.assert_called_once()
        
        await agent.shutdown()

{% endif %}


class Test{{ agent_class }}AgentErrorHandling:
    """Error handling and resilience tests"""
    
    async def test_task_timeout_handling(self, agent):
        """Test task execution with timeout"""
        task = TaskRequest(
            task_type="test",
            input_data={"message": "timeout test"},
            timeout=1  # 1 second timeout
        )
        
        result = await agent.execute_task(task)
        
        # Should complete regardless of timeout for this simple test
        assert isinstance(result, TaskResponse)
    
    async def test_exception_handling(self, agent):
        """Test agent handles exceptions gracefully"""
        # Create a task that might cause issues
        task = TaskRequest(
            task_type="nonexistent_type",
            input_data=None
        )
        
        result = await agent.execute_task(task)
        
        # Should handle gracefully
        assert isinstance(result, TaskResponse)
        assert result.task_id == task.task_id
    
    async def test_shutdown_while_idle(self, agent):
        """Test agent can shutdown while idle"""
        result = await agent.shutdown()
        assert result is True


class Test{{ agent_class }}AgentPerformance:
    """Performance and load tests"""
    
    @pytest.mark.performance
    async def test_task_execution_performance(self, agent):
        """Test task execution performance"""
        import time
        
        task = TaskRequest(
            task_type="test",
            input_data={"message": "performance test"}
        )
        
        start_time = time.time()
        result = await agent.execute_task(task)
        execution_time = time.time() - start_time
        
        assert result.status in [AgentStatus.COMPLETED, AgentStatus.ERROR]
        # Ensure reasonable execution time (< 5 seconds for simple tasks)
        assert execution_time < 5.0
    
    @pytest.mark.performance
    async def test_multiple_sequential_tasks(self, agent):
        """Test performance with multiple sequential tasks"""
        tasks = []
        results = []
        
        for i in range(10):
            task = TaskRequest(
                task_type="test",
                input_data={"message": f"Sequential task {i}"}
            )
            tasks.append(task)
        
        start_time = time.time()
        for task in tasks:
            result = await agent.execute_task(task)
            results.append(result)
        total_time = time.time() - start_time
        
        assert len(results) == 10
        # Ensure reasonable total time (< 30 seconds for 10 simple tasks)
        assert total_time < 30.0
        
        # All tasks should have completed or errored
        for result in results:
            assert result.status in [AgentStatus.COMPLETED, AgentStatus.ERROR]


class Test{{ agent_class }}AgentIntegration:
    """Integration tests with external systems"""
    
    async def test_agent_lifecycle(self, agent_config):
        """Test complete agent lifecycle"""
        # Create agent
        agent = create_agent(agent_config)
        assert agent.status == AgentStatus.IDLE
        
        # Initialize
        init_result = await agent.initialize()
        assert init_result is True
        
        # Execute task
        task = TaskRequest(
            task_type="test",
            input_data={"message": "lifecycle test"}
        )
        result = await agent.execute_task(task)
        assert isinstance(result, TaskResponse)
        
        # Get status
        status = await agent.get_status()
        assert status["agent_id"] == agent.agent_id
        
        # Shutdown
        shutdown_result = await agent.shutdown()
        assert shutdown_result is True
    
    async def test_config_validation(self):
        """Test agent handles various configuration scenarios"""
        # Empty config
        empty_agent = create_agent({})
        assert empty_agent.name == "{{ agent_name }}"
        
        # Config with extra fields
        extended_config = {
            "extra_field": "extra_value",
            "custom_setting": 123
        }
        extended_agent = create_agent(extended_config)
        assert extended_agent.config["extra_field"] == "extra_value"


# Utility functions for testing
def create_test_task(task_type: str = "test", **kwargs) -> TaskRequest:
    """Helper function to create test tasks"""
    default_data = {"message": "test message"}
    default_data.update(kwargs.get('input_data', {}))
    
    return TaskRequest(
        task_type=task_type,
        input_data=default_data,
        priority=kwargs.get('priority', 'medium')
    )


@pytest.mark.asyncio
async def test_agent_factory():
    """Test the create_agent factory function"""
    config = {"test": True}
    agent = create_agent(config)
    
    assert isinstance(agent, {{ agent_class }}Agent)
    assert agent.config["test"] is True


# Fixtures for complex testing scenarios
@pytest.fixture
def mock_external_api():
    """Mock external API for testing integrations"""
    with patch('httpx.AsyncClient') as mock_client:
        mock_instance = AsyncMock()
        mock_client.return_value = mock_instance
        
        # Setup default responses
        mock_instance.get.return_value.json = AsyncMock(return_value={"status": "ok"})
        mock_instance.post.return_value.json = AsyncMock(return_value={"result": "success"})
        
        yield mock_instance


# Performance benchmarks
@pytest.mark.benchmark
class Test{{ agent_class }}AgentBenchmarks:
    """Benchmark tests for performance monitoring"""
    
    async def test_task_throughput(self, agent):
        """Benchmark task processing throughput"""
        import time
        
        num_tasks = 50
        tasks = [create_test_task(f"task_{i}") for i in range(num_tasks)]
        
        start_time = time.time()
        results = await asyncio.gather(*[
            agent.execute_task(task) for task in tasks
        ])
        end_time = time.time()
        
        total_time = end_time - start_time
        throughput = num_tasks / total_time
        
        print(f"Task throughput: {throughput:.2f} tasks/second")
        
        assert len(results) == num_tasks
        assert throughput > 1.0  # At least 1 task per second


if __name__ == "__main__":
    pytest.main([__file__, "-v"])