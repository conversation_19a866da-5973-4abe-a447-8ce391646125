"""
Agent Generation Templates and Code Generation Engine
"""

import os
import ast
import json
from typing import Dict, Any, List, Optional
from enum import Enum
from dataclasses import dataclass
from pathlib import Path
from jinja2 import Environment, FileSystemLoader, Template
import yaml

from config.settings import settings


class AgentType(str, Enum):
    """Supported agent types for generation"""
    ASSISTANT = "assistant"
    ANALYST = "analyst"
    SPECIALIST = "specialist"
    COORDINATOR = "coordinator"
    PROCESSOR = "processor"
    MONITOR = "monitor"
    FULLSTACK = "fullstack"


class ProgrammingLanguage(str, Enum):
    """Supported programming languages"""
    PYTHON = "python"
    TYPESCRIPT = "typescript"
    JAVASCRIPT = "javascript"


@dataclass
class AgentTemplate:
    """Agent template definition"""
    name: str
    description: str
    agent_type: AgentType
    language: ProgrammingLanguage
    capabilities: List[str]
    dependencies: List[str]
    template_path: str
    config_schema: Dict[str, Any]
    examples: List[Dict[str, Any]]


@dataclass
class GenerationRequest:
    """Agent generation request"""
    agent_name: str
    agent_type: AgentType
    language: ProgrammingLanguage
    description: str
    capabilities: List[str]
    configuration: Dict[str, Any]
    requirements: Optional[str] = None
    agent_id: Optional[str] = None
    custom_logic: Optional[str] = None
    deployment_config: Optional[Dict[str, Any]] = None


@dataclass
class GeneratedCode:
    """Generated code result"""
    agent_code: str
    dockerfile: str
    requirements: List[str]
    config_file: str
    tests: str
    deployment_manifest: str
    documentation: str
    frontend_code: Optional[str] = None
    docker_compose: Optional[str] = None
    build_script: Optional[str] = None
    deploy_script: Optional[str] = None
    a2a_config: Optional[str] = None


class TemplateManager:
    """Manages agent templates and code generation"""
    
    def __init__(self):
        self.templates_dir = Path(__file__).parent / "templates"
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.templates_dir)),
            trim_blocks=True,
            lstrip_blocks=True
        )
        # Add custom filters
        self.jinja_env.filters['datetime'] = self._datetime_filter
        self.available_templates: Dict[str, AgentTemplate] = {}
        self._load_templates()
    
    def _datetime_filter(self, value, format_str="%Y-%m-%d %H:%M:%S"):
        """Custom Jinja2 filter for datetime formatting"""
        from datetime import datetime
        if value == "now":
            return datetime.now().strftime(format_str)
        return str(value)
    
    def _load_templates(self):
        """Load all available templates"""
        templates_config_path = self.templates_dir / "templates.yaml"
        if templates_config_path.exists():
            with open(templates_config_path, 'r') as f:
                templates_config = yaml.safe_load(f)
                
            for template_config in templates_config.get('templates', []):
                # Convert string enum values to proper enums
                template_config['agent_type'] = AgentType(template_config['agent_type'])
                template_config['language'] = ProgrammingLanguage(template_config['language'])
                template = AgentTemplate(**template_config)
                self.available_templates[template.name] = template
    
    def get_available_templates(self) -> List[AgentTemplate]:
        """Get list of available templates"""
        return list(self.available_templates.values())
    
    def get_template(self, template_name: str) -> Optional[AgentTemplate]:
        """Get specific template by name"""
        return self.available_templates.get(template_name)
    
    def get_templates_by_type(self, agent_type: AgentType) -> List[AgentTemplate]:
        """Get templates filtered by agent type"""
        return [t for t in self.available_templates.values() if t.agent_type == agent_type]
    
    def get_templates_by_language(self, language: ProgrammingLanguage) -> List[AgentTemplate]:
        """Get templates filtered by programming language"""
        return [t for t in self.available_templates.values() if t.language == language]


class CodeGenerator:
    """Generates agent code from templates"""
    
    def __init__(self, template_manager: TemplateManager):
        self.template_manager = template_manager
        self.jinja_env = template_manager.jinja_env
    
    def generate_agent(self, request: GenerationRequest) -> GeneratedCode:
        """Generate complete agent code package"""
        
        # Find appropriate template
        template = self._select_template(request)
        if not template:
            raise ValueError(f"No suitable template found for {request.agent_type} in {request.language}")
        
        # Prepare template context
        context = self._prepare_context(request, template)
        
        # Check if this is a comprehensive template (fullstack web agent)
        if (template.name == "python_fullstack_web" or 
            (template.agent_type == AgentType.FULLSTACK and template.language == ProgrammingLanguage.PYTHON)):
            return self._generate_from_comprehensive_template(template, context)
        
        # Generate all components
        agent_code = self._generate_agent_code(template, context)
        dockerfile = self._generate_dockerfile(template, context)
        requirements = self._generate_requirements(template, context)
        config_file = self._generate_config(template, context)
        tests = self._generate_tests(template, context)
        deployment_manifest = self._generate_deployment(template, context)
        documentation = self._generate_documentation(template, context)
        
        # Generate additional components for full-stack and A2A support
        frontend_code = None
        docker_compose = None
        build_script = None
        deploy_script = None
        a2a_config = None
        
        if request.agent_type == AgentType.FULLSTACK:
            frontend_code = self._generate_frontend_code(template, context)
            docker_compose = self._generate_docker_compose(template, context)
        
        # Always generate build/deploy scripts and A2A config
        build_script = self._generate_build_script(template, context)
        deploy_script = self._generate_deploy_script(template, context)
        a2a_config = self._generate_a2a_config(template, context)
        
        return GeneratedCode(
            agent_code=agent_code,
            dockerfile=dockerfile,
            requirements=requirements,
            config_file=config_file,
            tests=tests,
            deployment_manifest=deployment_manifest,
            documentation=documentation,
            frontend_code=frontend_code,
            docker_compose=docker_compose,
            build_script=build_script,
            deploy_script=deploy_script,
            a2a_config=a2a_config
        )
    
    def _generate_from_comprehensive_template(self, template: AgentTemplate, context: Dict[str, Any]) -> GeneratedCode:
        """Generate agent from comprehensive template that includes all files in one template"""
        template_file = "python/fullstack_web_agent.j2"
        jinja_template = self.template_manager.jinja_env.get_template(template_file)
        comprehensive_output = jinja_template.render(**context)
        
        # Parse the comprehensive output that uses FILE_SEPARATOR
        files = self._parse_comprehensive_template_output(comprehensive_output)
        
        return GeneratedCode(
            agent_code=files.get("app.py", ""),
            dockerfile=files.get("Dockerfile", ""),
            requirements=files.get("requirements.txt", "").split("\n") if files.get("requirements.txt") else [],
            config_file=files.get(".env", ""),
            tests=files.get("validate_build.py", ""),
            deployment_manifest=files.get("docker-compose.yml", ""),
            documentation=files.get("README.md", ""),
            frontend_code=self._combine_frontend_files(files),
            docker_compose=files.get("docker-compose.yml", ""),
            build_script=files.get("validate_build.py", ""),
            deploy_script="",
            a2a_config=""
        )
    
    def _parse_comprehensive_template_output(self, output: str) -> Dict[str, str]:
        """Parse comprehensive template output that uses FILE_SEPARATOR markers"""
        files = {}
        current_file = None
        current_content = []
        
        lines = output.split('\n')
        for line in lines:
            if line.strip() == '---FILE_SEPARATOR---':
                if current_file and current_content:
                    files[current_file] = '\n'.join(current_content)
                current_file = None
                current_content = []
            elif line.startswith('## ') and current_file is None:
                # Extract filename from ## filename
                current_file = line[3:].strip()
                current_content = []
            elif current_file:
                current_content.append(line)
        
        # Don't forget the last file
        if current_file and current_content:
            files[current_file] = '\n'.join(current_content)
        
        return files
    
    def _combine_frontend_files(self, files: Dict[str, str]) -> str:
        """Combine all frontend files into a structured format"""
        frontend_files = {}
        
        for filename, content in files.items():
            if filename.startswith('frontend/'):
                frontend_files[filename] = content
        
        # Return as JSON structure for storage
        import json
        return json.dumps(frontend_files, indent=2) if frontend_files else None
    
    def _select_template(self, request: GenerationRequest) -> Optional[AgentTemplate]:
        """Select the best template for the request"""
        # First try exact match
        for template in self.template_manager.available_templates.values():
            if (template.agent_type == request.agent_type and 
                template.language == request.language):
                return template
        
        # Fallback to language match
        language_templates = self.template_manager.get_templates_by_language(request.language)
        return language_templates[0] if language_templates else None
    
    def _prepare_context(self, request: GenerationRequest, template: AgentTemplate) -> Dict[str, Any]:
        """Prepare template rendering context"""
        context = {
            'agent_name': request.agent_name,
            'agent_class': self._to_class_name(request.agent_name),
            'agent_type': request.agent_type.value,
            'language': request.language.value,
            'description': request.description,
            'capabilities': request.capabilities,
            'configuration': request.configuration,
            'custom_logic': request.custom_logic,
            'deployment_config': request.deployment_config or {},
            'template': template,
            'imports': self._generate_imports(template, request),
            'dependencies': template.dependencies,
        }
        
        # Add additional context for comprehensive template
        if (template.name == "python_fullstack_web" or 
            (template.agent_type == AgentType.FULLSTACK and template.language == ProgrammingLanguage.PYTHON)):
            context.update({
                'agent_id': request.agent_id or 'agent-' + str(hash(request.agent_name))[:8],
                'backend_port': request.configuration.get('backend_port', 8000),
                'frontend_port': request.configuration.get('frontend_port', 3000),
                'api_endpoints': request.configuration.get('api_endpoints', []),
                'frontend_pages': request.configuration.get('frontend_pages', []),
                'frontend_dependencies': request.configuration.get('frontend_dependencies', {}),
                'additional_python_deps': request.configuration.get('additional_python_deps', []),
                'env_vars': request.configuration.get('env_vars', {}),
            })
        
        return context
    
    def _generate_agent_code(self, template: AgentTemplate, context: Dict[str, Any]) -> str:
        """Generate main agent code"""
        # Use comprehensive template for fullstack web agents
        if (template.name == "python_fullstack_web" or 
            (template.agent_type == AgentType.FULLSTACK and template.language == ProgrammingLanguage.PYTHON)):
            template_file = "python/fullstack_web_agent.j2"
        else:
            template_file = f"{template.language.value}/agent.{self._get_file_extension(template.language)}.j2"
        
        jinja_template = self.jinja_env.get_template(template_file)
        return jinja_template.render(**context)
    
    def _generate_dockerfile(self, template: AgentTemplate, context: Dict[str, Any]) -> str:
        """Generate Dockerfile for the agent"""
        jinja_template = self.jinja_env.get_template("docker/Dockerfile.j2")
        return jinja_template.render(**context)
    
    def _generate_requirements(self, template: AgentTemplate, context: Dict[str, Any]) -> List[str]:
        """Generate requirements/dependencies list"""
        base_requirements = template.dependencies.copy()
        
        # Add capability-specific requirements
        capability_requirements = {
            'web_scraping': ['requests', 'beautifulsoup4', 'selenium'],
            'data_analysis': ['pandas', 'numpy', 'matplotlib'],
            'nlp': ['transformers', 'spacy', 'nltk'],
            'computer_vision': ['opencv-python', 'pillow', 'scikit-image'],
            'database': ['sqlalchemy', 'psycopg2-binary'],
            'api_integration': ['httpx', 'requests'],
        }
        
        for capability in context['capabilities']:
            if capability in capability_requirements:
                base_requirements.extend(capability_requirements[capability])
        
        return list(set(base_requirements))  # Remove duplicates
    
    def _generate_config(self, template: AgentTemplate, context: Dict[str, Any]) -> str:
        """Generate configuration file"""
        config_data = {
            'agent': {
                'name': context['agent_name'],
                'type': context['agent_type'],
                'version': '1.0.0',
                'description': context['description'],
                'capabilities': context['capabilities']
            },
            'runtime': {
                'max_concurrent_tasks': 5,
                'timeout_seconds': 300,
                'retry_attempts': 3
            },
            'custom': context['configuration']
        }
        
        if template.language == ProgrammingLanguage.PYTHON:
            return yaml.dump(config_data, default_flow_style=False)
        else:
            return json.dumps(config_data, indent=2)
    
    def _generate_tests(self, template: AgentTemplate, context: Dict[str, Any]) -> str:
        """Generate test suite for the agent"""
        template_file = f"{template.language.value}/tests.{self._get_file_extension(template.language)}.j2"
        jinja_template = self.jinja_env.get_template(template_file)
        return jinja_template.render(**context)
    
    def _generate_deployment(self, template: AgentTemplate, context: Dict[str, Any]) -> str:
        """Generate Kubernetes deployment manifest"""
        jinja_template = self.jinja_env.get_template("kubernetes/deployment.yaml.j2")
        return jinja_template.render(**context)
    
    def _generate_documentation(self, template: AgentTemplate, context: Dict[str, Any]) -> str:
        """Generate README documentation for the agent"""
        jinja_template = self.jinja_env.get_template("docs/README.md.j2")
        return jinja_template.render(**context)
    
    def _to_class_name(self, agent_name: str) -> str:
        """Convert agent name to Python class name"""
        return ''.join(word.capitalize() for word in agent_name.replace('-', '_').split('_'))
    
    def _get_file_extension(self, language: ProgrammingLanguage) -> str:
        """Get file extension for language"""
        extensions = {
            ProgrammingLanguage.PYTHON: 'py',
            ProgrammingLanguage.TYPESCRIPT: 'ts',
            ProgrammingLanguage.JAVASCRIPT: 'js'
        }
        return extensions[language]
    
    def _generate_imports(self, template: AgentTemplate, request: GenerationRequest) -> List[str]:
        """Generate import statements based on capabilities"""
        imports = []
        
        if request.language == ProgrammingLanguage.PYTHON:
            base_imports = [
                "import asyncio",
                "import logging",
                "from typing import Dict, Any, List, Optional",
                "from datetime import datetime",
            ]
            imports.extend(base_imports)
            
            # Capability-specific imports
            capability_imports = {
                'web_scraping': ["import requests", "from bs4 import BeautifulSoup"],
                'data_analysis': ["import pandas as pd", "import numpy as np"],
                'nlp': ["from transformers import pipeline"],
                'api_integration': ["import httpx"],
            }
            
            for capability in request.capabilities:
                if capability in capability_imports:
                    imports.extend(capability_imports[capability])
        
        return imports
    
    def _generate_frontend_code(self, template: AgentTemplate, context: Dict[str, Any]) -> str:
        """Generate frontend code for full-stack agents"""
        if template.language == ProgrammingLanguage.TYPESCRIPT:
            frontend_template = "fullstack/frontend.tsx.j2"
        else:
            frontend_template = "fullstack/frontend.html.j2"
        
        try:
            jinja_template = self.jinja_env.get_template(frontend_template)
            return jinja_template.render(**context)
        except Exception:
            # Fallback to basic HTML frontend
            return self._generate_basic_frontend(context)
    
    def _generate_basic_frontend(self, context: Dict[str, Any]) -> str:
        """Generate basic HTML frontend as fallback"""
        return f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{context['agent_name']} - Agent Interface</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .container {{ max-width: 800px; margin: 0 auto; }}
        .chat-box {{ border: 1px solid #ccc; height: 400px; overflow-y: auto; padding: 10px; margin: 10px 0; }}
        .input-area {{ display: flex; gap: 10px; }}
        .input-area input {{ flex: 1; padding: 10px; }}
        .input-area button {{ padding: 10px 20px; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>{context['agent_name']} Agent Interface</h1>
        <p>{context['description']}</p>
        <div class="chat-box" id="chatBox"></div>
        <div class="input-area">
            <input type="text" id="messageInput" placeholder="Enter your message...">
            <button onclick="sendMessage()">Send</button>
        </div>
    </div>
    <script>
        async function sendMessage() {{
            const input = document.getElementById('messageInput');
            const chatBox = document.getElementById('chatBox');
            const message = input.value.trim();
            if (!message) return;
            
            chatBox.innerHTML += '<div><strong>You:</strong> ' + message + '</div>';
            input.value = '';
            
            try {{
                const response = await fetch('/api/chat', {{
                    method: 'POST',
                    headers: {{ 'Content-Type': 'application/json' }},
                    body: JSON.stringify({{ message: message }})
                }});
                const data = await response.json();
                chatBox.innerHTML += '<div><strong>Agent:</strong> ' + data.response + '</div>';
                chatBox.scrollTop = chatBox.scrollHeight;
            }} catch (error) {{
                chatBox.innerHTML += '<div><strong>Error:</strong> Failed to get response</div>';
            }}
        }}
        
        document.getElementById('messageInput').addEventListener('keypress', function(e) {{
            if (e.key === 'Enter') sendMessage();
        }});
    </script>
</body>
</html>"""
    
    def _generate_docker_compose(self, template: AgentTemplate, context: Dict[str, Any]) -> str:
        """Generate docker-compose.yml for full-stack deployment"""
        return f"""version: '3.8'

services:
  {context['agent_name']}-backend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - AGENT_NAME={context['agent_name']}
      - AGENT_TYPE={context['agent_type']}
      - A2A_ENABLED=true
      - A2A_PORT=8001
    volumes:
      - ./config:/app/config:ro
      - ./logs:/app/logs
    networks:
      - agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  {context['agent_name']}-frontend:
    image: nginx:alpine
    ports:
      - "3000:80"
    volumes:
      - ./frontend.html:/usr/share/nginx/html/index.html:ro
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - {context['agent_name']}-backend
    networks:
      - agent-network
    restart: unless-stopped

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    networks:
      - agent-network
    restart: unless-stopped

networks:
  agent-network:
    driver: bridge

volumes:
  logs:
  config:
"""
    
    def _generate_build_script(self, template: AgentTemplate, context: Dict[str, Any]) -> str:
        """Generate build script with all parameters"""
        from datetime import datetime
        script = f"""#!/bin/bash
# Build script for {context['agent_name']} agent
# Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

set -e

echo "Building {context['agent_name']} agent..."

# Build parameters from configuration
AGENT_NAME="{context['agent_name']}"
AGENT_TYPE="{context['agent_type']}"
IMAGE_TAG="${{AGENT_NAME}}:latest"

# Build Docker image
echo "Building Docker image..."
docker build -t "$IMAGE_TAG" .

# Run tests
echo "Running tests..."
docker run --rm "$IMAGE_TAG" python -m pytest tests/ -v

# Create build artifacts
echo "Creating build artifacts..."
mkdir -p build/
cp -r config/ build/
cp -r logs/ build/
"""

        # Add full-stack specific build steps
        if context.get('frontend_code'):
            script += """
# Build frontend
echo "Building frontend..."
mkdir -p build/frontend/
cp frontend.html build/frontend/
cp nginx.conf build/frontend/
"""

        # Add deployment configuration from parameters
        deployment_config = context.get('deployment_config', {})
        if deployment_config:
            script += f"""
# Apply deployment configuration
echo "Configuring deployment with parameters:"
echo "  - Namespace: {deployment_config.get('namespace', 'default')}"
echo "  - Replicas: {deployment_config.get('replicas', 1)}"
echo "  - CPU Limit: {deployment_config.get('cpu_limit', '500m')}"
echo "  - Memory Limit: {deployment_config.get('memory_limit', '512Mi')}"
"""

        script += """
echo "Build completed successfully!"
echo "Image: $IMAGE_TAG"
echo "Build artifacts in: build/"
"""
        return script
    
    def _generate_deploy_script(self, template: AgentTemplate, context: Dict[str, Any]) -> str:
        """Generate deployment script with A2A support"""
        from datetime import datetime
        deployment_config = context.get('deployment_config', {})
        
        script = f"""#!/bin/bash
# Deploy script for {context['agent_name']} agent with A2A support
# Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

set -e

AGENT_NAME="{context['agent_name']}"
NAMESPACE="{deployment_config.get('namespace', 'default')}"
IMAGE_TAG="${{AGENT_NAME}}:latest"

echo "Deploying $AGENT_NAME agent to Kubernetes..."

# Create namespace if it doesn't exist
kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -

# Apply all Kubernetes manifests
echo "Applying Kubernetes manifests..."
kubectl apply -f deployment.yaml -n "$NAMESPACE"

# Wait for deployment to be ready
echo "Waiting for deployment to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/$AGENT_NAME-agent -n "$NAMESPACE"

# Verify A2A protocol is enabled
echo "Verifying A2A protocol..."
kubectl exec -n "$NAMESPACE" deployment/$AGENT_NAME-agent -- python -c "
import sys
sys.path.append('/app')
from protocols.a2a_protocol import A2AProtocolHandler
handler = A2AProtocolHandler()
print('A2A Protocol Status:', 'ENABLED' if handler.is_enabled() else 'DISABLED')
"

# Get service endpoints
echo "Service endpoints:"
kubectl get svc -n "$NAMESPACE" -l app=$AGENT_NAME-agent

# Show A2A communication test
echo "Testing A2A communication..."
kubectl exec -n "$NAMESPACE" deployment/$AGENT_NAME-agent -- python -c "
import asyncio
import sys
sys.path.append('/app')
from protocols.a2a_protocol import A2AProtocolHandler, A2AMessage, A2AMessageType

async def test_a2a():
    handler = A2AProtocolHandler()
    
    # Create test message
    test_msg = A2AMessage(
        message_type=A2AMessageType.TASK_REQUEST,
        sender_id='deploy-test',
        recipient_id='{context['agent_name']}',
        content={{'task': 'health_check', 'test': True}},
        priority='normal'
    )
    
    # Process message (simulates A2A communication)
    response = await handler.process_message(test_msg)
    print('A2A Test Response:', response.content)

asyncio.run(test_a2a())
"

echo "Deployment completed successfully!"
echo "Agent $AGENT_NAME is running in namespace $NAMESPACE with A2A support enabled"
"""
        return script
    
    def _generate_a2a_config(self, template: AgentTemplate, context: Dict[str, Any]) -> str:
        """Generate A2A protocol configuration"""
        config = {
            "a2a_protocol": {
                "enabled": True,
                "agent_id": context['agent_name'],
                "agent_type": context['agent_type'],
                "capabilities": context['capabilities'],
                "communication": {
                    "protocol": "http",
                    "port": 8001,
                    "timeout": 30,
                    "retry_attempts": 3,
                    "heartbeat_interval": 60
                },
                "message_types": {
                    "supported": [
                        "TASK_REQUEST",
                        "TASK_RESPONSE", 
                        "STATUS_UPDATE",
                        "CAPABILITY_QUERY",
                        "COORDINATION_REQUEST",
                        "RESOURCE_REQUEST",
                        "ERROR_REPORT",
                        "HEARTBEAT",
                        "SHUTDOWN_REQUEST",
                        "BATCH_REQUEST",
                        "STREAM_REQUEST"
                    ]
                },
                "routing": {
                    "discovery_service": "http://agent-registry:8080/discovery",
                    "load_balancer": "round_robin",
                    "failover_enabled": True
                },
                "security": {
                    "authentication": "jwt",
                    "encryption": "tls",
                    "rate_limiting": {
                        "max_requests_per_minute": 100,
                        "burst_size": 20
                    }
                },
                "monitoring": {
                    "metrics_enabled": True,
                    "health_check_endpoint": "/a2a/health",
                    "metrics_endpoint": "/a2a/metrics"
                }
            },
            "integration": {
                "orchestrator": {
                    "enabled": True,
                    "endpoint": "http://orchestrator:8080",
                    "registration_required": True
                },
                "workflow_engine": {
                    "enabled": context['agent_type'] in ['coordinator', 'fullstack'],
                    "n8n_compatible": True
                }
            }
        }
        
        return yaml.dump(config, default_flow_style=False)


class AgentBuilder:
    """High-level agent builder interface"""
    
    def __init__(self):
        self.template_manager = TemplateManager()
        self.code_generator = CodeGenerator(self.template_manager)
    
    def create_agent(self, request: GenerationRequest) -> GeneratedCode:
        """Create a new agent from the request"""
        return self.code_generator.generate_agent(request)
    
    def get_available_templates(self) -> List[Dict[str, Any]]:
        """Get available templates for the UI"""
        templates = self.template_manager.get_available_templates()
        return [
            {
                'name': t.name,
                'description': t.description,
                'agent_type': t.agent_type.value,
                'language': t.language.value,
                'capabilities': t.capabilities,
                'config_schema': t.config_schema,
                'examples': t.examples
            }
            for t in templates
        ]
    
    def validate_request(self, request: GenerationRequest) -> List[str]:
        """Validate generation request and return errors if any"""
        errors = []
        
        if not request.agent_name.strip():
            errors.append("Agent name is required")
        
        if not request.description.strip():
            errors.append("Agent description is required")
        
        if not request.capabilities:
            errors.append("At least one capability is required")
        
        # Validate agent name format
        if not request.agent_name.replace('-', '').replace('_', '').isalnum():
            errors.append("Agent name must contain only letters, numbers, hyphens, and underscores")
        
        return errors


# Global instance
agent_builder = AgentBuilder()