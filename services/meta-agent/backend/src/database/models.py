"""
AI Agent Platform - Database Models
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from enum import Enum
import uuid

from sqlalchemy import (
    Column, String, Integer, <PERSON>olean, DateTime, Text, JSON,
    ForeignKey, Index, UniqueConstraint, Float
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID as PostgreSQL_UUID
from sqlalchemy.dialects import postgresql
import uuid


# PostgreSQL UUID type - simplified since we only support PostgreSQL
def uuid_column():
    return Column(PostgreSQL_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)


Base = declarative_base()


class AgentStatus(str, Enum):
    """Agent status enumeration"""
    CREATED = "created"
    STARTING = "starting"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPED = "stopped"
    ERROR = "error"
    TERMINATED = "terminated"


class TaskStatus(str, Enum):
    """Task status enumeration"""
    PENDING = "pending"
    ASSIGNED = "assigned"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class OrchestrationPattern(str, Enum):
    """Orchestration pattern enumeration"""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    HIERARCHICAL = "hierarchical"
    PEER_TO_PEER = "peer_to_peer"
    EVENT_DRIVEN = "event_driven"


# Base model with common fields
class BaseModel:
    """Base model with common fields"""
    id = Column(PostgreSQL_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    created_by = Column(String(255), nullable=True)
    updated_by = Column(String(255), nullable=True)


class User(BaseModel, Base):
    """User model"""
    __tablename__ = "users"
    
    username = Column(String(255), unique=True, nullable=False)
    email = Column(String(255), unique=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)
    last_login = Column(DateTime, nullable=True)
    
    # Enhanced user fields
    first_name = Column(String(255), nullable=True)
    last_name = Column(String(255), nullable=True)
    avatar_url = Column(String(500), nullable=True)
    is_verified = Column(Boolean, default=False, nullable=False)
    verified_at = Column(DateTime, nullable=True)
    last_login_at = Column(DateTime, nullable=True)
    
    # OAuth fields
    oauth_provider = Column(String(50), nullable=True)  # 'google', 'github', 'microsoft'
    oauth_provider_id = Column(String(255), nullable=True)
    
    # Relationships
    agents = relationship("Agent", back_populates="owner")
    orchestrations = relationship("Orchestration", back_populates="owner")
    oauth_tokens = relationship("OAuthToken", back_populates="user")
    user_roles = relationship("UserRole", back_populates="user")
    
    __table_args__ = (
        Index("ix_users_email", "email"),
        Index("ix_users_username", "username"),
    )


class Agent(BaseModel, Base):
    """Agent model"""
    __tablename__ = "agents"
    
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    type = Column(String(100), nullable=False)  # e.g., 'assistant', 'specialist', 'coordinator'
    status = Column(String(50), default=AgentStatus.CREATED, nullable=False)
    
    # Configuration
    config = Column(JSON, nullable=False, default=lambda: {})
    capabilities = Column(JSON, nullable=False, default=lambda: [])
    constraints = Column(JSON, nullable=False, default=lambda: {})
    
    # Runtime information
    version = Column(String(50), nullable=False, default="1.0.0")
    runtime_info = Column(JSON, nullable=True)
    last_heartbeat = Column(DateTime, nullable=True)
    
    # Resource usage
    cpu_usage = Column(Float, nullable=True)
    memory_usage = Column(Integer, nullable=True)  # in MB
    
    # Relationships
    owner_id = Column(PostgreSQL_UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    owner = relationship("User", back_populates="agents")
    
    tasks = relationship("Task", back_populates="assigned_agent")
    orchestration_members = relationship("OrchestrationMember", back_populates="agent")
    
    __table_args__ = (
        Index("ix_agents_owner_id", "owner_id"),
        Index("ix_agents_status", "status"),
        Index("ix_agents_type", "type"),
    )


class Task(BaseModel, Base):
    """Task model"""
    __tablename__ = "tasks"
    
    title = Column(String(500), nullable=False)
    description = Column(Text, nullable=True)
    type = Column(String(100), nullable=False)
    status = Column(String(50), default=TaskStatus.PENDING, nullable=False)
    priority = Column(Integer, default=5, nullable=False)  # 1-10 scale
    
    # Task data
    input_data = Column(JSON, nullable=True)
    output_data = Column(JSON, nullable=True)
    task_metadata = Column(JSON, nullable=False, default=lambda: {})
    
    # Timing
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    deadline = Column(DateTime, nullable=True)
    
    # Progress tracking
    progress_percentage = Column(Integer, default=0, nullable=False)
    steps_completed = Column(Integer, default=0, nullable=False)
    total_steps = Column(Integer, nullable=True)
    
    # Relationships
    assigned_agent_id = Column(PostgreSQL_UUID(as_uuid=True), ForeignKey("agents.id"), nullable=True)
    assigned_agent = relationship("Agent", back_populates="tasks")
    
    orchestration_id = Column(PostgreSQL_UUID(as_uuid=True), ForeignKey("orchestrations.id"), nullable=True)
    orchestration = relationship("Orchestration", back_populates="tasks")
    
    parent_task_id = Column(PostgreSQL_UUID(as_uuid=True), ForeignKey("tasks.id"), nullable=True)
    subtasks = relationship("Task", backref="parent_task", remote_side="Task.id")
    
    __table_args__ = (
        Index("ix_tasks_status", "status"),
        Index("ix_tasks_priority", "priority"),
        Index("ix_tasks_assigned_agent_id", "assigned_agent_id"),
    )


class Orchestration(BaseModel, Base):
    """Multi-agent orchestration model"""
    __tablename__ = "orchestrations"
    
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    pattern = Column(String(50), default=OrchestrationPattern.SEQUENTIAL, nullable=False)
    status = Column(String(50), default="created", nullable=False)
    
    # Configuration
    config = Column(JSON, nullable=False, default=lambda: {})
    execution_plan = Column(JSON, nullable=False, default=lambda: {})
    
    # Timing
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # Progress
    progress_percentage = Column(Integer, default=0, nullable=False)
    
    # Relationships
    owner_id = Column(PostgreSQL_UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    owner = relationship("User", back_populates="orchestrations")
    
    tasks = relationship("Task", back_populates="orchestration")
    members = relationship("OrchestrationMember", back_populates="orchestration")
    
    __table_args__ = (
        Index("ix_orchestrations_owner_id", "owner_id"),
        Index("ix_orchestrations_status", "status"),
    )


class OrchestrationMember(BaseModel, Base):
    """Orchestration member relationship model"""
    __tablename__ = "orchestration_members"
    
    role = Column(String(100), nullable=False)  # e.g., 'leader', 'worker', 'coordinator'
    join_order = Column(Integer, nullable=False, default=0)
    
    # Relationships
    orchestration_id = Column(PostgreSQL_UUID(as_uuid=True), ForeignKey("orchestrations.id"), nullable=False)
    orchestration = relationship("Orchestration", back_populates="members")
    
    agent_id = Column(PostgreSQL_UUID(as_uuid=True), ForeignKey("agents.id"), nullable=False)
    agent = relationship("Agent", back_populates="orchestration_members")
    
    __table_args__ = (
        UniqueConstraint("orchestration_id", "agent_id", name="unique_orchestration_agent"),
        Index("ix_orchestration_members_orchestration_id", "orchestration_id"),
        Index("ix_orchestration_members_agent_id", "agent_id"),
    )


class AIModel(BaseModel, Base):
    """AI Model configuration model"""
    __tablename__ = "ai_models"
    
    name = Column(String(255), nullable=False, unique=True)
    provider = Column(String(100), nullable=False)  # 'openai', 'anthropic', 'google'
    model_id = Column(String(255), nullable=False)
    version = Column(String(50), nullable=True)
    
    # Configuration
    config = Column(JSON, nullable=False, default=lambda: {})
    capabilities = Column(JSON, nullable=False, default=lambda: [])
    
    # Limits and pricing
    max_tokens = Column(Integer, nullable=True)
    input_cost_per_token = Column(Float, nullable=True)
    output_cost_per_token = Column(Float, nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    is_default = Column(Boolean, default=False, nullable=False)
    
    __table_args__ = (
        Index("ix_ai_models_provider", "provider"),
        Index("ix_ai_models_is_active", "is_active"),
    )


class AgentGeneration(BaseModel, Base):
    """Agent generation request model"""
    __tablename__ = "agent_generations"
    
    specification = Column(JSON, nullable=False)
    generated_code = Column(JSON, nullable=True)
    status = Column(String(50), default="pending", nullable=False)
    
    # Generation settings
    target_framework = Column(String(100), nullable=False)  # 'fastapi', 'spring_boot', 'react'
    deployment_target = Column(String(100), nullable=False)  # 'kubernetes', 'docker', 'serverless'
    
    # Results
    generated_files = Column(JSON, nullable=True)
    build_logs = Column(Text, nullable=True)
    test_results = Column(JSON, nullable=True)
    
    # Relationships
    requester_id = Column(PostgreSQL_UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    __table_args__ = (
        Index("ix_agent_generations_status", "status"),
        Index("ix_agent_generations_target_framework", "target_framework"),
    )


class Communication(BaseModel, Base):
    """Agent-to-Agent communication log"""
    __tablename__ = "communications"
    
    protocol = Column(String(50), nullable=False)  # 'a2a', 'google_adk', 'http'
    message_type = Column(String(100), nullable=False)
    content = Column(JSON, nullable=False)
    
    # Relationships
    sender_agent_id = Column(PostgreSQL_UUID(as_uuid=True), ForeignKey("agents.id"), nullable=False)
    receiver_agent_id = Column(PostgreSQL_UUID(as_uuid=True), ForeignKey("agents.id"), nullable=True)
    
    # Status
    status = Column(String(50), default="sent", nullable=False)  # sent, delivered, read, error
    response_required = Column(Boolean, default=False, nullable=False)
    response_received = Column(Boolean, default=False, nullable=False)
    
    __table_args__ = (
        Index("ix_communications_sender_agent_id", "sender_agent_id"),
        Index("ix_communications_receiver_agent_id", "receiver_agent_id"),
        Index("ix_communications_status", "status"),
    )


class OAuthToken(BaseModel, Base):
    """OAuth token storage model"""
    __tablename__ = "oauth_tokens"
    
    provider = Column(String(50), nullable=False)  # 'google', 'github', 'microsoft'
    access_token = Column(Text, nullable=False)
    refresh_token = Column(Text, nullable=True)
    token_type = Column(String(20), default="Bearer", nullable=False)
    expires_in = Column(Integer, nullable=True)  # seconds
    expires_at = Column(DateTime, nullable=True)
    scope = Column(String(500), nullable=True)
    
    # Relationships
    user_id = Column(PostgreSQL_UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    user = relationship("User", back_populates="oauth_tokens")
    
    __table_args__ = (
        UniqueConstraint("user_id", "provider", name="unique_user_provider_token"),
        Index("ix_oauth_tokens_user_id", "user_id"),
        Index("ix_oauth_tokens_provider", "provider"),
        Index("ix_oauth_tokens_expires_at", "expires_at"),
    )


class Role(BaseModel, Base):
    """Role definition model"""
    __tablename__ = "roles"
    
    name = Column(String(100), unique=True, nullable=False)
    display_name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    is_system = Column(Boolean, default=False, nullable=False)  # System roles cannot be deleted
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    permissions = relationship("RolePermission", back_populates="role")
    user_roles = relationship("UserRole", back_populates="role")
    
    __table_args__ = (
        Index("ix_roles_name", "name"),
        Index("ix_roles_is_active", "is_active"),
    )


class Permission(BaseModel, Base):
    """Permission definition model"""
    __tablename__ = "permissions"
    
    name = Column(String(100), unique=True, nullable=False)
    display_name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    resource = Column(String(100), nullable=False)  # 'agents', 'orchestrations', 'tasks', 'users'
    action = Column(String(50), nullable=False)  # 'create', 'read', 'update', 'delete', 'execute'
    is_system = Column(Boolean, default=False, nullable=False)
    
    # Relationships
    role_permissions = relationship("RolePermission", back_populates="permission")
    
    __table_args__ = (
        Index("ix_permissions_name", "name"),
        Index("ix_permissions_resource", "resource"),
        Index("ix_permissions_action", "action"),
    )


class UserRole(BaseModel, Base):
    """User-Role association model"""
    __tablename__ = "user_roles"
    
    granted_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    granted_by = Column(PostgreSQL_UUID(as_uuid=True), nullable=True)  # User who granted the role
    expires_at = Column(DateTime, nullable=True)  # Role expiration
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    user_id = Column(PostgreSQL_UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    user = relationship("User", back_populates="user_roles")
    
    role_id = Column(PostgreSQL_UUID(as_uuid=True), ForeignKey("roles.id"), nullable=False)
    role = relationship("Role", back_populates="user_roles")
    
    __table_args__ = (
        UniqueConstraint("user_id", "role_id", name="unique_user_role"),
        Index("ix_user_roles_user_id", "user_id"),
        Index("ix_user_roles_role_id", "role_id"),
        Index("ix_user_roles_is_active", "is_active"),
    )


class RolePermission(BaseModel, Base):
    """Role-Permission association model"""
    __tablename__ = "role_permissions"
    
    # Relationships
    role_id = Column(PostgreSQL_UUID(as_uuid=True), ForeignKey("roles.id"), nullable=False)
    role = relationship("Role", back_populates="permissions")
    
    permission_id = Column(PostgreSQL_UUID(as_uuid=True), ForeignKey("permissions.id"), nullable=False)
    permission = relationship("Permission", back_populates="role_permissions")
    
    __table_args__ = (
        UniqueConstraint("role_id", "permission_id", name="unique_role_permission"),
        Index("ix_role_permissions_role_id", "role_id"),
        Index("ix_role_permissions_permission_id", "permission_id"),
    )


class MFADevice(BaseModel, Base):
    """Multi-Factor Authentication device model"""
    __tablename__ = "mfa_devices"
    
    device_type = Column(String(20), nullable=False)  # 'totp', 'sms', 'email', 'webauthn'
    device_name = Column(String(100), nullable=False)  # User-friendly name
    secret_key = Column(Text, nullable=True)  # For TOTP devices
    phone_number = Column(String(20), nullable=True)  # For SMS devices
    is_verified = Column(Boolean, default=False, nullable=False)
    is_primary = Column(Boolean, default=False, nullable=False)
    last_used_at = Column(DateTime, nullable=True)
    
    # Backup codes
    backup_codes = Column(JSON, nullable=True)  # Encrypted backup codes
    backup_codes_used = Column(JSON, nullable=True, default=lambda: [])
    
    # Relationships
    user_id = Column(PostgreSQL_UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    user = relationship("User")
    
    __table_args__ = (
        Index("ix_mfa_devices_user_id", "user_id"),
        Index("ix_mfa_devices_device_type", "device_type"),
        Index("ix_mfa_devices_is_verified", "is_verified"),
    )


class LoginAttempt(BaseModel, Base):
    """Login attempt tracking for security"""
    __tablename__ = "login_attempts"
    
    email = Column(String(255), nullable=False)
    ip_address = Column(String(45), nullable=False)  # IPv6 support
    user_agent = Column(Text, nullable=True)
    success = Column(Boolean, nullable=False)
    failure_reason = Column(String(100), nullable=True)
    mfa_required = Column(Boolean, default=False, nullable=False)
    mfa_success = Column(Boolean, nullable=True)
    oauth_provider = Column(String(50), nullable=True)
    
    # Geographic info (optional)
    country = Column(String(2), nullable=True)  # ISO country code
    city = Column(String(100), nullable=True)
    
    __table_args__ = (
        Index("ix_login_attempts_email", "email"),
        Index("ix_login_attempts_ip_address", "ip_address"),
        Index("ix_login_attempts_created_at", "created_at"),
        Index("ix_login_attempts_success", "success"),
    )