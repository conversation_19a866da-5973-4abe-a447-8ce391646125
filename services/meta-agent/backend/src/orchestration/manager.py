"""
AI Agent Platform - Orchestration Manager
"""

import asyncio
from typing import Dict, List, Optional, Any
from uuid import UUID
import structlog

from .orchestrator import OrchestrationEngine, OrchestrationStatus
from database.models import Orchestration, OrchestrationPattern
from config.settings import settings

logger = structlog.get_logger()


class OrchestrationManager:
    """Manages multiple orchestration engines"""
    
    def __init__(self):
        self.orchestrations: Dict[UUID, OrchestrationEngine] = {}
        self.max_concurrent_orchestrations = 100  # Configurable limit
        
        logger.info(
            "Orchestration manager initialized",
            max_orchestrations=self.max_concurrent_orchestrations
        )
    
    async def create_orchestration(
        self,
        orchestration: Orchestration,
        agent_ids: List[UUID],
        execution_plan: Optional[Dict[str, Any]] = None
    ) -> OrchestrationEngine:
        """Create a new orchestration engine"""
        try:
            orchestration_id = orchestration.id
            
            # Check if orchestration already exists
            if orchestration_id in self.orchestrations:
                logger.warning(
                    "Orchestration already exists",
                    orchestration_id=str(orchestration_id)
                )
                return self.orchestrations[orchestration_id]
            
            # Check concurrent orchestration limit
            running_count = len([
                o for o in self.orchestrations.values()
                if o.status == OrchestrationStatus.RUNNING
            ])
            
            if running_count >= self.max_concurrent_orchestrations:
                raise RuntimeError(
                    f"Maximum concurrent orchestrations exceeded: {running_count}"
                )
            
            # Create orchestration config
            config = {
                **orchestration.config,
                'pattern': orchestration.pattern,
                'agent_ids': agent_ids,
                'execution_plan': execution_plan or orchestration.execution_plan
            }
            
            # Create orchestration engine
            engine = OrchestrationEngine(orchestration_id, config)
            self.orchestrations[orchestration_id] = engine
            
            logger.info(
                "Orchestration created successfully",
                orchestration_id=str(orchestration_id),
                pattern=orchestration.pattern
            )
            
            return engine
            
        except Exception as e:
            logger.error(
                "Failed to create orchestration",
                orchestration_id=str(orchestration.id),
                error=str(e)
            )
            raise
    
    async def start_orchestration(self, orchestration_id: UUID) -> bool:
        """Start an orchestration"""
        try:
            if orchestration_id not in self.orchestrations:
                logger.error(
                    "Orchestration not found",
                    orchestration_id=str(orchestration_id)
                )
                return False
            
            engine = self.orchestrations[orchestration_id]
            return await engine.start()
            
        except Exception as e:
            logger.error(
                "Failed to start orchestration",
                orchestration_id=str(orchestration_id),
                error=str(e)
            )
            return False
    
    async def stop_orchestration(
        self,
        orchestration_id: UUID,
        graceful: bool = True
    ) -> bool:
        """Stop an orchestration"""
        try:
            if orchestration_id not in self.orchestrations:
                return False
            
            engine = self.orchestrations[orchestration_id]
            success = await engine.stop(graceful)
            
            if success:
                # Remove from active orchestrations
                del self.orchestrations[orchestration_id]
            
            return success
            
        except Exception as e:
            logger.error(
                "Failed to stop orchestration",
                orchestration_id=str(orchestration_id),
                error=str(e)
            )
            return False
    
    async def pause_orchestration(self, orchestration_id: UUID) -> bool:
        """Pause an orchestration"""
        try:
            if orchestration_id not in self.orchestrations:
                return False
            
            engine = self.orchestrations[orchestration_id]
            return await engine.pause()
            
        except Exception as e:
            logger.error(
                "Failed to pause orchestration",
                orchestration_id=str(orchestration_id),
                error=str(e)
            )
            return False
    
    async def resume_orchestration(self, orchestration_id: UUID) -> bool:
        """Resume an orchestration"""
        try:
            if orchestration_id not in self.orchestrations:
                return False
            
            engine = self.orchestrations[orchestration_id]
            return await engine.resume()
            
        except Exception as e:
            logger.error(
                "Failed to resume orchestration",
                orchestration_id=str(orchestration_id),
                error=str(e)
            )
            return False
    
    async def get_orchestration_progress(
        self,
        orchestration_id: UUID
    ) -> Optional[Dict[str, Any]]:
        """Get orchestration progress"""
        try:
            if orchestration_id not in self.orchestrations:
                return None
            
            engine = self.orchestrations[orchestration_id]
            return await engine.get_progress()
            
        except Exception as e:
            logger.error(
                "Failed to get orchestration progress",
                orchestration_id=str(orchestration_id),
                error=str(e)
            )
            return None
    
    async def add_task_to_orchestration(
        self,
        orchestration_id: UUID,
        task_id: UUID,
        task_type: str,
        task_data: Dict[str, Any],
        dependencies: Optional[List[UUID]] = None
    ) -> bool:
        """Add a task to an orchestration"""
        try:
            if orchestration_id not in self.orchestrations:
                return False
            
            engine = self.orchestrations[orchestration_id]
            return await engine.add_task(
                task_id, task_type, task_data, dependencies
            )
            
        except Exception as e:
            logger.error(
                "Failed to add task to orchestration",
                orchestration_id=str(orchestration_id),
                task_id=str(task_id),
                error=str(e)
            )
            return False
    
    async def list_active_orchestrations(self) -> List[Dict[str, Any]]:
        """List all active orchestrations"""
        try:
            active_orchestrations = []
            
            for orch_id, engine in self.orchestrations.items():
                progress = await engine.get_progress()
                active_orchestrations.append(progress)
            
            return active_orchestrations
            
        except Exception as e:
            logger.error("Failed to list active orchestrations", error=str(e))
            return []
    
    async def get_orchestration_stats(self) -> Dict[str, Any]:
        """Get orchestration statistics"""
        try:
            total_orchestrations = len(self.orchestrations)
            
            # Count by status
            status_counts = {}
            pattern_counts = {}
            
            for engine in self.orchestrations.values():
                # Status counts
                status = engine.status
                status_counts[status] = status_counts.get(status, 0) + 1
                
                # Pattern counts
                pattern = engine.pattern
                pattern_counts[pattern] = pattern_counts.get(pattern, 0) + 1
            
            # Calculate totals
            total_agents_involved = sum(
                len(engine.agent_ids) for engine in self.orchestrations.values()
            )
            
            total_tasks = sum(
                engine.total_steps for engine in self.orchestrations.values()
            )
            
            completed_tasks = sum(
                engine.completed_steps for engine in self.orchestrations.values()
            )
            
            return {
                'total_orchestrations': total_orchestrations,
                'max_concurrent_orchestrations': self.max_concurrent_orchestrations,
                'status_breakdown': status_counts,
                'pattern_breakdown': pattern_counts,
                'total_agents_involved': total_agents_involved,
                'task_statistics': {
                    'total_tasks': total_tasks,
                    'completed_tasks': completed_tasks,
                    'completion_rate': (
                        completed_tasks / total_tasks * 100
                        if total_tasks > 0 else 0
                    )
                }
            }
            
        except Exception as e:
            logger.error("Failed to get orchestration stats", error=str(e))
            return {}
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on orchestrations"""
        try:
            healthy_orchestrations = 0
            unhealthy_orchestrations = 0
            orchestration_statuses = {}
            
            for orch_id, engine in self.orchestrations.items():
                try:
                    progress = await engine.get_progress()
                    status = progress['status']
                    
                    orchestration_statuses[str(orch_id)] = {
                        'status': status,
                        'pattern': progress['pattern'],
                        'progress_percentage': progress['progress_percentage'],
                        'agent_count': progress['agent_count']
                    }
                    
                    if status in [OrchestrationStatus.RUNNING, OrchestrationStatus.PAUSED]:
                        healthy_orchestrations += 1
                    else:
                        unhealthy_orchestrations += 1
                        
                except Exception as e:
                    unhealthy_orchestrations += 1
                    orchestration_statuses[str(orch_id)] = {
                        'status': 'error',
                        'error': str(e)
                    }
            
            return {
                'healthy_orchestrations': healthy_orchestrations,
                'unhealthy_orchestrations': unhealthy_orchestrations,
                'total_orchestrations': len(self.orchestrations),
                'orchestration_statuses': orchestration_statuses,
                'timestamp': asyncio.get_event_loop().time()
            }
            
        except Exception as e:
            logger.error("Orchestration health check failed", error=str(e))
            return {
                'healthy_orchestrations': 0,
                'unhealthy_orchestrations': len(self.orchestrations),
                'total_orchestrations': len(self.orchestrations),
                'error': str(e)
            }
    
    async def shutdown_all_orchestrations(
        self,
        graceful: bool = True
    ) -> bool:
        """Shutdown all orchestrations"""
        try:
            logger.info(
                "Shutting down all orchestrations",
                total_orchestrations=len(self.orchestrations)
            )
            
            # Create shutdown tasks
            shutdown_tasks = []
            for orch_id in list(self.orchestrations.keys()):
                task = asyncio.create_task(
                    self.stop_orchestration(orch_id, graceful)
                )
                shutdown_tasks.append(task)
            
            # Wait for all shutdowns
            if shutdown_tasks:
                results = await asyncio.gather(*shutdown_tasks, return_exceptions=True)
                
                successful_shutdowns = sum(1 for result in results if result is True)
                logger.info(
                    "Orchestration shutdown completed",
                    successful=successful_shutdowns,
                    total=len(shutdown_tasks)
                )
            
            return True
            
        except Exception as e:
            logger.error("Failed to shutdown all orchestrations", error=str(e))
            return False


# Global orchestration manager instance
orchestration_manager = OrchestrationManager()