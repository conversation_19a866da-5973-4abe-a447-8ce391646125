"""JSON-based Compound Agent System.

This module implements a system for defining and managing compound agents
using JSON configurations, similar to n8n workflows. Compound agents
consist of multiple individual agents with defined connections and communication patterns.
"""

from typing import Dict, Any, List, Optional, Set, Tuple
from dataclasses import dataclass, field
from enum import Enum
import asyncio
import json
import uuid
from datetime import datetime
import jsonschema
from intelligence.gateway import AIGateway
from intelligence.providers.base import AIMessage
from utils.logging import get_logger, LoggerMixin
from utils.exceptions import AgentError, ValidationError
from protocols.a2a import A2AProtocolHandler, A2AMessage, A2AMessageType, MessagePriority
from services.mcp_integration import MCPIntegrationManager

logger = get_logger(__name__)

class ConnectionType(str, Enum):
    """Types of connections between agents."""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    CONDITIONAL = "conditional"
    LOOP = "loop"
    WEBHOOK = "webhook"
    EVENT = "event"
    MANUAL = "manual"

class ExecutionMode(str, Enum):
    """Execution modes for compound agents."""
    SYNCHRONOUS = "synchronous"
    ASYNCHRONOUS = "asynchronous"
    HYBRID = "hybrid"

class CompoundAgentStatus(str, Enum):
    """Status of compound agent execution."""
    INACTIVE = "inactive"
    STARTING = "starting"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class AgentNode:
    """Individual agent node in compound agent."""
    id: str
    name: str
    agent_type: str
    configuration: Dict[str, Any] = field(default_factory=dict)
    position: Dict[str, int] = field(default_factory=dict)  # x, y coordinates
    inputs: Dict[str, Any] = field(default_factory=dict)
    outputs: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    retry_count: int = 0
    max_retries: int = 3
    timeout: int = 300  # seconds

@dataclass
class Connection:
    """Connection between agent nodes."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    source_node: str = ""
    source_output: str = ""
    target_node: str = ""
    target_input: str = ""
    connection_type: ConnectionType = ConnectionType.SEQUENTIAL
    condition: Optional[str] = None  # For conditional connections
    transform: Optional[Dict[str, Any]] = None  # Data transformation
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class CompoundAgentDefinition:
    """Complete compound agent definition."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    version: str = "1.0.0"
    nodes: Dict[str, AgentNode] = field(default_factory=dict)
    connections: List[Connection] = field(default_factory=list)
    triggers: List[Dict[str, Any]] = field(default_factory=list)
    variables: Dict[str, Any] = field(default_factory=dict)
    settings: Dict[str, Any] = field(default_factory=dict)
    execution_mode: ExecutionMode = ExecutionMode.ASYNCHRONOUS
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    tags: List[str] = field(default_factory=list)

@dataclass
class ExecutionContext:
    """Runtime execution context for compound agent."""
    execution_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    compound_agent_id: str = ""
    status: CompoundAgentStatus = CompoundAgentStatus.INACTIVE
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    current_nodes: Set[str] = field(default_factory=set)
    completed_nodes: Set[str] = field(default_factory=set)
    failed_nodes: Set[str] = field(default_factory=set)
    node_results: Dict[str, Any] = field(default_factory=dict)
    variables: Dict[str, Any] = field(default_factory=dict)
    error: Optional[str] = None
    logs: List[Dict[str, Any]] = field(default_factory=list)

class CompoundAgentSystem(LoggerMixin):
    """System for managing JSON-based compound agents."""
    
    def __init__(
        self,
        ai_gateway: AIGateway,
        a2a_handler: A2AProtocolHandler,
        mcp_manager: Optional[MCPIntegrationManager] = None
    ):
        self.ai_gateway = ai_gateway
        self.a2a_handler = a2a_handler
        self.mcp_manager = mcp_manager
        
        self.compound_agents: Dict[str, CompoundAgentDefinition] = {}
        self.active_executions: Dict[str, ExecutionContext] = {}
        self.execution_history: List[ExecutionContext] = []
        
        # JSON schema for validation
        self.schema = self._get_compound_agent_schema()
        
        # Built-in agent types and their configurations
        self.builtin_agent_types = self._get_builtin_agent_types()
    
    def _get_compound_agent_schema(self) -> Dict[str, Any]:
        """Get JSON schema for compound agent validation."""
        
        return {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "type": "object",
            "required": ["id", "name", "nodes", "connections"],
            "properties": {
                "id": {"type": "string"},
                "name": {"type": "string", "minLength": 1},
                "description": {"type": "string"},
                "version": {"type": "string", "pattern": r"^\d+\.\d+\.\d+$"},
                "nodes": {
                    "type": "object",
                    "patternProperties": {
                        "^[a-zA-Z0-9_-]+$": {
                            "type": "object",
                            "required": ["id", "name", "agent_type"],
                            "properties": {
                                "id": {"type": "string"},
                                "name": {"type": "string", "minLength": 1},
                                "agent_type": {"type": "string"},
                                "configuration": {"type": "object"},
                                "position": {
                                    "type": "object",
                                    "properties": {
                                        "x": {"type": "integer"},
                                        "y": {"type": "integer"}
                                    }
                                },
                                "inputs": {"type": "object"},
                                "outputs": {"type": "object"},
                                "metadata": {"type": "object"},
                                "retry_count": {"type": "integer", "minimum": 0},
                                "max_retries": {"type": "integer", "minimum": 0},
                                "timeout": {"type": "integer", "minimum": 1}
                            }
                        }
                    }
                },
                "connections": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "required": ["source_node", "target_node"],
                        "properties": {
                            "id": {"type": "string"},
                            "source_node": {"type": "string"},
                            "source_output": {"type": "string"},
                            "target_node": {"type": "string"},
                            "target_input": {"type": "string"},
                            "connection_type": {
                                "type": "string",
                                "enum": ["sequential", "parallel", "conditional", "loop", "webhook", "event", "manual"]
                            },
                            "condition": {"type": "string"},
                            "transform": {"type": "object"},
                            "metadata": {"type": "object"}
                        }
                    }
                },
                "triggers": {"type": "array"},
                "variables": {"type": "object"},
                "settings": {"type": "object"},
                "execution_mode": {
                    "type": "string",
                    "enum": ["synchronous", "asynchronous", "hybrid"]
                },
                "tags": {
                    "type": "array",
                    "items": {"type": "string"}
                }
            }
        }
    
    def _get_builtin_agent_types(self) -> Dict[str, Dict[str, Any]]:
        """Get built-in agent type definitions."""
        
        return {
            "http_request": {
                "name": "HTTP Request",
                "description": "Make HTTP requests to external APIs",
                "inputs": ["url", "method", "headers", "data"],
                "outputs": ["response", "status_code", "headers"],
                "configuration": {
                    "url": {"type": "string", "required": True},
                    "method": {"type": "string", "default": "GET", "enum": ["GET", "POST", "PUT", "DELETE", "PATCH"]},
                    "headers": {"type": "object", "default": {}},
                    "timeout": {"type": "integer", "default": 30}
                }
            },
            "data_transformer": {
                "name": "Data Transformer", 
                "description": "Transform data using JavaScript expressions",
                "inputs": ["input_data"],
                "outputs": ["output_data"],
                "configuration": {
                    "transformation": {"type": "string", "required": True},
                    "language": {"type": "string", "default": "javascript", "enum": ["javascript", "python", "jq"]}
                }
            },
            "ai_processor": {
                "name": "AI Processor",
                "description": "Process data using AI models",
                "inputs": ["prompt", "context", "data"],
                "outputs": ["response", "confidence", "usage"],
                "configuration": {
                    "model": {"type": "string", "default": "gpt-4o"},
                    "provider": {"type": "string", "default": "openai"},
                    "temperature": {"type": "number", "default": 0.7},
                    "max_tokens": {"type": "integer", "default": 1000}
                }
            },
            "condition": {
                "name": "Condition",
                "description": "Conditional routing based on data",
                "inputs": ["input_data"],
                "outputs": ["true_path", "false_path"],
                "configuration": {
                    "condition": {"type": "string", "required": True},
                    "language": {"type": "string", "default": "javascript"}
                }
            },
            "delay": {
                "name": "Delay",
                "description": "Add delay between operations",
                "inputs": ["input_data"],
                "outputs": ["output_data"],
                "configuration": {
                    "delay_seconds": {"type": "integer", "default": 1, "minimum": 0}
                }
            },
            "email_sender": {
                "name": "Email Sender",
                "description": "Send emails via SMTP or service",
                "inputs": ["to", "subject", "body", "attachments"],
                "outputs": ["message_id", "status"],
                "configuration": {
                    "smtp_host": {"type": "string"},
                    "smtp_port": {"type": "integer", "default": 587},
                    "username": {"type": "string"},
                    "password": {"type": "string"},
                    "from_email": {"type": "string", "required": True}
                }
            },
            "file_processor": {
                "name": "File Processor",
                "description": "Process files (read, write, transform)",
                "inputs": ["file_path", "content", "operation"],
                "outputs": ["result", "file_info"],
                "configuration": {
                    "operation": {"type": "string", "enum": ["read", "write", "append", "delete"], "required": True},
                    "encoding": {"type": "string", "default": "utf-8"}
                }
            },
            "database_query": {
                "name": "Database Query",
                "description": "Execute database queries",
                "inputs": ["query", "parameters"],
                "outputs": ["results", "row_count"],
                "configuration": {
                    "connection_string": {"type": "string", "required": True},
                    "database_type": {"type": "string", "enum": ["postgresql", "mysql", "sqlite", "mongodb"], "required": True}
                }
            },
            "webhook": {
                "name": "Webhook",
                "description": "Receive webhooks and trigger workflows",
                "inputs": [],
                "outputs": ["headers", "body", "method", "query_params"],
                "configuration": {
                    "path": {"type": "string", "required": True},
                    "methods": {"type": "array", "items": {"type": "string"}, "default": ["POST"]}
                }
            },
            "mcp_tool": {
                "name": "MCP Tool",
                "description": "Execute MCP server tools",
                "inputs": ["tool_name", "parameters"],
                "outputs": ["result", "metadata"],
                "configuration": {
                    "server_id": {"type": "string"},
                    "tool_name": {"type": "string", "required": True}
                }
            }
        }
    
    def create_compound_agent(self, definition: Dict[str, Any]) -> str:
        """Create a new compound agent from JSON definition."""
        
        try:
            # Validate JSON schema
            jsonschema.validate(definition, self.schema)
            
            # Create compound agent object
            compound_agent = self._json_to_compound_agent(definition)
            
            # Validate business logic
            self._validate_compound_agent_logic(compound_agent)
            
            # Store the compound agent
            self.compound_agents[compound_agent.id] = compound_agent
            
            self.log_operation(
                "compound_agent_created",
                agent_id=compound_agent.id,
                name=compound_agent.name,
                nodes=len(compound_agent.nodes),
                connections=len(compound_agent.connections)
            )
            
            return compound_agent.id
            
        except jsonschema.ValidationError as e:
            raise ValidationError(f"Schema validation failed: {e.message}")
        except Exception as e:
            raise AgentError(f"Failed to create compound agent: {e}")
    
    def _json_to_compound_agent(self, definition: Dict[str, Any]) -> CompoundAgentDefinition:
        """Convert JSON definition to compound agent object."""
        
        # Parse nodes
        nodes = {}
        for node_id, node_data in definition.get("nodes", {}).items():
            node = AgentNode(
                id=node_data["id"],
                name=node_data["name"],
                agent_type=node_data["agent_type"],
                configuration=node_data.get("configuration", {}),
                position=node_data.get("position", {}),
                inputs=node_data.get("inputs", {}),
                outputs=node_data.get("outputs", {}),
                metadata=node_data.get("metadata", {}),
                retry_count=node_data.get("retry_count", 0),
                max_retries=node_data.get("max_retries", 3),
                timeout=node_data.get("timeout", 300)
            )
            nodes[node_id] = node
        
        # Parse connections
        connections = []
        for conn_data in definition.get("connections", []):
            connection = Connection(
                id=conn_data.get("id", str(uuid.uuid4())),
                source_node=conn_data["source_node"],
                source_output=conn_data.get("source_output", ""),
                target_node=conn_data["target_node"],
                target_input=conn_data.get("target_input", ""),
                connection_type=ConnectionType(conn_data.get("connection_type", "sequential")),
                condition=conn_data.get("condition"),
                transform=conn_data.get("transform"),
                metadata=conn_data.get("metadata", {})
            )
            connections.append(connection)
        
        # Create compound agent
        compound_agent = CompoundAgentDefinition(
            id=definition.get("id", str(uuid.uuid4())),
            name=definition["name"],
            description=definition.get("description", ""),
            version=definition.get("version", "1.0.0"),
            nodes=nodes,
            connections=connections,
            triggers=definition.get("triggers", []),
            variables=definition.get("variables", {}),
            settings=definition.get("settings", {}),
            execution_mode=ExecutionMode(definition.get("execution_mode", "asynchronous")),
            tags=definition.get("tags", [])
        )
        
        return compound_agent
    
    def _validate_compound_agent_logic(self, compound_agent: CompoundAgentDefinition) -> None:
        """Validate compound agent business logic."""
        
        # Check for circular dependencies
        if self._has_circular_dependencies(compound_agent):
            raise ValidationError("Compound agent has circular dependencies")
        
        # Check that all referenced nodes exist
        node_ids = set(compound_agent.nodes.keys())
        for connection in compound_agent.connections:
            if connection.source_node not in node_ids:
                raise ValidationError(f"Source node {connection.source_node} not found")
            if connection.target_node not in node_ids:
                raise ValidationError(f"Target node {connection.target_node} not found")
        
        # Validate agent types
        for node in compound_agent.nodes.values():
            if node.agent_type not in self.builtin_agent_types:
                # Could be custom agent type - log warning
                self.logger.warning(f"Unknown agent type: {node.agent_type}")
        
        # Check for unreachable nodes
        reachable_nodes = self._get_reachable_nodes(compound_agent)
        unreachable_nodes = node_ids - reachable_nodes
        if unreachable_nodes:
            self.logger.warning(f"Unreachable nodes detected: {unreachable_nodes}")
    
    def _has_circular_dependencies(self, compound_agent: CompoundAgentDefinition) -> bool:
        """Check for circular dependencies in compound agent."""
        
        # Build adjacency list
        graph = {node_id: [] for node_id in compound_agent.nodes.keys()}
        for connection in compound_agent.connections:
            if connection.connection_type != ConnectionType.LOOP:  # Loops are allowed
                graph[connection.source_node].append(connection.target_node)
        
        # Perform DFS to detect cycles
        visited = set()
        rec_stack = set()
        
        def dfs(node_id: str) -> bool:
            visited.add(node_id)
            rec_stack.add(node_id)
            
            for neighbor in graph.get(node_id, []):
                if neighbor not in visited:
                    if dfs(neighbor):
                        return True
                elif neighbor in rec_stack:
                    return True
            
            rec_stack.remove(node_id)
            return False
        
        for node_id in compound_agent.nodes.keys():
            if node_id not in visited:
                if dfs(node_id):
                    return True
        
        return False
    
    def _get_reachable_nodes(self, compound_agent: CompoundAgentDefinition) -> Set[str]:
        """Get all reachable nodes from entry points."""
        
        # Find entry points (nodes with no incoming connections)
        incoming_nodes = set()
        for connection in compound_agent.connections:
            incoming_nodes.add(connection.target_node)
        
        entry_points = set(compound_agent.nodes.keys()) - incoming_nodes
        
        # If no entry points, all nodes with triggers are entry points
        if not entry_points and compound_agent.triggers:
            # Assume all nodes could be entry points for triggered workflows
            entry_points = set(compound_agent.nodes.keys())
        
        # BFS from entry points
        reachable = set()
        queue = list(entry_points)
        
        while queue:
            current = queue.pop(0)
            if current in reachable:
                continue
            
            reachable.add(current)
            
            # Add connected nodes to queue
            for connection in compound_agent.connections:
                if connection.source_node == current and connection.target_node not in reachable:
                    queue.append(connection.target_node)
        
        return reachable
    
    async def execute_compound_agent(
        self,
        compound_agent_id: str,
        initial_data: Optional[Dict[str, Any]] = None,
        variables: Optional[Dict[str, Any]] = None
    ) -> str:
        """Execute a compound agent and return execution ID."""
        
        compound_agent = self.compound_agents.get(compound_agent_id)
        if not compound_agent:
            raise AgentError(f"Compound agent {compound_agent_id} not found")
        
        # Create execution context
        context = ExecutionContext(
            compound_agent_id=compound_agent_id,
            status=CompoundAgentStatus.STARTING,
            start_time=datetime.now(),
            variables={**compound_agent.variables, **(variables or {})}
        )
        
        if initial_data:
            context.variables.update(initial_data)
        
        self.active_executions[context.execution_id] = context
        
        # Start execution
        asyncio.create_task(self._execute_compound_agent_impl(compound_agent, context))
        
        self.log_operation(
            "compound_agent_execution_started",
            execution_id=context.execution_id,
            compound_agent_id=compound_agent_id
        )
        
        return context.execution_id
    
    async def _execute_compound_agent_impl(
        self,
        compound_agent: CompoundAgentDefinition,
        context: ExecutionContext
    ) -> None:
        """Implementation of compound agent execution."""
        
        try:
            context.status = CompoundAgentStatus.RUNNING
            
            # Find entry nodes (nodes with no incoming connections or triggered nodes)
            entry_nodes = self._find_entry_nodes(compound_agent)
            
            if not entry_nodes:
                raise AgentError("No entry nodes found in compound agent")
            
            # Execute nodes based on execution mode
            if compound_agent.execution_mode == ExecutionMode.SYNCHRONOUS:
                await self._execute_synchronously(compound_agent, context, entry_nodes)
            else:
                await self._execute_asynchronously(compound_agent, context, entry_nodes)
            
            # Check final status
            if context.failed_nodes:
                context.status = CompoundAgentStatus.FAILED
                context.error = f"Nodes failed: {', '.join(context.failed_nodes)}"
            else:
                context.status = CompoundAgentStatus.COMPLETED
            
            context.end_time = datetime.now()
            
            self.log_operation(
                "compound_agent_execution_completed",
                execution_id=context.execution_id,
                status=context.status.value,
                duration=(context.end_time - context.start_time).total_seconds()
            )
            
        except Exception as e:
            context.status = CompoundAgentStatus.FAILED
            context.error = str(e)
            context.end_time = datetime.now()
            
            self.log_error(
                "compound_agent_execution_failed",
                e,
                execution_id=context.execution_id
            )
        
        finally:
            # Move to history and cleanup
            self.execution_history.append(context)
            if context.execution_id in self.active_executions:
                del self.active_executions[context.execution_id]
    
    def _find_entry_nodes(self, compound_agent: CompoundAgentDefinition) -> List[str]:
        """Find entry nodes for compound agent execution."""
        
        # Nodes with no incoming connections
        incoming_nodes = set()
        for connection in compound_agent.connections:
            incoming_nodes.add(connection.target_node)
        
        entry_nodes = list(set(compound_agent.nodes.keys()) - incoming_nodes)
        
        # If no natural entry nodes, check for webhook or trigger nodes
        if not entry_nodes:
            for node_id, node in compound_agent.nodes.items():
                if node.agent_type in ["webhook", "trigger"]:
                    entry_nodes.append(node_id)
        
        # If still no entry nodes, use all nodes (parallel execution)
        if not entry_nodes:
            entry_nodes = list(compound_agent.nodes.keys())
        
        return entry_nodes
    
    async def _execute_synchronously(
        self,
        compound_agent: CompoundAgentDefinition,
        context: ExecutionContext,
        entry_nodes: List[str]
    ) -> None:
        """Execute compound agent synchronously."""
        
        # Build execution order using topological sort
        execution_order = self._topological_sort(compound_agent)
        
        for node_id in execution_order:
            if node_id in context.failed_nodes:
                continue
            
            # Check if all dependencies are satisfied
            if not self._are_dependencies_satisfied(node_id, compound_agent, context):
                continue
            
            # Execute node
            await self._execute_node(compound_agent.nodes[node_id], context)
    
    async def _execute_asynchronously(
        self,
        compound_agent: CompoundAgentDefinition,
        context: ExecutionContext,
        entry_nodes: List[str]
    ) -> None:
        """Execute compound agent asynchronously."""
        
        # Start with entry nodes
        ready_nodes = set(entry_nodes)
        
        while ready_nodes or context.current_nodes:
            # Execute ready nodes
            tasks = []
            for node_id in list(ready_nodes):
                if node_id not in context.current_nodes and node_id not in context.completed_nodes:
                    context.current_nodes.add(node_id)
                    ready_nodes.remove(node_id)
                    
                    node = compound_agent.nodes[node_id]
                    task = asyncio.create_task(self._execute_node(node, context))
                    tasks.append((node_id, task))
            
            # Wait for at least one task to complete
            if tasks:
                done, pending = await asyncio.wait(
                    [task for _, task in tasks],
                    return_when=asyncio.FIRST_COMPLETED
                )
                
                # Process completed tasks
                for node_id, task in tasks:
                    if task in done:
                        context.current_nodes.discard(node_id)
                        
                        # Check for newly ready nodes
                        newly_ready = self._find_ready_nodes(compound_agent, context)
                        ready_nodes.update(newly_ready)
            
            # If no tasks and no ready nodes, break
            if not tasks and not ready_nodes:
                break
            
            # Small delay to prevent tight loop
            await asyncio.sleep(0.1)
    
    def _topological_sort(self, compound_agent: CompoundAgentDefinition) -> List[str]:
        """Perform topological sort on compound agent nodes."""
        
        # Build graph
        graph = {node_id: [] for node_id in compound_agent.nodes.keys()}
        in_degree = {node_id: 0 for node_id in compound_agent.nodes.keys()}
        
        for connection in compound_agent.connections:
            if connection.connection_type != ConnectionType.LOOP:
                graph[connection.source_node].append(connection.target_node)
                in_degree[connection.target_node] += 1
        
        # Kahn's algorithm
        queue = [node_id for node_id, degree in in_degree.items() if degree == 0]
        result = []
        
        while queue:
            node_id = queue.pop(0)
            result.append(node_id)
            
            for neighbor in graph[node_id]:
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    queue.append(neighbor)
        
        return result
    
    def _are_dependencies_satisfied(
        self,
        node_id: str,
        compound_agent: CompoundAgentDefinition,
        context: ExecutionContext
    ) -> bool:
        """Check if node dependencies are satisfied."""
        
        # Find incoming connections
        for connection in compound_agent.connections:
            if connection.target_node == node_id:
                # Check if source node is completed
                if connection.source_node not in context.completed_nodes:
                    return False
                
                # Check condition if present
                if connection.condition:
                    if not self._evaluate_condition(connection.condition, context):
                        return False
        
        return True
    
    def _find_ready_nodes(
        self,
        compound_agent: CompoundAgentDefinition,
        context: ExecutionContext
    ) -> Set[str]:
        """Find nodes that are ready to execute."""
        
        ready = set()
        
        for node_id in compound_agent.nodes.keys():
            if (node_id not in context.completed_nodes and 
                node_id not in context.failed_nodes and
                node_id not in context.current_nodes):
                
                if self._are_dependencies_satisfied(node_id, compound_agent, context):
                    ready.add(node_id)
        
        return ready
    
    async def _execute_node(self, node: AgentNode, context: ExecutionContext) -> None:
        """Execute a single agent node."""
        
        try:
            context.logs.append({
                "timestamp": datetime.now().isoformat(),
                "node_id": node.id,
                "event": "node_execution_started"
            })
            
            # Prepare node inputs
            inputs = await self._prepare_node_inputs(node, context)
            
            # Execute based on agent type
            result = await self._execute_node_by_type(node, inputs, context)
            
            # Store result
            context.node_results[node.id] = result
            context.completed_nodes.add(node.id)
            
            context.logs.append({
                "timestamp": datetime.now().isoformat(),
                "node_id": node.id,
                "event": "node_execution_completed",
                "result_size": len(str(result))
            })
            
        except Exception as e:
            context.failed_nodes.add(node.id)
            context.node_results[node.id] = {"error": str(e)}
            
            context.logs.append({
                "timestamp": datetime.now().isoformat(),
                "node_id": node.id,
                "event": "node_execution_failed",
                "error": str(e)
            })
            
            self.log_error("node_execution_failed", e, node_id=node.id, execution_id=context.execution_id)
    
    async def _prepare_node_inputs(self, node: AgentNode, context: ExecutionContext) -> Dict[str, Any]:
        """Prepare inputs for node execution."""
        
        inputs = {}
        
        # Add configured inputs
        inputs.update(node.inputs)
        
        # Add data from connected nodes
        compound_agent = self.compound_agents[context.compound_agent_id]
        for connection in compound_agent.connections:
            if connection.target_node == node.id:
                source_result = context.node_results.get(connection.source_node, {})
                
                # Get specific output if specified
                if connection.source_output and connection.source_output in source_result:
                    data = source_result[connection.source_output]
                else:
                    data = source_result
                
                # Apply transformation if specified
                if connection.transform:
                    data = await self._apply_data_transform(data, connection.transform)
                
                # Map to target input
                input_key = connection.target_input or "input_data"
                inputs[input_key] = data
        
        # Add context variables
        inputs.update(context.variables)
        
        return inputs
    
    async def _execute_node_by_type(
        self,
        node: AgentNode,
        inputs: Dict[str, Any],
        context: ExecutionContext
    ) -> Dict[str, Any]:
        """Execute node based on its agent type."""
        
        if node.agent_type == "http_request":
            return await self._execute_http_request(node, inputs)
        elif node.agent_type == "data_transformer":
            return await self._execute_data_transformer(node, inputs)
        elif node.agent_type == "ai_processor":
            return await self._execute_ai_processor(node, inputs)
        elif node.agent_type == "condition":
            return await self._execute_condition(node, inputs)
        elif node.agent_type == "delay":
            return await self._execute_delay(node, inputs)
        elif node.agent_type == "mcp_tool":
            return await self._execute_mcp_tool(node, inputs)
        else:
            # Custom agent type - try to find and execute
            return await self._execute_custom_agent(node, inputs, context)
    
    async def _execute_http_request(self, node: AgentNode, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Execute HTTP request node."""
        
        import aiohttp
        
        config = node.configuration
        url = config.get("url") or inputs.get("url")
        method = config.get("method", "GET")
        headers = {**config.get("headers", {}), **inputs.get("headers", {})}
        data = inputs.get("data")
        timeout = config.get("timeout", 30)
        
        if not url:
            raise AgentError("No URL specified for HTTP request")
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
            async with session.request(method, url, headers=headers, json=data) as response:
                response_data = await response.text()
                
                try:
                    response_json = await response.json()
                except:
                    response_json = None
                
                return {
                    "response": response_json or response_data,
                    "status_code": response.status,
                    "headers": dict(response.headers)
                }
    
    async def _execute_ai_processor(self, node: AgentNode, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Execute AI processor node."""
        
        config = node.configuration
        model = config.get("model", "gpt-4o")
        provider = config.get("provider", "openai")
        temperature = config.get("temperature", 0.7)
        max_tokens = config.get("max_tokens", 1000)
        
        # Build prompt
        prompt = inputs.get("prompt", "")
        context_data = inputs.get("context", "")
        data = inputs.get("data", "")
        
        if context_data:
            prompt += f"\n\nContext: {context_data}"
        if data:
            prompt += f"\n\nData: {data}"
        
        # Call AI
        messages = [AIMessage(role="user", content=prompt)]
        
        response = await self.ai_gateway.chat_completion(
            messages=messages,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            preferred_provider=provider
        )
        
        return {
            "response": response.content,
            "confidence": 1.0,  # Could be calculated from response metadata
            "usage": response.usage or {}
        }
    
    async def _execute_delay(self, node: AgentNode, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Execute delay node."""
        
        delay_seconds = node.configuration.get("delay_seconds", 1)
        await asyncio.sleep(delay_seconds)
        
        return {
            "output_data": inputs.get("input_data"),
            "delay_applied": delay_seconds
        }
    
    async def _execute_mcp_tool(self, node: AgentNode, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Execute MCP tool node."""
        
        if not self.mcp_manager:
            raise AgentError("MCP manager not available")
        
        config = node.configuration
        tool_name = config.get("tool_name") or inputs.get("tool_name")
        server_id = config.get("server_id")
        parameters = inputs.get("parameters", {})
        
        if not tool_name:
            raise AgentError("No tool name specified for MCP tool")
        
        result = await self.mcp_manager.execute_tool(tool_name, parameters, server_id)
        
        return {
            "result": result,
            "metadata": {"tool_name": tool_name, "server_id": server_id}
        }
    
    async def _execute_custom_agent(
        self,
        node: AgentNode,
        inputs: Dict[str, Any],
        context: ExecutionContext
    ) -> Dict[str, Any]:
        """Execute custom agent type."""
        
        # This would integrate with the existing agent system
        # For now, return a placeholder result
        
        return {
            "result": f"Custom agent {node.agent_type} executed",
            "inputs_processed": len(inputs),
            "node_id": node.id
        }
    
    async def _apply_data_transform(self, data: Any, transform: Dict[str, Any]) -> Any:
        """Apply data transformation."""
        
        transform_type = transform.get("type", "javascript")
        expression = transform.get("expression", "")
        
        if transform_type == "javascript":
            # Simple JavaScript-like transformations
            # In production, would use a safe JS engine
            if expression == "JSON.stringify":
                return json.dumps(data)
            elif expression == "JSON.parse":
                return json.loads(data) if isinstance(data, str) else data
            elif expression.startswith("data."):
                # Simple property access
                property_path = expression[5:]  # Remove "data."
                current = data
                for prop in property_path.split("."):
                    if isinstance(current, dict):
                        current = current.get(prop)
                    else:
                        return None
                return current
        
        return data
    
    def _evaluate_condition(self, condition: str, context: ExecutionContext) -> bool:
        """Evaluate condition expression."""
        
        # Simple condition evaluation
        # In production, would use a safe expression engine
        
        try:
            # Replace variable references
            condition = condition.replace("$", "context.variables.get('")
            condition = condition.replace("{", "', ")
            condition = condition.replace("}", ")")
            
            # Basic safety check
            if any(dangerous in condition.lower() for dangerous in ["import", "exec", "eval", "__"]):
                return False
            
            # Evaluate (simplified)
            return bool(eval(condition))
        except:
            return False
    
    def get_compound_agent(self, compound_agent_id: str) -> Optional[CompoundAgentDefinition]:
        """Get compound agent definition."""
        return self.compound_agents.get(compound_agent_id)
    
    def list_compound_agents(self) -> List[CompoundAgentDefinition]:
        """List all compound agents."""
        return list(self.compound_agents.values())
    
    def update_compound_agent(self, compound_agent_id: str, definition: Dict[str, Any]) -> bool:
        """Update compound agent definition."""
        
        try:
            # Validate new definition
            jsonschema.validate(definition, self.schema)
            
            # Update existing agent
            if compound_agent_id in self.compound_agents:
                updated_agent = self._json_to_compound_agent(definition)
                updated_agent.id = compound_agent_id  # Preserve ID
                updated_agent.updated_at = datetime.now()
                
                self._validate_compound_agent_logic(updated_agent)
                self.compound_agents[compound_agent_id] = updated_agent
                
                self.log_operation("compound_agent_updated", agent_id=compound_agent_id)
                return True
            
            return False
            
        except Exception as e:
            self.log_error("compound_agent_update_failed", e, agent_id=compound_agent_id)
            return False
    
    def delete_compound_agent(self, compound_agent_id: str) -> bool:
        """Delete compound agent."""
        
        if compound_agent_id in self.compound_agents:
            del self.compound_agents[compound_agent_id]
            self.log_operation("compound_agent_deleted", agent_id=compound_agent_id)
            return True
        
        return False
    
    def get_execution_status(self, execution_id: str) -> Optional[ExecutionContext]:
        """Get execution status."""
        
        # Check active executions
        if execution_id in self.active_executions:
            return self.active_executions[execution_id]
        
        # Check history
        for context in self.execution_history:
            if context.execution_id == execution_id:
                return context
        
        return None
    
    def list_active_executions(self) -> List[ExecutionContext]:
        """List all active executions."""
        return list(self.active_executions.values())
    
    async def cancel_execution(self, execution_id: str) -> bool:
        """Cancel active execution."""
        
        context = self.active_executions.get(execution_id)
        if context and context.status == CompoundAgentStatus.RUNNING:
            context.status = CompoundAgentStatus.CANCELLED
            context.end_time = datetime.now()
            
            self.log_operation("compound_agent_execution_cancelled", execution_id=execution_id)
            return True
        
        return False
    
    def get_builtin_agent_types_info(self) -> Dict[str, Dict[str, Any]]:
        """Get information about built-in agent types."""
        return self.builtin_agent_types.copy()
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get system statistics."""
        
        return {
            "total_compound_agents": len(self.compound_agents),
            "active_executions": len(self.active_executions),
            "completed_executions": len(self.execution_history),
            "builtin_agent_types": len(self.builtin_agent_types),
            "average_nodes_per_agent": (
                sum(len(agent.nodes) for agent in self.compound_agents.values()) / 
                max(len(self.compound_agents), 1)
            )
        }