"""
Intelligence Layer - Core AI processing and reasoning capabilities.

This module provides the foundational AI intelligence for the agent platform,
including multi-provider AI integration, reasoning engines, and context management.
"""

from .gateway import AIGateway
from .providers.base import BaseAIProvider
from .reasoning.context_manager import ContextManager
from .embeddings.generator import EmbeddingGenerator

__all__ = [
    "AIGateway",
    "BaseAIProvider", 
    "ContextManager",
    "EmbeddingGenerator"
]