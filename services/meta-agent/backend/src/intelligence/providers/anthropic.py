"""Anthropic Claude provider implementation."""

import anthropic
from typing import List, Optional, Dict, Any, AsyncIterator
from .base import BaseAIProvider, AIMessage, AIResponse, ModelInfo, ModelCapability
import json
import base64

class AnthropicProvider(BaseAIProvider):
    """Anthropic Claude provider implementation."""
    
    def __init__(self, api_key: str, **kwargs):
        super().__init__(api_key, **kwargs)
        self.client = None
        
    async def initialize(self) -> bool:
        """Initialize Anthropic client."""
        try:
            self.client = anthropic.AsyncAnthropic(api_key=self.api_key)
            return True
        except Exception as e:
            print(f"Anthropic initialization failed: {e}")
            return False
    
    async def chat_completion(
        self,
        messages: List[AIMessage],
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
        **kwargs
    ) -> AIResponse:
        """Generate chat completion using <PERSON>."""
        if not self.client:
            await self.initialize()
        
        # Convert messages to Anthropic format
        system_message = ""
        anthropic_messages = []
        
        for msg in messages:
            if msg.role == "system":
                system_message += msg.content + "\n"
            else:
                anthropic_messages.append({
                    "role": msg.role,
                    "content": msg.content
                })
        
        try:
            response = await self.client.messages.create(
                model=model,
                system=system_message.strip() if system_message else None,
                messages=anthropic_messages,
                temperature=temperature,
                max_tokens=max_tokens or 4096,
                **kwargs
            )
            
            return AIResponse(
                content=response.content[0].text,
                model=model,
                usage={
                    "prompt_tokens": response.usage.input_tokens,
                    "completion_tokens": response.usage.output_tokens,
                    "total_tokens": response.usage.input_tokens + response.usage.output_tokens
                },
                finish_reason=response.stop_reason
            )
        except Exception as e:
            raise Exception(f"Anthropic chat completion failed: {e}")
    
    async def stream_completion(
        self,
        messages: List[AIMessage],
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> AsyncIterator[str]:
        """Stream chat completion from Claude."""
        if not self.client:
            await self.initialize()
        
        system_message = ""
        anthropic_messages = []
        
        for msg in messages:
            if msg.role == "system":
                system_message += msg.content + "\n"
            else:
                anthropic_messages.append({
                    "role": msg.role,
                    "content": msg.content
                })
        
        try:
            async with self.client.messages.stream(
                model=model,
                system=system_message.strip() if system_message else None,
                messages=anthropic_messages,
                temperature=temperature,
                max_tokens=max_tokens or 4096,
                **kwargs
            ) as stream:
                async for text in stream.text_stream:
                    yield text
        except Exception as e:
            raise Exception(f"Anthropic stream completion failed: {e}")
    
    async def generate_embedding(
        self,
        text: str,
        model: Optional[str] = None
    ) -> List[float]:
        """Generate embeddings - not directly supported by Anthropic."""
        raise NotImplementedError("Anthropic does not provide embedding models")
    
    async def analyze_image(
        self,
        image_data: bytes,
        prompt: str,
        model: Optional[str] = None
    ) -> AIResponse:
        """Analyze image using Claude Vision."""
        if not self.client:
            await self.initialize()
        
        model = model or "claude-3-5-sonnet-20241022"
        
        # Encode image to base64
        image_b64 = base64.b64encode(image_data).decode()
        
        # Determine image type (assume JPEG for simplicity)
        media_type = "image/jpeg"
        
        try:
            response = await self.client.messages.create(
                model=model,
                max_tokens=4096,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image",
                                "source": {
                                    "type": "base64",
                                    "media_type": media_type,
                                    "data": image_b64
                                }
                            },
                            {
                                "type": "text",
                                "text": prompt
                            }
                        ]
                    }
                ]
            )
            
            return AIResponse(
                content=response.content[0].text,
                model=model,
                usage={
                    "prompt_tokens": response.usage.input_tokens,
                    "completion_tokens": response.usage.output_tokens,
                    "total_tokens": response.usage.input_tokens + response.usage.output_tokens
                }
            )
        except Exception as e:
            raise Exception(f"Anthropic image analysis failed: {e}")
    
    async def function_calling(
        self,
        messages: List[AIMessage],
        functions: List[Dict[str, Any]],
        model: str,
        **kwargs
    ) -> AIResponse:
        """Execute function calling with Claude."""
        if not self.client:
            await self.initialize()
        
        system_message = ""
        anthropic_messages = []
        
        for msg in messages:
            if msg.role == "system":
                system_message += msg.content + "\n"
            else:
                anthropic_messages.append({
                    "role": msg.role,
                    "content": msg.content
                })
        
        # Convert functions to Anthropic tools format
        tools = [
            {
                "name": func["name"],
                "description": func.get("description", ""),
                "input_schema": func.get("parameters", {})
            }
            for func in functions
        ]
        
        try:
            response = await self.client.messages.create(
                model=model,
                system=system_message.strip() if system_message else None,
                messages=anthropic_messages,
                tools=tools,
                max_tokens=4096,
                **kwargs
            )
            
            content = ""
            function_calls = []
            
            for content_block in response.content:
                if content_block.type == "text":
                    content += content_block.text
                elif content_block.type == "tool_use":
                    function_calls.append({
                        "id": content_block.id,
                        "name": content_block.name,
                        "arguments": content_block.input
                    })
            
            return AIResponse(
                content=content,
                model=model,
                metadata={"function_calls": function_calls} if function_calls else None,
                usage={
                    "prompt_tokens": response.usage.input_tokens,
                    "completion_tokens": response.usage.output_tokens,
                    "total_tokens": response.usage.input_tokens + response.usage.output_tokens
                }
            )
        except Exception as e:
            raise Exception(f"Anthropic function calling failed: {e}")
    
    def get_available_models(self) -> List[ModelInfo]:
        """Get available Anthropic models."""
        return [
            ModelInfo(
                name="claude-3-5-sonnet-20241022",
                provider="anthropic",
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CHAT_COMPLETION,
                    ModelCapability.CODE_GENERATION,
                    ModelCapability.FUNCTION_CALLING,
                    ModelCapability.IMAGE_ANALYSIS
                ],
                max_tokens=200000,
                cost_per_token=0.000003,
                supports_streaming=True,
                supports_functions=True
            ),
            ModelInfo(
                name="claude-3-5-haiku-20241022",
                provider="anthropic",
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CHAT_COMPLETION,
                    ModelCapability.CODE_GENERATION,
                    ModelCapability.FUNCTION_CALLING
                ],
                max_tokens=200000,
                cost_per_token=0.0000008,
                supports_streaming=True,
                supports_functions=True
            ),
            ModelInfo(
                name="claude-3-opus-20240229",
                provider="anthropic",
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CHAT_COMPLETION,
                    ModelCapability.CODE_GENERATION,
                    ModelCapability.FUNCTION_CALLING,
                    ModelCapability.IMAGE_ANALYSIS
                ],
                max_tokens=200000,
                cost_per_token=0.000015,
                supports_streaming=True,
                supports_functions=True
            )
        ]
    
    def estimate_cost(
        self,
        input_tokens: int,
        output_tokens: int,
        model: str
    ) -> float:
        """Estimate cost for Anthropic usage."""
        model_costs = {
            "claude-3-5-sonnet-20241022": {"input": 0.000003, "output": 0.000015},
            "claude-3-5-haiku-20241022": {"input": 0.0000008, "output": 0.000004},
            "claude-3-opus-20240229": {"input": 0.000015, "output": 0.000075}
        }
        
        if model not in model_costs:
            return 0.0
            
        costs = model_costs[model]
        return (input_tokens * costs["input"]) + (output_tokens * costs["output"])
    
    @property
    def provider_name(self) -> str:
        return "anthropic"