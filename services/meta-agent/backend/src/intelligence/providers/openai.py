"""OpenAI provider implementation with advanced features."""

import asyncio
import openai
from typing import List, Optional, Dict, Any, AsyncIterator
from .base import BaseAIProvider, AIMessage, AIResponse, ModelInfo, ModelCapability
import json
import base64

class OpenAIProvider(BaseAIProvider):
    """OpenAI provider implementation."""
    
    def __init__(self, api_key: str, **kwargs):
        super().__init__(api_key, **kwargs)
        self.client = None
        self.organization = kwargs.get('organization')
        
    async def initialize(self) -> bool:
        """Initialize OpenAI client."""
        try:
            openai.api_key = self.api_key
            if self.organization:
                openai.organization = self.organization
            self.client = openai.AsyncOpenAI(api_key=self.api_key)
            return True
        except Exception as e:
            print(f"OpenAI initialization failed: {e}")
            return False
    
    async def chat_completion(
        self,
        messages: List[AIMessage],
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
        **kwargs
    ) -> AIResponse:
        """Generate chat completion using OpenAI."""
        if not self.client:
            await self.initialize()
            
        openai_messages = [
            {"role": msg.role, "content": msg.content} 
            for msg in messages
        ]
        
        try:
            response = await self.client.chat.completions.create(
                model=model,
                messages=openai_messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=stream,
                **kwargs
            )
            
            return AIResponse(
                content=response.choices[0].message.content,
                model=model,
                usage={
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                finish_reason=response.choices[0].finish_reason
            )
        except Exception as e:
            raise Exception(f"OpenAI chat completion failed: {e}")
    
    async def stream_completion(
        self,
        messages: List[AIMessage],
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> AsyncIterator[str]:
        """Stream chat completion from OpenAI."""
        if not self.client:
            await self.initialize()
            
        openai_messages = [
            {"role": msg.role, "content": msg.content} 
            for msg in messages
        ]
        
        try:
            stream = await self.client.chat.completions.create(
                model=model,
                messages=openai_messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=True,
                **kwargs
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
        except Exception as e:
            raise Exception(f"OpenAI stream completion failed: {e}")
    
    async def generate_embedding(
        self,
        text: str,
        model: Optional[str] = None
    ) -> List[float]:
        """Generate embeddings using OpenAI."""
        if not self.client:
            await self.initialize()
            
        model = model or "text-embedding-3-small"
        
        try:
            response = await self.client.embeddings.create(
                model=model,
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            raise Exception(f"OpenAI embedding generation failed: {e}")
    
    async def analyze_image(
        self,
        image_data: bytes,
        prompt: str,
        model: Optional[str] = None
    ) -> AIResponse:
        """Analyze image using OpenAI Vision."""
        if not self.client:
            await self.initialize()
            
        model = model or "gpt-4o"
        
        # Encode image to base64
        image_b64 = base64.b64encode(image_data).decode()
        
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{image_b64}"
                        }
                    }
                ]
            }
        ]
        
        try:
            response = await self.client.chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=1000
            )
            
            return AIResponse(
                content=response.choices[0].message.content,
                model=model,
                usage={
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                }
            )
        except Exception as e:
            raise Exception(f"OpenAI image analysis failed: {e}")
    
    async def function_calling(
        self,
        messages: List[AIMessage],
        functions: List[Dict[str, Any]],
        model: str,
        **kwargs
    ) -> AIResponse:
        """Execute function calling with OpenAI."""
        if not self.client:
            await self.initialize()
            
        openai_messages = [
            {"role": msg.role, "content": msg.content} 
            for msg in messages
        ]
        
        # Convert functions to OpenAI format
        tools = [
            {"type": "function", "function": func}
            for func in functions
        ]
        
        try:
            response = await self.client.chat.completions.create(
                model=model,
                messages=openai_messages,
                tools=tools,
                tool_choice="auto",
                **kwargs
            )
            
            message = response.choices[0].message
            content = message.content or ""
            
            # Handle function calls
            if message.tool_calls:
                function_calls = []
                for tool_call in message.tool_calls:
                    function_calls.append({
                        "id": tool_call.id,
                        "name": tool_call.function.name,
                        "arguments": json.loads(tool_call.function.arguments)
                    })
                
                return AIResponse(
                    content=content,
                    model=model,
                    metadata={"function_calls": function_calls},
                    usage={
                        "prompt_tokens": response.usage.prompt_tokens,
                        "completion_tokens": response.usage.completion_tokens,
                        "total_tokens": response.usage.total_tokens
                    }
                )
            
            return AIResponse(
                content=content,
                model=model,
                usage={
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                }
            )
        except Exception as e:
            raise Exception(f"OpenAI function calling failed: {e}")
    
    def get_available_models(self) -> List[ModelInfo]:
        """Get available OpenAI models."""
        return [
            ModelInfo(
                name="gpt-4o",
                provider="openai",
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CHAT_COMPLETION,
                    ModelCapability.CODE_GENERATION,
                    ModelCapability.FUNCTION_CALLING,
                    ModelCapability.IMAGE_ANALYSIS
                ],
                max_tokens=128000,
                cost_per_token=0.000015,
                supports_streaming=True,
                supports_functions=True
            ),
            ModelInfo(
                name="gpt-4o-mini",
                provider="openai",
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CHAT_COMPLETION,
                    ModelCapability.CODE_GENERATION,
                    ModelCapability.FUNCTION_CALLING
                ],
                max_tokens=128000,
                cost_per_token=0.000001,
                supports_streaming=True,
                supports_functions=True
            ),
            ModelInfo(
                name="text-embedding-3-small",
                provider="openai",
                capabilities=[ModelCapability.EMBEDDING],
                max_tokens=8192,
                cost_per_token=0.00000002
            )
        ]
    
    def estimate_cost(
        self,
        input_tokens: int,
        output_tokens: int,
        model: str
    ) -> float:
        """Estimate cost for OpenAI usage."""
        model_costs = {
            "gpt-4o": {"input": 0.000005, "output": 0.000015},
            "gpt-4o-mini": {"input": 0.00000015, "output": 0.0000006},
            "text-embedding-3-small": {"input": 0.00000002, "output": 0}
        }
        
        if model not in model_costs:
            return 0.0
            
        costs = model_costs[model]
        return (input_tokens * costs["input"]) + (output_tokens * costs["output"])
    
    @property
    def provider_name(self) -> str:
        return "openai"