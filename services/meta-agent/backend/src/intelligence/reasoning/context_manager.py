"""Context management for AI conversations and agent memory."""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import json
import hashlib
from intelligence.providers.base import AIMessage

@dataclass
class ContextWindow:
    """Context window configuration."""
    max_tokens: int = 4000
    max_messages: int = 50
    retention_hours: int = 24
    compression_threshold: float = 0.8

@dataclass
class ConversationContext:
    """Conversation context with metadata."""
    id: str
    messages: List[AIMessage] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    token_count: int = 0
    summary: Optional[str] = None

class ContextManager:
    """Advanced context management for AI conversations."""
    
    def __init__(self, window_config: Optional[ContextWindow] = None):
        self.window_config = window_config or ContextWindow()
        self.contexts: Dict[str, ConversationContext] = {}
        self.compressed_contexts: Dict[str, str] = {}
        
    def create_context(
        self,
        context_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a new conversation context."""
        if not context_id:
            context_id = self._generate_context_id()
        
        context = ConversationContext(
            id=context_id,
            metadata=metadata or {}
        )
        
        self.contexts[context_id] = context
        return context_id
    
    async def add_message(
        self,
        context_id: str,
        message: AIMessage,
        estimated_tokens: Optional[int] = None
    ) -> bool:
        """Add message to context with window management."""
        if context_id not in self.contexts:
            return False
        
        context = self.contexts[context_id]
        
        # Estimate tokens if not provided
        if not estimated_tokens:
            estimated_tokens = self._estimate_tokens(message.content)
        
        # Check if we need to compress or truncate
        projected_tokens = context.token_count + estimated_tokens
        if projected_tokens > self.window_config.max_tokens:
            await self._manage_context_window(context)
        
        # Add message
        context.messages.append(message)
        context.token_count += estimated_tokens
        context.updated_at = datetime.now()
        
        return True
    
    def get_context_messages(
        self,
        context_id: str,
        max_tokens: Optional[int] = None,
        include_summary: bool = True
    ) -> List[AIMessage]:
        """Get context messages within token limit."""
        if context_id not in self.contexts:
            return []
        
        context = self.contexts[context_id]
        max_tokens = max_tokens or self.window_config.max_tokens
        
        messages = []
        current_tokens = 0
        
        # Add summary if available and requested
        if include_summary and context.summary:
            summary_msg = AIMessage(
                role="system",
                content=f"Previous conversation summary: {context.summary}"
            )
            summary_tokens = self._estimate_tokens(context.summary)
            if summary_tokens < max_tokens * 0.2:  # Use max 20% for summary
                messages.append(summary_msg)
                current_tokens += summary_tokens
        
        # Add recent messages in reverse order
        for message in reversed(context.messages):
            msg_tokens = self._estimate_tokens(message.content)
            if current_tokens + msg_tokens > max_tokens:
                break
            
            messages.insert(-len([m for m in messages if m.role != "system"]), message)
            current_tokens += msg_tokens
        
        return messages
    
    def update_context_metadata(
        self,
        context_id: str,
        metadata: Dict[str, Any],
        merge: bool = True
    ) -> bool:
        """Update context metadata."""
        if context_id not in self.contexts:
            return False
        
        context = self.contexts[context_id]
        
        if merge:
            context.metadata.update(metadata)
        else:
            context.metadata = metadata
        
        context.updated_at = datetime.now()
        return True
    
    def get_context_info(self, context_id: str) -> Optional[Dict[str, Any]]:
        """Get context information and statistics."""
        if context_id not in self.contexts:
            return None
        
        context = self.contexts[context_id]
        
        return {
            "id": context.id,
            "message_count": len(context.messages),
            "token_count": context.token_count,
            "created_at": context.created_at.isoformat(),
            "updated_at": context.updated_at.isoformat(),
            "metadata": context.metadata,
            "has_summary": context.summary is not None,
            "is_compressed": context_id in self.compressed_contexts
        }
    
    def cleanup_expired_contexts(self) -> int:
        """Clean up expired contexts based on retention policy."""
        now = datetime.now()
        expired_contexts = []
        
        for context_id, context in self.contexts.items():
            age = now - context.updated_at
            if age > timedelta(hours=self.window_config.retention_hours):
                expired_contexts.append(context_id)
        
        for context_id in expired_contexts:
            self.delete_context(context_id)
        
        return len(expired_contexts)
    
    def delete_context(self, context_id: str) -> bool:
        """Delete a context and its compressed version."""
        deleted = False
        
        if context_id in self.contexts:
            del self.contexts[context_id]
            deleted = True
        
        if context_id in self.compressed_contexts:
            del self.compressed_contexts[context_id]
            deleted = True
        
        return deleted
    
    async def _manage_context_window(self, context: ConversationContext):
        """Manage context window by compression or truncation."""
        # If context is getting large, compress older messages
        if len(context.messages) > self.window_config.max_messages:
            await self._compress_context(context)
        
        # If still too large, truncate oldest messages
        while (context.token_count > self.window_config.max_tokens * 0.8 and 
               len(context.messages) > 5):
            removed_message = context.messages.pop(0)
            context.token_count -= self._estimate_tokens(removed_message.content)
    
    async def _compress_context(self, context: ConversationContext):
        """Compress older messages into a summary."""
        if len(context.messages) < 10:
            return
        
        # Take first 60% of messages for compression
        compress_count = int(len(context.messages) * 0.6)
        messages_to_compress = context.messages[:compress_count]
        
        # Create summary prompt
        conversation_text = "\n".join([
            f"{msg.role}: {msg.content}"
            for msg in messages_to_compress
        ])
        
        summary_prompt = f"""Please create a concise summary of this conversation that captures:
1. Key topics discussed
2. Important decisions made
3. Action items or next steps
4. Context that would be important for continuing the conversation

Conversation:
{conversation_text}

Summary:"""
        
        try:
            # Use a lightweight model for summarization
            from gateway import AIGateway
            gateway = AIGateway()
            
            summary_messages = [
                AIMessage(role="user", content=summary_prompt)
            ]
            
            response = await gateway.chat_completion(
                messages=summary_messages,
                model="gpt-4o-mini",  # Use cheaper model for summarization
                temperature=0.3,
                max_tokens=500
            )
            
            context.summary = response.content
            
            # Remove compressed messages
            context.messages = context.messages[compress_count:]
            
            # Recalculate token count
            context.token_count = sum(
                self._estimate_tokens(msg.content)
                for msg in context.messages
            )
            
        except Exception as e:
            print(f"Context compression failed: {e}")
            # Fallback: just truncate oldest messages
            context.messages = context.messages[compress_count:]
    
    def _estimate_tokens(self, text: str) -> int:
        """Estimate token count for text."""
        # Rough approximation: 1 token ≈ 4 characters
        return len(text) // 4 + 1
    
    def _generate_context_id(self) -> str:
        """Generate unique context ID."""
        timestamp = datetime.now().isoformat()
        return hashlib.md5(timestamp.encode()).hexdigest()[:12]
    
    def get_all_contexts(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all contexts."""
        return {
            context_id: self.get_context_info(context_id)
            for context_id in self.contexts.keys()
        }
    
    def export_context(self, context_id: str) -> Optional[Dict[str, Any]]:
        """Export context for backup or transfer."""
        if context_id not in self.contexts:
            return None
        
        context = self.contexts[context_id]
        
        return {
            "id": context.id,
            "messages": [
                {
                    "role": msg.role,
                    "content": msg.content,
                    "metadata": msg.metadata,
                    "timestamp": msg.timestamp
                }
                for msg in context.messages
            ],
            "metadata": context.metadata,
            "created_at": context.created_at.isoformat(),
            "updated_at": context.updated_at.isoformat(),
            "summary": context.summary,
            "token_count": context.token_count
        }
    
    def import_context(self, context_data: Dict[str, Any]) -> bool:
        """Import context from backup or transfer."""
        try:
            context_id = context_data["id"]
            
            messages = [
                AIMessage(
                    role=msg["role"],
                    content=msg["content"],
                    metadata=msg.get("metadata"),
                    timestamp=msg.get("timestamp")
                )
                for msg in context_data["messages"]
            ]
            
            context = ConversationContext(
                id=context_id,
                messages=messages,
                metadata=context_data["metadata"],
                created_at=datetime.fromisoformat(context_data["created_at"]),
                updated_at=datetime.fromisoformat(context_data["updated_at"]),
                summary=context_data.get("summary"),
                token_count=context_data["token_count"]
            )
            
            self.contexts[context_id] = context
            return True
            
        except Exception as e:
            print(f"Context import failed: {e}")
            return False