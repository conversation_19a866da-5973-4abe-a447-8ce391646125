"""
Chain of Thought Reasoning for AI Agents
Advanced reasoning capabilities with step-by-step problem solving
"""

from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import asyncio
import json
from datetime import datetime

from intelligence.providers.base import AIMessage
from intelligence.gateway import AIGateway
from utils.logging import get_logger

logger = get_logger(__name__)

class ReasoningStep(str, Enum):
    """Types of reasoning steps"""
    ANALYSIS = "analysis"
    HYPOTHESIS = "hypothesis"
    VERIFICATION = "verification"
    CONCLUSION = "conclusion"
    REFLECTION = "reflection"

@dataclass
class ThoughtStep:
    """Individual step in chain of thought reasoning"""
    step_number: int
    step_type: ReasoningStep
    description: str
    reasoning: str
    confidence: float = 0.0
    evidence: List[str] = field(default_factory=list)
    assumptions: List[str] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class ReasoningChain:
    """Complete chain of thought reasoning process"""
    id: str
    problem: str
    steps: List[ThoughtStep] = field(default_factory=list)
    final_answer: Optional[str] = None
    overall_confidence: float = 0.0
    total_reasoning_time: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None

class ChainOfThoughtReasoner:
    """Chain of Thought reasoning engine for complex problem solving"""
    
    def __init__(self, ai_gateway: AIGateway):
        self.ai_gateway = ai_gateway
        self.reasoning_chains: Dict[str, ReasoningChain] = {}
        
        # Reasoning templates
        self.step_prompts = {
            ReasoningStep.ANALYSIS: "Let's analyze this problem step by step:",
            ReasoningStep.HYPOTHESIS: "Based on the analysis, here are potential hypotheses:",
            ReasoningStep.VERIFICATION: "Let's verify our hypotheses:",
            ReasoningStep.CONCLUSION: "Drawing conclusions from our reasoning:",
            ReasoningStep.REFLECTION: "Reflecting on our reasoning process:"
        }
    
    async def reason_through_problem(
        self, 
        problem: str, 
        context: Optional[Dict[str, Any]] = None,
        max_steps: int = 10,
        confidence_threshold: float = 0.8
    ) -> ReasoningChain:
        """
        Reason through a complex problem using chain of thought methodology
        """
        start_time = datetime.now()
        
        # Create reasoning chain
        chain = ReasoningChain(
            id=f"reasoning_{int(start_time.timestamp())}",
            problem=problem,
            metadata=context or {}
        )
        
        self.reasoning_chains[chain.id] = chain
        
        logger.info(f"Starting chain of thought reasoning for: {problem[:100]}...")
        
        try:
            # Step 1: Initial Analysis
            await self._add_analysis_step(chain, problem, context)
            
            # Step 2: Generate hypotheses
            await self._add_hypothesis_step(chain)
            
            # Step 3: Verification loop
            for step_num in range(3, max_steps):
                if chain.overall_confidence >= confidence_threshold:
                    break
                    
                if step_num % 2 == 1:  # Odd steps: verification
                    await self._add_verification_step(chain, step_num)
                else:  # Even steps: refinement
                    await self._add_refinement_step(chain, step_num)
            
            # Final step: Conclusion
            await self._add_conclusion_step(chain)
            
            # Reflection step
            await self._add_reflection_step(chain)
            
            # Calculate final metrics
            chain.completed_at = datetime.now()
            chain.total_reasoning_time = (chain.completed_at - start_time).total_seconds()
            chain.overall_confidence = self._calculate_overall_confidence(chain)
            
            logger.info(f"Completed reasoning chain in {chain.total_reasoning_time:.2f}s with confidence {chain.overall_confidence:.2f}")
            
            return chain
            
        except Exception as e:
            logger.error(f"Chain of thought reasoning failed: {e}")
            chain.final_answer = f"Reasoning failed: {str(e)}"
            chain.overall_confidence = 0.0
            return chain
    
    async def _add_analysis_step(
        self, 
        chain: ReasoningChain, 
        problem: str, 
        context: Optional[Dict[str, Any]]
    ) -> None:
        """Add initial analysis step"""
        
        prompt = f"""
{self.step_prompts[ReasoningStep.ANALYSIS]}

Problem: {problem}

Context: {json.dumps(context, indent=2) if context else "None provided"}

Please provide a detailed analysis of this problem including:
1. Key components and variables
2. Potential challenges or complexities
3. What information might be needed
4. Initial observations

Format your response as structured analysis.
"""
        
        response = await self._get_ai_response(prompt)
        
        step = ThoughtStep(
            step_number=1,
            step_type=ReasoningStep.ANALYSIS,
            description="Initial problem analysis",
            reasoning=response,
            confidence=0.7  # Initial analysis confidence
        )
        
        chain.steps.append(step)
    
    async def _add_hypothesis_step(self, chain: ReasoningChain) -> None:
        """Add hypothesis generation step"""
        
        previous_analysis = chain.steps[-1].reasoning if chain.steps else ""
        
        prompt = f"""
{self.step_prompts[ReasoningStep.HYPOTHESIS]}

Based on this analysis:
{previous_analysis}

Generate 3-5 potential hypotheses or approaches to solve this problem. For each hypothesis:
1. State the hypothesis clearly
2. Explain the reasoning behind it
3. Identify what evidence would support or refute it
4. Estimate confidence level (0-1)

Format as numbered hypotheses.
"""
        
        response = await self._get_ai_response(prompt)
        
        step = ThoughtStep(
            step_number=len(chain.steps) + 1,
            step_type=ReasoningStep.HYPOTHESIS,
            description="Hypothesis generation",
            reasoning=response,
            confidence=0.6
        )
        
        chain.steps.append(step)
    
    async def _add_verification_step(self, chain: ReasoningChain, step_num: int) -> None:
        """Add verification step"""
        
        # Get previous hypotheses
        hypothesis_step = next((s for s in chain.steps if s.step_type == ReasoningStep.HYPOTHESIS), None)
        previous_step = chain.steps[-1] if chain.steps else None
        
        prompt = f"""
{self.step_prompts[ReasoningStep.VERIFICATION]}

Hypotheses to verify:
{hypothesis_step.reasoning if hypothesis_step else "No hypotheses found"}

Previous reasoning:
{previous_step.reasoning if previous_step else ""}

Now let's verify these hypotheses by:
1. Examining available evidence
2. Testing logical consistency
3. Identifying potential flaws or gaps
4. Ranking hypotheses by likelihood

Provide detailed verification analysis.
"""
        
        response = await self._get_ai_response(prompt)
        
        step = ThoughtStep(
            step_number=step_num,
            step_type=ReasoningStep.VERIFICATION,
            description=f"Verification round {step_num - 2}",
            reasoning=response,
            confidence=self._extract_confidence_from_response(response)
        )
        
        chain.steps.append(step)
    
    async def _add_refinement_step(self, chain: ReasoningChain, step_num: int) -> None:
        """Add refinement step"""
        
        recent_steps = chain.steps[-2:] if len(chain.steps) >= 2 else chain.steps
        recent_reasoning = "\n\n".join([f"Step {s.step_number}: {s.reasoning}" for s in recent_steps])
        
        prompt = f"""
Based on our recent reasoning:
{recent_reasoning}

Let's refine our understanding by:
1. Addressing any identified gaps or inconsistencies
2. Exploring alternative perspectives
3. Strengthening the most promising hypotheses
4. Identifying additional evidence needed

Provide refined analysis and updated reasoning.
"""
        
        response = await self._get_ai_response(prompt)
        
        step = ThoughtStep(
            step_number=step_num,
            step_type=ReasoningStep.ANALYSIS,  # Refinement is a type of analysis
            description=f"Refinement round {step_num - 2}",
            reasoning=response,
            confidence=self._extract_confidence_from_response(response)
        )
        
        chain.steps.append(step)
    
    async def _add_conclusion_step(self, chain: ReasoningChain) -> None:
        """Add final conclusion step"""
        
        all_reasoning = "\n\n".join([
            f"Step {s.step_number} ({s.step_type.value}): {s.reasoning}" 
            for s in chain.steps
        ])
        
        prompt = f"""
{self.step_prompts[ReasoningStep.CONCLUSION]}

Complete reasoning chain:
{all_reasoning}

Original problem: {chain.problem}

Based on all our reasoning above, please provide:
1. A clear, final answer to the original problem
2. The key reasoning that supports this answer
3. Confidence level in this conclusion (0-1)
4. Any important caveats or limitations
5. Alternative solutions if applicable

Format with clear FINAL ANSWER section.
"""
        
        response = await self._get_ai_response(prompt)
        
        # Extract final answer
        final_answer = self._extract_final_answer(response)
        chain.final_answer = final_answer
        
        step = ThoughtStep(
            step_number=len(chain.steps) + 1,
            step_type=ReasoningStep.CONCLUSION,
            description="Final conclusion",
            reasoning=response,
            confidence=self._extract_confidence_from_response(response)
        )
        
        chain.steps.append(step)
    
    async def _add_reflection_step(self, chain: ReasoningChain) -> None:
        """Add reflection step to evaluate reasoning quality"""
        
        prompt = f"""
{self.step_prompts[ReasoningStep.REFLECTION]}

Reasoning chain summary:
- Problem: {chain.problem}
- Steps taken: {len(chain.steps)}
- Final answer: {chain.final_answer}

Please reflect on this reasoning process:
1. Was the reasoning logical and well-structured?
2. Were there any gaps or weaknesses in the analysis?
3. Could the process have been more efficient?
4. How confident should we be in the final answer?
5. What could be improved for similar problems?

Provide constructive reflection and overall quality assessment.
"""
        
        response = await self._get_ai_response(prompt)
        
        step = ThoughtStep(
            step_number=len(chain.steps) + 1,
            step_type=ReasoningStep.REFLECTION,
            description="Process reflection",
            reasoning=response,
            confidence=self._extract_confidence_from_response(response)
        )
        
        chain.steps.append(step)
    
    async def _get_ai_response(self, prompt: str) -> str:
        """Get AI response for reasoning step"""
        
        messages = [
            AIMessage(
                role="system", 
                content="You are an expert reasoning assistant. Provide clear, logical, step-by-step analysis."
            ),
            AIMessage(role="user", content=prompt)
        ]
        
        try:
            response = await self.ai_gateway.chat_completion(
                messages=messages,
                model="gpt-4o",
                temperature=0.3,  # Lower temperature for more consistent reasoning
                max_tokens=2000
            )
            return response.content
            
        except Exception as e:
            logger.error(f"AI response failed: {e}")
            return f"Failed to get AI response: {str(e)}"
    
    def _extract_confidence_from_response(self, response: str) -> float:
        """Extract confidence score from AI response"""
        # Look for confidence indicators in the response
        confidence_keywords = [
            "confidence", "certain", "sure", "likely", "probable",
            "confident", "uncertainty", "doubt"
        ]
        
        # Simple heuristic based on language used
        confidence = 0.5  # Default
        
        response_lower = response.lower()
        
        if "very confident" in response_lower or "highly confident" in response_lower:
            confidence = 0.9
        elif "confident" in response_lower:
            confidence = 0.8
        elif "likely" in response_lower or "probable" in response_lower:
            confidence = 0.7
        elif "uncertain" in response_lower or "doubt" in response_lower:
            confidence = 0.4
        elif "unsure" in response_lower:
            confidence = 0.3
        
        # Look for explicit confidence percentages
        import re
        confidence_match = re.search(r'(\d+)%', response)
        if confidence_match:
            confidence = float(confidence_match.group(1)) / 100.0
        
        return min(max(confidence, 0.0), 1.0)  # Clamp to [0, 1]
    
    def _extract_final_answer(self, response: str) -> str:
        """Extract final answer from conclusion response"""
        
        # Look for FINAL ANSWER section
        import re
        
        final_answer_patterns = [
            r'FINAL ANSWER[:\s]+(.*?)(?:\n\n|\Z)',
            r'Final Answer[:\s]+(.*?)(?:\n\n|\Z)',
            r'Answer[:\s]+(.*?)(?:\n\n|\Z)',
            r'Conclusion[:\s]+(.*?)(?:\n\n|\Z)'
        ]
        
        for pattern in final_answer_patterns:
            match = re.search(pattern, response, re.IGNORECASE | re.DOTALL)
            if match:
                return match.group(1).strip()
        
        # If no specific section found, return first substantial paragraph
        paragraphs = [p.strip() for p in response.split('\n\n') if p.strip()]
        return paragraphs[0] if paragraphs else response[:200] + "..."
    
    def _calculate_overall_confidence(self, chain: ReasoningChain) -> float:
        """Calculate overall confidence for the reasoning chain"""
        
        if not chain.steps:
            return 0.0
        
        # Weight different step types differently
        weights = {
            ReasoningStep.ANALYSIS: 0.2,
            ReasoningStep.HYPOTHESIS: 0.2,
            ReasoningStep.VERIFICATION: 0.3,
            ReasoningStep.CONCLUSION: 0.4,
            ReasoningStep.REFLECTION: 0.1
        }
        
        weighted_confidence = 0.0
        total_weight = 0.0
        
        for step in chain.steps:
            weight = weights.get(step.step_type, 0.1)
            weighted_confidence += step.confidence * weight
            total_weight += weight
        
        return weighted_confidence / total_weight if total_weight > 0 else 0.0
    
    def get_reasoning_chain(self, chain_id: str) -> Optional[ReasoningChain]:
        """Get a reasoning chain by ID"""
        return self.reasoning_chains.get(chain_id)
    
    def get_chain_summary(self, chain_id: str) -> Optional[Dict[str, Any]]:
        """Get a summary of a reasoning chain"""
        
        chain = self.reasoning_chains.get(chain_id)
        if not chain:
            return None
        
        return {
            "id": chain.id,
            "problem": chain.problem,
            "final_answer": chain.final_answer,
            "confidence": chain.overall_confidence,
            "steps_count": len(chain.steps),
            "reasoning_time": chain.total_reasoning_time,
            "created_at": chain.created_at.isoformat(),
            "completed_at": chain.completed_at.isoformat() if chain.completed_at else None
        }
    
    def list_reasoning_chains(self) -> List[Dict[str, Any]]:
        """List all reasoning chains with summaries"""
        return [self.get_chain_summary(chain_id) for chain_id in self.reasoning_chains.keys()]