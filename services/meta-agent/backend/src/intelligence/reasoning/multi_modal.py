"""
Multi-Modal Processing for AI Agents
Handles text, image, audio, and video processing with AI models
"""

from typing import List, Dict, Any, Optional, Union, BinaryIO
from dataclasses import dataclass, field
from enum import Enum
import asyncio
import base64
import json
from datetime import datetime
from pathlib import Path
import mimetypes

from intelligence.providers.base import AIMessage
from intelligence.gateway import AIGateway
from utils.logging import get_logger

logger = get_logger(__name__)

class ModalityType(str, Enum):
    """Types of modalities supported"""
    TEXT = "text"
    IMAGE = "image"
    AUDIO = "audio"
    VIDEO = "video"
    DOCUMENT = "document"
    CODE = "code"

class ProcessingStatus(str, Enum):
    """Processing status"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

@dataclass
class MediaInput:
    """Input media for processing"""
    id: str
    modality: ModalityType
    content: Union[str, bytes, BinaryIO]
    mime_type: Optional[str] = None
    filename: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)

@dataclass
class ProcessingResult:
    """Result of multi-modal processing"""
    input_id: str
    modality: ModalityType
    extracted_text: Optional[str] = None
    description: Optional[str] = None
    analysis: Dict[str, Any] = field(default_factory=dict)
    confidence: float = 0.0
    processing_time: float = 0.0
    status: ProcessingStatus = ProcessingStatus.PENDING
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    processed_at: datetime = field(default_factory=datetime.now)

@dataclass
class MultiModalSession:
    """Session for multi-modal processing"""
    id: str
    inputs: List[MediaInput] = field(default_factory=list)
    results: List[ProcessingResult] = field(default_factory=list)
    context: str = ""
    final_response: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None

class MultiModalProcessor:
    """Multi-modal processing engine for AI agents"""
    
    def __init__(self, ai_gateway: AIGateway):
        self.ai_gateway = ai_gateway
        self.sessions: Dict[str, MultiModalSession] = {}
        
        # Supported file types
        self.supported_image_types = {
            'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 
            'image/webp', 'image/bmp', 'image/tiff'
        }
        self.supported_audio_types = {
            'audio/mpeg', 'audio/wav', 'audio/mp3', 'audio/mp4',
            'audio/aac', 'audio/ogg', 'audio/flac'
        }
        self.supported_video_types = {
            'video/mp4', 'video/avi', 'video/mov', 'video/mkv',
            'video/wmv', 'video/webm'
        }
        self.supported_document_types = {
            'application/pdf', 'text/plain', 'text/markdown',
            'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        }
    
    async def create_session(self, context: str = "") -> str:
        """Create a new multi-modal processing session"""
        
        session_id = f"multimodal_{int(datetime.now().timestamp())}"
        session = MultiModalSession(id=session_id, context=context)
        self.sessions[session_id] = session
        
        logger.info(f"Created multi-modal session: {session_id}")
        return session_id
    
    async def add_input(
        self, 
        session_id: str, 
        content: Union[str, bytes, BinaryIO],
        modality: Optional[ModalityType] = None,
        filename: Optional[str] = None,
        mime_type: Optional[str] = None
    ) -> str:
        """Add input to processing session"""
        
        session = self.sessions.get(session_id)
        if not session:
            raise ValueError(f"Session {session_id} not found")
        
        # Auto-detect modality if not provided
        if not modality:
            modality = self._detect_modality(content, filename, mime_type)
        
        # Auto-detect MIME type if not provided
        if not mime_type and filename:
            mime_type, _ = mimetypes.guess_type(filename)
        
        input_id = f"input_{len(session.inputs) + 1}"
        
        media_input = MediaInput(
            id=input_id,
            modality=modality,
            content=content,
            mime_type=mime_type,
            filename=filename
        )
        
        session.inputs.append(media_input)
        
        logger.info(f"Added {modality.value} input to session {session_id}: {input_id}")
        return input_id
    
    async def process_session(self, session_id: str) -> MultiModalSession:
        """Process all inputs in a session"""
        
        session = self.sessions.get(session_id)
        if not session:
            raise ValueError(f"Session {session_id} not found")
        
        logger.info(f"Processing session {session_id} with {len(session.inputs)} inputs")
        
        # Process each input
        for media_input in session.inputs:
            try:
                result = await self._process_single_input(media_input)
                session.results.append(result)
            except Exception as e:
                logger.error(f"Failed to process input {media_input.id}: {e}")
                error_result = ProcessingResult(
                    input_id=media_input.id,
                    modality=media_input.modality,
                    status=ProcessingStatus.FAILED,
                    error=str(e)
                )
                session.results.append(error_result)
        
        # Generate final response combining all results
        session.final_response = await self._generate_final_response(session)
        session.completed_at = datetime.now()
        
        logger.info(f"Completed processing session {session_id}")
        return session
    
    async def _process_single_input(self, media_input: MediaInput) -> ProcessingResult:
        """Process a single media input"""
        
        start_time = datetime.now()
        
        result = ProcessingResult(
            input_id=media_input.id,
            modality=media_input.modality,
            status=ProcessingStatus.PROCESSING
        )
        
        try:
            if media_input.modality == ModalityType.TEXT:
                result = await self._process_text(media_input, result)
            elif media_input.modality == ModalityType.IMAGE:
                result = await self._process_image(media_input, result)
            elif media_input.modality == ModalityType.AUDIO:
                result = await self._process_audio(media_input, result)
            elif media_input.modality == ModalityType.VIDEO:
                result = await self._process_video(media_input, result)
            elif media_input.modality == ModalityType.DOCUMENT:
                result = await self._process_document(media_input, result)
            elif media_input.modality == ModalityType.CODE:
                result = await self._process_code(media_input, result)
            else:
                raise ValueError(f"Unsupported modality: {media_input.modality}")
            
            result.status = ProcessingStatus.COMPLETED
            
        except Exception as e:
            result.status = ProcessingStatus.FAILED
            result.error = str(e)
            logger.error(f"Processing failed for {media_input.id}: {e}")
        
        finally:
            result.processing_time = (datetime.now() - start_time).total_seconds()
            result.processed_at = datetime.now()
        
        return result
    
    async def _process_text(self, media_input: MediaInput, result: ProcessingResult) -> ProcessingResult:
        """Process text input"""
        
        text_content = str(media_input.content)
        
        # For text, we can directly analyze it
        result.extracted_text = text_content
        
        # Analyze the text content
        analysis_prompt = f"""
Analyze this text content and provide:
1. Main topic or theme
2. Key entities mentioned
3. Sentiment analysis
4. Summary (if longer than 100 words)
5. Language and style analysis

Text to analyze:
{text_content}

Provide analysis in JSON format.
"""
        
        analysis_response = await self._get_ai_analysis(analysis_prompt)
        
        try:
            result.analysis = json.loads(analysis_response)
        except json.JSONDecodeError:
            result.analysis = {"raw_analysis": analysis_response}
        
        result.description = f"Text analysis of {len(text_content)} characters"
        result.confidence = 0.9  # High confidence for direct text
        
        return result
    
    async def _process_image(self, media_input: MediaInput, result: ProcessingResult) -> ProcessingResult:
        """Process image input"""
        
        # Convert image to base64 for AI processing
        if isinstance(media_input.content, bytes):
            image_data = media_input.content
        else:
            # If it's a file path or file object, read the content
            if hasattr(media_input.content, 'read'):
                image_data = media_input.content.read()
            else:
                with open(str(media_input.content), 'rb') as f:
                    image_data = f.read()
        
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        
        # Analyze image with vision model
        analysis_prompt = """
Analyze this image and provide:
1. Detailed description of what you see
2. Objects and people identified
3. Scene description (indoor/outdoor, setting)
4. Colors and composition
5. Any text visible in the image
6. Emotional tone or mood
7. Quality assessment

Provide comprehensive analysis in JSON format.
"""
        
        # Create message with image
        messages = [
            AIMessage(role="system", content="You are an expert image analysis AI."),
            AIMessage(
                role="user", 
                content=analysis_prompt,
                image_data=image_base64,
                image_type=media_input.mime_type
            )
        ]
        
        try:
            response = await self.ai_gateway.chat_completion(
                messages=messages,
                model="gpt-4o",  # Use vision-capable model
                max_tokens=1500
            )
            
            # Try to parse as JSON, fallback to raw response
            try:
                result.analysis = json.loads(response.content)
                result.description = result.analysis.get("description", "Image analysis completed")
                if "text_content" in result.analysis:
                    result.extracted_text = result.analysis["text_content"]
            except json.JSONDecodeError:
                result.analysis = {"raw_analysis": response.content}
                result.description = "Image analysis completed"
            
            result.confidence = 0.8
            
        except Exception as e:
            logger.error(f"Image analysis failed: {e}")
            result.analysis = {"error": str(e)}
            result.description = "Image analysis failed"
            result.confidence = 0.0
        
        return result
    
    async def _process_audio(self, media_input: MediaInput, result: ProcessingResult) -> ProcessingResult:
        """Process audio input"""
        
        # For audio, we would typically use speech-to-text
        # This is a simplified implementation
        
        result.description = f"Audio file: {media_input.filename or 'unknown'}"
        result.analysis = {
            "modality": "audio",
            "mime_type": media_input.mime_type,
            "note": "Audio processing requires speech-to-text service integration"
        }
        
        # In a real implementation, you would:
        # 1. Convert audio to appropriate format
        # 2. Send to speech-to-text service (Whisper, etc.)
        # 3. Analyze the transcribed text
        
        result.extracted_text = "[Audio transcription would be here with proper STT integration]"
        result.confidence = 0.5  # Lower confidence without actual processing
        
        return result
    
    async def _process_video(self, media_input: MediaInput, result: ProcessingResult) -> ProcessingResult:
        """Process video input"""
        
        result.description = f"Video file: {media_input.filename or 'unknown'}"
        result.analysis = {
            "modality": "video",
            "mime_type": media_input.mime_type,
            "note": "Video processing requires frame extraction and audio separation"
        }
        
        # In a real implementation, you would:
        # 1. Extract key frames from video
        # 2. Process audio track with STT
        # 3. Analyze visual content of frames
        # 4. Combine temporal analysis
        
        result.extracted_text = "[Video analysis would include visual and audio content]"
        result.confidence = 0.4  # Lower confidence without actual processing
        
        return result
    
    async def _process_document(self, media_input: MediaInput, result: ProcessingResult) -> ProcessingResult:
        """Process document input"""
        
        # For documents, extract text and analyze
        if media_input.mime_type == "text/plain":
            if isinstance(media_input.content, str):
                text_content = media_input.content
            else:
                text_content = media_input.content.decode('utf-8')
        else:
            # For other document types, you would use appropriate parsers
            # (PyPDF2 for PDF, python-docx for DOCX, etc.)
            text_content = "[Document text extraction requires specific parsers for this format]"
        
        result.extracted_text = text_content
        
        # Analyze document structure and content
        analysis_prompt = f"""
Analyze this document and provide:
1. Document type and structure
2. Main topics covered
3. Key information extracted
4. Summary of contents
5. Language and writing style

Document content:
{text_content[:2000]}...  # Truncate for analysis

Provide analysis in JSON format.
"""
        
        analysis_response = await self._get_ai_analysis(analysis_prompt)
        
        try:
            result.analysis = json.loads(analysis_response)
        except json.JSONDecodeError:
            result.analysis = {"raw_analysis": analysis_response}
        
        result.description = f"Document analysis: {media_input.filename or 'document'}"
        result.confidence = 0.8
        
        return result
    
    async def _process_code(self, media_input: MediaInput, result: ProcessingResult) -> ProcessingResult:
        """Process code input"""
        
        code_content = str(media_input.content)
        result.extracted_text = code_content
        
        # Analyze code
        analysis_prompt = f"""
Analyze this code and provide:
1. Programming language
2. Code purpose and functionality
3. Key functions/classes identified
4. Code quality assessment
5. Potential issues or improvements
6. Dependencies identified

Code to analyze:
{code_content}

Provide analysis in JSON format.
"""
        
        analysis_response = await self._get_ai_analysis(analysis_prompt)
        
        try:
            result.analysis = json.loads(analysis_response)
        except json.JSONDecodeError:
            result.analysis = {"raw_analysis": analysis_response}
        
        result.description = f"Code analysis: {media_input.filename or 'code snippet'}"
        result.confidence = 0.9
        
        return result
    
    async def _get_ai_analysis(self, prompt: str) -> str:
        """Get AI analysis for content"""
        
        messages = [
            AIMessage(role="system", content="You are an expert content analyzer. Provide detailed, structured analysis."),
            AIMessage(role="user", content=prompt)
        ]
        
        response = await self.ai_gateway.chat_completion(
            messages=messages,
            model="gpt-4o",
            temperature=0.3,
            max_tokens=1500
        )
        
        return response.content
    
    async def _generate_final_response(self, session: MultiModalSession) -> str:
        """Generate final response combining all processing results"""
        
        # Collect all analyses
        all_content = []
        
        for result in session.results:
            if result.status == ProcessingStatus.COMPLETED:
                content_summary = {
                    "type": result.modality.value,
                    "description": result.description,
                    "text": result.extracted_text,
                    "analysis": result.analysis,
                    "confidence": result.confidence
                }
                all_content.append(content_summary)
        
        # Generate comprehensive response
        synthesis_prompt = f"""
Based on the multi-modal content analysis below, provide a comprehensive synthesis:

Session Context: {session.context}

Content Analysis:
{json.dumps(all_content, indent=2)}

Please provide:
1. Overall summary of all content
2. Key insights across modalities
3. Connections between different inputs
4. Final conclusions or recommendations
5. Areas that need clarification

Format as a clear, coherent response.
"""
        
        messages = [
            AIMessage(role="system", content="You are an expert multi-modal content synthesizer."),
            AIMessage(role="user", content=synthesis_prompt)
        ]
        
        try:
            response = await self.ai_gateway.chat_completion(
                messages=messages,
                model="gpt-4o",
                temperature=0.4,
                max_tokens=2000
            )
            return response.content
            
        except Exception as e:
            logger.error(f"Final response generation failed: {e}")
            return f"Multi-modal processing completed with {len(session.results)} results. Error in synthesis: {str(e)}"
    
    def _detect_modality(
        self, 
        content: Union[str, bytes, BinaryIO], 
        filename: Optional[str] = None,
        mime_type: Optional[str] = None
    ) -> ModalityType:
        """Auto-detect content modality"""
        
        # Check MIME type first
        if mime_type:
            if mime_type in self.supported_image_types:
                return ModalityType.IMAGE
            elif mime_type in self.supported_audio_types:
                return ModalityType.AUDIO
            elif mime_type in self.supported_video_types:
                return ModalityType.VIDEO
            elif mime_type in self.supported_document_types:
                return ModalityType.DOCUMENT
            elif mime_type.startswith('text/'):
                return ModalityType.TEXT
        
        # Check filename extension
        if filename:
            ext = Path(filename).suffix.lower()
            
            if ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff']:
                return ModalityType.IMAGE
            elif ext in ['.mp3', '.wav', '.aac', '.ogg', '.flac', '.m4a']:
                return ModalityType.AUDIO
            elif ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.webm']:
                return ModalityType.VIDEO
            elif ext in ['.pdf', '.doc', '.docx', '.txt', '.md']:
                return ModalityType.DOCUMENT
            elif ext in ['.py', '.js', '.java', '.cpp', '.c', '.html', '.css', '.sql']:
                return ModalityType.CODE
        
        # Check content type
        if isinstance(content, str):
            # Check if it looks like code
            code_indicators = ['def ', 'function ', 'class ', 'import ', '#include', '<?php']
            if any(indicator in content for indicator in code_indicators):
                return ModalityType.CODE
            return ModalityType.TEXT
        
        # Default to document for binary content
        return ModalityType.DOCUMENT
    
    def get_session(self, session_id: str) -> Optional[MultiModalSession]:
        """Get session by ID"""
        return self.sessions.get(session_id)
    
    def get_session_summary(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session summary"""
        
        session = self.sessions.get(session_id)
        if not session:
            return None
        
        return {
            "id": session.id,
            "context": session.context,
            "inputs_count": len(session.inputs),
            "results_count": len(session.results),
            "completed": session.completed_at is not None,
            "created_at": session.created_at.isoformat(),
            "completed_at": session.completed_at.isoformat() if session.completed_at else None,
            "final_response": session.final_response
        }