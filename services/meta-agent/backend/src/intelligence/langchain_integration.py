"""LangChain integration for enhanced AI agent capabilities."""

from typing import Any, Dict, List, Optional, Callable, AsyncIterator
from langchain_core.language_models import BaseLanguageModel
from langchain_core.prompts import ChatPromptTemplate, PromptTemplate
from langchain_core.output_parsers import <PERSON>sonOutputParser, StrOutputParser
from langchain_core.runnables import Runnable, RunnableLambda, RunnablePassthrough
from langchain_core.runnables.config import RunnableConfig
from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage, AIMessage
from langchain_community.llms import OpenAI
from langchain_openai import ChatOpenAI
from langchain_anthropic import ChatAnthropic
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.chains import LLMChain, ConversationChain
from langchain.memory import ConversationBufferWindowMemory, ConversationSummaryBufferMemory
from langchain.agents import AgentType, initialize_agent, create_openai_functions_agent
from langchain.tools import Tool
from langchain.schema import AgentAction, AgentFinish
import asyncio
from pydantic import BaseModel
from .gateway import AIGateway
from .providers.base import AIMessage as PlatformMessage, AIResponse
import json

class LangChainProvider:
    """LangChain provider wrapper for platform integration."""
    
    def __init__(self):
        self.models: Dict[str, BaseLanguageModel] = {}
        self.chains: Dict[str, Runnable] = {}
        self.agents: Dict[str, Any] = {}
        self.tools: Dict[str, Tool] = {}
        
    async def initialize_models(self, ai_gateway: AIGateway) -> bool:
        """Initialize LangChain models from AI gateway providers."""
        try:
            # Get provider configurations from gateway
            provider_stats = ai_gateway.get_provider_stats()
            
            # Initialize OpenAI models
            if "openai" in provider_stats:
                self.models["openai"] = ChatOpenAI(
                    model="gpt-4o",
                    temperature=0.7,
                    streaming=True
                )
                self.models["openai-mini"] = ChatOpenAI(
                    model="gpt-4o-mini",
                    temperature=0.7
                )
            
            # Initialize Anthropic models
            if "anthropic" in provider_stats:
                self.models["claude"] = ChatAnthropic(
                    model="claude-3-5-sonnet-20241022",
                    temperature=0.7,
                    streaming=True
                )
                self.models["claude-haiku"] = ChatAnthropic(
                    model="claude-3-5-haiku-20241022",
                    temperature=0.7
                )
            
            # Initialize Google models
            if "google" in provider_stats:
                self.models["gemini"] = ChatGoogleGenerativeAI(
                    model="gemini-1.5-pro",
                    temperature=0.7
                )
                self.models["gemini-flash"] = ChatGoogleGenerativeAI(
                    model="gemini-1.5-flash",
                    temperature=0.7
                )
            
            return len(self.models) > 0
            
        except Exception as e:
            print(f"LangChain model initialization failed: {e}")
            return False
    
    def create_chain(
        self,
        chain_id: str,
        template: str,
        model: str = "openai",
        output_parser: Optional[str] = None,
        memory: Optional[str] = None
    ) -> bool:
        """Create a LangChain chain with specified configuration."""
        try:
            if model not in self.models:
                raise ValueError(f"Model {model} not available")
            
            # Create prompt template
            prompt = ChatPromptTemplate.from_template(template)
            
            # Select output parser
            parser = StrOutputParser()
            if output_parser == "json":
                parser = JsonOutputParser()
            
            # Create basic chain
            chain = prompt | self.models[model] | parser
            
            # Add memory if specified
            if memory:
                if memory == "window":
                    memory_obj = ConversationBufferWindowMemory(k=10)
                elif memory == "summary":
                    memory_obj = ConversationSummaryBufferMemory(
                        llm=self.models[model],
                        max_token_limit=1000
                    )
                else:
                    memory_obj = ConversationBufferWindowMemory(k=5)
                
                # Wrap chain with memory
                chain = ConversationChain(
                    llm=self.models[model],
                    memory=memory_obj,
                    prompt=prompt
                )
            
            self.chains[chain_id] = chain
            return True
            
        except Exception as e:
            print(f"Chain creation failed: {e}")
            return False
    
    def create_agent(
        self,
        agent_id: str,
        tools: List[str],
        model: str = "openai",
        agent_type: str = "openai_functions",
        system_message: Optional[str] = None
    ) -> bool:
        """Create a LangChain agent with specified tools."""
        try:
            if model not in self.models:
                raise ValueError(f"Model {model} not available")
            
            # Get tools
            agent_tools = []
            for tool_name in tools:
                if tool_name in self.tools:
                    agent_tools.append(self.tools[tool_name])
            
            # Create agent
            if agent_type == "openai_functions":
                if system_message:
                    prompt = ChatPromptTemplate.from_messages([
                        ("system", system_message),
                        ("human", "{input}"),
                        ("placeholder", "{agent_scratchpad}")
                    ])
                else:
                    prompt = ChatPromptTemplate.from_messages([
                        ("system", "You are a helpful AI assistant."),
                        ("human", "{input}"),
                        ("placeholder", "{agent_scratchpad}")
                    ])
                
                agent = create_openai_functions_agent(
                    llm=self.models[model],
                    tools=agent_tools,
                    prompt=prompt
                )
            else:
                agent = initialize_agent(
                    tools=agent_tools,
                    llm=self.models[model],
                    agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
                    verbose=True
                )
            
            self.agents[agent_id] = agent
            return True
            
        except Exception as e:
            print(f"Agent creation failed: {e}")
            return False
    
    def register_tool(
        self,
        name: str,
        description: str,
        function: Callable,
        return_direct: bool = False
    ) -> bool:
        """Register a custom tool for use with agents."""
        try:
            tool = Tool(
                name=name,
                description=description,
                func=function,
                return_direct=return_direct
            )
            
            self.tools[name] = tool
            return True
            
        except Exception as e:
            print(f"Tool registration failed: {e}")
            return False
    
    async def run_chain(
        self,
        chain_id: str,
        inputs: Dict[str, Any],
        config: Optional[RunnableConfig] = None
    ) -> Any:
        """Run a LangChain chain with inputs."""
        if chain_id not in self.chains:
            raise ValueError(f"Chain {chain_id} not found")
        
        chain = self.chains[chain_id]
        
        try:
            if hasattr(chain, 'ainvoke'):
                return await chain.ainvoke(inputs, config=config)
            else:
                # Fallback to sync execution
                return await asyncio.to_thread(chain.invoke, inputs, config)
        except Exception as e:
            raise Exception(f"Chain execution failed: {e}")
    
    async def run_agent(
        self,
        agent_id: str,
        input_text: str,
        config: Optional[RunnableConfig] = None
    ) -> Dict[str, Any]:
        """Run a LangChain agent with input."""
        if agent_id not in self.agents:
            raise ValueError(f"Agent {agent_id} not found")
        
        agent = self.agents[agent_id]
        
        try:
            if hasattr(agent, 'ainvoke'):
                result = await agent.ainvoke({"input": input_text}, config=config)
            else:
                result = await asyncio.to_thread(agent.invoke, {"input": input_text})
            
            return {
                "output": result.get("output", str(result)),
                "intermediate_steps": result.get("intermediate_steps", [])
            }
            
        except Exception as e:
            raise Exception(f"Agent execution failed: {e}")
    
    async def stream_chain(
        self,
        chain_id: str,
        inputs: Dict[str, Any],
        config: Optional[RunnableConfig] = None
    ) -> AsyncIterator[Any]:
        """Stream results from a LangChain chain."""
        if chain_id not in self.chains:
            raise ValueError(f"Chain {chain_id} not found")
        
        chain = self.chains[chain_id]
        
        try:
            if hasattr(chain, 'astream'):
                async for chunk in chain.astream(inputs, config=config):
                    yield chunk
            else:
                # Fallback: run chain and yield result
                result = await self.run_chain(chain_id, inputs, config)
                yield result
                
        except Exception as e:
            raise Exception(f"Chain streaming failed: {e}")

class LangChainAgentWrapper:
    """Wrapper to integrate LangChain agents with platform agent system."""
    
    def __init__(self, langchain_provider: LangChainProvider):
        self.provider = langchain_provider
    
    async def execute_task(
        self,
        agent_id: str,
        task: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Execute a task using LangChain agent."""
        
        task_type = task.get("type", "general")
        task_input = task.get("input", "")
        
        if task_type == "chain":
            # Run a specific chain
            chain_id = task.get("chain_id")
            inputs = task.get("inputs", {})
            
            result = await self.provider.run_chain(chain_id, inputs)
            
            return {
                "success": True,
                "result": result,
                "type": "chain_result"
            }
        
        elif task_type == "agent":
            # Run an agent
            result = await self.provider.run_agent(agent_id, task_input)
            
            return {
                "success": True,
                "result": result["output"],
                "intermediate_steps": result["intermediate_steps"],
                "type": "agent_result"
            }
        
        elif task_type == "custom":
            # Custom task execution
            custom_function = task.get("function")
            if custom_function and custom_function in self.provider.tools:
                tool = self.provider.tools[custom_function]
                result = await asyncio.to_thread(tool.func, task_input)
                
                return {
                    "success": True,
                    "result": result,
                    "type": "custom_result"
                }
        
        return {
            "success": False,
            "error": f"Unknown task type: {task_type}"
        }

class LangChainWorkflowBuilder:
    """Build complex workflows using LangChain components."""
    
    def __init__(self, provider: LangChainProvider):
        self.provider = provider
        self.workflows: Dict[str, Dict[str, Any]] = {}
    
    def create_workflow(
        self,
        workflow_id: str,
        steps: List[Dict[str, Any]],
        flow_type: str = "sequential"
    ) -> bool:
        """Create a workflow with multiple LangChain components."""
        
        try:
            workflow = {
                "id": workflow_id,
                "steps": steps,
                "flow_type": flow_type,
                "created_at": asyncio.get_event_loop().time()
            }
            
            # Validate workflow steps
            for step in steps:
                step_type = step.get("type")
                if step_type not in ["chain", "agent", "condition", "parallel"]:
                    raise ValueError(f"Invalid step type: {step_type}")
                
                if step_type == "chain" and step.get("chain_id") not in self.provider.chains:
                    raise ValueError(f"Chain {step.get('chain_id')} not found")
                
                if step_type == "agent" and step.get("agent_id") not in self.provider.agents:
                    raise ValueError(f"Agent {step.get('agent_id')} not found")
            
            self.workflows[workflow_id] = workflow
            return True
            
        except Exception as e:
            print(f"Workflow creation failed: {e}")
            return False
    
    async def execute_workflow(
        self,
        workflow_id: str,
        initial_input: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Execute a workflow with complex orchestration."""
        
        if workflow_id not in self.workflows:
            raise ValueError(f"Workflow {workflow_id} not found")
        
        workflow = self.workflows[workflow_id]
        steps = workflow["steps"]
        flow_type = workflow["flow_type"]
        
        results = []
        current_input = initial_input
        
        try:
            if flow_type == "sequential":
                for step in steps:
                    step_result = await self._execute_step(step, current_input, context)
                    results.append(step_result)
                    
                    # Pass output to next step
                    if step_result["success"]:
                        current_input = {"input": step_result["result"]}
            
            elif flow_type == "parallel":
                tasks = []
                for step in steps:
                    task = self._execute_step(step, current_input, context)
                    tasks.append(task)
                
                parallel_results = await asyncio.gather(*tasks, return_exceptions=True)
                results = parallel_results
            
            elif flow_type == "conditional":
                for step in steps:
                    condition = step.get("condition")
                    if condition and self._evaluate_condition(condition, current_input):
                        step_result = await self._execute_step(step, current_input, context)
                        results.append(step_result)
                        
                        if step.get("break_on_success", False) and step_result["success"]:
                            break
            
            return {
                "workflow_id": workflow_id,
                "success": True,
                "results": results,
                "execution_time": asyncio.get_event_loop().time() - workflow["created_at"]
            }
            
        except Exception as e:
            return {
                "workflow_id": workflow_id,
                "success": False,
                "error": str(e),
                "partial_results": results
            }
    
    async def _execute_step(
        self,
        step: Dict[str, Any],
        input_data: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Execute a single workflow step."""
        
        step_type = step["type"]
        
        if step_type == "chain":
            chain_id = step["chain_id"]
            result = await self.provider.run_chain(chain_id, input_data)
            
            return {
                "step_type": "chain",
                "chain_id": chain_id,
                "success": True,
                "result": result
            }
        
        elif step_type == "agent":
            agent_id = step["agent_id"]
            input_text = input_data.get("input", "")
            result = await self.provider.run_agent(agent_id, input_text)
            
            return {
                "step_type": "agent",
                "agent_id": agent_id,
                "success": True,
                "result": result["output"],
                "intermediate_steps": result["intermediate_steps"]
            }
        
        else:
            return {
                "step_type": step_type,
                "success": False,
                "error": f"Unsupported step type: {step_type}"
            }
    
    def _evaluate_condition(self, condition: Dict[str, Any], input_data: Dict[str, Any]) -> bool:
        """Evaluate a condition for conditional workflow execution."""
        
        condition_type = condition.get("type", "always")
        
        if condition_type == "always":
            return True
        elif condition_type == "never":
            return False
        elif condition_type == "key_exists":
            key = condition.get("key")
            return key in input_data
        elif condition_type == "value_equals":
            key = condition.get("key")
            value = condition.get("value")
            return input_data.get(key) == value
        elif condition_type == "custom":
            # Custom condition evaluation
            expression = condition.get("expression")
            try:
                return eval(expression, {"input": input_data})
            except:
                return False
        
        return False