"""
AI Agent Platform - Agent Tests
"""

import pytest
import uuid
from unittest.mock import AsyncMock, patch


class TestAgentAPI:
    """Test agent API endpoints"""
    
    def test_create_agent(self, client, authenticated_user, test_agent_data):
        """Test agent creation"""
        response = client.post(
            "/api/v1/agents",
            json=test_agent_data,
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 201
        data = response.json()
        
        assert data["name"] == test_agent_data["name"]
        assert data["description"] == test_agent_data["description"]
        assert data["type"] == test_agent_data["type"]
        assert data["status"] == "created"
        assert "id" in data
        assert "created_at" in data
    
    def test_create_agent_without_auth(self, client, test_agent_data):
        """Test agent creation without authentication"""
        response = client.post("/api/v1/agents", json=test_agent_data)
        assert response.status_code == 401
    
    def test_create_agent_invalid_data(self, client, authenticated_user):
        """Test agent creation with invalid data"""
        invalid_data = {"name": ""}  # Empty name should fail
        
        response = client.post(
            "/api/v1/agents",
            json=invalid_data,
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 422  # Validation error
    
    def test_get_agent(self, client, authenticated_user, test_agent):
        """Test getting agent by ID"""
        agent_id = test_agent["id"]
        
        response = client.get(
            f"/api/v1/agents/{agent_id}",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["id"] == agent_id
        assert data["name"] == test_agent["name"]
    
    def test_get_nonexistent_agent(self, client, authenticated_user):
        """Test getting nonexistent agent"""
        fake_id = str(uuid.uuid4())
        
        response = client.get(
            f"/api/v1/agents/{fake_id}",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 404
    
    def test_list_agents(self, client, authenticated_user, test_agent):
        """Test listing agents"""
        response = client.get(
            "/api/v1/agents",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "agents" in data
        assert "total" in data
        assert len(data["agents"]) >= 1
        assert any(agent["id"] == test_agent["id"] for agent in data["agents"])
    
    def test_list_agents_with_status_filter(self, client, authenticated_user, test_agent):
        """Test listing agents with status filter"""
        response = client.get(
            "/api/v1/agents?status=created",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # All returned agents should have 'created' status
        for agent in data["agents"]:
            assert agent["status"] == "created"
    
    def test_update_agent(self, client, authenticated_user, test_agent):
        """Test updating agent"""
        agent_id = test_agent["id"]
        update_data = {
            "name": "Updated Agent Name",
            "description": "Updated description"
        }
        
        response = client.put(
            f"/api/v1/agents/{agent_id}",
            json=update_data,
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["name"] == update_data["name"]
        assert data["description"] == update_data["description"]
    
    @patch('src.agents.manager.agent_manager.start_agent')
    def test_start_agent(self, mock_start_agent, client, authenticated_user, test_agent):
        """Test starting agent"""
        mock_start_agent.return_value = True
        agent_id = test_agent["id"]
        
        response = client.post(
            f"/api/v1/agents/{agent_id}/start",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] == True
        mock_start_agent.assert_called_once()
    
    @patch('src.agents.manager.agent_manager.stop_agent')
    def test_stop_agent(self, mock_stop_agent, client, authenticated_user, test_agent):
        """Test stopping agent"""
        mock_stop_agent.return_value = True
        agent_id = test_agent["id"]
        
        response = client.post(
            f"/api/v1/agents/{agent_id}/stop",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] == True
        mock_stop_agent.assert_called_once()
    
    def test_delete_agent(self, client, authenticated_user, test_agent):
        """Test deleting agent"""
        agent_id = test_agent["id"]
        
        response = client.delete(
            f"/api/v1/agents/{agent_id}",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 204
        
        # Verify agent is deleted
        response = client.get(
            f"/api/v1/agents/{agent_id}",
            headers=authenticated_user["headers"]
        )
        assert response.status_code == 404


class TestAgentService:
    """Test agent service layer"""
    
    @pytest.mark.asyncio
    async def test_create_agent_service(self, test_db, test_user_data):
        """Test agent service creation"""
        from src.services.agents import AgentService
        
        # Create a user first
        from src.auth.security import SecurityService
        security_service = SecurityService(test_db)
        user = await security_service.create_user(
            username=test_user_data["username"],
            email=test_user_data["email"],
            password=test_user_data["password"],
            full_name=test_user_data["full_name"]
        )
        
        # Create agent
        agent_service = AgentService(test_db)
        agent = await agent_service.create_agent(
            owner_id=user.id,
            name="Test Service Agent",
            description="Created via service layer",
            agent_type="assistant",
            config={"test": "config"},
            capabilities=["test_capability"]
        )
        
        assert agent.name == "Test Service Agent"
        assert agent.description == "Created via service layer"
        assert agent.type == "assistant"
        assert agent.owner_id == user.id
        assert agent.status.value == "created"
    
    @pytest.mark.asyncio
    async def test_get_agent_service(self, test_db, test_user_data):
        """Test getting agent via service"""
        from src.services.agents import AgentService
        from src.auth.security import SecurityService
        
        security_service = SecurityService(test_db)
        user = await security_service.create_user(
            username=test_user_data["username"],
            email=test_user_data["email"],
            password=test_user_data["password"]
        )
        
        agent_service = AgentService(test_db)
        created_agent = await agent_service.create_agent(
            owner_id=user.id,
            name="Test Agent",
            agent_type="assistant"
        )
        
        # Get agent
        retrieved_agent = await agent_service.get_agent(created_agent.id)
        
        assert retrieved_agent is not None
        assert retrieved_agent.id == created_agent.id
        assert retrieved_agent.name == "Test Agent"
    
    @pytest.mark.asyncio
    async def test_list_agents_service(self, test_db, test_user_data):
        """Test listing agents via service"""
        from src.services.agents import AgentService
        from src.auth.security import SecurityService
        from src.database.models import AgentStatus
        
        security_service = SecurityService(test_db)
        user = await security_service.create_user(
            username=test_user_data["username"],
            email=test_user_data["email"],
            password=test_user_data["password"]
        )
        
        agent_service = AgentService(test_db)
        
        # Create multiple agents
        agent1 = await agent_service.create_agent(
            owner_id=user.id,
            name="Agent 1",
            agent_type="assistant"
        )
        agent2 = await agent_service.create_agent(
            owner_id=user.id,
            name="Agent 2",
            agent_type="specialist"
        )
        
        # List all agents
        agents = await agent_service.list_agents(owner_id=user.id)
        
        assert len(agents) == 2
        agent_names = [agent.name for agent in agents]
        assert "Agent 1" in agent_names
        assert "Agent 2" in agent_names
        
        # List with status filter
        created_agents = await agent_service.list_agents(
            owner_id=user.id,
            status=AgentStatus.CREATED
        )
        assert len(created_agents) == 2