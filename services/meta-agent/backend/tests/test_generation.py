"""
AI Agent Platform - Agent Generation Tests
"""

import pytest
import json
import uuid
from unittest.mock import AsyncMock, patch, MagicMock
from fastapi.testclient import TestClient


class TestGenerationAPI:
    """Test agent generation API endpoints"""
    
    def test_get_capabilities(self, client):
        """Test getting available capabilities"""
        response = client.get("/api/v1/generation/capabilities")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "capabilities" in data
        assert isinstance(data["capabilities"], list)
        assert len(data["capabilities"]) > 0
        
        # Check first capability has required fields
        cap = data["capabilities"][0]
        assert "name" in cap
        assert "description" in cap
    
    def test_get_examples(self, client):
        """Test getting generation examples"""
        response = client.get("/api/v1/generation/examples")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "examples" in data
        assert isinstance(data["examples"], list)
        assert len(data["examples"]) > 0
        
        # Check first example has required fields
        example = data["examples"][0]
        assert "name" in example
        assert "config" in example
        
        config = example["config"]
        assert "agent_name" in config
        assert "agent_type" in config
        assert "language" in config
        assert "capabilities" in config
    
    def test_get_templates(self, client):
        """Test getting available templates"""
        response = client.get("/api/v1/generation/templates")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_validate_generation_request_valid(self, client):
        """Test validation with valid request"""
        valid_request = {
            "agent_name": "test-agent",
            "agent_type": "assistant",
            "language": "python",
            "description": "A test agent for validation",
            "capabilities": ["conversation", "data_analysis"],
            "configuration": {"max_tokens": 100},
            "custom_logic": "# Custom code here",
            "deployment_config": {"cpu_limit": "500m"}
        }
        
        response = client.post(
            "/api/v1/generation/validate",
            json=valid_request
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "valid" in data
        assert "errors" in data
        assert "warnings" in data
        assert isinstance(data["errors"], list)
        assert isinstance(data["warnings"], list)
    
    def test_validate_generation_request_invalid(self, client):
        """Test validation with invalid request"""
        invalid_request = {
            "agent_name": "",  # Empty name should fail
            "agent_type": "assistant",
            "language": "python",
            "description": "",  # Empty description should fail
            "capabilities": [],  # Empty capabilities should fail
        }
        
        response = client.post(
            "/api/v1/generation/validate",
            json=invalid_request
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["valid"] is False
        assert len(data["errors"]) > 0
    
    def test_validate_generation_request_warnings(self, client):
        """Test validation generates warnings for edge cases"""
        request_with_warnings = {
            "agent_name": "a",  # Short name should generate warning
            "agent_type": "assistant",
            "language": "python",
            "description": "A test agent",
            "capabilities": ["conversation"] + [f"cap_{i}" for i in range(15)],  # Many capabilities
            "custom_logic": "# " + "x" * 15000  # Very large custom logic
        }
        
        response = client.post(
            "/api/v1/generation/validate",
            json=request_with_warnings
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert len(data["warnings"]) > 0
    
    @patch('api.generation.agent_builder')
    def test_generate_agent_success(self, mock_builder, client):
        """Test successful agent generation"""
        # Mock the agent builder
        mock_generated_code = MagicMock()
        mock_generated_code.agent_code = "# Generated agent code"
        mock_generated_code.dockerfile = "FROM python:3.11"
        mock_generated_code.requirements = ["fastapi", "uvicorn"]
        mock_generated_code.config_file = "# Config file"
        mock_generated_code.tests = "# Test code"
        mock_generated_code.deployment_manifest = "# Deployment YAML"
        mock_generated_code.documentation = "# Documentation"
        
        mock_builder.validate_request.return_value = []
        mock_builder.create_agent.return_value = mock_generated_code
        
        valid_request = {
            "agent_name": "test-agent",
            "agent_type": "assistant",
            "language": "python",
            "description": "A test agent",
            "capabilities": ["conversation", "data_analysis"],
            "configuration": {},
            "custom_logic": "",
            "deployment_config": {}
        }
        
        response = client.post(
            "/api/v1/generation/generate",
            json=valid_request
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "generation_id" in data
        assert data["status"] == "pending"
        assert data["message"] == "Agent generation started"
        assert isinstance(data["generation_id"], str)
    
    def test_generate_agent_validation_failure(self, client):
        """Test agent generation with validation failure"""
        invalid_request = {
            "agent_name": "",  # This should fail validation
            "agent_type": "assistant",
            "language": "python",
            "description": "",
            "capabilities": []
        }
        
        response = client.post(
            "/api/v1/generation/generate",
            json=invalid_request
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "detail" in data
        assert "Validation failed" in data["detail"]
    
    def test_get_generation_status_not_found(self, client):
        """Test getting status for non-existent generation"""
        fake_id = str(uuid.uuid4())
        
        response = client.get(f"/api/v1/generation/status/{fake_id}")
        
        assert response.status_code == 404
        data = response.json()
        assert data["detail"] == "Generation not found"
    
    def test_download_agent_not_found(self, client):
        """Test downloading non-existent generation"""
        fake_id = str(uuid.uuid4())
        
        response = client.get(f"/api/v1/generation/download/{fake_id}")
        
        assert response.status_code == 404
        data = response.json()
        assert data["detail"] == "Generation not found"
    
    def test_cleanup_generation_not_found(self, client):
        """Test cleanup for non-existent generation"""
        fake_id = str(uuid.uuid4())
        
        response = client.delete(f"/api/v1/generation/cleanup/{fake_id}")
        
        assert response.status_code == 404
        data = response.json()
        assert data["detail"] == "Generation not found"


class TestGenerationWorkflow:
    """Test end-to-end generation workflow"""
    
    @patch('api.generation.agent_builder')
    def test_complete_generation_workflow(self, mock_builder, client):
        """Test complete workflow from generation to download"""
        # Mock the agent builder
        mock_generated_code = MagicMock()
        mock_generated_code.agent_code = "# Generated agent code"
        mock_generated_code.dockerfile = "FROM python:3.11"
        mock_generated_code.requirements = ["fastapi", "uvicorn"]
        mock_generated_code.config_file = "# Config file"
        mock_generated_code.tests = "# Test code"
        mock_generated_code.deployment_manifest = "# Deployment YAML"
        mock_generated_code.documentation = "# Documentation"
        
        mock_builder.validate_request.return_value = []
        mock_builder.create_agent.return_value = mock_generated_code
        
        valid_request = {
            "agent_name": "test-workflow-agent",
            "agent_type": "assistant",
            "language": "python",
            "description": "An agent for testing the complete workflow",
            "capabilities": ["conversation"],
            "configuration": {},
        }
        
        # Step 1: Start generation
        response = client.post(
            "/api/v1/generation/generate",
            json=valid_request
        )
        
        assert response.status_code == 200
        generation_data = response.json()
        generation_id = generation_data["generation_id"]
        
        # Step 2: Check initial status
        response = client.get(f"/api/v1/generation/status/{generation_id}")
        assert response.status_code == 200
        status_data = response.json()
        assert status_data["generation_id"] == generation_id
        assert status_data["status"] in ["pending", "generating"]
        
        # Note: In a real test, we would wait for the background task to complete
        # or mock the status store to simulate completion


class TestGenerationValidation:
    """Test generation request validation logic"""
    
    def test_agent_name_validation(self, client):
        """Test various agent name validation scenarios"""
        test_cases = [
            ("", False, "Empty name should fail"),
            ("a", True, "Single character should pass validation but may warn"),
            ("valid-agent-name", True, "Valid name should pass"),
            ("agent_with_underscores", True, "Underscores should be allowed"),
            ("agent-with-123", True, "Numbers should be allowed"),
            ("agent with spaces", False, "Spaces should fail"),
            ("agent@special", False, "Special characters should fail"),
        ]
        
        for agent_name, should_pass, description in test_cases:
            request = {
                "agent_name": agent_name,
                "agent_type": "assistant",
                "language": "python",
                "description": "Test agent",
                "capabilities": ["conversation"],
            }
            
            response = client.post("/api/v1/generation/validate", json=request)
            assert response.status_code == 200
            
            data = response.json()
            if should_pass:
                assert data["valid"] is True or len(data["errors"]) == 0, f"{description}: {data}"
            else:
                assert data["valid"] is False, f"{description}: {data}"
    
    def test_capability_validation(self, client):
        """Test capability validation"""
        # Test with no capabilities
        request = {
            "agent_name": "test-agent",
            "agent_type": "assistant", 
            "language": "python",
            "description": "Test agent",
            "capabilities": [],
        }
        
        response = client.post("/api/v1/generation/validate", json=request)
        assert response.status_code == 200
        
        data = response.json()
        assert data["valid"] is False
        assert any("capability" in error.lower() for error in data["errors"])


class TestGenerationConfiguration:
    """Test generation configuration handling"""
    
    def test_default_configuration(self, client):
        """Test generation with minimal configuration"""
        minimal_request = {
            "agent_name": "minimal-agent",
            "agent_type": "assistant",
            "language": "python", 
            "description": "Minimal test agent",
            "capabilities": ["conversation"],
        }
        
        response = client.post("/api/v1/generation/validate", json=minimal_request)
        assert response.status_code == 200
        
        data = response.json()
        assert data["valid"] is True
    
    def test_advanced_configuration(self, client):
        """Test generation with advanced configuration"""
        advanced_request = {
            "agent_name": "advanced-agent",
            "agent_type": "specialist",
            "language": "typescript",
            "description": "Advanced test agent with custom configuration",
            "capabilities": ["conversation", "data_analysis", "api_integration"],
            "configuration": {
                "max_concurrent_tasks": 10,
                "timeout_seconds": 300,
                "custom_setting": "value"
            },
            "custom_logic": """
                # Custom initialization logic
                def custom_setup():
                    print("Setting up custom functionality")
                    return True
            """,
            "deployment_config": {
                "cpu_limit": "1000m",
                "memory_limit": "1Gi",
                "replicas": 2
            }
        }
        
        response = client.post("/api/v1/generation/validate", json=advanced_request)
        assert response.status_code == 200
        
        data = response.json()
        assert data["valid"] is True


# Fixtures for generation tests
@pytest.fixture
def sample_generation_request():
    """Sample valid generation request"""
    return {
        "agent_name": "sample-test-agent",
        "agent_type": "assistant",
        "language": "python",
        "description": "A sample agent for testing purposes",
        "capabilities": ["conversation", "data_analysis"],
        "configuration": {
            "max_tokens": 100,
            "temperature": 0.7
        },
        "custom_logic": "# Sample custom logic",
        "deployment_config": {
            "cpu_limit": "500m",
            "memory_limit": "512Mi"
        }
    }


@pytest.fixture
def invalid_generation_request():
    """Sample invalid generation request"""
    return {
        "agent_name": "",  # Invalid: empty name
        "agent_type": "assistant",
        "language": "python", 
        "description": "",  # Invalid: empty description
        "capabilities": [],  # Invalid: no capabilities
    }