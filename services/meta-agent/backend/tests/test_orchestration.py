"""
AI Agent Platform - Orchestration Tests
"""

import pytest
import uuid
from unittest.mock import AsyncMock, patch


class TestOrchestrationAPI:
    """Test orchestration API endpoints"""
    
    def test_create_orchestration(self, client, authenticated_user, test_orchestration_data):
        """Test orchestration creation"""
        response = client.post(
            "/api/v1/orchestrations",
            json=test_orchestration_data,
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 201
        data = response.json()
        
        assert data["name"] == test_orchestration_data["name"]
        assert data["description"] == test_orchestration_data["description"]
        assert data["pattern"] == test_orchestration_data["pattern"]
        assert data["status"] == "created"
        assert "id" in data
    
    def test_create_orchestration_without_auth(self, client, test_orchestration_data):
        """Test orchestration creation without authentication"""
        response = client.post("/api/v1/orchestrations", json=test_orchestration_data)
        assert response.status_code == 401
    
    def test_create_orchestration_with_agents(self, client, authenticated_user, test_agent, test_orchestration_data):
        """Test orchestration creation with agents"""
        test_orchestration_data["agent_ids"] = [test_agent["id"]]
        
        response = client.post(
            "/api/v1/orchestrations",
            json=test_orchestration_data,
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 201
        data = response.json()
        assert len(data["members"]) == 1
        assert data["members"][0]["agent_id"] == test_agent["id"]
    
    def test_get_orchestration(self, client, authenticated_user, test_orchestration):
        """Test getting orchestration by ID"""
        orchestration_id = test_orchestration["id"]
        
        response = client.get(
            f"/api/v1/orchestrations/{orchestration_id}",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["id"] == orchestration_id
        assert data["name"] == test_orchestration["name"]
    
    def test_get_nonexistent_orchestration(self, client, authenticated_user):
        """Test getting nonexistent orchestration"""
        fake_id = str(uuid.uuid4())
        
        response = client.get(
            f"/api/v1/orchestrations/{fake_id}",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 404
    
    def test_list_orchestrations(self, client, authenticated_user, test_orchestration):
        """Test listing orchestrations"""
        response = client.get(
            "/api/v1/orchestrations",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "orchestrations" in data
        assert "total" in data
        assert len(data["orchestrations"]) >= 1
        assert any(orch["id"] == test_orchestration["id"] for orch in data["orchestrations"])
    
    def test_list_orchestrations_with_pattern_filter(self, client, authenticated_user, test_orchestration):
        """Test listing orchestrations with pattern filter"""
        response = client.get(
            "/api/v1/orchestrations?pattern=sequential",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # All returned orchestrations should have 'sequential' pattern
        for orch in data["orchestrations"]:
            assert orch["pattern"] == "sequential"
    
    def test_update_orchestration(self, client, authenticated_user, test_orchestration):
        """Test updating orchestration"""
        orchestration_id = test_orchestration["id"]
        update_data = {
            "name": "Updated Orchestration Name",
            "description": "Updated description"
        }
        
        response = client.put(
            f"/api/v1/orchestrations/{orchestration_id}",
            json=update_data,
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["name"] == update_data["name"]
        assert data["description"] == update_data["description"]
    
    @patch('src.orchestration.manager.orchestration_manager.start_orchestration')
    def test_start_orchestration(self, mock_start, client, authenticated_user, test_orchestration):
        """Test starting orchestration"""
        mock_start.return_value = True
        orchestration_id = test_orchestration["id"]
        
        response = client.post(
            f"/api/v1/orchestrations/{orchestration_id}/start",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] == True
        mock_start.assert_called_once()
    
    @patch('src.orchestration.manager.orchestration_manager.stop_orchestration')
    def test_stop_orchestration(self, mock_stop, client, authenticated_user, test_orchestration):
        """Test stopping orchestration"""
        mock_stop.return_value = True
        orchestration_id = test_orchestration["id"]
        
        response = client.post(
            f"/api/v1/orchestrations/{orchestration_id}/stop",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] == True
        mock_stop.assert_called_once()
    
    def test_add_agent_to_orchestration(self, client, authenticated_user, test_orchestration, test_agent):
        """Test adding agent to orchestration"""
        orchestration_id = test_orchestration["id"]
        agent_data = {
            "agent_id": test_agent["id"],
            "role": "worker"
        }
        
        response = client.post(
            f"/api/v1/orchestrations/{orchestration_id}/agents",
            json=agent_data,
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["message"] == "Agent added to orchestration successfully"
    
    def test_remove_agent_from_orchestration(self, client, authenticated_user, test_orchestration, test_agent):
        """Test removing agent from orchestration"""
        orchestration_id = test_orchestration["id"]
        agent_id = test_agent["id"]
        
        # First add the agent
        agent_data = {"agent_id": agent_id, "role": "worker"}
        client.post(
            f"/api/v1/orchestrations/{orchestration_id}/agents",
            json=agent_data,
            headers=authenticated_user["headers"]
        )
        
        # Then remove it
        response = client.delete(
            f"/api/v1/orchestrations/{orchestration_id}/agents/{agent_id}",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 204
    
    def test_delete_orchestration(self, client, authenticated_user, test_orchestration):
        """Test deleting orchestration"""
        orchestration_id = test_orchestration["id"]
        
        response = client.delete(
            f"/api/v1/orchestrations/{orchestration_id}",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 204
        
        # Verify orchestration is deleted
        response = client.get(
            f"/api/v1/orchestrations/{orchestration_id}",
            headers=authenticated_user["headers"]
        )
        assert response.status_code == 404


class TestOrchestrationService:
    """Test orchestration service layer"""
    
    @pytest.mark.asyncio
    async def test_create_orchestration_service(self, test_db, test_user_data):
        """Test orchestration service creation"""
        from src.services.orchestration import OrchestrationService
        from src.auth.security import SecurityService
        from src.database.models import OrchestrationPattern
        
        # Create user first
        security_service = SecurityService(test_db)
        user = await security_service.create_user(
            username=test_user_data["username"],
            email=test_user_data["email"],
            password=test_user_data["password"]
        )
        
        # Create orchestration
        orchestration_service = OrchestrationService(test_db)
        orchestration = await orchestration_service.create_orchestration(
            owner_id=user.id,
            name="Test Service Orchestration",
            description="Created via service layer",
            pattern=OrchestrationPattern.SEQUENTIAL,
            config={"test": "config"},
            execution_plan={"steps": ["step1", "step2"]}
        )
        
        assert orchestration.name == "Test Service Orchestration"
        assert orchestration.description == "Created via service layer"
        assert orchestration.pattern == OrchestrationPattern.SEQUENTIAL
        assert orchestration.owner_id == user.id
        assert orchestration.status == "created"
    
    @pytest.mark.asyncio
    async def test_get_orchestration_service(self, test_db, test_user_data):
        """Test getting orchestration via service"""
        from src.services.orchestration import OrchestrationService
        from src.auth.security import SecurityService
        from src.database.models import OrchestrationPattern
        
        security_service = SecurityService(test_db)
        user = await security_service.create_user(
            username=test_user_data["username"],
            email=test_user_data["email"],
            password=test_user_data["password"]
        )
        
        orchestration_service = OrchestrationService(test_db)
        created_orchestration = await orchestration_service.create_orchestration(
            owner_id=user.id,
            name="Test Orchestration",
            pattern=OrchestrationPattern.PARALLEL
        )
        
        # Get orchestration
        retrieved_orchestration = await orchestration_service.get_orchestration(
            created_orchestration.id
        )
        
        assert retrieved_orchestration is not None
        assert retrieved_orchestration.id == created_orchestration.id
        assert retrieved_orchestration.name == "Test Orchestration"
    
    @pytest.mark.asyncio
    async def test_list_orchestrations_service(self, test_db, test_user_data):
        """Test listing orchestrations via service"""
        from src.services.orchestration import OrchestrationService
        from src.auth.security import SecurityService
        from src.database.models import OrchestrationPattern
        
        security_service = SecurityService(test_db)
        user = await security_service.create_user(
            username=test_user_data["username"],
            email=test_user_data["email"],
            password=test_user_data["password"]
        )
        
        orchestration_service = OrchestrationService(test_db)
        
        # Create multiple orchestrations
        orch1 = await orchestration_service.create_orchestration(
            owner_id=user.id,
            name="Orchestration 1",
            pattern=OrchestrationPattern.SEQUENTIAL
        )
        orch2 = await orchestration_service.create_orchestration(
            owner_id=user.id,
            name="Orchestration 2",
            pattern=OrchestrationPattern.PARALLEL
        )
        
        # List all orchestrations
        orchestrations = await orchestration_service.list_orchestrations(
            owner_id=user.id
        )
        
        assert len(orchestrations) == 2
        orch_names = [orch.name for orch in orchestrations]
        assert "Orchestration 1" in orch_names
        assert "Orchestration 2" in orch_names
        
        # List with pattern filter
        sequential_orchestrations = await orchestration_service.list_orchestrations(
            owner_id=user.id,
            pattern=OrchestrationPattern.SEQUENTIAL
        )
        assert len(sequential_orchestrations) == 1
        assert sequential_orchestrations[0].name == "Orchestration 1"


class TestOrchestrationEngine:
    """Test orchestration engine"""
    
    @pytest.mark.asyncio
    async def test_orchestration_engine_creation(self):
        """Test creating orchestration engine"""
        from src.orchestration.orchestrator import OrchestrationEngine
        from src.database.models import OrchestrationPattern
        
        config = {
            'pattern': OrchestrationPattern.SEQUENTIAL,
            'agent_ids': [uuid.uuid4(), uuid.uuid4()],
            'execution_plan': {'steps': ['step1', 'step2']}
        }
        
        orchestration_id = uuid.uuid4()
        engine = OrchestrationEngine(orchestration_id, config)
        
        assert engine.orchestration_id == orchestration_id
        assert engine.pattern == OrchestrationPattern.SEQUENTIAL
        assert len(engine.agent_ids) == 2
        assert engine.status.value == "created"
    
    @pytest.mark.asyncio
    async def test_orchestration_engine_add_task(self):
        """Test adding task to orchestration engine"""
        from src.orchestration.orchestrator import OrchestrationEngine
        from src.database.models import OrchestrationPattern
        
        config = {
            'pattern': OrchestrationPattern.SEQUENTIAL,
            'agent_ids': [uuid.uuid4()],
            'execution_plan': {}
        }
        
        engine = OrchestrationEngine(uuid.uuid4(), config)
        
        task_id = uuid.uuid4()
        task_data = {
            'title': 'Test Task',
            'type': 'test',
            'input_data': {'test': 'data'}
        }
        
        success = await engine.add_task(task_id, 'test', task_data)
        assert success == True
        assert task_id in engine.tasks
        assert engine.total_steps == 1
    
    @pytest.mark.asyncio
    async def test_orchestration_progress(self):
        """Test getting orchestration progress"""
        from src.orchestration.orchestrator import OrchestrationEngine
        from src.database.models import OrchestrationPattern
        
        config = {
            'pattern': OrchestrationPattern.SEQUENTIAL,
            'agent_ids': [uuid.uuid4()],
            'execution_plan': {}
        }
        
        engine = OrchestrationEngine(uuid.uuid4(), config)
        
        # Add tasks
        task1_id = uuid.uuid4()
        task2_id = uuid.uuid4()
        
        await engine.add_task(task1_id, 'test', {'title': 'Task 1'})
        await engine.add_task(task2_id, 'test', {'title': 'Task 2'})
        
        # Get progress
        progress = await engine.get_progress()
        
        assert progress['total_steps'] == 2
        assert progress['completed_steps'] == 0
        assert progress['progress_percentage'] == 0.0
        assert progress['status'] == "created"