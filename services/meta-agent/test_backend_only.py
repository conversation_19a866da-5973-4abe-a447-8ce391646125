#!/usr/bin/env python3
"""
Test backend functionality only
"""

import requests
import json

def main():
    print('🔧 Testing Backend Functionality Only')
    print('=' * 50)

    # Test Backend
    try:
        backend = requests.get('http://localhost:8000/health', timeout=5)
        print(f'✅ Backend: {backend.status_code} - {backend.json()}')
    except Exception as e:
        print(f'❌ Backend: {e}')
        return

    # Test Agent Creation
    print('\n🤖 Testing Agent Creation...')
    agent_data = {
        'name': 'Backend Test Agent',
        'description': 'Testing backend functionality',
        'type': 'assistant',
        'config': {},
        'capabilities': ['conversation']
    }

    try:
        response = requests.post('http://localhost:8000/api/v1/public/agents', json=agent_data, timeout=10)
        if response.status_code == 200:
            agent = response.json()
            agent_id = agent['id']
            print(f'✅ Agent Created: {agent["name"]} (ID: {agent_id})')
            
            # Test Deployment
            print('\n🚀 Testing Deployment...')
            deploy_response = requests.post(f'http://localhost:8000/api/v1/public/agents/{agent_id}/deploy', timeout=10)
            if deploy_response.status_code == 200:
                deployment = deploy_response.json()
                print(f'✅ Agent Deployed: {deployment["deployment_url"]}')
                
                # Test Deployed Interface
                print('\n🌐 Testing Deployed Interface...')
                interface_response = requests.get(f'http://localhost:8000/api/v1/public/deployed/{agent_id}', timeout=10)
                if interface_response.status_code == 200:
                    interface_data = interface_response.json()
                    print(f'✅ Interface Working: {interface_data["interface"]["title"]}')
                else:
                    print(f'❌ Interface failed: {interface_response.status_code}')
            else:
                print(f'❌ Deployment failed: {deploy_response.status_code}')
        else:
            print(f'❌ Creation failed: {response.status_code}')
            print(f'   Error: {response.text}')
    except Exception as e:
        print(f'❌ Error: {e}')

    # Test Dashboard APIs
    print('\n📊 Testing Dashboard APIs...')
    endpoints = [
        ('System Stats', 'http://localhost:8000/api/v1/public/runtime/system/stats'),
        ('System Health', 'http://localhost:8000/api/v1/public/runtime/system/health'),
        ('Agents List', 'http://localhost:8000/api/v1/public/agents'),
    ]

    for name, url in endpoints:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f'✅ {name}: Working')
                if name == 'Agents List':
                    print(f'   Total agents: {data.get("total", 0)}')
                elif name == 'System Stats':
                    print(f'   Active agents: {data.get("active_agents", 0)}')
            else:
                print(f'❌ {name}: {response.status_code}')
        except Exception as e:
            print(f'❌ {name}: {e}')

    print('\n🎉 Backend functionality is working!')
    print('🌐 Backend URLs:')
    print('   Health: http://localhost:8000/health')
    print('   API Docs: http://localhost:8000/docs')
    print('   Agents: http://localhost:8000/api/v1/public/agents')

if __name__ == "__main__":
    main()
