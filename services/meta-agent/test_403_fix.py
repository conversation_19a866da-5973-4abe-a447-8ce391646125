#!/usr/bin/env python3
"""
Test script to verify 403 Forbidden errors are fixed
Tests all public endpoints that frontend uses
"""

import requests
import json
import time
import sys

BASE_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:3000"

def test_public_endpoints():
    """Test all public endpoints that frontend uses"""
    print("🔐 Testing Public Endpoints (403 Fix)")
    print("=" * 50)
    
    endpoints_to_test = [
        # Agent management endpoints
        ("GET", "/api/v1/public/agents", "List agents"),
        ("GET", "/api/v1/public/runtime/system/stats", "System stats"),
        ("GET", "/api/v1/public/runtime/system/health", "System health"),
        ("GET", "/api/v1/public/runtime/agents/active", "Active agents"),
    ]
    
    successful_endpoints = 0
    
    for method, endpoint, description in endpoints_to_test:
        print(f"\n🔍 Testing: {description}")
        print(f"   {method} {endpoint}")
        
        try:
            if method == "GET":
                response = requests.get(f"{BASE_URL}{endpoint}")
            elif method == "POST":
                response = requests.post(f"{BASE_URL}{endpoint}", json={})
            
            if response.status_code == 200:
                print(f"   ✅ Success: {response.status_code}")
                successful_endpoints += 1
            elif response.status_code == 403:
                print(f"   ❌ 403 Forbidden: Still has authentication issues")
            elif response.status_code == 404:
                print(f"   ⚠️  404 Not Found: Endpoint may not exist")
            else:
                print(f"   ⚠️  Status: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\n📊 Endpoint Test Results:")
    print(f"   Successful: {successful_endpoints}/{len(endpoints_to_test)}")
    print(f"   Success rate: {(successful_endpoints/len(endpoints_to_test)*100):.1f}%")
    
    return successful_endpoints >= len(endpoints_to_test) * 0.8

def test_agent_operations():
    """Test complete agent operations without 403 errors"""
    print("\n🤖 Testing Agent Operations")
    print("=" * 40)
    
    # Step 1: Create agent
    print("\n1️⃣ Creating test agent...")
    agent_data = {
        "name": "403 Fix Test Agent",
        "description": "Testing 403 error fixes",
        "type": "assistant",
        "capabilities": ["natural_language"],
        "config": {}
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/v1/public/agents", json=agent_data)
        if response.status_code == 403:
            print("❌ 403 Forbidden on agent creation")
            return False
        response.raise_for_status()
        agent = response.json()
        agent_id = agent["id"]
        print(f"✅ Agent created: {agent_id}")
    except Exception as e:
        print(f"❌ Failed to create agent: {e}")
        return False
    
    # Step 2: Test deployment
    print("\n2️⃣ Testing deployment...")
    try:
        response = requests.post(f"{BASE_URL}/api/v1/public/agents/{agent_id}/deploy")
        if response.status_code == 403:
            print("❌ 403 Forbidden on deployment")
            return False
        response.raise_for_status()
        deployment = response.json()
        print(f"✅ Deployment successful: {deployment['deployment_url']}")
    except Exception as e:
        print(f"❌ Failed to deploy: {e}")
        return False
    
    # Step 3: Test deployment status
    print("\n3️⃣ Testing deployment status...")
    try:
        response = requests.get(f"{BASE_URL}/api/v1/public/agents/{agent_id}/deployment")
        if response.status_code == 403:
            print("❌ 403 Forbidden on deployment status")
            return False
        response.raise_for_status()
        status = response.json()
        print(f"✅ Status check successful: {status['deployed']}")
    except Exception as e:
        print(f"❌ Failed to check status: {e}")
        return False
    
    # Step 4: Test chat
    print("\n4️⃣ Testing chat...")
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/public/deployed/{agent_id}/chat",
            json={"message": "Hello"},
            headers={"Content-Type": "application/json"}
        )
        if response.status_code == 403:
            print("❌ 403 Forbidden on chat")
            return False
        response.raise_for_status()
        chat_response = response.json()
        print(f"✅ Chat successful: {chat_response['status']}")
    except Exception as e:
        print(f"❌ Failed to chat: {e}")
        return False
    
    print("\n✅ All agent operations successful - no 403 errors!")
    return True

def test_frontend_pages():
    """Test frontend page accessibility"""
    print("\n🌐 Testing Frontend Pages")
    print("=" * 30)
    
    pages = [
        ("/", "Home"),
        ("/dashboard", "Dashboard"),
        ("/agents", "Agents List"),
        ("/agents/create", "Agent Creation")
    ]
    
    accessible_pages = 0
    
    for path, name in pages:
        try:
            response = requests.get(f"{FRONTEND_URL}{path}", timeout=10)
            if response.status_code == 200:
                print(f"✅ {name}: Accessible")
                accessible_pages += 1
            else:
                print(f"⚠️  {name}: Status {response.status_code}")
        except Exception as e:
            print(f"❌ {name}: Error - {e}")
    
    print(f"\n📊 Frontend accessibility: {accessible_pages}/{len(pages)}")
    return accessible_pages >= len(pages) * 0.75

def main():
    """Run comprehensive 403 error fix tests"""
    print("🔐 TESTING 403 FORBIDDEN ERROR FIXES")
    print("=" * 60)
    
    # Test public endpoints
    endpoints_ok = test_public_endpoints()
    
    # Test agent operations
    operations_ok = test_agent_operations()
    
    # Test frontend pages
    frontend_ok = test_frontend_pages()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    tests = [
        ("Public Endpoints", endpoints_ok),
        ("Agent Operations", operations_ok),
        ("Frontend Pages", frontend_ok)
    ]
    
    passed_tests = sum(1 for _, passed in tests if passed)
    
    for test_name, passed in tests:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    print(f"\n📈 Overall Success Rate: {passed_tests}/{len(tests)} ({passed_tests/len(tests)*100:.1f}%)")
    
    if passed_tests >= len(tests) * 0.8:
        print("\n🎉 403 ERRORS FIXED!")
        print("✅ All public endpoints: Working")
        print("✅ Agent operations: No authentication errors")
        print("✅ Frontend integration: Functional")
        
        print(f"\n📋 USER TESTING:")
        print(f"1. Dashboard: {FRONTEND_URL}/dashboard")
        print(f"2. Agents: {FRONTEND_URL}/agents")
        print(f"3. Create Agent: {FRONTEND_URL}/agents/create")
        print(f"4. Deploy agents and test chat functionality")
        
        return True
    else:
        print("\n❌ 403 ERRORS STILL EXIST")
        print("   Some endpoints still require authentication")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
