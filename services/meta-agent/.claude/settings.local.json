{"permissions": {"allow": ["Bash(cp:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(docker compose:*)", "Bash(npm install:*)", "Bash(npm run build:*)", "Bash(grep:*)", "Bash(ls:*)", "Bash(find:*)", "<PERSON><PERSON>(docker ps:*)", "Bash(__NEW_LINE__ echo \"\")", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(docker exec:*)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(git clone:*)", "Bash(kill:*)", "Bash(npm run dev:*)", "Bash(PORT=3000 npm run dev)", "Bash(rm:*)", "Bash(export PYTHONPATH=\"\")"], "deny": []}}