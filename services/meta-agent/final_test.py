#!/usr/bin/env python3
"""
Final test for both frontend and backend
"""

import requests

def main():
    print('🧪 Testing Frontend-Backend Connection')
    print('=' * 40)

    # Test backend
    try:
        backend = requests.get('http://localhost:8000/health', timeout=5)
        print(f'✅ Backend: {backend.status_code}')
    except Exception as e:
        print(f'❌ Backend: {e}')
        return

    # Test frontend
    try:
        frontend = requests.get('http://localhost:3000', timeout=10)
        print(f'✅ Frontend: {frontend.status_code}')
    except Exception as e:
        print(f'❌ Frontend: {e}')
        return

    # Test agents API
    try:
        agents = requests.get('http://localhost:8000/api/v1/public/agents', timeout=5)
        data = agents.json()
        total = data.get('total', 0)
        print(f'✅ Agents API: {agents.status_code} - {total} agents')
    except Exception as e:
        print(f'❌ Agents API: {e}')
        return

    print('\n🎯 Both servers are running!')
    print('🌐 URLs:')
    print('   Frontend: http://localhost:3000')
    print('   Backend: http://localhost:8000')
    print('   Agents: http://localhost:3000/agents')
    print('   Create: http://localhost:3000/agents/create')

if __name__ == "__main__":
    main()
