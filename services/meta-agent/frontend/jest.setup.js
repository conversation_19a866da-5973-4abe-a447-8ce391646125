import '@testing-library/jest-dom'

// Global test setup
beforeAll(() => {
  // Set test environment variables
  process.env.VITE_API_URL = 'http://localhost:8000'
  
  // Mock window.fetch only if backend is not available
  // For integration tests, we want real API calls
  if (process.env.NODE_ENV !== 'test-integration') {
    global.fetch = jest.fn()
  }
})

afterEach(() => {
  // Clear all mocks after each test
  jest.clearAllMocks()
})

// Mock React Router
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
  useParams: () => ({}),
  useLocation: () => ({
    pathname: '/',
    search: '',
    hash: '',
    state: null,
  }),
}))

// Mock import.meta.env for Vite
Object.defineProperty(window, 'import', {
  value: {
    meta: {
      env: {
        VITE_API_URL: 'http://localhost:8000',
        MODE: 'test',
        DEV: false,
        PROD: false,
        SSR: false,
      }
    }
  }
})

// Global test utilities
global.testUtils = {
  // Helper to wait for async operations
  waitFor: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Helper to generate test data
  generateTestUser: () => ({
    username: `testuser_${Date.now()}`,
    email: `test_${Date.now()}@example.com`,
    password: 'TestPassword123!',
    full_name: 'Test User'
  }),
  
  generateTestAgent: () => ({
    name: `Test Agent ${Date.now()}`,
    description: 'A test agent for integration testing',
    type: 'assistant',
    config: {
      max_concurrent_tasks: 5,
      timeout_seconds: 300
    },
    capabilities: ['text_analysis', 'data_processing']
  })
}