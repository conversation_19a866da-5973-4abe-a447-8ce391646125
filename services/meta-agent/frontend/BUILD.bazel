load("@rules_oci//oci:defs.bzl", "oci_image")
load("@rules_pkg//pkg:tar.bzl", "pkg_tar")

package(default_visibility = ["//visibility:public"])

# Development server using existing npm scripts
sh_binary(
    name = "dev",
    srcs = ["scripts/run_dev.sh"],
    data = [
        "package.json",
        "package-lock.json",
        "vite.config.ts",
        "tsconfig.json",
        "tailwind.config.js",
        "postcss.config.js",
    ] + glob([
        "src/**/*",
        "public/**/*", 
        "*.html",
    ], allow_empty = True),
)

# Production build using existing npm scripts
sh_binary(
    name = "build",
    srcs = ["scripts/run_build.sh"],
    data = [
        "package.json",
        "package-lock.json", 
        "vite.config.ts",
        "tsconfig.json",
        "tailwind.config.js",
        "postcss.config.js",
    ] + glob([
        "src/**/*",
        "public/**/*", 
        "*.html",
    ], allow_empty = True),
)

# Preview server using existing npm scripts
sh_binary(
    name = "preview",
    srcs = ["scripts/run_preview.sh"],
    data = [":build"],
)

# Type checking
sh_binary(
    name = "type_check",
    srcs = ["scripts/run_type_check.sh"],
    data = [
        "package.json",
        "tsconfig.json",
    ] + glob(["src/**/*.ts", "src/**/*.tsx"], allow_empty = True),
)

# Linting
sh_binary(
    name = "lint",
    srcs = ["scripts/run_lint.sh"],
    data = [
        "package.json",
        "eslint.config.js",
    ] + glob(["src/**/*.ts", "src/**/*.tsx"], allow_empty = True),
)

# Generate API client
sh_binary(
    name = "generate_client",
    srcs = ["scripts/generate-client.sh"],
    data = ["//services/meta-agent/rest-api:generate_typescript_client"],
)

# Build with client generation
sh_binary(
    name = "build_with_client",
    srcs = ["scripts/build-with-client.sh"],
    data = [
        ":generate_client",
        ":build",
        "//services/meta-agent/frontend/api-client:generate_client",
    ],
)

# Install dependencies (for first-time setup)
sh_binary(
    name = "install",
    srcs = ["scripts/run_install.sh"],
    data = [
        "package.json",
        "package-lock.json",
    ],
)

# Unit tests using Jest
sh_binary(
    name = "test",
    srcs = ["scripts/run_tests.sh"],
    data = [
        "package.json",
        "jest.config.js",
        "jest.setup.js",
    ] + glob([
        "src/**/*.test.ts",
        "src/**/*.test.tsx",
        "src/**/*.spec.ts",
        "src/**/*.spec.tsx",
    ], allow_empty = True),
)

# Integration tests using Jest
sh_binary(
    name = "test_integration",
    srcs = ["scripts/run_integration_tests.sh"],
    data = [
        "package.json",
        "jest.config.integration.js",
        "jest.setup.integration.js",
    ] + glob([
        "src/__tests__/integration/**/*",
    ], allow_empty = True),
)

# E2E tests using Playwright
sh_binary(
    name = "test_e2e",
    srcs = ["scripts/run_e2e_tests.sh"],
    data = [
        "package.json",
        "playwright.config.ts",
    ] + glob([
        "tests/e2e/**/*",
    ], allow_empty = True),
)

# Watch mode for tests
sh_binary(
    name = "test_watch",
    srcs = ["scripts/run_test_watch.sh"],
    data = [
        "package.json",
        "jest.config.js",
        "jest.setup.js",
    ] + glob([
        "src/**/*.test.ts",
        "src/**/*.test.tsx",
    ], allow_empty = True),
)

# Test suite for frontend
test_suite(
    name = "frontend_tests",
    tags = ["frontend", "meta-agent"],
    tests = [
        ":test",
        ":test_integration",
        ":type_check",
        ":lint",
    ],
)

# Clean build artifacts
sh_binary(
    name = "clean",
    srcs = ["scripts/run_clean.sh"],
)

# Development with backend connectivity
sh_binary(
    name = "dev_with_backend",
    srcs = ["scripts/run_dev_with_backend.sh"],
    data = [
        ":generate_client",
        "//services/meta-agent/frontend/api-client:generate_client",
    ],
    tags = ["web", "dev", "backend"],
)

# Package frontend files for container deployment
pkg_tar(
    name = "frontend_files",
    srcs = glob(
        ["dist/**/*"],
        allow_empty = True,
    ),
    package_dir = "/usr/share/nginx/html",
)

# Package nginx configuration
pkg_tar(
    name = "nginx_config",
    srcs = ["nginx.conf"],
    package_dir = "/etc/nginx/conf.d",
)

# OCI image for frontend deployment
oci_image(
    name = "frontend_image",
    base = "@nginx_base",
    exposed_ports = ["80"],
    tars = [
        ":frontend_files",
        ":nginx_config",
    ],
    visibility = ["//visibility:public"],
)

# Alternative Docker build using existing Dockerfile
genrule(
    name = "frontend_docker_build",
    srcs = [
        "Dockerfile",
        "nginx.conf",
        ":build",
    ] + glob([
        "src/**/*",
        "public/**/*",
        "*.json",
        "*.js", 
        "*.ts",
        "*.html",
    ], allow_empty = True),
    outs = ["frontend_image.tar"],
    cmd = """
        # Copy all source files to a temporary directory
        mkdir -p $(@D)/frontend_build
        cp -r $(SRCS) $(@D)/frontend_build/
        
        # Build Docker image
        cd $(@D)/frontend_build
        docker build -t meta-agent-frontend:latest .
        docker save meta-agent-frontend:latest > $(@D)/frontend_image.tar
    """,
    visibility = ["//visibility:public"],
)