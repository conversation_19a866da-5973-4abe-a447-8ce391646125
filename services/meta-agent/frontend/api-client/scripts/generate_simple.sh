#!/bin/bash
set -euo pipefail

# Navigate to api-client directory
cd "$(dirname "$0")/.."

echo "🔄 Generating Meta-Agent API Client (Simple Mode)..."

# Check if OpenAPI spec exists
if [ ! -f "../../backend/openapi/openapi.yaml" ]; then
    echo "❌ OpenAPI specification not found at ../../backend/openapi/openapi.yaml"
    echo "   Please ensure backend OpenAPI spec is generated first:"
    echo "   bazel run //services/meta-agent/backend:generate_openapi"
    exit 1
fi

# Check if openapi-generator-cli is available
if ! command -v openapi-generator-cli &> /dev/null; then
    echo "⚠️ openapi-generator-cli not found. Installing globally..."
    npm install -g @openapitools/openapi-generator-cli
fi

# Remove existing generated files
if [ -d "src" ]; then
    echo "🧹 Cleaning existing generated files..."
    rm -rf src
fi

# Generate TypeScript client
echo "🏗️ Generating TypeScript API client..."
openapi-generator-cli generate \
    -i ../../backend/openapi/openapi.yaml \
    -g typescript-fetch \
    -o . \
    --additional-properties=typescriptThreePlus=true,supportsES6=true,npmName=@meta-agent/api-client,npmVersion=1.0.0,stringEnums=true \
    --model-name-mappings Error=ApiError \
    --skip-validate-spec

echo "✅ API client generated successfully!"
echo "📁 Generated files are in src/ directory"