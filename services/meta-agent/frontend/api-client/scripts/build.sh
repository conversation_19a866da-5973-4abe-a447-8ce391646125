#!/bin/bash
set -euo pipefail

# Navigate to api-client directory
cd "$(dirname "$0")/.."

echo "🏗️ Building Meta-Agent API Client..."

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Check if TypeScript source files exist
if [ ! -d "src" ]; then
    echo "⚠️ Source files not found. Generating client first..."
    ./scripts/generate_client_npm.sh
fi

# Remove existing build output
if [ -d "dist" ]; then
    echo "🧹 Cleaning existing build output..."
    rm -rf dist
fi

# Build TypeScript client
echo "🔨 Compiling TypeScript..."
npx tsc

echo "✅ API client built successfully!"
echo "📁 Built files are in dist/ directory"