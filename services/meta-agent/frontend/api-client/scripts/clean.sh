#!/bin/bash
set -euo pipefail

# Navigate to api-client directory
cd "$(dirname "$0")/.."

echo "🧹 Cleaning Meta-Agent API Client Build Artifacts..."

# Remove generated source files
if [ -d "src" ]; then
    echo "🗑️ Removing generated source files..."
    rm -rf src
fi

# Remove build output
if [ -d "dist" ]; then
    echo "🗑️ Removing build output..."
    rm -rf dist
fi

# Remove TypeScript build info
if [ -f "tsconfig.tsbuildinfo" ]; then
    echo "🗑️ Removing TypeScript build info..."
    rm -f tsconfig.tsbuildinfo
fi

# Remove generated config files
generated_files=(
    "package.json"
    "tsconfig.json" 
    "openapi-generator-config.json"
    ".openapi-generator"
    ".openapi-generator-ignore"
    "README.md"
    "git_push.sh"
)

for file in "${generated_files[@]}"; do
    if [ -f "$file" ]; then
        echo "🗑️ Removing generated file: $file"
        rm -f "$file"
    fi
done

echo "✅ Cleanup completed successfully!"
echo "💡 To clean node_modules as well, run: rm -rf node_modules"