#!/bin/bash
set -euo pipefail

# Navigate to api-client directory
cd "$(dirname "$0")/.."

echo "🔄 Generating Meta-Agent API Client using NPM..."

# Check if OpenAPI spec exists
if [ ! -f "../../backend/openapi/openapi.yaml" ]; then
    echo "❌ OpenAPI specification not found at ../../backend/openapi/openapi.yaml"
    echo "   Please ensure backend OpenAPI spec is generated first:"
    echo "   bazel run //services/meta-agent/backend:generate_openapi"
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Remove existing generated files
if [ -d "src" ]; then
    echo "🧹 Cleaning existing generated files..."
    rm -rf src
fi

# Generate TypeScript client
echo "🏗️ Generating TypeScript API client..."
npx openapi-generator-cli generate \
    -i ../../backend/openapi/openapi.yaml \
    -g typescript-fetch \
    -o . \
    -c openapi-generator-config.json

echo "✅ API client generated successfully!"
echo "📁 Generated files are in src/ directory"