#!/bin/bash
set -euo pipefail

# Navigate to api-client directory
cd "$(dirname "$0")/.."

echo "📦 Installing Meta-Agent API Client Dependencies..."

# Clean install
if [ -d "node_modules" ]; then
    echo "🧹 Cleaning existing node_modules..."
    rm -rf node_modules
fi

if [ -f "package-lock.json" ]; then
    echo "🧹 Cleaning package-lock.json..."
    rm -f package-lock.json
fi

# Fresh install
echo "⬇️ Installing dependencies..."
npm install

echo "✅ Dependencies installed successfully!"
echo "📁 node_modules created with all required packages"