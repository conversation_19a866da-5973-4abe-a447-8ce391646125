# AI Agent Platform - Frontend

This is the frontend application for the AI Agent Platform, built with React, TypeScript, Vite, and shadcn/ui.

## Tech Stack

- **React** 18.2.0
- **TypeScript** 5.3.2
- **Vite** 5.4.1
- **React Router** 6.26.2
- **Tailwind CSS** 3.3.6
- **shadcn/ui** - Component library
- **React Query** - Server state management
- **Axios** - API client

## Prerequisites

- Node.js 18+ (recommended: 20+)
- npm 9+ or yarn
- Backend service running on http://localhost:8000

## Installation

1. Install dependencies:
```bash
npm install
```

2. Copy environment variables:
```bash
cp .env.example .env
```

3. Update `.env` with your configuration:
```
VITE_API_URL=http://localhost:8000
```

## Development

### Start the frontend only:
```bash
npm run dev
```

The application will run on http://localhost:3000

### Start frontend and backend together:
```bash
npm run dev:all
```

This will start both the frontend (port 3000) and backend (port 8000) services.

## Build

```bash
npm run build
```

The production build will be in the `dist` directory.

## Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Preview production build
- `npm run lint` - Run ESLint
- `npm run type-check` - Check TypeScript types
- `npm run test` - Run tests (Jest)
- `npm run test:e2e` - Run end-to-end tests (Playwright)

## Project Structure

```
src/
├── components/     # Reusable components
│   ├── ui/        # shadcn/ui components
│   └── Layout/    # Layout components
├── contexts/      # React contexts
├── hooks/         # Custom hooks
├── lib/           # Utilities
├── pages/         # Page components
├── services/      # API services
├── styles/        # Global styles
└── types/         # TypeScript types
```

## API Proxy

The development server is configured to proxy API requests to the backend:
- All requests to `/api/v1/*` are proxied to `http://localhost:8000`

## Key Features

- **Agent Management**: Create, view, edit, and deploy AI agents
- **Dashboard**: Real-time system monitoring and metrics
- **Deployment**: One-click agent deployment with live status
- **AI-Powered Creation**: Generate agent configurations using natural language

## Migration from Next.js

This project was migrated from Next.js to Vite. Key changes include:
- Next.js App Router → React Router v6
- `next/link` → `react-router-dom` Link
- `next/navigation` → `react-router-dom` hooks
- Environment variables: `NEXT_PUBLIC_*` → `VITE_*`