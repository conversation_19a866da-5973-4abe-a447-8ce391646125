import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import type { WorkflowComponent } from '@/types/api';

export interface ComponentCategory {
  name: string;
  display_name: string;
  description: string;
  icon?: string;
  components: WorkflowComponent[];
}

export interface ComponentsState {
  // Components data
  components: WorkflowComponent[];
  categories: ComponentCategory[];
  
  // Loading state
  isLoading: boolean;
  lastUpdated: string | null;
  
  // Error state
  error: string | null;
  
  // Search and filtering
  searchQuery: string;
  selectedCategory: string | null;
  
  // Actions
  fetchComponents: () => Promise<void>;
  setSearchQuery: (query: string) => void;
  setSelectedCategory: (category: string | null) => void;
  clearSearch: () => void;
  
  // Selectors
  getComponentsByCategory: (category: string) => WorkflowComponent[];
  getComponentByType: (type: string) => WorkflowComponent | undefined;
  searchComponents: (query: string) => WorkflowComponent[];
  getFilteredComponents: () => WorkflowComponent[];
}

// Mock component data - replace with actual API calls
const mockComponents: WorkflowComponent[] = [
  {
    type: 'TextInput',
    display_name: 'Text Input',
    description: 'Simple text input component',
    category: 'inputs',
    base_classes: ['str'],
    template: {
      value: {
        type: 'str',
        required: false,
        placeholder: 'Enter text here...',
      },
    },
    output_types: ['str'],
    input_types: [],
    official: true,
  },
  {
    type: 'LLMChain',
    display_name: 'LLM Chain',
    description: 'Chain that calls a language model',
    category: 'chains',
    base_classes: ['Chain'],
    template: {
      prompt: {
        type: 'PromptTemplate',
        required: true,
      },
      llm: {
        type: 'LLM',
        required: true,
      },
    },
    output_types: ['str'],
    input_types: ['PromptTemplate', 'LLM'],
    official: true,
  },
  {
    type: 'OpenAI',
    display_name: 'OpenAI',
    description: 'OpenAI language model',
    category: 'llms',
    base_classes: ['LLM'],
    template: {
      model_name: {
        type: 'str',
        required: true,
        options: ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo'],
      },
      temperature: {
        type: 'float',
        required: false,
        value: 0.7,
      },
    },
    output_types: ['LLM'],
    input_types: [],
    official: true,
  },
  {
    type: 'PromptTemplate',
    display_name: 'Prompt Template',
    description: 'Template for formatting prompts',
    category: 'prompts',
    base_classes: ['PromptTemplate'],
    template: {
      template: {
        type: 'str',
        required: true,
        multiline: true,
        placeholder: 'Enter your prompt template here...',
      },
      input_variables: {
        type: 'list',
        required: true,
      },
    },
    output_types: ['PromptTemplate'],
    input_types: [],
    official: true,
  },
  {
    type: 'VectorStore',
    display_name: 'Vector Store',
    description: 'Store and retrieve vectors',
    category: 'vectorstores',
    base_classes: ['VectorStore'],
    template: {
      texts: {
        type: 'list',
        required: true,
      },
      embeddings: {
        type: 'Embeddings',
        required: true,
      },
    },
    output_types: ['VectorStore'],
    input_types: ['Embeddings'],
    official: true,
  },
];

const mockCategories: ComponentCategory[] = [
  {
    name: 'inputs',
    display_name: 'Inputs',
    description: 'Input components for data entry',
    icon: 'input',
    components: mockComponents.filter(c => c.category === 'inputs'),
  },
  {
    name: 'llms',
    display_name: 'Language Models',
    description: 'Large language model components',
    icon: 'brain',
    components: mockComponents.filter(c => c.category === 'llms'),
  },
  {
    name: 'chains',
    display_name: 'Chains',
    description: 'Chain components for workflows',
    icon: 'link',
    components: mockComponents.filter(c => c.category === 'chains'),
  },
  {
    name: 'prompts',
    display_name: 'Prompts',
    description: 'Prompt template components',
    icon: 'message-square',
    components: mockComponents.filter(c => c.category === 'prompts'),
  },
  {
    name: 'vectorstores',
    display_name: 'Vector Stores',
    description: 'Vector storage and retrieval',
    icon: 'database',
    components: mockComponents.filter(c => c.category === 'vectorstores'),
  },
];

export const useComponentsStore = create<ComponentsState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    components: [],
    categories: [],
    isLoading: false,
    lastUpdated: null,
    error: null,
    searchQuery: '',
    selectedCategory: null,

    // Actions
    fetchComponents: async () => {
      set({ isLoading: true, error: null });
      
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // In real implementation, this would be an API call
        set({
          components: mockComponents,
          categories: mockCategories,
          isLoading: false,
          lastUpdated: new Date().toISOString(),
        });
      } catch (error) {
        set({
          error: error instanceof Error ? error.message : 'Failed to fetch components',
          isLoading: false,
        });
      }
    },

    setSearchQuery: (query) => {
      set({ searchQuery: query });
    },

    setSelectedCategory: (category) => {
      set({ selectedCategory: category });
    },

    clearSearch: () => {
      set({ searchQuery: '', selectedCategory: null });
    },

    // Selectors
    getComponentsByCategory: (category) => {
      return get().components.filter(component => component.category === category);
    },

    getComponentByType: (type) => {
      return get().components.find(component => component.type === type);
    },

    searchComponents: (query) => {
      const { components } = get();
      const lowercaseQuery = query.toLowerCase();
      
      return components.filter(component =>
        component.display_name.toLowerCase().includes(lowercaseQuery) ||
        component.description.toLowerCase().includes(lowercaseQuery) ||
        component.type.toLowerCase().includes(lowercaseQuery)
      );
    },

    getFilteredComponents: () => {
      const { components, searchQuery, selectedCategory } = get();
      
      let filtered = components;
      
      // Filter by category
      if (selectedCategory) {
        filtered = filtered.filter(component => component.category === selectedCategory);
      }
      
      // Filter by search query
      if (searchQuery) {
        const lowercaseQuery = searchQuery.toLowerCase();
        filtered = filtered.filter(component =>
          component.display_name.toLowerCase().includes(lowercaseQuery) ||
          component.description.toLowerCase().includes(lowercaseQuery) ||
          component.type.toLowerCase().includes(lowercaseQuery)
        );
      }
      
      return filtered;
    },
  }))
);

// Selector hooks for convenience
export const useComponents = () => useComponentsStore(state => state.components);
export const useComponentCategories = () => useComponentsStore(state => state.categories);
export const useComponentsLoading = () => useComponentsStore(state => state.isLoading);
export const useFilteredComponents = () => useComponentsStore(state => state.getFilteredComponents());

// Initialize components on store creation
useComponentsStore.getState().fetchComponents();