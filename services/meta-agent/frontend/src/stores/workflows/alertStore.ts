import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

export type AlertType = 'info' | 'success' | 'warning' | 'error';

export interface Alert {
  id: string;
  type: AlertType;
  title: string;
  message?: string;
  duration?: number; // Auto-dismiss after duration (ms), null for persistent
  actions?: Array<{
    label: string;
    action: () => void;
    variant?: 'primary' | 'secondary';
  }>;
  timestamp: number;
  dismissed?: boolean;
}

export interface WorkflowAlertState {
  alerts: Alert[];
  
  // Actions
  addAlert: (alert: Omit<Alert, 'id' | 'timestamp'>) => string;
  removeAlert: (id: string) => void;
  dismissAlert: (id: string) => void;
  clearAllAlerts: () => void;
  clearAlertsByType: (type: AlertType) => void;
  
  // Convenience methods
  success: (title: string, message?: string, duration?: number) => string;
  error: (title: string, message?: string, duration?: number) => string;
  warning: (title: string, message?: string, duration?: number) => string;
  info: (title: string, message?: string, duration?: number) => string;
  
  // Workflow-specific alerts
  workflowSaved: (workflowName: string) => string;
  workflowSaveError: (error: string) => string;
  workflowExecutionStarted: (workflowName: string) => string;
  workflowExecutionCompleted: (workflowName: string) => string;
  workflowExecutionFailed: (workflowName: string, error: string) => string;
  nodeValidationError: (nodeName: string, error: string) => string;
  connectionError: (source: string, target: string, reason: string) => string;
}

const generateId = () => `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

export const useWorkflowAlertStore = create<WorkflowAlertState>()(
  subscribeWithSelector((set, get) => ({
    alerts: [],

    addAlert: (alertData) => {
      const id = generateId();
      const alert: Alert = {
        ...alertData,
        id,
        timestamp: Date.now(),
      };

      set(state => ({
        alerts: [...state.alerts, alert],
      }));

      // Auto-dismiss if duration is specified
      if (alert.duration && alert.duration > 0) {
        setTimeout(() => {
          get().removeAlert(id);
        }, alert.duration);
      }

      return id;
    },

    removeAlert: (id) => {
      set(state => ({
        alerts: state.alerts.filter(alert => alert.id !== id),
      }));
    },

    dismissAlert: (id) => {
      set(state => ({
        alerts: state.alerts.map(alert =>
          alert.id === id ? { ...alert, dismissed: true } : alert
        ),
      }));
    },

    clearAllAlerts: () => {
      set({ alerts: [] });
    },

    clearAlertsByType: (type) => {
      set(state => ({
        alerts: state.alerts.filter(alert => alert.type !== type),
      }));
    },

    // Convenience methods
    success: (title, message, duration = 4000) => {
      return get().addAlert({
        type: 'success',
        title,
        message,
        duration,
      });
    },

    error: (title, message, duration = 0) => { // Errors persist by default
      return get().addAlert({
        type: 'error',
        title,
        message,
        duration,
      });
    },

    warning: (title, message, duration = 6000) => {
      return get().addAlert({
        type: 'warning',
        title,
        message,
        duration,
      });
    },

    info: (title, message, duration = 4000) => {
      return get().addAlert({
        type: 'info',
        title,
        message,
        duration,
      });
    },

    // Workflow-specific alerts
    workflowSaved: (workflowName) => {
      return get().success(
        'Workflow Saved',
        `"${workflowName}" has been saved successfully.`,
        3000
      );
    },

    workflowSaveError: (error) => {
      return get().error(
        'Failed to Save Workflow',
        `Could not save workflow: ${error}`,
        0
      );
    },

    workflowExecutionStarted: (workflowName) => {
      return get().info(
        'Workflow Execution Started',
        `"${workflowName}" is now running.`,
        4000
      );
    },

    workflowExecutionCompleted: (workflowName) => {
      return get().success(
        'Workflow Completed',
        `"${workflowName}" has finished executing successfully.`,
        5000
      );
    },

    workflowExecutionFailed: (workflowName, error) => {
      return get().error(
        'Workflow Execution Failed',
        `"${workflowName}" failed to execute: ${error}`,
        0
      );
    },

    nodeValidationError: (nodeName, error) => {
      return get().warning(
        'Node Validation Error',
        `Node "${nodeName}": ${error}`,
        6000
      );
    },

    connectionError: (source, target, reason) => {
      return get().warning(
        'Connection Error',
        `Cannot connect "${source}" to "${target}": ${reason}`,
        5000
      );
    },
  }))
);

// Selector hooks
export const useWorkflowAlerts = () => useWorkflowAlertStore(state => state.alerts);
export const useActiveAlerts = () => useWorkflowAlertStore(state => 
  state.alerts.filter(alert => !alert.dismissed)
);
export const useAlertsCount = () => useWorkflowAlertStore(state => 
  state.alerts.filter(alert => !alert.dismissed).length
);
export const useErrorAlerts = () => useWorkflowAlertStore(state =>
  state.alerts.filter(alert => alert.type === 'error' && !alert.dismissed)
);