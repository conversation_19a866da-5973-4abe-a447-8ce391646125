import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import type { 
  Workflow, 
  WorkflowTemplate, 
  WorkflowExecution, 
  ListResponse,
  CreateWorkflowRequest,
  UpdateWorkflowRequest,
} from '@/types/api';
import { WorkflowStatus, WorkflowExecutionStatus } from '@/types/api';

export interface WorkflowsManagerState {
  // Workflows data
  workflows: Workflow[];
  templates: WorkflowTemplate[];
  executions: WorkflowExecution[];
  
  // Pagination & filtering
  totalWorkflows: number;
  currentPage: number;
  pageSize: number;
  searchQuery: string;
  selectedCategory: string | null;
  selectedTags: string[];
  
  // Loading states
  isLoadingWorkflows: boolean;
  isLoadingTemplates: boolean;
  isLoadingExecutions: boolean;
  
  // Error states
  error: string | null;
  
  // Actions - Workflows CRUD
  fetchWorkflows: (page?: number, query?: string) => Promise<void>;
  createWorkflow: (data: CreateWorkflowRequest) => Promise<Workflow | null>;
  updateWorkflow: (id: string, data: UpdateWorkflowRequest) => Promise<Workflow | null>;
  deleteWorkflow: (id: string) => Promise<boolean>;
  duplicateWorkflow: (id: string) => Promise<Workflow | null>;
  
  // Actions - Templates
  fetchTemplates: (category?: string) => Promise<void>;
  createFromTemplate: (templateId: string, name: string) => Promise<Workflow | null>;
  
  // Actions - Executions
  fetchExecutions: (workflowId?: string) => Promise<void>;
  executeWorkflow: (workflowId: string, inputData?: Record<string, any>) => Promise<WorkflowExecution | null>;
  cancelExecution: (executionId: string) => Promise<boolean>;
  
  // Actions - Filtering & Search
  setSearchQuery: (query: string) => void;
  setSelectedCategory: (category: string | null) => void;
  setSelectedTags: (tags: string[]) => void;
  clearFilters: () => void;
  
  // Actions - Pagination
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
  
  // Actions - State management
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // Selectors
  getWorkflowById: (id: string) => Workflow | undefined;
  getTemplateById: (id: string) => WorkflowTemplate | undefined;
  getExecutionById: (id: string) => WorkflowExecution | undefined;
  getWorkflowsByCategory: (category: string) => Workflow[];
  getRecentWorkflows: (limit?: number) => Workflow[];
  getWorkflowExecutions: (workflowId: string) => WorkflowExecution[];
}

// Mock API service - replace with actual API calls
const mockApiService = {
  async getWorkflows(page: number = 1, query: string = ''): Promise<ListResponse<Workflow>> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Mock data - replace with actual API call
    const mockWorkflows: Workflow[] = [
      {
        id: '1',
        name: 'Sample Workflow',
        description: 'A sample workflow for demonstration',
        status: WorkflowStatus.DRAFT,
        owner_id: 'user-1',
        data: { nodes: [], edges: [] },
        tags: ['sample', 'demo'],
        is_public: false,
        version: 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ];
    
    return {
      items: mockWorkflows,
      total: mockWorkflows.length,
      limit: 10,
      offset: (page - 1) * 10,
    };
  },

  async createWorkflow(data: CreateWorkflowRequest): Promise<Workflow> {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    return {
      id: `workflow-${Date.now()}`,
      name: data.name,
      description: data.description,
      status: WorkflowStatus.DRAFT,
      owner_id: 'current-user',
      data: data.data || { nodes: [], edges: [] },
      tags: data.tags || [],
      is_public: data.is_public || false,
      category: data.category,
      version: 1,
      template_id: data.template_id,
      agent_id: data.agent_id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
  },

  async updateWorkflow(id: string, data: UpdateWorkflowRequest): Promise<Workflow> {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // Mock response - replace with actual API call
    return {
      id,
      name: data.name || 'Updated Workflow',
      description: data.description,
      status: data.status || WorkflowStatus.DRAFT,
      owner_id: 'current-user',
      data: data.data || { nodes: [], edges: [] },
      tags: data.tags || [],
      is_public: data.is_public || false,
      category: data.category,
      version: 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
  },

  async deleteWorkflow(id: string): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 300));
    // Mock deletion
  },
};

export const useWorkflowsManagerStore = create<WorkflowsManagerState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    workflows: [],
    templates: [],
    executions: [],
    totalWorkflows: 0,
    currentPage: 1,
    pageSize: 10,
    searchQuery: '',
    selectedCategory: null,
    selectedTags: [],
    isLoadingWorkflows: false,
    isLoadingTemplates: false,
    isLoadingExecutions: false,
    error: null,

    // Workflows CRUD
    fetchWorkflows: async (page = 1, query = '') => {
      set({ isLoadingWorkflows: true, error: null });
      try {
        const response = await mockApiService.getWorkflows(page, query);
        set({
          workflows: response.items,
          totalWorkflows: response.total,
          currentPage: page,
          isLoadingWorkflows: false,
        });
      } catch (error) {
        set({
          error: error instanceof Error ? error.message : 'Failed to fetch workflows',
          isLoadingWorkflows: false,
        });
      }
    },

    createWorkflow: async (data) => {
      try {
        const workflow = await mockApiService.createWorkflow(data);
        set(state => ({
          workflows: [workflow, ...state.workflows],
          totalWorkflows: state.totalWorkflows + 1,
        }));
        return workflow;
      } catch (error) {
        set({ error: error instanceof Error ? error.message : 'Failed to create workflow' });
        return null;
      }
    },

    updateWorkflow: async (id, data) => {
      try {
        const workflow = await mockApiService.updateWorkflow(id, data);
        set(state => ({
          workflows: state.workflows.map(w => w.id === id ? workflow : w),
        }));
        return workflow;
      } catch (error) {
        set({ error: error instanceof Error ? error.message : 'Failed to update workflow' });
        return null;
      }
    },

    deleteWorkflow: async (id) => {
      try {
        await mockApiService.deleteWorkflow(id);
        set(state => ({
          workflows: state.workflows.filter(w => w.id !== id),
          totalWorkflows: state.totalWorkflows - 1,
        }));
        return true;
      } catch (error) {
        set({ error: error instanceof Error ? error.message : 'Failed to delete workflow' });
        return false;
      }
    },

    duplicateWorkflow: async (id) => {
      const workflow = get().getWorkflowById(id);
      if (!workflow) {
        set({ error: 'Workflow not found' });
        return null;
      }

      try {
        const duplicated = await mockApiService.createWorkflow({
          name: `${workflow.name} (Copy)`,
          description: workflow.description,
          data: workflow.data,
          tags: workflow.tags,
          is_public: false,
          category: workflow.category,
        });
        
        set(state => ({
          workflows: [duplicated, ...state.workflows],
          totalWorkflows: state.totalWorkflows + 1,
        }));
        
        return duplicated;
      } catch (error) {
        set({ error: error instanceof Error ? error.message : 'Failed to duplicate workflow' });
        return null;
      }
    },

    // Templates (mock implementation)
    fetchTemplates: async (category) => {
      set({ isLoadingTemplates: true, error: null });
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 500));
      set({ templates: [], isLoadingTemplates: false });
    },

    createFromTemplate: async (templateId, name) => {
      const template = get().getTemplateById(templateId);
      if (!template) {
        set({ error: 'Template not found' });
        return null;
      }

      return await get().createWorkflow({
        name,
        description: template.description,
        data: template.data,
        tags: template.tags,
        category: template.category,
        template_id: templateId,
      });
    },

    // Executions (mock implementation)
    fetchExecutions: async (workflowId) => {
      set({ isLoadingExecutions: true, error: null });
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 500));
      set({ executions: [], isLoadingExecutions: false });
    },

    executeWorkflow: async (workflowId, inputData) => {
      // Mock implementation
      const execution: WorkflowExecution = {
        id: `exec-${Date.now()}`,
        workflow_id: workflowId,
        status: WorkflowExecutionStatus.RUNNING,
        input_data: inputData,
        executed_by: 'current-user',
        started_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      
      set(state => ({
        executions: [execution, ...state.executions],
      }));
      
      return execution;
    },

    cancelExecution: async (executionId) => {
      // Mock implementation
      set(state => ({
        executions: state.executions.map(exec =>
          exec.id === executionId ? { ...exec, status: WorkflowExecutionStatus.CANCELLED } : exec
        ),
      }));
      return true;
    },

    // Filtering & Search
    setSearchQuery: (query) => {
      set({ searchQuery: query, currentPage: 1 });
      get().fetchWorkflows(1, query);
    },

    setSelectedCategory: (category) => {
      set({ selectedCategory: category, currentPage: 1 });
      get().fetchWorkflows(1, get().searchQuery);
    },

    setSelectedTags: (tags) => {
      set({ selectedTags: tags, currentPage: 1 });
      get().fetchWorkflows(1, get().searchQuery);
    },

    clearFilters: () => {
      set({ 
        searchQuery: '', 
        selectedCategory: null, 
        selectedTags: [], 
        currentPage: 1 
      });
      get().fetchWorkflows(1, '');
    },

    // Pagination
    setCurrentPage: (page) => {
      set({ currentPage: page });
      get().fetchWorkflows(page, get().searchQuery);
    },

    setPageSize: (size) => {
      set({ pageSize: size, currentPage: 1 });
      get().fetchWorkflows(1, get().searchQuery);
    },

    // State management
    setError: (error) => set({ error }),
    clearError: () => set({ error: null }),

    // Selectors
    getWorkflowById: (id) => {
      return get().workflows.find(w => w.id === id);
    },

    getTemplateById: (id) => {
      return get().templates.find(t => t.id === id);
    },

    getExecutionById: (id) => {
      return get().executions.find(e => e.id === id);
    },

    getWorkflowsByCategory: (category) => {
      return get().workflows.filter(w => w.category === category);
    },

    getRecentWorkflows: (limit = 5) => {
      return get().workflows
        .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
        .slice(0, limit);
    },

    getWorkflowExecutions: (workflowId) => {
      return get().executions.filter(e => e.workflow_id === workflowId);
    },
  }))
);

// Selector hooks for convenience
export const useWorkflows = () => useWorkflowsManagerStore(state => state.workflows);
export const useWorkflowTemplates = () => useWorkflowsManagerStore(state => state.templates);
export const useWorkflowExecutions = () => useWorkflowsManagerStore(state => state.executions);
export const useWorkflowsLoading = () => useWorkflowsManagerStore(state => ({
  workflows: state.isLoadingWorkflows,
  templates: state.isLoadingTemplates,
  executions: state.isLoadingExecutions,
}));