import {
  addEdge,
  applyEdgeChanges,
  applyNode<PERSON>hang<PERSON>,
  type Edge,
  type EdgeChange,
  type Node,
  type NodeChange,
  type ReactFlowInstance,
  type Connection,
  type Viewport,
} from '@xyflow/react';
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import type { Workflow, WorkflowStatus } from '@/types/api';
import { FLOW_CONSTANTS } from '@/constants/workflows';

export interface FlowState {
  // Current workflow data
  currentWorkflow: Workflow | null;
  
  // React Flow state
  nodes: Node[];
  edges: Edge[];
  viewport: Viewport;
  reactFlowInstance: ReactFlowInstance | null;
  
  // UI state
  isLoading: boolean;
  isSaving: boolean;
  hasUnsavedChanges: boolean;
  selectedNodes: string[];
  selectedEdges: string[];
  
  // Validation state
  isValid: boolean;
  validationErrors: string[];
  
  // Actions
  setCurrentWorkflow: (workflow: Workflow | null) => void;
  setNodes: (nodes: Node[] | ((nodes: Node[]) => Node[])) => void;
  setEdges: (edges: Edge[] | ((edges: Edge[]) => Edge[])) => void;
  setViewport: (viewport: Viewport) => void;
  setReactFlowInstance: (instance: ReactFlowInstance | null) => void;
  
  // React Flow actions
  onNodesChange: (changes: NodeChange[]) => void;
  onEdgesChange: (changes: EdgeChange[]) => void;
  onConnect: (connection: Connection) => void;
  
  // Workflow actions
  addNode: (node: Omit<Node, 'id'>) => void;
  removeNode: (nodeId: string) => void;
  updateNode: (nodeId: string, updates: Partial<Node>) => void;
  duplicateNode: (nodeId: string) => void;
  
  // Edge actions
  removeEdge: (edgeId: string) => void;
  
  // Selection actions
  selectNode: (nodeId: string, multi?: boolean) => void;
  selectEdge: (edgeId: string, multi?: boolean) => void;
  clearSelection: () => void;
  
  // Validation
  validateFlow: () => boolean;
  
  // State management
  setLoading: (loading: boolean) => void;
  setSaving: (saving: boolean) => void;
  setHasUnsavedChanges: (hasChanges: boolean) => void;
  
  // Reset
  resetFlow: () => void;
  
  // Utility
  fitView: () => void;
  centerView: () => void;
  getNodeById: (id: string) => Node | undefined;
  getEdgeById: (id: string) => Edge | undefined;
}

const initialState = {
  currentWorkflow: null,
  nodes: [],
  edges: [],
  viewport: FLOW_CONSTANTS.DEFAULT_VIEWPORT,
  reactFlowInstance: null,
  isLoading: false,
  isSaving: false,
  hasUnsavedChanges: false,
  selectedNodes: [],
  selectedEdges: [],
  isValid: true,
  validationErrors: [],
};

export const useFlowStore = create<FlowState>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,

    setCurrentWorkflow: (workflow) => {
      set({ 
        currentWorkflow: workflow,
        nodes: workflow?.data?.nodes || [],
        edges: workflow?.data?.edges || [],
        viewport: workflow?.data?.viewport || FLOW_CONSTANTS.DEFAULT_VIEWPORT,
        hasUnsavedChanges: false,
      });
    },

    setNodes: (nodesOrFunction) => {
      const newNodes = typeof nodesOrFunction === 'function' 
        ? nodesOrFunction(get().nodes) 
        : nodesOrFunction;
      set({ 
        nodes: newNodes,
        hasUnsavedChanges: true,
      });
    },

    setEdges: (edgesOrFunction) => {
      const newEdges = typeof edgesOrFunction === 'function'
        ? edgesOrFunction(get().edges)
        : edgesOrFunction;
      set({ 
        edges: newEdges,
        hasUnsavedChanges: true,
      });
    },

    setViewport: (viewport) => {
      set({ viewport });
    },

    setReactFlowInstance: (instance) => {
      set({ reactFlowInstance: instance });
    },

    onNodesChange: (changes) => {
      set({
        nodes: applyNodeChanges(changes, get().nodes),
        hasUnsavedChanges: true,
      });
      get().validateFlow();
    },

    onEdgesChange: (changes) => {
      set({
        edges: applyEdgeChanges(changes, get().edges),
        hasUnsavedChanges: true,
      });
      get().validateFlow();
    },

    onConnect: (connection) => {
      const newEdge = {
        ...connection,
        id: `${connection.source}-${connection.target}-${Date.now()}`,
        type: 'default',
      } as Edge;
      
      set({
        edges: addEdge(newEdge, get().edges),
        hasUnsavedChanges: true,
      });
      get().validateFlow();
    },

    addNode: (nodeData) => {
      const newNode: Node = {
        ...nodeData,
        id: `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        position: nodeData.position || { x: 100, y: 100 },
      };
      
      set({
        nodes: [...get().nodes, newNode],
        hasUnsavedChanges: true,
      });
    },

    removeNode: (nodeId) => {
      const { nodes, edges } = get();
      set({
        nodes: nodes.filter(node => node.id !== nodeId),
        edges: edges.filter(edge => edge.source !== nodeId && edge.target !== nodeId),
        selectedNodes: get().selectedNodes.filter(id => id !== nodeId),
        hasUnsavedChanges: true,
      });
    },

    updateNode: (nodeId, updates) => {
      set({
        nodes: get().nodes.map(node => 
          node.id === nodeId ? { ...node, ...updates } : node
        ),
        hasUnsavedChanges: true,
      });
    },

    duplicateNode: (nodeId) => {
      const node = get().getNodeById(nodeId);
      if (!node) return;
      
      const newNode: Node = {
        ...node,
        id: `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        position: {
          x: node.position.x + 50,
          y: node.position.y + 50,
        },
        selected: false,
      };
      
      set({
        nodes: [...get().nodes, newNode],
        hasUnsavedChanges: true,
      });
    },

    removeEdge: (edgeId) => {
      set({
        edges: get().edges.filter(edge => edge.id !== edgeId),
        selectedEdges: get().selectedEdges.filter(id => id !== edgeId),
        hasUnsavedChanges: true,
      });
    },

    selectNode: (nodeId, multi = false) => {
      const { selectedNodes } = get();
      if (multi) {
        const newSelection = selectedNodes.includes(nodeId)
          ? selectedNodes.filter(id => id !== nodeId)
          : [...selectedNodes, nodeId];
        set({ selectedNodes: newSelection });
      } else {
        set({ selectedNodes: [nodeId], selectedEdges: [] });
      }
    },

    selectEdge: (edgeId, multi = false) => {
      const { selectedEdges } = get();
      if (multi) {
        const newSelection = selectedEdges.includes(edgeId)
          ? selectedEdges.filter(id => id !== edgeId)
          : [...selectedEdges, edgeId];
        set({ selectedEdges: newSelection });
      } else {
        set({ selectedEdges: [edgeId], selectedNodes: [] });
      }
    },

    clearSelection: () => {
      set({ selectedNodes: [], selectedEdges: [] });
    },

    validateFlow: () => {
      const { nodes, edges } = get();
      const errors: string[] = [];
      
      // Basic validation
      if (nodes.length === 0) {
        errors.push('Flow must contain at least one node');
      }
      
      // Check for disconnected nodes (if we want connected flows)
      const connectedNodeIds = new Set<string>();
      edges.forEach(edge => {
        connectedNodeIds.add(edge.source);
        connectedNodeIds.add(edge.target);
      });
      
      const disconnectedNodes = nodes.filter(node => 
        !connectedNodeIds.has(node.id) && nodes.length > 1
      );
      
      if (disconnectedNodes.length > 0) {
        errors.push(`Found ${disconnectedNodes.length} disconnected node(s)`);
      }
      
      const isValid = errors.length === 0;
      set({ isValid, validationErrors: errors });
      
      return isValid;
    },

    setLoading: (loading) => set({ isLoading: loading }),
    setSaving: (saving) => set({ isSaving: saving }),
    setHasUnsavedChanges: (hasChanges) => set({ hasUnsavedChanges: hasChanges }),

    resetFlow: () => {
      set({
        ...initialState,
        reactFlowInstance: get().reactFlowInstance, // Keep the instance
      });
    },

    fitView: () => {
      get().reactFlowInstance?.fitView();
    },

    centerView: () => {
      get().reactFlowInstance?.setCenter(0, 0);
    },

    getNodeById: (id) => {
      return get().nodes.find(node => node.id === id);
    },

    getEdgeById: (id) => {
      return get().edges.find(edge => edge.id === id);
    },
  }))
);

// Selector helpers
export const useCurrentWorkflow = () => useFlowStore(state => state.currentWorkflow);
export const useFlowNodes = () => useFlowStore(state => state.nodes);
export const useFlowEdges = () => useFlowStore(state => state.edges);
export const useFlowViewport = () => useFlowStore(state => state.viewport);
export const useFlowValidation = () => useFlowStore(state => ({ 
  isValid: state.isValid, 
  errors: state.validationErrors 
}));
export const useFlowUnsavedChanges = () => useFlowStore(state => state.hasUnsavedChanges);