@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 20% 98%;
    --foreground: 215 25% 27%;
    --card: 0 0% 100%;
    --card-foreground: 215 25% 27%;
    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 27%;
    --primary: 231 48% 48%;
    --primary-foreground: 210 20% 98%;
    --secondary: 214 32% 91%;
    --secondary-foreground: 215 25% 27%;
    --muted: 214 32% 91%;
    --muted-foreground: 215 16% 47%;
    --accent: 214 32% 91%;
    --accent-foreground: 215 25% 27%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 20% 98%;
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 231 48% 48%;
    --radius: 0.75rem;
    
    --sidebar-background: 220 23% 95%;
    --sidebar-foreground: 215 25% 27%;
    --sidebar-primary: 231 48% 35%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 214 32% 88%;
    --sidebar-accent-foreground: 215 25% 27%;
    --sidebar-border: 214 32% 88%;
    --sidebar-ring: 231 48% 48%;
  }

  .dark {
    --background: 215 28% 17%;
    --foreground: 210 20% 98%;
    --card: 215 28% 17%;
    --card-foreground: 210 20% 98%;
    --popover: 215 28% 17%;
    --popover-foreground: 210 20% 98%;
    --primary: 231 48% 48%;
    --primary-foreground: 210 20% 98%;
    --secondary: 215 28% 25%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 28% 25%;
    --muted-foreground: 217 11% 65%;
    --accent: 215 28% 25%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 63% 31%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 28% 25%;
    --input: 215 28% 25%;
    --ring: 231 48% 48%;
    
    --sidebar-background: 215 32% 15%;
    --sidebar-foreground: 210 20% 98%;
    --sidebar-primary: 231 48% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 215 28% 25%;
    --sidebar-accent-foreground: 210 20% 98%;
    --sidebar-border: 215 28% 22%;
    --sidebar-ring: 231 48% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-smooth: antialiased;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }
  
  /* Smooth transitions for all interactive elements */
  *, *::before, *::after {
    transition: background-color 200ms ease, border-color 200ms ease, color 200ms ease, 
                box-shadow 200ms ease, transform 200ms ease, opacity 200ms ease;
  }
  
  /* Focus rings with calming colors */
  *:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
    border-radius: 0.375rem;
  }
}

/* Custom animations */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-in-from-top {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-in-from-bottom {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-in-from-left {
  from {
    transform: translateX(-10px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-from-right {
  from {
    transform: translateX(10px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes scale-in {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

.animate-slide-in-from-top {
  animation: slide-in-from-top 0.3s ease-out;
}

.animate-slide-in-from-bottom {
  animation: slide-in-from-bottom 0.3s ease-out;
}

.animate-slide-in-from-left {
  animation: slide-in-from-left 0.3s ease-out;
}

.animate-slide-in-from-right {
  animation: slide-in-from-right 0.3s ease-out;
}

.animate-scale-in {
  animation: scale-in 0.3s ease-out;
}

/* Card hover animations - gentler effect */
.card-hover {
  @apply transition-all duration-500 ease-out;
}

.card-hover:hover {
  @apply transform -translate-y-0.5 shadow-md;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

/* Loading animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* Status colors */
.status-online {
  @apply text-green-600 bg-green-50 border-green-200;
}

.status-offline {
  @apply text-red-600 bg-red-50 border-red-200;
}

.status-pending {
  @apply text-yellow-600 bg-yellow-50 border-yellow-200;
}

.status-running {
  @apply text-blue-600 bg-blue-50 border-blue-200;
}

/* Utility classes */
.text-gradient {
  @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
}

.glass-effect {
  @apply bg-white/20 backdrop-blur-sm border border-white/30;
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

/* Sidebar menu item improvements */
.sidebar-menu-item {
  @apply text-sidebar-foreground transition-all duration-200;
}

.sidebar-menu-item:hover {
  @apply bg-sidebar-accent text-sidebar-foreground;
  background-color: hsl(var(--sidebar-accent));
  color: hsl(var(--sidebar-foreground));
}

.sidebar-menu-item.active {
  @apply font-medium shadow-sm;
  background-color: hsl(var(--sidebar-primary)) !important;
  color: hsl(var(--sidebar-primary-foreground)) !important;
}

.sidebar-menu-item.active * {
  color: hsl(var(--sidebar-primary-foreground)) !important;
}