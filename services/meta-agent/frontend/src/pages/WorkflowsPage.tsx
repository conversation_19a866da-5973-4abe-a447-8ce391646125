/**
 * WorkflowsPage - Main workflows listing and management page
 */

import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { 
  Plus, 
  Search, 
  Filter, 
  Grid3X3, 
  List, 
  Play, 
  Edit, 
  Trash2,
  Copy,
  MoreHorizontal,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useWorkflows } from '@/services/workflows';
import type { Workflow } from '@/types/api';

interface WorkflowCardProps {
  workflow: Workflow;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onDuplicate: (id: string) => void;
  onExecute: (id: string) => void;
}

const WorkflowCard: React.FC<WorkflowCardProps> = ({
  workflow,
  onEdit,
  onDelete,
  onDuplicate,
  onExecute,
}) => {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold truncate">
            {workflow.name}
          </CardTitle>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit(workflow.id)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onExecute(workflow.id)}>
                <Play className="mr-2 h-4 w-4" />
                Execute
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onDuplicate(workflow.id)}>
                <Copy className="mr-2 h-4 w-4" />
                Duplicate
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => onDelete(workflow.id)}
                className="text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        
        {workflow.description && (
          <p className="text-sm text-gray-600 line-clamp-2">
            {workflow.description}
          </p>
        )}
      </CardHeader>
      
      <CardContent>
        <div className="flex items-center justify-between mb-3">
          <Badge variant={workflow.status === 'published' ? 'default' : 'secondary'}>
            {workflow.status}
          </Badge>
          <span className="text-xs text-gray-500">
            v{workflow.version}
          </span>
        </div>
        
        <div className="flex flex-wrap gap-1 mb-3">
          {workflow.tags.slice(0, 3).map((tag) => (
            <Badge key={tag} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {workflow.tags.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{workflow.tags.length - 3}
            </Badge>
          )}
        </div>
        
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>{workflow.data.nodes.length} nodes</span>
          <span>
            {new Date(workflow.updated_at).toLocaleDateString()}
          </span>
        </div>
      </CardContent>
    </Card>
  );
};

export const WorkflowsPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  
  // Use the workflow service hook
  const { data: workflowsResponse, isLoading, error } = useWorkflows({
    search: searchQuery,
  });
  
  const workflows = workflowsResponse?.items || [];

  const handleCreateWorkflow = () => {
    // Navigate to workflow creation page
    window.location.href = '/workflows/editor';
  };

  const handleEditWorkflow = (id: string) => {
    window.location.href = `/workflows/editor/${id}`;
  };

  const handleDeleteWorkflow = (id: string) => {
    if (confirm('Are you sure you want to delete this workflow?')) {
      // TODO: Implement delete functionality
      console.log('Delete workflow:', id);
    }
  };

  const handleDuplicateWorkflow = (id: string) => {
    // TODO: Implement duplicate functionality
    console.log('Duplicate workflow:', id);
  };

  const handleExecuteWorkflow = (id: string) => {
    // TODO: Implement execute functionality
    console.log('Execute workflow:', id);
  };

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Error Loading Workflows</h2>
          <p className="text-gray-600">
            There was an error loading your workflows. Please try again.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Workflows</h1>
          <p className="text-gray-600 mt-2">
            Create and manage your AI workflows
          </p>
        </div>
        
        <Button onClick={handleCreateWorkflow} className="flex items-center space-x-2">
          <Plus className="w-4 h-4" />
          <span>New Workflow</span>
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search workflows..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-80"
            />
          </div>
          
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4 mr-2" />
            Filters
          </Button>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Grid3X3 className="w-4 h-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Content */}
      {isLoading ? (
        <div className="text-center py-12">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <p className="mt-4 text-gray-600">Loading workflows...</p>
        </div>
      ) : workflows.length === 0 ? (
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No workflows found
          </h3>
          <p className="text-gray-600 mb-6">
            {searchQuery 
              ? 'No workflows match your search criteria.' 
              : 'Get started by creating your first workflow.'
            }
          </p>
          {!searchQuery && (
            <Button onClick={handleCreateWorkflow}>
              <Plus className="w-4 h-4 mr-2" />
              Create Your First Workflow
            </Button>
          )}
        </div>
      ) : (
        <div className={
          viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
            : 'space-y-4'
        }>
          {workflows.map((workflow) => (
            <WorkflowCard
              key={workflow.id}
              workflow={workflow}
              onEdit={handleEditWorkflow}
              onDelete={handleDeleteWorkflow}
              onDuplicate={handleDuplicateWorkflow}
              onExecute={handleExecuteWorkflow}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default WorkflowsPage;