import { useParams } from 'react-router-dom'
import { AppLayout } from '@/components/Layout/AppLayout'

export default function TaskDetailPage() {
  const { id } = useParams()

  return (
    <AppLayout>
      <div className="space-y-6">
        <h1 className="text-3xl font-bold">Task Details</h1>
        <p>Task ID: {id}</p>
        <p>Detailed information about the task will be displayed here.</p>
      </div>
    </AppLayout>
  )
}