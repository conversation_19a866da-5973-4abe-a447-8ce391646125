/**
 * AI Agent Platform - Unified Agent Creation Page
 * Combines templates, AI-powered, and form-based agent creation
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { AppLayout } from '@/components/Layout/AppLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Progress } from '@/components/ui/progress';
import {
  ArrowLeft,
  Bot,
  Loader2,
  CheckCircle2,
  AlertCircle,
  Code,
  Settings,
  Zap,
  Plus,
  Sparkles,
  Clock,
  X,
  FileText,
  Brain,
  Wand2,
  Play,
  RefreshCw,
  Eye,
  Download,
  ChevronDown,
  ChevronRight,
  Info
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Link } from 'react-router-dom';
import { useAgentsApi } from '@/lib/api-client/hooks';
import type { AgentCreate, AgentCreateTypeEnum } from '@/api-generated/api';

// Interfaces for unified creation
interface Template {
  name: string;
  description: string;
  agent_type: string;
  language: string;
  capabilities: string[];
  config_schema: any;
  examples: any[];
}

interface GenerationStatus {
  generation_id: string;
  status: string;
  progress: number;
  message: string;
  download_url?: string;
  error?: string;
}

interface GenerationRequest {
  name: string;
  description: string;
  requirements: string;
  type: string;
  framework: string;
  language: string;
  capabilities: string[];
  deployment: {
    environment: { [key: string]: string };
    port: number;
    resources: {
      cpu_limit: string;
      memory_limit: string;
    };
    auto_scale: boolean;
    replicas: number;
  };
  advanced_options: {
    use_ai_workflow: boolean;
    custom_templates: any[];
    integration_endpoints: any[];
    testing_enabled: boolean;
    documentation_level: string;
  };
}

// Agent types and configurations
const agentTypes = [
  { value: 'assistant', label: 'Assistant', description: 'General-purpose conversational agents' },
  { value: 'analyst', label: 'Analyst', description: 'Data analysis and reporting agents' },
  { value: 'processor', label: 'Processor', description: 'Data processing and ETL agents' },
  { value: 'monitor', label: 'Monitor', description: 'System monitoring and alerting agents' },
  { value: 'specialist', label: 'Specialist', description: 'Domain-specific expert agents' },
  { value: 'coordinator', label: 'Coordinator', description: 'Multi-agent coordination agents' }
];

const languages = [
  { value: 'python', label: 'Python', description: 'Versatile and powerful' },
  { value: 'typescript', label: 'TypeScript', description: 'Type-safe JavaScript for web' },
  { value: 'javascript', label: 'JavaScript', description: 'Web-native scripting' }
];

const mockCapabilities = [
  { name: 'natural_language', description: 'Natural language processing and understanding' },
  { name: 'data_analysis', description: 'Data analysis and statistical processing' },
  { name: 'web_scraping', description: 'Web scraping and content extraction' },
  { name: 'api_integration', description: 'REST API integration and management' },
  { name: 'database_access', description: 'Database connectivity and operations' },
  { name: 'file_processing', description: 'File reading, writing, and processing' },
  { name: 'scheduling', description: 'Task scheduling and automation' },
  { name: 'monitoring', description: 'System and application monitoring' },
  { name: 'notifications', description: 'Email, SMS, and push notifications' },
  { name: 'machine_learning', description: 'ML model inference and training' }
];

const mockTemplates = [
  {
    name: 'Data Processor',
    description: 'Process and transform data from various sources',
    agent_type: 'processor',
    language: 'python',
    capabilities: ['data_analysis', 'file_processing', 'database_access'],
    config_schema: {},
    examples: []
  },
  {
    name: 'API Monitor',
    description: 'Monitor API endpoints for health and performance',
    agent_type: 'monitor',
    language: 'typescript',
    capabilities: ['api_integration', 'monitoring', 'notifications'],
    config_schema: {},
    examples: []
  },
  {
    name: 'Content Assistant',
    description: 'Generate and manage content using AI',
    agent_type: 'assistant',
    language: 'python',
    capabilities: ['natural_language', 'web_scraping', 'file_processing'],
    config_schema: {},
    examples: []
  }
];

export default function CreateAgentPage() {
  const navigate = useNavigate();
  const agentsApi = useAgentsApi();

  // State management
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [success, setSuccess] = useState(false);
  const [aiParsing, setAiParsing] = useState(false);
  const [aiError, setAiError] = useState<string | null>(null);
  const [aiProviders, setAiProviders] = useState<string[]>([]);

  // Template and generation state
  const [templates, setTemplates] = useState<Template[]>(mockTemplates);
  const [availableCapabilities, setAvailableCapabilities] = useState<any[]>(mockCapabilities);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationStatus, setGenerationStatus] = useState<GenerationStatus | null>(null);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [showStatus, setShowStatus] = useState(false);
  const [activeTab, setActiveTab] = useState('templates');

  // Validation state
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [validationWarnings, setValidationWarnings] = useState<string[]>([]);

  // Animation effect
  useEffect(() => {
    setIsVisible(true);
  }, []);

  // Form data state
  const [formData, setFormData] = useState<GenerationRequest>({
    name: '',
    description: '',
    requirements: '',
    type: 'api_service',
    framework: 'fastapi',
    language: 'python',
    capabilities: [],
    deployment: {
      environment: { NODE_ENV: 'development' },
      port: 8000,
      resources: {
        cpu_limit: '500m',
        memory_limit: '512Mi'
      },
      auto_scale: false,
      replicas: 1
    },
    advanced_options: {
      use_ai_workflow: false,
      custom_templates: [],
      integration_endpoints: [],
      testing_enabled: true,
      documentation_level: 'standard'
    }
  });

  // AI-powered creation state
  const [aiPrompt, setAiPrompt] = useState('');
  const [aiGeneratedConfig, setAiGeneratedConfig] = useState<any>(null);

  // Simple toast replacement
  const toast = (opts: any) => {
    alert(`${opts.title}: ${opts.description}`);
  };

  // Load template into form
  const loadTemplate = (template: Template) => {
    setFormData(prev => ({
      ...prev,
      name: template.name.toLowerCase().replace(/\s+/g, '-'),
      description: template.description,
      type: template.agent_type as any,
      language: template.language as any,
      capabilities: template.capabilities
    }));
    setActiveTab('form');
    toast({ title: 'Template Loaded', description: `${template.name} template loaded successfully` });
  };

  // AI-powered generation (simplified for demo)
  const generateWithAI = async () => {
    if (!aiPrompt.trim()) {
      setAiError('Please provide a description of what you want to create');
      return;
    }

    setAiParsing(true);
    setAiError(null);

    try {
      // Simple AI-like parsing based on keywords (fallback for demo)
      const prompt = aiPrompt.toLowerCase();

      // Basic keyword detection for agent type
      let agentType = 'assistant';
      if (prompt.includes('monitor') || prompt.includes('watch') || prompt.includes('alert')) {
        agentType = 'monitor';
      } else if (prompt.includes('analyze') || prompt.includes('data') || prompt.includes('report')) {
        agentType = 'analyst';
      } else if (prompt.includes('process') || prompt.includes('transform') || prompt.includes('etl')) {
        agentType = 'processor';
      } else if (prompt.includes('coordinate') || prompt.includes('manage') || prompt.includes('orchestrate')) {
        agentType = 'coordinator';
      }

      // Basic capability detection
      const capabilities = [];
      if (prompt.includes('database') || prompt.includes('sql') || prompt.includes('data')) {
        capabilities.push('database_access');
      }
      if (prompt.includes('api') || prompt.includes('rest') || prompt.includes('http')) {
        capabilities.push('api_integration');
      }
      if (prompt.includes('file') || prompt.includes('csv') || prompt.includes('document')) {
        capabilities.push('file_processing');
      }
      if (prompt.includes('email') || prompt.includes('notification') || prompt.includes('alert')) {
        capabilities.push('notifications');
      }
      if (prompt.includes('web') || prompt.includes('scrape') || prompt.includes('crawl')) {
        capabilities.push('web_scraping');
      }
      if (prompt.includes('schedule') || prompt.includes('cron') || prompt.includes('timer')) {
        capabilities.push('scheduling');
      }
      if (capabilities.length === 0) {
        capabilities.push('natural_language', 'api_integration');
      }

      // Generate a simple name from the prompt
      const name = aiPrompt
        .toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '-')
        .substring(0, 30)
        .replace(/^-+|-+$/g, '') || 'ai-agent';

      const parsedRequirements = {
        name,
        type: agentType,
        language: 'python',
        framework: 'fastapi',
        capabilities,
        reasoning: `Based on your description, I've configured this as a ${agentType} agent with ${capabilities.join(', ')} capabilities.`,
        confidence: 85
      };

      // Map AI response to form data
      const aiConfig = {
        name: parsedRequirements.name,
        description: parsedRequirements.reasoning || aiPrompt,
        requirements: aiPrompt,
        type: parsedRequirements.type,
        language: parsedRequirements.language,
        framework: parsedRequirements.framework,
        capabilities: parsedRequirements.capabilities,
        // Map deployment settings
        deployment: {
          ...(formData.deployment || {}),
          environment: { NODE_ENV: 'development' },
          auto_scale: false,
          replicas: 1
        },
        // Map advanced options
        advanced_options: {
          ...(formData.advanced_options || {})
        }
      };

      setAiGeneratedConfig({
        ...parsedRequirements,
        confidence: parsedRequirements.confidence,
        reasoning: parsedRequirements.reasoning
      });

      setFormData(prev => ({
        ...prev,
        ...aiConfig,
        type: aiConfig.type || prev.type,
        language: aiConfig.language || prev.language,
        framework: aiConfig.framework || prev.framework
      }));
      setActiveTab('form');
      toast({
        title: 'AI Generation Complete',
        description: `Agent configuration generated with ${parsedRequirements.confidence}% confidence`
      });
    } catch (error) {
      console.error('AI generation failed:', error);
      setAiError('Failed to generate agent configuration. Please try again or check your description.');
    } finally {
      setAiParsing(false);
    }
  };

  // Validation
  const validateForm = () => {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!formData.name.trim()) errors.push('Agent name is required');
    if (!formData.description.trim()) errors.push('Description is required');
    if (!formData.requirements.trim()) warnings.push('Requirements help generate better agents');
    if (!formData.capabilities || formData.capabilities.length === 0) warnings.push('Consider adding capabilities for better functionality');

    setValidationErrors(errors);
    setValidationWarnings(warnings);

    return errors.length === 0;
  };

  // Submit form
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    setError(null);

    try {
      // Prepare agent data for creation (simplified format for backend)
      const agentData: AgentCreate = {
        name: formData.name,
        description: formData.description,
        type: formData.type as any,
        capabilities: formData.capabilities || [],
        config: {
          requirements: formData.requirements,
          language: formData.language,
          framework: formData.framework,
          deployment: formData.deployment,
          advanced_options: formData.advanced_options
        }
      };

      console.log('Creating agent with data:', agentData);
      const response = await agentsApi.createAgentApiV1AgentsPost(agentData);
      console.log('Agent created successfully:', response);

      setSuccess(true);
      toast({
        title: 'Success',
        description: `Agent "${formData.name}" created successfully! Redirecting to agents list...`
      });

      // Add a small delay to show success message, then redirect
      setTimeout(() => {
        navigate('/agents');
      }, 1500);
    } catch (err: any) {
      console.error('Failed to create agent:', err);
      const errorMessage = err.response?.data?.error || err.message || 'Failed to create agent';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage
      });
    } finally {
      setLoading(false);
    }
  };

  // Toggle capability
  const toggleCapability = (capability: string) => {
    setFormData(prev => ({
      ...prev,
      capabilities: (prev.capabilities || []).includes(capability)
        ? (prev.capabilities || []).filter(c => c !== capability)
        : [...(prev.capabilities || []), capability]
    }));
  };

  // Load AI providers on mount
  useEffect(() => {
    const loadProviders = async () => {
      try {
        // Mock AI providers until backend endpoints are implemented
        const mockProviders = ['OpenAI', 'Anthropic', 'Google'];
        setAiProviders(mockProviders);
      } catch (error) {
        console.error('Failed to load AI providers:', error);
      }
    };

    loadProviders();
  }, []);

  return (
    <AppLayout>
      <div className={cn(
        "container mx-auto py-8 px-4 max-w-6xl transition-all duration-1000 ease-out",
        isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
      )}>
        {/* Enhanced Header with Gradient Background */}
        <div className="relative overflow-hidden bg-gradient-to-r from-emerald-600 via-teal-600 to-cyan-600 rounded-2xl p-8 text-white shadow-2xl mb-8">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div className="space-y-3">
                <div className="flex items-center space-x-4">
                  <Link to="/agents">
                    <Button variant="secondary" size="sm" className="bg-white/20 hover:bg-white/30 text-white border-white/30 backdrop-blur-sm">
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      Back to Agents
                    </Button>
                  </Link>
                </div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-white to-emerald-100 bg-clip-text text-transparent">
                  🚀 Create AI Agent
                </h1>
                <p className="text-emerald-100 text-lg">Build intelligent agents with templates, AI assistance, or custom configuration</p>
                <div className="flex items-center gap-4 mt-4">
                  <div className="flex items-center gap-2 bg-white/20 rounded-full px-4 py-2 backdrop-blur-sm">
                    <Sparkles className="w-4 h-4 text-yellow-300" />
                    <span className="text-sm font-medium">AI-Powered Creation</span>
                  </div>
                  <div className="flex items-center gap-2 bg-white/20 rounded-full px-4 py-2 backdrop-blur-sm">
                    <Zap className="w-4 h-4 text-blue-300" />
                    <span className="text-sm">Instant Deployment</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Animated Background Elements */}
          <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
            <div className="absolute -top-4 -right-4 w-32 h-32 bg-white/5 rounded-full animate-pulse"></div>
            <div className="absolute top-1/2 left-8 w-20 h-20 bg-white/10 rounded-full animate-bounce delay-500"></div>
            <div className="absolute bottom-4 right-1/3 w-16 h-16 bg-white/5 rounded-full animate-pulse delay-1000"></div>
          </div>
        </div>

        {/* Success/Error Messages */}
        {success && (
          <Alert className="mb-6 border-green-200 bg-green-50">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              Agent created successfully! Redirecting to agents list...
            </AlertDescription>
          </Alert>
        )}

        {error && (
          <Alert className="mb-6 border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="templates" className="flex items-center space-x-2">
              <FileText className="h-4 w-4" />
              <span>Templates</span>
            </TabsTrigger>
            <TabsTrigger value="ai" className="flex items-center space-x-2">
              <Brain className="h-4 w-4" />
              <span>AI-Powered</span>
            </TabsTrigger>
            <TabsTrigger value="form" className="flex items-center space-x-2">
              <Settings className="h-4 w-4" />
              <span>Form-Based</span>
            </TabsTrigger>
          </TabsList>

          {/* Templates Tab */}
          <TabsContent value="templates" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="h-5 w-5" />
                  <span>Agent Templates</span>
                </CardTitle>
                <CardDescription>
                  Start with pre-built templates for common agent types
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {templates.map((template, index) => (
                    <Card key={index} className="border-2 border-dashed hover:border-primary/50 transition-colors">
                      <CardContent className="pt-6">
                        <div className="space-y-3">
                          <h4 className="font-semibold">{template.name}</h4>
                          <p className="text-sm text-muted-foreground line-clamp-2">
                            {template.description}
                          </p>
                          <div className="flex gap-2 flex-wrap">
                            <Badge variant="secondary">
                              {template.agent_type}
                            </Badge>
                            <Badge variant="outline">
                              {template.language}
                            </Badge>
                          </div>
                          <div className="space-y-2">
                            <p className="text-xs font-medium text-muted-foreground">Capabilities:</p>
                            <div className="flex gap-1 flex-wrap">
                              {template.capabilities.slice(0, 3).map((cap, capIndex) => (
                                <Badge key={capIndex} variant="outline" className="text-xs">
                                  {cap.replace('_', ' ')}
                                </Badge>
                              ))}
                              {template.capabilities.length > 3 && (
                                <Badge variant="outline" className="text-xs">
                                  +{template.capabilities.length - 3} more
                                </Badge>
                              )}
                            </div>
                          </div>
                          <Button
                            size="sm"
                            onClick={() => loadTemplate(template)}
                            className="w-full"
                          >
                            <Play className="h-4 w-4 mr-2" />
                            Use Template
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* AI-Powered Tab */}
          <TabsContent value="ai" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Brain className="h-5 w-5" />
                  <span>AI-Powered Generation</span>
                </CardTitle>
                <CardDescription>
                  Describe what you want to create and let AI generate the agent configuration
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="ai-prompt">Describe your agent</Label>
                  <Textarea
                    id="ai-prompt"
                    placeholder="I want to create an agent that monitors my website uptime and sends notifications when it goes down..."
                    value={aiPrompt}
                    onChange={(e) => setAiPrompt(e.target.value)}
                    rows={4}
                    className="resize-none"
                  />
                  <div className="text-sm text-muted-foreground">
                    <p className="mb-2">💡 <strong>Example prompts:</strong></p>
                    <ul className="space-y-1 text-xs">
                      <li>• "Create a customer support agent that can answer questions about our products and escalate complex issues"</li>
                      <li>• "Build an agent that monitors social media mentions and analyzes sentiment"</li>
                      <li>• "I need an agent that processes CSV files and generates reports with charts"</li>
                      <li>• "Create an agent that integrates with Slack and manages team notifications"</li>
                    </ul>
                  </div>
                </div>

                {aiError && (
                  <Alert className="border-red-200 bg-red-50">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    <AlertDescription className="text-red-800">
                      {aiError}
                    </AlertDescription>
                  </Alert>
                )}

                {aiGeneratedConfig && (
                  <div className="space-y-4">
                    <Alert className="border-green-200 bg-green-50">
                      <CheckCircle2 className="h-4 w-4 text-green-600" />
                      <AlertDescription className="text-green-800">
                        AI has generated a configuration for your agent with {aiGeneratedConfig.confidence}% confidence.
                      </AlertDescription>
                    </Alert>

                    <Card className="bg-gray-50">
                      <CardHeader>
                        <CardTitle className="text-lg">Generated Configuration</CardTitle>
                        <CardDescription>
                          Review the AI-generated settings below
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label className="text-sm font-medium">Agent Name</Label>
                            <p className="text-sm text-gray-600">{aiGeneratedConfig.name}</p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium">Type</Label>
                            <p className="text-sm text-gray-600 capitalize">{aiGeneratedConfig.type}</p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium">Language</Label>
                            <p className="text-sm text-gray-600">{aiGeneratedConfig.language}</p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium">Framework</Label>
                            <p className="text-sm text-gray-600">{aiGeneratedConfig.framework}</p>
                          </div>
                        </div>

                        {aiGeneratedConfig.capabilities && aiGeneratedConfig.capabilities.length > 0 && (
                          <div>
                            <Label className="text-sm font-medium">Capabilities</Label>
                            <div className="flex flex-wrap gap-2 mt-1">
                              {aiGeneratedConfig.capabilities.map((capability: string, index: number) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {capability}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}

                        {aiGeneratedConfig.reasoning && (
                          <div>
                            <Label className="text-sm font-medium">AI Reasoning</Label>
                            <p className="text-sm text-gray-600 mt-1">{aiGeneratedConfig.reasoning}</p>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                )}

                <div className="flex gap-2">
                  <Button
                    onClick={generateWithAI}
                    disabled={aiParsing || !aiPrompt.trim()}
                    className="flex-1"
                  >
                    {aiParsing ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Wand2 className="h-4 w-4 mr-2" />
                        Generate with AI
                      </>
                    )}
                  </Button>

                  {aiGeneratedConfig && (
                    <Button
                      variant="outline"
                      onClick={() => setActiveTab('form')}
                    >
                      <Settings className="h-4 w-4 mr-2" />
                      Review & Edit
                    </Button>
                  )}
                </div>

                {aiProviders.length > 0 && (
                  <div className="pt-4 border-t">
                    <p className="text-sm text-muted-foreground mb-2">
                      Available AI providers: {aiProviders.join(', ')}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Form-Based Tab */}
          <TabsContent value="form" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Left Column - Basic Configuration */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Basic Configuration</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="agent-name">Agent Name *</Label>
                      <Input
                        id="agent-name"
                        placeholder="my-awesome-agent"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description">Description *</Label>
                      <Textarea
                        id="description"
                        placeholder="Describe what your agent does..."
                        value={formData.description}
                        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                        rows={3}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="requirements">Requirements</Label>
                      <Textarea
                        id="requirements"
                        placeholder="Detailed requirements for your agent..."
                        value={formData.requirements}
                        onChange={(e) => setFormData(prev => ({ ...prev, requirements: e.target.value }))}
                        rows={4}
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Agent Type & Language</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>Agent Type</Label>
                      <div className="grid grid-cols-2 gap-2">
                        {agentTypes.map((type) => (
                          <Button
                            key={type.value}
                            variant={formData.type === type.value ? "default" : "outline"}
                            size="sm"
                            onClick={() => setFormData(prev => ({ ...prev, type: type.value }))}
                            className="justify-start text-left"
                          >
                            {type.label}
                          </Button>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Programming Language</Label>
                      <div className="grid grid-cols-1 gap-2">
                        {languages.map((lang) => (
                          <Button
                            key={lang.value}
                            variant={formData.language === lang.value ? "default" : "outline"}
                            size="sm"
                            onClick={() => setFormData(prev => ({ ...prev, language: lang.value }))}
                            className="justify-start"
                          >
                            <div className="text-left">
                              <div className="font-medium">{lang.label}</div>
                              <div className="text-xs text-muted-foreground">{lang.description}</div>
                            </div>
                          </Button>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Right Column - Capabilities */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Capabilities</CardTitle>
                    <CardDescription>
                      Select the capabilities your agent needs
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {mockCapabilities.map((capability) => (
                        <div
                          key={capability.name}
                          className={cn(
                            "p-3 border rounded-lg cursor-pointer transition-colors",
                            (formData.capabilities || []).includes(capability.name)
                              ? "border-primary bg-primary/5"
                              : "border-border hover:bg-muted/50"
                          )}
                          onClick={() => toggleCapability(capability.name)}
                        >
                          <div className="flex items-start space-x-3">
                            <div className={cn(
                              "w-4 h-4 rounded border-2 mt-0.5 transition-colors",
                              (formData.capabilities || []).includes(capability.name)
                                ? "bg-primary border-primary"
                                : "border-muted-foreground"
                            )}>
                              {(formData.capabilities || []).includes(capability.name) && (
                                <CheckCircle2 className="w-3 h-3 text-white" />
                              )}
                            </div>
                            <div className="flex-1">
                              <h4 className="font-medium text-sm">
                                {capability.name.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                              </h4>
                              <p className="text-xs text-muted-foreground">
                                {capability.description}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Validation Messages */}
            {(validationErrors.length > 0 || validationWarnings.length > 0) && (
              <div className="space-y-4">
                {validationErrors.length > 0 && (
                  <Alert className="border-red-200 bg-red-50">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    <AlertDescription className="text-red-800">
                      <ul className="list-disc list-inside space-y-1">
                        {validationErrors.map((error, index) => (
                          <li key={index}>{error}</li>
                        ))}
                      </ul>
                    </AlertDescription>
                  </Alert>
                )}

                {validationWarnings.length > 0 && (
                  <Alert className="border-yellow-200 bg-yellow-50">
                    <Info className="h-4 w-4 text-yellow-600" />
                    <AlertDescription className="text-yellow-800">
                      <ul className="list-disc list-inside space-y-1">
                        {validationWarnings.map((warning, index) => (
                          <li key={index}>{warning}</li>
                        ))}
                      </ul>
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            )}

            {/* Actions */}
            <div className="flex items-center justify-between pt-6 border-t">
              <Button
                variant="outline"
                onClick={() => setShowPreview(!showPreview)}
              >
                <Eye className="h-4 w-4 mr-2" />
                {showPreview ? 'Hide' : 'Preview'} Configuration
              </Button>

              <div className="flex items-center space-x-4">
                <Button
                  variant="outline"
                  disabled={loading}
                  onClick={() => navigate('/agents')}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSubmit}
                  disabled={loading || !formData.name || !formData.description}
                >
                  {loading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Agent
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Configuration Preview */}
            {showPreview && (
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>Configuration Preview</CardTitle>
                  <CardDescription>
                    Review your agent configuration before creating
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <pre className="text-xs bg-muted p-4 rounded-lg overflow-x-auto">
                    {JSON.stringify({
                      name: formData.name,
                      description: formData.description,
                      type: formData.type,
                      language: formData.language,
                      capabilities: formData.capabilities,
                      requirements: formData.requirements
                    }, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  )
}