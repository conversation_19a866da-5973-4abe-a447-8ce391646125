import { useParams } from 'react-router-dom'
import { AppLayout } from '@/components/Layout/AppLayout'

export default function DeployedAgentPage() {
  const { agentId } = useParams()

  return (
    <AppLayout>
      <div className="space-y-6">
        <h1 className="text-3xl font-bold">Deployed Agent</h1>
        <p>Agent ID: {agentId}</p>
        <p>Monitor and manage your deployed agent here.</p>
      </div>
    </AppLayout>
  )
}