/**
 * AI Agent Platform - Agents List Page
 * Comprehensive agent management with full CRUD operations
 */

import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { AppLayout } from '@/components/Layout/AppLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Plus,
  Search,
  RefreshCw,
  MoreVertical,
  Play,
  Square,
  Edit,
  Trash2,
  Eye,
  AlertCircle,
  Bot,
  Activity,
  Cpu,
  MemoryStick,
  Clock,
  ExternalLink,
  Globe,
  Container,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAgentsApi, useDeploymentApi } from '@/lib/api-client/hooks';
import type { AgentResponse, AgentStatus } from '@/api-generated/api';

type Agent = AgentResponse;

export default function AgentsPage() {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(true);
  const agentsApi = useAgentsApi();
  const deploymentApi = useDeploymentApi();
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  
  // Simple toast replacement
  const toast = (opts: any) => {
    alert(`${opts.title}: ${opts.description}`);
  };

  useEffect(() => {
    loadAgents();
  }, [statusFilter]);

  const loadAgents = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch real agents from API using agent service
      const response = await agentsApi.listAgentsApiV1AgentsGet(statusFilter === 'all' ? undefined : statusFilter as any, 50);
      // Handle actual API response format
      setAgents(response.data.agents || []);
    } catch (err) {
      console.error('Failed to load agents:', err);
      setError('Failed to load agents');
    } finally {
      setLoading(false);
    }
  };

  const handleStartAgent = async (agent: Agent) => {
    try {
      await agentsApi.startAgentApiV1AgentsAgentIdStartPost(agent.id);
      
      toast({
        title: 'Agent Started',
        description: `Agent "${agent.name}" has been started successfully.`,
      });
      await loadAgents();
    } catch (err) {
      toast({
        title: 'Failed to Start Agent',
        description: 'There was an error starting the agent.',
        variant: 'destructive',
      });
    }
  };

  const handleStopAgent = async (agent: Agent) => {
    try {
      await agentsApi.stopAgentApiV1AgentsAgentIdStopPost(agent.id);
      
      toast({
        title: 'Agent Stopped',
        description: `Agent "${agent.name}" has been stopped successfully.`,
      });
      await loadAgents();
    } catch (err) {
      toast({
        title: 'Failed to Stop Agent',
        description: 'There was an error stopping the agent.',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteAgent = async (agent: Agent) => {
    if (window.confirm(`Are you sure you want to delete "${agent.name}"? This action cannot be undone.`)) {
      try {
        await agentsApi.deleteAgentApiV1AgentsAgentIdDelete(agent.id);
        
        toast({
          title: 'Agent Deleted',
          description: `Agent "${agent.name}" has been deleted successfully.`,
        });
        await loadAgents();
      } catch (err) {
        toast({
          title: 'Failed to Delete Agent',
          description: 'There was an error deleting the agent.',
          variant: 'destructive',
        });
      }
    }
  };

  const filteredAgents = agents.filter(agent =>
    agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    agent.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    agent.type?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusVariant = (status: Agent['status']) => {
    switch (status) {
      case 'running':
        return 'default';
      case 'paused':
        return 'secondary';
      case 'stopped':
        return 'outline';
      case 'error':
        return 'destructive';
      case 'created':
        return 'secondary';
      case 'starting':
        return 'secondary';
      case 'terminated':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const getStatusColor = (status: Agent['status']) => {
    switch (status) {
      case 'running':
        return 'bg-green-500';
      case 'paused':
        return 'bg-yellow-500';
      case 'stopped':
        return 'bg-gray-400';
      case 'error':
        return 'bg-red-500';
      case 'created':
        return 'bg-purple-500';
      case 'starting':
        return 'bg-orange-500';
      case 'terminated':
        return 'bg-gray-600';
      default:
        return 'bg-gray-400';
    }
  };

  const formatRelativeTime = (date: string) => {
    const now = new Date();
    const then = new Date(date);
    const diff = now.getTime() - then.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  if (loading) {
    return (
      <AppLayout>
        <div className="space-y-6">
          <div className="h-8 bg-muted rounded w-1/4 animate-pulse"></div>
          <div className="h-16 bg-muted rounded animate-pulse"></div>
          <div className="h-96 bg-muted rounded animate-pulse"></div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Agents</h1>
            <p className="text-muted-foreground">
              Manage your AI agents and monitor their status
            </p>
          </div>
          <Button asChild>
            <Link to="/agents/create">
              <Plus className="h-4 w-4 mr-2" />
              Create Agent
            </Link>
          </Button>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-4">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search agents..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9"
                />
              </div>

              <select 
                value={statusFilter} 
                onChange={(e) => setStatusFilter(e.target.value)}
                className="flex h-10 w-48 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="all">All Status</option>
                <option value="running">Running</option>
                <option value="stopped">Stopped</option>
                <option value="paused">Paused</option>
                <option value="error">Error</option>
                <option value="ready">Ready</option>
                <option value="created">Created</option>
                <option value="building">Building</option>
              </select>

              <Button
                variant="outline"
                onClick={loadAgents}
                disabled={loading}
              >
                <RefreshCw className={cn("h-4 w-4 mr-2", loading && "animate-spin")} />
                Refresh
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Error Alert */}
        {error && (
          <div className="flex items-center space-x-2 rounded-lg border border-destructive bg-destructive/10 p-4">
            <AlertCircle className="h-4 w-4 text-destructive" />
            <div className="flex-1 text-sm text-destructive">{error}</div>
            <Button variant="outline" size="sm" onClick={loadAgents}>
              Retry
            </Button>
          </div>
        )}

        {/* Stats Summary */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Agents</CardTitle>
              <Bot className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{filteredAgents.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Running</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {filteredAgents.filter(a => a.status === 'running').length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Stopped</CardTitle>
              <Square className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-500">
                {filteredAgents.filter(a => a.status === 'stopped').length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">With Errors</CardTitle>
              <AlertCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {filteredAgents.filter(a => a.status === 'error').length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Agents Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bot className="h-5 w-5" />
              Agent Management
            </CardTitle>
            <CardDescription>
              Monitor and control your AI agents
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="border-b">
                  <tr className="border-b">
                    <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Agent</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Type</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Deployment</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Resources</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Performance</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Last Activity</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider w-12">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-border">
                  {filteredAgents.length === 0 ? (
                    <tr>
                      <td colSpan={8} className="text-center py-8">
                        <div className="flex flex-col items-center space-y-2">
                          <Bot className="h-8 w-8 text-muted-foreground" />
                          <p className="text-muted-foreground">
                            {searchTerm || statusFilter !== 'all' ? 'No agents match your filters' : 'No agents created yet'}
                          </p>
                          {!searchTerm && statusFilter === 'all' && (
                            <Button variant="outline" size="sm" asChild>
                              <Link to="/agents/create">
                                <Plus className="h-4 w-4 mr-2" />
                                Create your first agent
                              </Link>
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ) : (
                    filteredAgents.map((agent) => (
                      <tr key={agent.id} className="hover:bg-muted/50">
                        <td className="px-6 py-4">
                          <div className="space-y-1">
                            <div className="font-medium">{agent.name}</div>
                            {agent.description && (
                              <div className="text-sm text-muted-foreground line-clamp-1">
                                {agent.description}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <Badge variant="outline">
                            {agent.type?.replace('_', ' ') || 'Unknown'}
                          </Badge>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center gap-2">
                            <div className={cn("h-2 w-2 rounded-full", getStatusColor(agent.status))} />
                            <Badge variant={getStatusVariant(agent.status) as any}>
                              {agent.status}
                            </Badge>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <DeploymentStatus agent={agent} />
                        </td>
                        <td className="px-6 py-4">
                          <div className="space-y-1">
                            <div className="flex items-center gap-1 text-xs">
                              <Cpu className="h-3 w-3" />
                              <span>N/A</span>
                            </div>
                            <div className="flex items-center gap-1 text-xs">
                              <MemoryStick className="h-3 w-3" />
                              <span>N/A</span>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm">
                            {(agent as any).framework || 'N/A'}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            {agent.updated_at ? formatRelativeTime(agent.updated_at) : 'Never'}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center gap-1">
                            <Button variant="ghost" size="sm" asChild>
                              <Link to={`/agents/${agent.id}`}>
                                <Eye className="h-4 w-4" />
                              </Link>
                            </Button>
                            {(agent.status === 'stopped' || agent.status === 'created' || agent.status === 'paused') && (
                              <Button variant="ghost" size="sm" onClick={() => handleStartAgent(agent)}>
                                <Play className="h-4 w-4" />
                              </Button>
                            )}
                            {agent.status === 'running' && (
                              <Button variant="ghost" size="sm" onClick={() => handleStopAgent(agent)}>
                                <Square className="h-4 w-4" />
                              </Button>
                            )}
                            <Button variant="ghost" size="sm" onClick={() => handleDeleteAgent(agent)}>
                              <Trash2 className="h-4 w-4 text-destructive" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}

// Deployment Status Component
function DeploymentStatus({ agent }: { agent: Agent }) {
  const [deploymentInfo, setDeploymentInfo] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showRequirements, setShowRequirements] = useState(false);
  const [requirements, setRequirements] = useState('');
  const agentsApi = useAgentsApi();
  const deploymentApi = useDeploymentApi();

  // Simple toast replacement
  const toast = (opts: any) => {
    alert(`${opts.title}: ${opts.description}`);
  };

  const fetchDeploymentStatus = async () => {
    setLoading(true);
    setError(null);
    try {
      // Deployment API not yet implemented in backend
      const deployment = null; // await deploymentApi.getDeployment(agent.id);
      setDeploymentInfo(deployment);
    } catch (err: any) {
      // 404 is expected when agent is not deployed
      if (err.status !== 404) {
        const errorMessage = err.message || err.response?.data?.detail || 'Failed to fetch deployment status';
        setError(errorMessage);
      }
      setDeploymentInfo(null);
    } finally {
      setLoading(false);
    }
  };

  const updateAgentRequirements = async () => {
    if (!requirements.trim()) {
      setError('Requirements cannot be empty');
      return;
    }
    
    setLoading(true);
    setError(null);
    try {
      await agentsApi.updateAgentApiV1AgentsAgentIdPut(agent.id, {
        config: {
          ...(agent as any).config,
          requirements: requirements.trim()
        }
      });
      setShowRequirements(false);
      setRequirements('');
      // Now try to deploy
      await deployAgent();
    } catch (err: any) {
      const errorMessage = err.message || err.response?.data?.detail || 'Unknown error';
      setError(errorMessage);
      console.error('Update requirements error:', err);
    } finally {
      setLoading(false);
    }
  };

  const deployAgent = async () => {
    setLoading(true);
    setError(null);
    try {
      console.log(`🚀 Starting deployment for agent ${agent.id}...`);
      const response = await agentsApi.deployAgentEndpointApiV1AgentsAgentIdDeployPost(agent.id);
      const deployment = response.data;
      console.log('✅ Deploy success:', deployment);

      toast({
        title: 'Deployment Started',
        description: `Agent "${agent.name}" deployment has been initiated successfully.`,
      });

      // Clear any existing errors since deployment was successful
      setError(null);

      // If the deploy response contains deployment info, use it immediately
      if (deployment?.deployment_url || deployment?.frontend_url) {
        setDeploymentInfo({
          deployed: true,
          status: 'running',
          url: deployment.frontend_url || deployment.deployment_url,
          backend_url: deployment.backend_url,
          frontend_url: deployment.frontend_url,
          health_status: 'healthy'
        });
      }

      // Poll deployment status for up to 30 seconds
      let pollCount = 0;
      const maxPolls = 15; // 30 seconds with 2-second intervals

      const pollDeploymentStatus = async () => {
        if (pollCount >= maxPolls) return;

        pollCount++;
        try {
          // Deployment API not yet implemented in backend
          const deployment = null; // await deploymentApi.getDeployment(agent.id);
          setDeploymentInfo(deployment);
          setError(null); // Clear any errors on successful fetch
        } catch (err: any) {
          // During polling, only show errors if it's not a 404 and we've tried multiple times
          if (err.status !== 404 && pollCount > 3) {
            const errorMessage = err.message || err.response?.data?.detail || 'Failed to fetch deployment status';
            setError(`Deployment status check failed: ${errorMessage}`);
          }
        }

        // Continue polling if deployment is still in progress
        if (pollCount < maxPolls) {
          setTimeout(pollDeploymentStatus, 2000);
        }
      };

      // Start polling after a brief delay
      setTimeout(pollDeploymentStatus, 3000);

    } catch (err: any) {
      let errorMessage = 'Failed to deploy agent';

      // Handle different error formats
      if (err.message) {
        errorMessage = err.message;
      } else if (err.response?.data?.detail) {
        errorMessage = err.response.data.detail;
      } else if (err.detail) {
        errorMessage = err.detail;
      } else if (typeof err === 'string') {
        errorMessage = err;
      }

      // Handle specific error cases
      if (err.status === 500) {
        errorMessage = `Server error during deployment: ${errorMessage}`;
      } else if (err.status === 404) {
        errorMessage = 'Agent not found or deployment endpoint unavailable';
      } else if (err.status === 400) {
        errorMessage = `Deployment failed: ${errorMessage}`;
      }

      setError(errorMessage);
      console.error('❌ Deploy error:', err);

      toast({
        title: 'Deployment Failed',
        description: errorMessage,
        variant: 'destructive',
      });

      // If the error is about missing requirements, show the requirements input
      if (errorMessage.toLowerCase().includes('requirements')) {
        setShowRequirements(true);
      }
    } finally {
      setLoading(false);
    }
  };

  const stopDeployment = async () => {
    setLoading(true);
    setError(null);
    try {
      // Deployment API not yet implemented in backend
      const result = null; // await deploymentApi.stopDeployment(agent.id);
      console.log('Stop deployment success:', result);
      setDeploymentInfo(null);

      toast({
        title: 'Deployment Stopped',
        description: `Agent "${agent.name}" deployment has been stopped successfully.`,
      });

    } catch (err: any) {
      let errorMessage = 'Failed to stop deployment';

      if (err.message) {
        errorMessage = err.message;
      } else if (err.response?.data?.detail) {
        errorMessage = err.response.data.detail;
      } else if (err.detail) {
        errorMessage = err.detail;
      }

      setError(errorMessage);
      console.error('Stop deployment error:', err);

      toast({
        title: 'Failed to Stop Deployment',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch deployment status on mount
  React.useEffect(() => {
    fetchDeploymentStatus();
  }, [agent.id]);

  if (loading) {
    return (
      <div className="flex items-center gap-2">
        <div className="h-2 w-2 rounded-full bg-gray-300 animate-pulse" />
        <span className="text-xs text-muted-foreground">Loading...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col gap-1">
        <div className="flex items-center gap-2">
          <AlertCircle className="h-3 w-3 text-red-500" />
          <span className="text-xs text-red-600">Error</span>
        </div>
        <div className="text-xs text-red-600 max-w-48 truncate" title={error}>
          {error}
        </div>
        
        {showRequirements ? (
          <div className="flex flex-col gap-2 mt-2">
            <textarea
              value={requirements}
              onChange={(e) => setRequirements(e.target.value)}
              placeholder="Enter agent requirements (e.g., 'Create a weather API that...')"
              className="text-xs border rounded p-2 min-h-[60px] w-64 resize-none"
              disabled={loading}
            />
            <div className="flex gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={updateAgentRequirements}
                disabled={loading || !requirements.trim()}
                className="h-6 px-2 text-xs"
              >
                <Zap className="h-3 w-3 mr-1" />
                Add & Deploy
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowRequirements(false)}
                className="h-6 px-2 text-xs"
              >
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <Button
            variant="ghost"
            size="sm"
            onClick={deployAgent}
            className="h-6 px-2 text-xs self-start"
          >
            <Container className="h-3 w-3 mr-1" />
            Retry
          </Button>
        )}
      </div>
    );
  }

  if (!deploymentInfo || !deploymentInfo.deployed) {
    return (
      <div className="flex items-center gap-2">
        <div className="h-2 w-2 rounded-full bg-gray-400" />
        <span className="text-xs text-muted-foreground">Not deployed</span>
        <Button
          variant="ghost"
          size="sm"
          onClick={deployAgent}
          className="h-6 px-2 text-xs"
        >
          <Container className="h-3 w-3 mr-1" />
          Deploy
        </Button>
      </div>
    );
  }

  const isHealthy = deploymentInfo.health_status === 'healthy';
  const isRunning = deploymentInfo.status === 'running';

  return (
    <div className="space-y-1">
      <div className="flex items-center gap-2">
        <div className={cn(
          "h-2 w-2 rounded-full",
          isHealthy && isRunning ? "bg-green-500" :
          isRunning ? "bg-yellow-500" : "bg-red-500"
        )} />
        <Badge variant="outline" className="text-xs">
          {deploymentInfo.status}
        </Badge>
      </div>

      <div className="flex items-center gap-1">
        {deploymentInfo.url && (
          <Button
            variant="ghost"
            size="sm"
            asChild
            className="h-6 px-2 text-xs"
          >
            <Link to={`/deployed/${agent.id}`}>
              <ExternalLink className="h-3 w-3 mr-1" />
              Open
            </Link>
          </Button>
        )}

        <Button
          variant="ghost"
          size="sm"
          onClick={stopDeployment}
          className="h-6 px-2 text-xs text-red-600 hover:text-red-700"
        >
          <Square className="h-3 w-3 mr-1" />
          Stop
        </Button>
      </div>

      {deploymentInfo.port && (
        <div className="text-xs text-muted-foreground">
          Port: {deploymentInfo.port}
        </div>
      )}
    </div>
  );
}