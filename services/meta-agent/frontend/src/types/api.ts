/**
 * AI Agent Platform - API Types
 */

// Base types
export interface BaseResponse {
  id: string;
  created_at: string;
  updated_at: string;
}

// User types
export interface User {
  id: string;
  username: string;
  email: string;
  full_name?: string;
  bio?: string;
  is_active: boolean;
  is_superuser: boolean;
  created_at: string;
  last_login?: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  full_name?: string;
}

export interface TokenResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
}

// Agent types
export enum AgentStatus {
  CREATED = 'created',
  STARTING = 'starting',
  RUNNING = 'running',
  PAUSED = 'paused',
  STOPPED = 'stopped',
  ERROR = 'error',
  TERMINATED = 'terminated'
}

export interface Agent extends BaseResponse {
  name: string;
  description?: string;
  type: string;
  status: AgentStatus;
  version: string;
  owner_id: string;
  config: Record<string, any>;
  capabilities: string[];
  constraints: Record<string, any>;
  last_heartbeat?: string;
  cpu_usage?: number;
  memory_usage?: number;
}

export interface CreateAgentRequest {
  name: string;
  description?: string;
  type?: string;
  config?: Record<string, any>;
  capabilities?: string[];
}

export interface UpdateAgentRequest {
  name?: string;
  description?: string;
  config?: Record<string, any>;
  capabilities?: string[];
  constraints?: Record<string, any>;
  requirements?: string;
  user_prompt?: string;
}

// Task types
export enum TaskStatus {
  PENDING = 'pending',
  ASSIGNED = 'assigned',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export interface Task extends BaseResponse {
  title: string;
  description?: string;
  type: string;
  status: TaskStatus;
  priority: number;
  input_data?: Record<string, any>;
  output_data?: Record<string, any>;
  metadata: Record<string, any>;
  progress_percentage: number;
  steps_completed: number;
  total_steps?: number;
  assigned_agent_id?: string;
  orchestration_id?: string;
  parent_task_id?: string;
  started_at?: string;
  completed_at?: string;
  deadline?: string;
}

// Orchestration types
export enum OrchestrationPattern {
  SEQUENTIAL = 'sequential',
  PARALLEL = 'parallel',
  HIERARCHICAL = 'hierarchical',
  PEER_TO_PEER = 'peer_to_peer',
  EVENT_DRIVEN = 'event_driven'
}

export interface Orchestration extends BaseResponse {
  name: string;
  description?: string;
  pattern: OrchestrationPattern;
  status: string;
  progress_percentage: number;
  owner_id: string;
  config: Record<string, any>;
  execution_plan: Record<string, any>;
  started_at?: string;
  completed_at?: string;
  members: OrchestrationMember[];
  agent_ids: string[];
}

export interface OrchestrationMember {
  agent_id: string;
  role: string;
  join_order: number;
}

export interface CreateOrchestrationRequest {
  name: string;
  description?: string;
  pattern: OrchestrationPattern;
  config?: Record<string, any>;
  execution_plan?: Record<string, any>;
  agent_ids: string[];
}

// Runtime types
export interface RuntimeInfo {
  agent_id: string;
  status: string;
  process_id?: number;
  start_time?: string;
  last_heartbeat?: string;
  cpu_usage: number;
  memory_usage: number;
  current_task?: Record<string, any>;
  queue_size: number;
  task_history_count: number;
  capabilities: string[];
  constraints: Record<string, any>;
}

export interface SystemStats {
  total_agents: number;
  active_agents: number;
  max_concurrent_agents: number;
  status_breakdown: Record<string, number>;
  cpu_usage: number;
  memory_usage: number;
  uptime: string;
  resource_usage: {
    total_cpu_usage: number;
    total_memory_mb: number;
    average_cpu_per_agent: number;
    average_memory_per_agent: number;
  };
  task_statistics: {
    queued_tasks: number;
    completed_tasks: number;
  };
}

// API Response types
export interface ListResponse<T> {
  items: T[];
  total: number;
  limit: number;
  offset: number;
}

export interface SuccessResponse {
  success: boolean;
  message: string;
}

export interface ErrorResponse {
  error: string;
  detail?: string;
  code?: string;
}

// Deployed Agent types
export interface DeployedAgentInterface {
  agent: Agent;
  deployment: {
    id: string;
    agent_id: string;
    status: string;
    url: string;
    port: number;
    health_status: string;
    deployed_at: string;
    last_health_check: string;
  };
  interface: {
    title: string;
    description: string;
    capabilities: string[];
    status: string;
    health: string;
    deployed_at: string;
    endpoints: Array<{
      path: string;
      method: string;
      description: string;
    }>;
  };
}

export interface ChatMessage {
  message: string;
}

export interface ChatResponse {
  response: string;
  agent_id: string;
  agent_name: string;
  timestamp: string;
  status: string;
}

// Workflow types following API-first approach
export enum WorkflowStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived'
}

export enum WorkflowExecutionStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export interface Workflow extends BaseResponse {
  name: string;
  description?: string;
  status: WorkflowStatus;
  owner_id: string;
  data: {
    nodes: any[];
    edges: any[];
    viewport?: {
      x: number;
      y: number;
      zoom: number;
    };
  };
  tags: string[];
  is_public: boolean;
  category?: string;
  version: number;
  template_id?: string;
  agent_id?: string; // Link to associated agent
}

export interface CreateWorkflowRequest {
  name: string;
  description?: string;
  data?: Workflow['data'];
  tags?: string[];
  is_public?: boolean;
  category?: string;
  template_id?: string;
  agent_id?: string;
}

export interface UpdateWorkflowRequest {
  name?: string;
  description?: string;
  data?: Workflow['data'];
  tags?: string[];
  is_public?: boolean;
  category?: string;
  status?: WorkflowStatus;
}

export interface WorkflowExecution extends BaseResponse {
  workflow_id: string;
  status: WorkflowExecutionStatus;
  input_data?: Record<string, any>;
  output_data?: Record<string, any>;
  error_message?: string;
  started_at?: string;
  completed_at?: string;
  duration_ms?: number;
  executed_by: string; // user_id
  agent_id?: string;
}

export interface WorkflowTemplate extends BaseResponse {
  name: string;
  description: string;
  category: string;
  tags: string[];
  data: Workflow['data'];
  is_public: boolean;
  author_id: string;
  usage_count: number;
  rating?: number;
  difficulty_level: 'beginner' | 'intermediate' | 'advanced';
  preview_image?: string;
}

export interface WorkflowComponent {
  type: string;
  display_name: string;
  description: string;
  icon?: string;
  category: string;
  base_classes: string[];
  template: Record<string, any>;
  output_types?: string[];
  input_types?: string[];
  documentation?: string;
  beta?: boolean;
  official: boolean;
}

export interface ExecuteWorkflowRequest {
  workflow_id: string;
  input_data?: Record<string, any>;
  agent_id?: string;
}

// Agent-Workflow Integration types
export interface AgentWorkflow {
  agent_id: string;
  workflow_id: string;
  is_primary: boolean; // Is this the main workflow for the agent
  auto_execute: boolean; // Should this workflow auto-execute when agent starts
  trigger_conditions?: Record<string, any>;
}