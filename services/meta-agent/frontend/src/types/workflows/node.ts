import { Node as ReactFlowNode, NodeProps } from '@xyflow/react';

export interface NodeField {
  name: string;
  display_name: string;
  field_type: string;
  required: boolean;
  placeholder?: string;
  multiline?: boolean;
  password?: boolean;
  options?: string[];
  value?: any;
  list?: boolean;
  show?: boolean;
  advanced?: boolean;
  info?: string;
  file_types?: string[];
  suffixes?: string[];
  input_types?: string[];
  dynamic?: boolean;
}

export interface NodeTemplate {
  type: string;
  display_name: string;
  description: string;
  icon?: string;
  beta?: boolean;
  documentation?: string;
  base_classes: string[];
  template: Record<string, NodeField>;
  custom_fields?: Record<string, any>;
  output_types?: string[];
  input_types?: string[];
  flow?: string;
  error?: string;
  frozen?: boolean;
}

export interface NodeData extends Record<string, unknown> {
  id: string;
  type: string;
  node?: NodeTemplate;
  template: Record<string, NodeField>;
  description: string;
  base_classes?: string[];
  display_name?: string;
  custom_fields?: Record<string, any>;
  output_types?: string[];
  input_types?: string[];
  frozen?: boolean;
  selected?: boolean;
  showNode?: boolean;
  edited?: boolean;
}

export interface VertexBuildType {
  id: string;
  display_name: string;
  description: string;
  built: boolean;
  artifacts?: {
    type: string;
    stream?: boolean;
    raw?: any;
    repr?: string;
  };
  build_params?: Record<string, any>;
  data?: NodeData;
  timestamp?: string;
  valid?: boolean;
  params?: Record<string, any>;
  results?: Record<string, any>;
}

export interface CustomNodeProps extends NodeProps {
  data: NodeData;
  selected: boolean;
}

export type NodeValidationStatus = 'valid' | 'invalid' | 'warning' | 'building' | 'built';