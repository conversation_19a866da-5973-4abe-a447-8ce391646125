import { FlowData } from './flow';

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  flow_data: FlowData['data'];
  is_public: boolean;
  created_at: string;
  updated_at: string;
  author?: {
    id: string;
    name: string;
    avatar?: string;
  };
  preview_image?: string;
  usage_count?: number;
  rating?: number;
  difficulty_level?: 'beginner' | 'intermediate' | 'advanced';
}

export interface TemplateCategory {
  id: string;
  name: string;
  description: string;
  icon?: string;
  template_count: number;
}

export interface TemplateFilter {
  category?: string;
  tags?: string[];
  difficulty_level?: string;
  author?: string;
  search?: string;
}