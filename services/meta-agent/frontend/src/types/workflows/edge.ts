import { Edge as ReactFlowEdge } from '@xyflow/react';

export interface EdgeData {
  targetHandle?: {
    fieldName: string;
    inputTypes?: string[];
    type: string;
    id: string;
  };
  sourceHandle?: {
    fieldName: string;
    outputTypes?: string[];
    type: string;
    id: string;
  };
  length?: number;
  showNode?: boolean;
}

export interface CustomEdgeProps {
  id: string;
  source: string;
  target: string;
  sourceX: number;
  sourceY: number;
  targetX: number;
  targetY: number;
  sourcePosition: string;
  targetPosition: string;
  data?: EdgeData;
  markerEnd?: string;
  style?: Record<string, any>;
  selected?: boolean;
}

export type EdgeType = 'default' | 'custom' | 'smoothstep' | 'step' | 'straight';

export interface ConnectionType {
  source: string;
  sourceHandle: string;
  target: string;
  targetHandle: string;
}