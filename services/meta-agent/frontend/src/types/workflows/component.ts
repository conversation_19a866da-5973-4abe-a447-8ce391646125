export interface ComponentType {
  base_classes: string[];
  display_name: string;
  description: string;
  icon?: string;
  beta?: boolean;
  documentation?: string;
  custom_fields?: Record<string, any>;
  output_types?: string[];
  input_types?: string[];
  template: Record<string, any>;
  type: string;
  flow?: string;
  error?: string;
  frozen?: boolean;
  field_order?: string[];
  official?: boolean;
}

export interface ComponentCategory {
  name: string;
  display_name: string;
  description?: string;
  icon?: string;
  components: ComponentType[];
}

export interface ComponentLibrary {
  categories: ComponentCategory[];
  version: string;
  last_updated: string;
}