import { Node, Edge } from '@xyflow/react';

export interface FlowData {
  id: string;
  name: string;
  description?: string;
  data: {
    nodes: Node[];
    edges: Edge[];
    viewport?: {
      x: number;
      y: number;
      zoom: number;
    };
  };
  created_at?: string;
  updated_at?: string;
  is_component?: boolean;
  user_id?: string;
  folder_id?: string;
  endpoint_name?: string;
  tags?: string[];
}

export interface FlowMetadata {
  id: string;
  name: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
  user_id?: string;
  folder_id?: string;
  tags?: string[];
  is_public?: boolean;
  is_component?: boolean;
}

export interface FlowVersion {
  id: string;
  flow_id: string;
  version_number: number;
  data: FlowData['data'];
  created_at: string;
  is_current: boolean;
}

export interface FlowExecution {
  id: string;
  flow_id: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  inputs?: Record<string, any>;
  outputs?: Record<string, any>;
  started_at: string;
  completed_at?: string;
  error?: string;
}

export interface FlowTemplate {
  id: string;
  name: string;
  description: string;
  flow_data: FlowData['data'];
  category: string;
  tags: string[];
  is_public: boolean;
  created_at: string;
  author?: string;
  preview_image?: string;
}

export type FlowStatus = 'draft' | 'published' | 'archived';