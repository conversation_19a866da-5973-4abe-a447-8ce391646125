/**
 * React hooks for using the Meta-Agent API client
 */

import { useMemo } from 'react';
import { 
  AgentsApi, 
  TasksApi, 
  AuthenticationApi,
  Default<PERSON>pi,
  RuntimeApi,
  Deployment<PERSON>pi
} from '@/api-generated/api';
import { Configuration } from '@/api-generated/configuration';

export function useApiConfiguration() {
  const configuration = useMemo(() => {
    const basePath = (import.meta as any).env?.VITE_API_URL || 'http://localhost:8000';
    // TODO: Add authentication token when auth is implemented
    const accessToken = undefined;
    
    return new Configuration({
      basePath: basePath,
      accessToken: accessToken,
    });
  }, []);

  return configuration;
}

// Hook for agents
export function useAgentsApi() {
  const configuration = useApiConfiguration();
  
  return useMemo(() => new AgentsApi(configuration), [configuration]);
}

// Hook for tasks
export function useTasksApi() {
  const configuration = useApiConfiguration();
  
  return useMemo(() => new TasksApi(configuration), [configuration]);
}

// Hook for system/runtime API
export function useSystemApi() {
  const configuration = useApiConfiguration();
  
  return useMemo(() => new RuntimeApi(configuration), [configuration]);
}

// Hook for authentication
export function useAuthApi() {
  const configuration = useApiConfiguration();
  
  return useMemo(() => new AuthenticationApi(configuration), [configuration]);
}

// Hook for deployment
export function useDeploymentApi() {
  const configuration = useApiConfiguration();
  
  return useMemo(() => new DeploymentApi(configuration), [configuration]);
}