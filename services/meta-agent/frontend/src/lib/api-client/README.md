# Meta-Agent API Client

This directory contains the OpenAPI-based API client for the Meta-Agent system, following the same patterns as the Orbit project for consistency and best practices.

## Architecture

The API client follows a layered architecture inspired by the Orbit project:

```
src/lib/api-client/
├── index.ts           # Main exports and re-exports
├── client.ts          # Core API client wrapper
├── hooks.ts           # React hooks for API operations
└── README.md          # This file

src/lib/api/
├── index.ts           # Compatibility layer with old API
├── types.ts           # Type definitions
└── provider.tsx       # React Query provider
```

## Key Features

### 1. Consistent Response Wrapper
All API methods return a consistent `ApiResponse<T>` format:

```typescript
interface ApiResponse<T> {
  data: T;
  status: number;
  statusText: string;
}
```

### 2. Service-Specific Hooks
Each major service area has its own hook for better organization:

```typescript
// Agents
const agentsApi = useAgentsApi();
const agents = await agentsApi.getAgents();

// Tasks
const tasksApi = useTasksApi();
const tasks = await tasksApi.getTasks();

// Migration
const migrationApi = useMigrationApi();
const analysis = await migrationApi.analyzeApplication(data);
```

### 3. Type Safety
All methods are fully typed using the existing type definitions from `@/types/api`, with plans to integrate OpenAPI-generated types when available.

### 4. Error Handling
Built-in error handling with consistent error responses:

```typescript
interface ApiError {
  code: string;
  message: string;
  details?: any;
}
```

## Usage Examples

### Basic Usage

```typescript
import { useAgentsApi } from '@/lib/api';

function AgentsPage() {
  const agentsApi = useAgentsApi();
  
  const loadAgents = async () => {
    try {
      const response = await agentsApi.getAgents();
      console.log('Agents:', response.data);
    } catch (error) {
      console.error('Failed to load agents:', error);
    }
  };
  
  return (
    <div>
      <button onClick={loadAgents}>Load Agents</button>
    </div>
  );
}
```

### Creating Resources

```typescript
import { useAgentsApi } from '@/lib/api';

function CreateAgent() {
  const agentsApi = useAgentsApi();
  
  const createAgent = async (agentData) => {
    try {
      const response = await agentsApi.createAgent(agentData);
      console.log('Created agent:', response.data);
    } catch (error) {
      console.error('Failed to create agent:', error);
    }
  };
  
  return (
    <form onSubmit={(e) => {
      e.preventDefault();
      createAgent({
        name: 'My Agent',
        description: 'Agent description',
        type: 'assistant'
      });
    }}>
      {/* form fields */}
    </form>
  );
}
```

### Error Handling

```typescript
import { useAgentsApi } from '@/lib/api';

function AgentsWithErrorHandling() {
  const agentsApi = useAgentsApi();
  
  const handleApiCall = async () => {
    try {
      const response = await agentsApi.getAgents();
      // Handle success
    } catch (error) {
      if (error.response?.status === 401) {
        // Handle authentication error
      } else if (error.response?.status === 404) {
        // Handle not found
      } else {
        // Handle other errors
      }
    }
  };
}
```

## Available Hooks

### Core Services
- `useAgentsApi()` - Agent management operations
- `useTasksApi()` - Task management operations  
- `useSystemApi()` - System stats and health
- `useGeneratorApi()` - Agent generation operations

### Advanced Services
- `useMigrationApi()` - Application migration operations
- `useWorkflowsApi()` - Workflow management
- `useIntelligenceApi()` - AI model operations
- `useConnectionsApi()` - Integration management
- `useProjectsApi()` - Project management

## Migration from Old API

### Before (Old API)
```typescript
import { agentService } from '@/services/agent.service';

const agents = await agentService.listAgents();
```

### After (New API)
```typescript
import { useAgentsApi } from '@/lib/api';

const agentsApi = useAgentsApi();
const response = await agentsApi.getAgents();
const agents = response.data;
```

## OpenAPI Integration (Future)

This structure is designed to seamlessly integrate with OpenAPI-generated clients when available:

1. Replace the manual type definitions with generated types
2. Replace the manual API methods with generated API classes
3. Keep the same wrapper structure and hooks for consistency

Example future integration:
```typescript
// From generated API
import { AgentsApi, Agent } from './generated/apis';
import { Configuration } from './generated/runtime';

// In client.ts
export class ApiClient {
  private agentsApi: AgentsApi;
  
  constructor(baseUrl: string, token?: string) {
    const config = new Configuration({
      basePath: baseUrl,
      accessToken: token,
    });
    
    this.agentsApi = new AgentsApi(config);
  }
  
  async getAgents(): Promise<ApiResponse<Agent[]>> {
    const response = await this.agentsApi.getAgents();
    return this.wrapResponse(response);
  }
}
```

## Best Practices

1. **Always use hooks**: Don't create ApiClient instances directly in components
2. **Handle errors gracefully**: Always wrap API calls in try-catch blocks
3. **Use TypeScript**: Leverage the full type safety provided
4. **Consistent naming**: Follow the established naming patterns
5. **Single responsibility**: Each hook focuses on one service area

## Future Enhancements

1. **OpenAPI Generation**: Integrate with OpenAPI spec for automatic client generation
2. **Caching**: Add intelligent caching strategies
3. **Retry Logic**: Implement automatic retry for failed requests
4. **Rate Limiting**: Add rate limiting awareness
5. **Offline Support**: Cache responses for offline functionality