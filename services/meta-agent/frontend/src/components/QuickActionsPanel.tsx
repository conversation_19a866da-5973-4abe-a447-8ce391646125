/**
 * Quick Actions Panel Component
 * Provides quick access to common platform actions
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Plus,
  Play,
  Pause,
  Square,
  Zap,
  Bot,
  Activity,
  Settings,
  Upload,
  Download,
  RefreshCw,
  Search,
  BarChart3,
  Users,
  Database,
  Workflow,
  Network,
  Brain,
  FileText,
  Code,
  Puzzle
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { useAgentsApi } from '@/lib/api-client/hooks';

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  href?: string;
  onClick?: () => void;
  badge?: string;
  color: string;
  category: 'create' | 'manage' | 'monitor' | 'analyze';
}

export function QuickActionsPanel() {
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const agentsApi = useAgentsApi();

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      const [systemStats, migrationSummary] = await Promise.all([
        agentsApi.listAgentsApiV1AgentsGet().then(response => response.data).catch(() => null),
        // Temporarily disable migration summary until backend endpoint is implemented
        Promise.resolve(null) // agentService.getMigrationSummary().catch(() => null)
      ]);

      setStats({ system: systemStats, migration: migrationSummary });
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    }
  };

  const handleQuickAction = async (actionId: string) => {
    setLoading(true);
    try {
      switch (actionId) {
        case 'refresh-all':
          await fetchStats();
          window.location.reload();
          break;
        case 'start-all-agents':
          // This would need a bulk start API
          console.log('Starting all agents...');
          break;
        case 'pause-all-agents':
          // This would need a bulk pause API
          console.log('Pausing all agents...');
          break;
        default:
          console.log(`Action ${actionId} not implemented`);
      }
    } catch (error) {
      console.error('Quick action failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const quickActions: QuickAction[] = [
    // Create Actions
    {
      id: 'create-agent',
      title: 'Create Agent',
      description: 'Create a new AI agent',
      icon: Bot,
      href: '/agents/create',
      color: 'bg-blue-50 hover:bg-blue-100 border-blue-200',
      category: 'create'
    },
    {
      id: 'create-automation',
      title: 'Create Automation',
      description: 'Build a new workflow or orchestration',
      icon: Zap,
      href: '/automations/create',
      color: 'bg-purple-50 hover:bg-purple-100 border-purple-200',
      category: 'create'
    },
    {
      id: 'add-connection',
      title: 'Add Connection',
      description: 'Connect external services or data sources',
      icon: Puzzle,
      href: '/connections/create',
      color: 'bg-green-50 hover:bg-green-100 border-green-200',
      category: 'create'
    },
    {
      id: 'start-migration',
      title: 'Start Migration',
      description: 'Migrate an existing application',
      icon: Upload,
      href: '/migration/create',
      color: 'bg-orange-50 hover:bg-orange-100 border-orange-200',
      category: 'create'
    },

    // Manage Actions
    {
      id: 'manage-agents',
      title: 'Manage Agents',
      description: `${stats?.system?.total_agents || 0} agents`,
      icon: Users,
      href: '/agents',
      badge: stats?.system?.active_agents ? `${stats.system.active_agents} active` : undefined,
      color: 'bg-indigo-50 hover:bg-indigo-100 border-indigo-200',
      category: 'manage'
    },
    {
      id: 'view-tasks',
      title: 'View Tasks',
      description: `${stats?.system?.task_statistics?.queued_tasks || 0} queued`,
      icon: Activity,
      href: '/tasks',
      badge: stats?.system?.task_statistics?.completed_tasks ? `${stats.system.task_statistics.completed_tasks} completed` : undefined,
      color: 'bg-cyan-50 hover:bg-cyan-100 border-cyan-200',
      category: 'manage'
    },
    {
      id: 'manage-automations',
      title: 'Automations',
      description: 'Manage workflows and orchestrations',
      icon: Network,
      href: '/automations',
      color: 'bg-violet-50 hover:bg-violet-100 border-violet-200',
      category: 'manage'
    },
    {
      id: 'platform-settings',
      title: 'Platform Settings',
      description: 'Configure platform preferences',
      icon: Settings,
      href: '/settings',
      color: 'bg-gray-50 hover:bg-gray-100 border-gray-200',
      category: 'manage'
    },

    // Monitor Actions
    {
      id: 'system-health',
      title: 'System Health',
      description: 'Monitor system performance',
      icon: Activity,
      href: '/dashboard/health',
      badge: stats?.system?.cpu_usage ? `${stats.system.cpu_usage}% CPU` : undefined,
      color: 'bg-emerald-50 hover:bg-emerald-100 border-emerald-200',
      category: 'monitor'
    },
    {
      id: 'ai-intelligence',
      title: 'AI Intelligence',
      description: 'Monitor AI providers and models',
      icon: Brain,
      href: '/intelligence',
      color: 'bg-pink-50 hover:bg-pink-100 border-pink-200',
      category: 'monitor'
    },
    {
      id: 'refresh-all',
      title: 'Refresh All',
      description: 'Refresh all dashboard data',
      icon: RefreshCw,
      onClick: () => handleQuickAction('refresh-all'),
      color: 'bg-slate-50 hover:bg-slate-100 border-slate-200',
      category: 'monitor'
    },

    // Analyze Actions
    {
      id: 'view-analytics',
      title: 'Analytics',
      description: 'View platform analytics and insights',
      icon: BarChart3,
      href: '/analytics',
      color: 'bg-amber-50 hover:bg-amber-100 border-amber-200',
      category: 'analyze'
    },
    {
      id: 'migration-summary',
      title: 'Migration Summary',
      description: `${stats?.migration?.total_projects || 0} projects`,
      icon: FileText,
      href: '/migration',
      badge: stats?.migration?.active_migrations ? `${stats.migration.active_migrations} active` : undefined,
      color: 'bg-teal-50 hover:bg-teal-100 border-teal-200',
      category: 'analyze'
    }
  ];

  const groupedActions = quickActions.reduce((acc, action) => {
    if (!acc[action.category]) {
      acc[action.category] = [];
    }
    acc[action.category].push(action);
    return acc;
  }, {} as Record<string, QuickAction[]>);

  const categoryLabels = {
    create: 'Create',
    manage: 'Manage',
    monitor: 'Monitor',
    analyze: 'Analyze'
  };

  const categoryIcons = {
    create: Plus,
    manage: Settings,
    monitor: Activity,
    analyze: BarChart3
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5" />
          Quick Actions
        </CardTitle>
        <CardDescription>
          Fast access to common platform operations
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {Object.entries(groupedActions).map(([category, actions]) => {
            const CategoryIcon = categoryIcons[category as keyof typeof categoryIcons];
            
            return (
              <div key={category}>
                <h4 className="text-sm font-medium text-muted-foreground mb-3 flex items-center gap-2">
                  <CategoryIcon className="h-4 w-4" />
                  {categoryLabels[category as keyof typeof categoryLabels]}
                </h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {actions.map((action) => {
                    const IconComponent = action.icon;
                    
                    const ActionButton = (
                      <div
                        className={`p-4 rounded-lg border transition-colors cursor-pointer ${action.color}`}
                      >
                        <div className="flex items-start justify-between mb-2">
                          <IconComponent className="h-5 w-5 text-gray-700" />
                          {action.badge && (
                            <Badge variant="secondary" className="text-xs">
                              {action.badge}
                            </Badge>
                          )}
                        </div>
                        <h5 className="font-medium text-gray-900 mb-1">
                          {action.title}
                        </h5>
                        <p className="text-sm text-gray-600">
                          {action.description}
                        </p>
                      </div>
                    );

                    if (action.href) {
                      return (
                        <Link key={action.id} to={action.href}>
                          {ActionButton}
                        </Link>
                      );
                    }

                    return (
                      <button
                        key={action.id}
                        onClick={action.onClick}
                        disabled={loading}
                        className="text-left w-full"
                      >
                        {ActionButton}
                      </button>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>

        {/* System Status Summary */}
        {stats?.system && (
          <div className="mt-6 pt-4 border-t">
            <h4 className="text-sm font-medium text-muted-foreground mb-3">System Status</h4>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 text-center">
              <div className="p-2 bg-blue-50 rounded-lg">
                <p className="text-lg font-semibold text-blue-600">
                  {stats.system.total_agents}
                </p>
                <p className="text-xs text-blue-800">Total Agents</p>
              </div>
              <div className="p-2 bg-green-50 rounded-lg">
                <p className="text-lg font-semibold text-green-600">
                  {stats.system.active_agents}
                </p>
                <p className="text-xs text-green-800">Active Agents</p>
              </div>
              <div className="p-2 bg-orange-50 rounded-lg">
                <p className="text-lg font-semibold text-orange-600">
                  {stats.system.cpu_usage}%
                </p>
                <p className="text-xs text-orange-800">CPU Usage</p>
              </div>
              <div className="p-2 bg-purple-50 rounded-lg">
                <p className="text-lg font-semibold text-purple-600">
                  {stats.system.memory_usage}%
                </p>
                <p className="text-xs text-purple-800">Memory Usage</p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
