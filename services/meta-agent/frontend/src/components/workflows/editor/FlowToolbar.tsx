/**
 * FlowToolbar - Toolbar for workflow editor with common actions
 */

import React from 'react';
import { 
  Save, 
  Play, 
  Square, 
  RotateCcw, 
  RotateCw, 
  ZoomIn, 
  ZoomOut, 
  Maximize,
  Download,
  Upload,
  Settings,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useFlowStore } from '@/stores/workflows/flowStore';
import { useWorkflowAlertStore } from '@/stores/workflows/alertStore';

interface FlowToolbarProps {
  onFitView?: () => void;
  onCenterView?: () => void;
  className?: string;
}

export const FlowToolbar: React.FC<FlowToolbarProps> = ({
  onFitView,
  onCenterView,
  className = '',
}) => {
  const {
    currentWorkflow,
    hasUnsavedChanges,
    isSaving,
    isValid,
    validationErrors,
  } = useFlowStore();
  
  const { success, error } = useWorkflowAlertStore();

  // Save workflow
  const handleSave = async () => {
    if (!currentWorkflow) return;
    
    try {
      // TODO: Implement save logic with API
      success('Workflow saved successfully');
    } catch (err) {
      error('Failed to save workflow', err instanceof Error ? err.message : 'Unknown error');
    }
  };

  // Execute workflow
  const handleExecute = async () => {
    if (!currentWorkflow || !isValid) {
      error('Cannot execute workflow', 'Please fix validation errors first');
      return;
    }
    
    try {
      // TODO: Implement execution logic with API
      success('Workflow execution started');
    } catch (err) {
      error('Failed to execute workflow', err instanceof Error ? err.message : 'Unknown error');
    }
  };

  // Stop execution
  const handleStop = () => {
    // TODO: Implement stop execution logic
    success('Workflow execution stopped');
  };

  // Undo/Redo (placeholder - would need proper implementation)
  const handleUndo = () => {
    // TODO: Implement undo logic
  };

  const handleRedo = () => {
    // TODO: Implement redo logic
  };

  // Import/Export (placeholder)
  const handleImport = () => {
    // TODO: Implement import logic
  };

  const handleExport = () => {
    // TODO: Implement export logic
  };

  // Settings
  const handleSettings = () => {
    // TODO: Open workflow settings modal
  };

  return (
    <div className={`flex items-center px-4 py-2 bg-white border-b ${className}`}>
      {/* File operations */}
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handleSave}
          disabled={!hasUnsavedChanges || isSaving}
          className="flex items-center space-x-1"
        >
          <Save className="w-4 h-4" />
          <span>{isSaving ? 'Saving...' : 'Save'}</span>
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={handleImport}
          className="flex items-center space-x-1"
        >
          <Upload className="w-4 h-4" />
          <span>Import</span>
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={handleExport}
          className="flex items-center space-x-1"
        >
          <Download className="w-4 h-4" />
          <span>Export</span>
        </Button>
      </div>

      <Separator orientation="vertical" className="h-6 mx-4" />

      {/* Execution controls */}
      <div className="flex items-center space-x-2">
        <Button
          variant={isValid ? "default" : "secondary"}
          size="sm"
          onClick={handleExecute}
          disabled={!isValid || !currentWorkflow}
          className="flex items-center space-x-1"
        >
          <Play className="w-4 h-4" />
          <span>Run</span>
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={handleStop}
          className="flex items-center space-x-1"
        >
          <Square className="w-4 h-4" />
          <span>Stop</span>
        </Button>
      </div>

      <Separator orientation="vertical" className="h-6 mx-4" />

      {/* Edit operations */}
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handleUndo}
          className="flex items-center space-x-1"
        >
          <RotateCcw className="w-4 h-4" />
          <span>Undo</span>
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={handleRedo}
          className="flex items-center space-x-1"
        >
          <RotateCw className="w-4 h-4" />
          <span>Redo</span>
        </Button>
      </div>

      <Separator orientation="vertical" className="h-6 mx-4" />

      {/* View controls */}
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={onFitView}
          className="flex items-center space-x-1"
        >
          <Maximize className="w-4 h-4" />
          <span>Fit</span>
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={onCenterView}
          className="flex items-center space-x-1"
        >
          <ZoomIn className="w-4 h-4" />
          <span>Center</span>
        </Button>
      </div>

      {/* Spacer */}
      <div className="flex-1" />

      {/* Status and settings */}
      <div className="flex items-center space-x-4">
        {/* Validation status */}
        {validationErrors.length > 0 && (
          <div className="text-sm text-red-600">
            {validationErrors.length} error{validationErrors.length > 1 ? 's' : ''}
          </div>
        )}
        
        {/* Unsaved changes indicator */}
        {hasUnsavedChanges && (
          <div className="text-sm text-orange-600">
            Unsaved changes
          </div>
        )}
        
        {/* Workflow name */}
        {currentWorkflow && (
          <div className="text-sm font-medium text-gray-700">
            {currentWorkflow.name}
          </div>
        )}
        
        {/* Settings */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleSettings}
          className="flex items-center space-x-1"
        >
          <Settings className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
};

export default FlowToolbar;