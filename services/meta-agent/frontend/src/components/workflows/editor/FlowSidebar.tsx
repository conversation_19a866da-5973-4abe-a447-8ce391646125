/**
 * FlowSidebar - Sidebar for workflow editor with components palette
 */

import React, { useState } from 'react';
import { Search, Package, Filter } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { useComponentsStore } from '@/stores/workflows/componentsStore';

interface FlowSidebarProps {
  className?: string;
}

interface ComponentItemProps {
  component: {
    type: string;
    display_name: string;
    description: string;
    icon?: string;
    category: string;
    official: boolean;
  };
}

const ComponentItem: React.FC<ComponentItemProps> = ({ component }) => {
  const onDragStart = (event: React.DragEvent, nodeType: string) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.effectAllowed = 'move';
  };

  return (
    <div
      className="p-3 border rounded-lg cursor-grab hover:bg-gray-50 transition-colors"
      draggable
      onDragStart={(event) => onDragStart(event, component.type)}
    >
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-2">
          {component.icon && (
            <div className="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
              <Package className="w-4 h-4 text-blue-600" />
            </div>
          )}
          <div className="min-w-0 flex-1">
            <div className="flex items-center space-x-2">
              <h4 className="text-sm font-medium text-gray-900 truncate">
                {component.display_name}
              </h4>
              {component.official && (
                <Badge variant="secondary" className="text-xs">
                  Official
                </Badge>
              )}
            </div>
            <p className="text-xs text-gray-500 mt-1 line-clamp-2">
              {component.description}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

interface ComponentCategoryProps {
  category: {
    name: string;
    display_name: string;
    description: string;
    components: any[];
  };
  searchQuery: string;
}

const ComponentCategory: React.FC<ComponentCategoryProps> = ({ 
  category, 
  searchQuery 
}) => {
  const filteredComponents = category.components.filter(component =>
    component.display_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    component.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    component.type.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (filteredComponents.length === 0) {
    return null;
  }

  return (
    <div className="mb-6">
      <div className="mb-3">
        <h3 className="text-sm font-semibold text-gray-900 uppercase tracking-wide">
          {category.display_name}
        </h3>
        <p className="text-xs text-gray-500 mt-1">
          {category.description}
        </p>
      </div>
      <div className="space-y-2">
        {filteredComponents.map((component) => (
          <ComponentItem key={component.type} component={component} />
        ))}
      </div>
    </div>
  );
};

export const FlowSidebar: React.FC<FlowSidebarProps> = ({ 
  className = '' 
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  
  const {
    categories,
    isLoading,
    searchQuery: storeSearchQuery,
    setSearchQuery: setStoreSearchQuery,
    setSelectedCategory: setStoreSelectedCategory,
    clearSearch,
  } = useComponentsStore();

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setStoreSearchQuery(query);
  };

  // Handle category filter
  const handleCategoryFilter = (categoryName: string | null) => {
    setSelectedCategory(categoryName);
    setStoreSelectedCategory(categoryName);
  };

  // Clear all filters
  const handleClearFilters = () => {
    setSearchQuery('');
    setSelectedCategory(null);
    clearSearch();
  };

  // Filter categories based on selected category
  const filteredCategories = selectedCategory
    ? categories.filter(cat => cat.name === selectedCategory)
    : categories;

  return (
    <div className={`bg-white border-r ${className}`}>
      <div className="p-4 border-b">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Components
        </h2>
        
        {/* Search */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search components..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            className="pl-10"
          />
        </div>
        
        {/* Category Filter */}
        <div className="mb-4">
          <div className="flex items-center space-x-2 mb-2">
            <Filter className="w-4 h-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Category</span>
          </div>
          <div className="flex flex-wrap gap-1">
            <Button
              variant={selectedCategory === null ? "default" : "outline"}
              size="sm"
              onClick={() => handleCategoryFilter(null)}
              className="text-xs"
            >
              All
            </Button>
            {categories.map((category) => (
              <Button
                key={category.name}
                variant={selectedCategory === category.name ? "default" : "outline"}
                size="sm"
                onClick={() => handleCategoryFilter(category.name)}
                className="text-xs"
              >
                {category.display_name}
              </Button>
            ))}
          </div>
          
          {(searchQuery || selectedCategory) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClearFilters}
              className="mt-2 text-xs"
            >
              Clear filters
            </Button>
          )}
        </div>
      </div>
      
      {/* Component List */}
      <ScrollArea className="flex-1">
        <div className="p-4">
          {isLoading ? (
            <div className="text-center text-gray-500 py-8">
              Loading components...
            </div>
          ) : filteredCategories.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              No components found
            </div>
          ) : (
            <div>
              {filteredCategories.map((category) => (
                <ComponentCategory
                  key={category.name}
                  category={category}
                  searchQuery={searchQuery}
                />
              ))}
            </div>
          )}
        </div>
      </ScrollArea>
      
      {/* Help Text */}
      <div className="p-4 border-t bg-gray-50">
        <p className="text-xs text-gray-600">
          💡 Drag components to the canvas to add them to your workflow
        </p>
      </div>
    </div>
  );
};

export default FlowSidebar;