/**
 * FlowEditor - Main workflow editor component using React Flow
 */

import React, { useCallback, useRef } from 'react';
import {
  ReactFlow,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  type Connection,
  type Edge,
  type Node,
  type ReactFlowInstance,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';

import { useFlowStore } from '@/stores/workflows/flowStore';
import { FlowToolbar } from './FlowToolbar';
import { FlowSidebar } from './FlowSidebar';
import { NodeTypes } from '../nodes/NodeTypes';
import { EdgeTypes } from '../edges/EdgeTypes';

interface FlowEditorProps {
  workflowId?: string;
  className?: string;
  readOnly?: boolean;
}

export const FlowEditor: React.FC<FlowEditorProps> = ({
  workflowId,
  className = '',
  readOnly = false,
}) => {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  
  // Zustand store hooks
  const {
    nodes,
    edges,
    viewport,
    onNodesChange,
    onEdgesChange,
    onConnect,
    setReactFlowInstance,
    fitView,
    centerView,
  } = useFlowStore();

  // React Flow instance ref
  const onInit = useCallback((reactFlowInstance: ReactFlowInstance) => {
    setReactFlowInstance(reactFlowInstance);
  }, [setReactFlowInstance]);

  // Handle connection creation
  const handleConnect = useCallback(
    (params: Connection) => {
      if (!readOnly) {
        onConnect(params);
      }
    },
    [onConnect, readOnly]
  );

  // Handle drag over for node dropping
  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  // Handle node drop from sidebar
  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();
      
      if (readOnly) return;

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
      const type = event.dataTransfer.getData('application/reactflow');
      
      if (typeof type === 'undefined' || !type || !reactFlowBounds) {
        return;
      }

      // Calculate position within the flow
      const position = {
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      };

      // Create new node data
      const newNode: Node = {
        id: `${type}-${Date.now()}`,
        type,
        position,
        data: {
          label: type,
          type,
        },
      };

      // Add node to store (store will handle ID generation and validation)
      // Note: We'll need to implement addNode in the store or use setNodes
    },
    [readOnly]
  );

  return (
    <div className={`flex h-full ${className}`}>
      {/* Sidebar */}
      {!readOnly && (
        <FlowSidebar className="w-80 border-r border-gray-200" />
      )}
      
      {/* Main editor area */}
      <div className="flex-1 flex flex-col">
        {/* Toolbar */}
        {!readOnly && (
          <FlowToolbar 
            onFitView={fitView}
            onCenterView={centerView}
            className="border-b border-gray-200"
          />
        )}
        
        {/* Flow canvas */}
        <div className="flex-1" ref={reactFlowWrapper}>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={handleConnect}
            onInit={onInit}
            onDrop={onDrop}
            onDragOver={onDragOver}
            nodeTypes={NodeTypes}
            edgeTypes={EdgeTypes}
            defaultViewport={viewport}
            fitView
            attributionPosition="bottom-left"
            className="workflow-editor"
            nodesDraggable={!readOnly}
            nodesConnectable={!readOnly}
            elementsSelectable={!readOnly}
          >
            {/* Background */}
            <Background 
              color="#f1f5f9" 
              gap={20} 
              size={1}
            />
            
            {/* Controls */}
            <Controls 
              position="bottom-right"
              showInteractive={!readOnly}
            />
            
            {/* Minimap */}
            <MiniMap
              position="bottom-left"
              nodeColor={(node) => {
                switch (node.type) {
                  case 'input':
                    return '#10b981';
                  case 'output':
                    return '#f59e0b';
                  default:
                    return '#6366f1';
                }
              }}
              maskColor="rgba(255, 255, 255, 0.2)"
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
              }}
            />
          </ReactFlow>
        </div>
      </div>
    </div>
  );
};

export default FlowEditor;