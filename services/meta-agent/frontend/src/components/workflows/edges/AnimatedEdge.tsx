/**
 * AnimatedEdge - Animated edge to show data flow
 */

import React from 'react';
import {
  BaseEdge,
  EdgeLabelRenderer,
  getSmoothStepPath,
  type EdgeProps,
} from '@xyflow/react';
import { Zap } from 'lucide-react';

export const AnimatedEdge: React.FC<EdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  markerEnd,
  selected,
  data,
}) => {
  const [edgePath, labelX, labelY] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  const isActive = data?.active || false;

  return (
    <>
      <BaseEdge
        path={edgePath}
        markerEnd={markerEnd}
        style={{
          ...style,
          strokeWidth: selected ? 3 : 2,
          stroke: isActive ? '#10b981' : selected ? '#3b82f6' : '#6b7280',
          strokeDasharray: isActive ? '5,5' : undefined,
          animation: isActive ? 'dash 1s linear infinite' : undefined,
        }}
      />
      
      {/* Animation indicator */}
      {isActive && (
        <EdgeLabelRenderer>
          <div
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
              pointerEvents: 'none',
            }}
            className="flex items-center justify-center w-6 h-6 bg-green-100 border border-green-300 rounded-full animate-pulse"
          >
            <Zap className="w-3 h-3 text-green-600" />
          </div>
        </EdgeLabelRenderer>
      )}
      
      <style>{`
        @keyframes dash {
          to {
            stroke-dashoffset: -10;
          }
        }
      `}</style>
    </>
  );
};

export default AnimatedEdge;