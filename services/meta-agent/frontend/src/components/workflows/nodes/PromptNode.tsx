/**
 * PromptNode - Prompt template node for text processing
 */

import React, { useState } from 'react';
import { <PERSON><PERSON>, Position, type NodeProps } from '@xyflow/react';
import { MessageSquare, Edit3, Eye, EyeOff } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';

interface PromptNodeData extends Record<string, unknown> {
  label: string;
  template?: string;
  variables?: string[];
  preview?: string;
}

export const PromptNode: React.FC<NodeProps> = ({ 
  data, 
  selected,
}) => {
  const nodeData = data as PromptNodeData;
  const { 
    label, 
    template = 'Enter your prompt template here...', 
    variables = [],
    preview 
  } = nodeData;
  
  const [isEditing, setIsEditing] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [currentTemplate, setCurrentTemplate] = useState(template);

  // Extract variables from template (variables in {variable} format)
  const extractVariables = (text: string) => {
    const matches = text.match(/\{([^}]+)\}/g);
    return matches ? matches.map(match => match.slice(1, -1)) : [];
  };

  const detectedVariables = extractVariables(currentTemplate);

  // Generate preview by replacing variables with sample values
  const generatePreview = () => {
    let previewText = currentTemplate;
    detectedVariables.forEach((variable, index) => {
      previewText = previewText.replace(
        new RegExp(`\\{${variable}\\}`, 'g'), 
        `[${variable.toUpperCase()}]`
      );
    });
    return previewText;
  };

  return (
    <div 
      className={`
        min-w-[300px] border-2 border-blue-300 bg-blue-50 rounded-lg shadow-sm transition-all
        ${selected ? 'ring-2 ring-blue-500 ring-offset-1' : ''}
      `}
    >
      {/* Input Handles for Variables */}
      {detectedVariables.map((variable, index) => (
        <Handle
          key={variable}
          type="target"
          position={Position.Left}
          id={variable}
          style={{
            top: 60 + (index * 25),
            background: '#3b82f6',
          }}
          className="w-3 h-3"
        />
      ))}

      {/* Node Header */}
      <div className="px-3 py-2 border-b border-blue-200 bg-blue-100 rounded-t-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <MessageSquare className="w-4 h-4 text-blue-700" />
            <h3 className="text-sm font-medium text-blue-900">
              {label}
            </h3>
          </div>
          
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowPreview(!showPreview)}
              className="w-6 h-6 p-0"
            >
              {showPreview ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEditing(!isEditing)}
              className="w-6 h-6 p-0"
            >
              <Edit3 className="w-3 h-3" />
            </Button>
          </div>
        </div>
      </div>

      {/* Node Body */}
      <div className="px-3 py-3 bg-white rounded-b-lg">
        {/* Variable Inputs Labels */}
        {detectedVariables.length > 0 && (
          <div className="mb-3">
            <div className="text-xs font-medium text-gray-700 mb-1">Variables:</div>
            {detectedVariables.map((variable, index) => (
              <div key={variable} className="text-xs text-gray-600 py-1">
                → {variable}
              </div>
            ))}
          </div>
        )}

        {/* Template Editor/Display */}
        <div className="mb-3">
          {isEditing ? (
            <Textarea
              value={currentTemplate}
              onChange={(e) => setCurrentTemplate(e.target.value)}
              placeholder="Enter your prompt template..."
              className="text-sm min-h-[80px] resize-none"
              onBlur={() => setIsEditing(false)}
              autoFocus
            />
          ) : (
            <div 
              className="bg-gray-50 border border-gray-200 rounded p-2 min-h-[80px] cursor-text"
              onClick={() => setIsEditing(true)}
            >
              <pre className="text-xs text-gray-700 whitespace-pre-wrap break-words">
                {currentTemplate || 'Click to edit template...'}
              </pre>
            </div>
          )}
        </div>

        {/* Preview */}
        {showPreview && (
          <div className="mb-3">
            <div className="text-xs font-medium text-gray-700 mb-1">Preview:</div>
            <div className="bg-yellow-50 border border-yellow-200 rounded p-2">
              <pre className="text-xs text-gray-700 whitespace-pre-wrap break-words">
                {generatePreview()}
              </pre>
            </div>
          </div>
        )}

        {/* Statistics */}
        <div className="flex justify-between text-xs text-gray-500">
          <span>{detectedVariables.length} variable{detectedVariables.length !== 1 ? 's' : ''}</span>
          <span>{currentTemplate.length} chars</span>
        </div>

        {/* Output Label */}
        <div className="text-right mt-3">
          <div className="text-xs text-gray-600 py-1">
            Formatted Prompt →
          </div>
        </div>
      </div>

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="prompt"
        style={{
          background: '#10b981',
        }}
        className="w-3 h-3"
      />
    </div>
  );
};

export default PromptNode;