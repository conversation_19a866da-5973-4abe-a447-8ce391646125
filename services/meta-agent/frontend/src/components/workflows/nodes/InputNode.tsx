/**
 * InputNode - Input node for workflow data entry
 */

import React from 'react';
import { <PERSON><PERSON>, Position, type NodeProps } from '@xyflow/react';
import { ArrowRight, Type } from 'lucide-react';

interface InputNodeData extends Record<string, unknown> {
  label: string;
  value?: string;
  placeholder?: string;
  type?: 'text' | 'number' | 'file';
}

export const InputNode: React.FC<NodeProps> = ({ 
  data, 
  selected,
}) => {
  const nodeData = data as InputNodeData;
  const { label, value, placeholder = 'Enter value...', type = 'text' } = nodeData;

  return (
    <div 
      className={`
        min-w-[250px] border-2 border-green-300 bg-green-50 rounded-lg shadow-sm transition-all
        ${selected ? 'ring-2 ring-green-500 ring-offset-1' : ''}
      `}
    >
      {/* Node Header */}
      <div className="px-3 py-2 border-b border-green-200 bg-green-100 rounded-t-lg">
        <div className="flex items-center space-x-2">
          <ArrowRight className="w-4 h-4 text-green-700" />
          <h3 className="text-sm font-medium text-green-900">
            Input: {label}
          </h3>
        </div>
      </div>

      {/* Node Body */}
      <div className="px-3 py-3 bg-white rounded-b-lg">
        <div className="flex items-center space-x-2 mb-2">
          <Type className="w-4 h-4 text-gray-500" />
          <span className="text-xs text-gray-600 capitalize">{type}</span>
        </div>
        
        {type === 'text' && (
          <input
            type="text"
            placeholder={placeholder}
            defaultValue={value}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
          />
        )}
        
        {type === 'number' && (
          <input
            type="number"
            placeholder={placeholder}
            defaultValue={value}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
          />
        )}
        
        {type === 'file' && (
          <input
            type="file"
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
          />
        )}
      </div>

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        style={{
          background: '#10b981',
        }}
        className="w-3 h-3"
      />
    </div>
  );
};

export default InputNode;