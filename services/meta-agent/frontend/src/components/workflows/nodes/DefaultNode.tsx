/**
 * DefaultNode - Basic node component for workflows
 */

import React from 'react';
import { <PERSON><PERSON>, Position, type NodeProps } from '@xyflow/react';
import { Settings, AlertCircle, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface DefaultNodeData extends Record<string, unknown> {
  label: string;
  type?: string;
  status?: 'valid' | 'invalid' | 'warning' | 'building' | 'built';
  description?: string;
  inputs?: Array<{ id: string; name: string; type: string }>;
  outputs?: Array<{ id: string; name: string; type: string }>;
}

export const DefaultNode: React.FC<NodeProps> = ({ 
  data, 
  selected,
  id,
}) => {
  const nodeData = data as DefaultNodeData;
  const { label, type, status = 'valid', description, inputs = [], outputs = [] } = nodeData;

  // Get node status color
  const getStatusColor = () => {
    switch (status) {
      case 'valid':
      case 'built':
        return 'border-green-500 bg-green-50';
      case 'invalid':
        return 'border-red-500 bg-red-50';
      case 'warning':
        return 'border-yellow-500 bg-yellow-50';
      case 'building':
        return 'border-blue-500 bg-blue-50';
      default:
        return 'border-gray-300 bg-white';
    }
  };

  // Get status icon
  const getStatusIcon = () => {
    switch (status) {
      case 'valid':
      case 'built':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'invalid':
      case 'warning':
        return <AlertCircle className="w-4 h-4 text-red-600" />;
      case 'building':
        return <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />;
      default:
        return null;
    }
  };

  return (
    <div 
      className={`
        min-w-[200px] border-2 rounded-lg shadow-sm transition-all
        ${getStatusColor()}
        ${selected ? 'ring-2 ring-blue-500 ring-offset-1' : ''}
      `}
    >
      {/* Node Header */}
      <div className="px-3 py-2 border-b border-gray-200 bg-white rounded-t-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <h3 className="text-sm font-medium text-gray-900 truncate">
              {label}
            </h3>
            {type && (
              <Badge variant="secondary" className="text-xs">
                {type}
              </Badge>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="w-6 h-6 p-0"
          >
            <Settings className="w-3 h-3" />
          </Button>
        </div>
        
        {description && (
          <p className="text-xs text-gray-500 mt-1 line-clamp-2">
            {description}
          </p>
        )}
      </div>

      {/* Input Handles */}
      {inputs.map((input, index) => (
        <Handle
          key={input.id}
          type="target"
          position={Position.Left}
          id={input.id}
          style={{
            top: 60 + (index * 30),
            background: '#6366f1',
          }}
          className="w-3 h-3"
        />
      ))}

      {/* Node Body */}
      <div className="px-3 py-2 bg-white rounded-b-lg">
        {/* Input Labels */}
        {inputs.length > 0 && (
          <div className="mb-2">
            {inputs.map((input: any, index: number) => (
              <div
                key={input.id}
                className="text-xs text-gray-600 py-1"
                style={{ marginTop: index * 4 }}
              >
                {input.name}
              </div>
            ))}
          </div>
        )}

        {/* Output Labels */}
        {outputs.length > 0 && (
          <div className="text-right">
            {outputs.map((output: any, index: number) => (
              <div
                key={output.id}
                className="text-xs text-gray-600 py-1"
                style={{ marginTop: index * 4 }}
              >
                {output.name}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Output Handles */}
      {outputs.map((output: any, index: number) => (
        <Handle
          key={output.id}
          type="source"
          position={Position.Right}
          id={output.id}
          style={{
            top: 60 + (index * 30),
            background: '#10b981',
          }}
          className="w-3 h-3"
        />
      ))}
    </div>
  );
};

export default DefaultNode;