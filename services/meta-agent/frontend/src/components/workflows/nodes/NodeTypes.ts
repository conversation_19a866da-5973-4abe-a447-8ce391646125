/**
 * Node Types - Define all available node types for React Flow
 */

import type { NodeTypes as ReactFlowNodeTypes } from '@xyflow/react';
import { DefaultNode } from './DefaultNode';
import { InputNode } from './InputNode';
import { OutputNode } from './OutputNode';
import { LLMNode } from './LLMNode';
import { PromptNode } from './PromptNode';

export const NodeTypes: ReactFlowNodeTypes = {
  default: DefaultNode,
  input: InputNode,
  output: OutputNode,
  llm: LLMNode,
  prompt: PromptNode,
};

export { DefaultNode, InputNode, OutputNode, LLMNode, PromptNode };