/**
 * LLMNode - Language Model node for AI processing
 */

import React from 'react';
import { <PERSON><PERSON>, Position, type NodeProps } from '@xyflow/react';
import { Brain, Settings, Zap } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface LLMNodeData extends Record<string, unknown> {
  label: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
  provider?: 'openai' | 'anthropic' | 'local';
  status?: 'idle' | 'processing' | 'complete' | 'error';
}

export const LLMNode: React.FC<NodeProps> = ({ 
  data, 
  selected,
}) => {
  const nodeData = data as LLMNodeData;
  const { 
    label, 
    model = 'gpt-3.5-turbo', 
    temperature = 0.7, 
    maxTokens = 1000,
    provider = 'openai',
    status = 'idle'
  } = nodeData;

  // Get provider color
  const getProviderColor = () => {
    switch (provider) {
      case 'openai':
        return 'bg-green-100 text-green-800';
      case 'anthropic':
        return 'bg-purple-100 text-purple-800';
      case 'local':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get status indicator
  const getStatusIndicator = () => {
    switch (status) {
      case 'processing':
        return <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />;
      case 'complete':
        return <div className="w-2 h-2 bg-green-500 rounded-full" />;
      case 'error':
        return <div className="w-2 h-2 bg-red-500 rounded-full" />;
      default:
        return <div className="w-2 h-2 bg-gray-300 rounded-full" />;
    }
  };

  return (
    <div 
      className={`
        min-w-[280px] border-2 border-purple-300 bg-purple-50 rounded-lg shadow-sm transition-all
        ${selected ? 'ring-2 ring-purple-500 ring-offset-1' : ''}
      `}
    >
      {/* Input Handles */}
      <Handle
        type="target"
        position={Position.Left}
        id="prompt"
        style={{
          top: 60,
          background: '#8b5cf6',
        }}
        className="w-3 h-3"
      />
      
      <Handle
        type="target"
        position={Position.Left}
        id="context"
        style={{
          top: 90,
          background: '#8b5cf6',
        }}
        className="w-3 h-3"
      />

      {/* Node Header */}
      <div className="px-3 py-2 border-b border-purple-200 bg-purple-100 rounded-t-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Brain className="w-4 h-4 text-purple-700" />
            <h3 className="text-sm font-medium text-purple-900">
              {label}
            </h3>
            {getStatusIndicator()}
          </div>
          
          <div className="flex items-center space-x-1">
            <Badge variant="secondary" className={`text-xs ${getProviderColor()}`}>
              {provider}
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              className="w-6 h-6 p-0"
            >
              <Settings className="w-3 h-3" />
            </Button>
          </div>
        </div>
      </div>

      {/* Node Body */}
      <div className="px-3 py-3 bg-white rounded-b-lg">
        {/* Input Labels */}
        <div className="mb-3">
          <div className="text-xs text-gray-600 py-1">
            → Prompt
          </div>
          <div className="text-xs text-gray-600 py-1">
            → Context (optional)
          </div>
        </div>

        {/* Model Configuration */}
        <div className="bg-gray-50 border border-gray-200 rounded p-2 mb-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs font-medium text-gray-700">Model</span>
            <span className="text-xs text-gray-600">{model}</span>
          </div>
          
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex justify-between">
              <span className="text-gray-600">Temp:</span>
              <span className="text-gray-800">{temperature}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Max tokens:</span>
              <span className="text-gray-800">{maxTokens}</span>
            </div>
          </div>
        </div>

        {/* Performance Indicator */}
        <div className="flex items-center space-x-2 text-xs text-gray-600">
          <Zap className="w-3 h-3" />
          <span>AI Processing</span>
        </div>

        {/* Output Label */}
        <div className="text-right mt-3">
          <div className="text-xs text-gray-600 py-1">
            Response →
          </div>
        </div>
      </div>

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="response"
        style={{
          background: '#10b981',
        }}
        className="w-3 h-3"
      />
    </div>
  );
};

export default LLMNode;