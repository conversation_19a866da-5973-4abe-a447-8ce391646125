/**
 * OutputNode - Output node for workflow results
 */

import React from 'react';
import { <PERSON><PERSON>, Position, type NodeProps } from '@xyflow/react';
import { ArrowLeft, Download, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface OutputNodeData extends Record<string, unknown> {
  label: string;
  value?: any;
  format?: 'text' | 'json' | 'file';
  preview?: boolean;
}

export const OutputNode: React.FC<NodeProps> = ({ 
  data, 
  selected,
}) => {
  const nodeData = data as OutputNodeData;
  const { label, value, format = 'text', preview = true } = nodeData;

  // Format value for display
  const formatValue = (val: any) => {
    if (!val) return 'No output';
    
    switch (format) {
      case 'json':
        return JSON.stringify(val, null, 2);
      case 'file':
        return val.name || 'File output';
      default:
        return String(val);
    }
  };

  const handleDownload = () => {
    if (!value) return;
    
    // Create download based on format
    const dataStr = formatValue(value);
    const dataBlob = new Blob([dataStr], { type: 'text/plain' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${label.replace(/\s+/g, '_')}_output.${format === 'json' ? 'json' : 'txt'}`;
    link.click();
    
    URL.revokeObjectURL(url);
  };

  const handlePreview = () => {
    // Open preview modal/popup
    console.log('Preview output:', value);
  };

  return (
    <div 
      className={`
        min-w-[250px] border-2 border-orange-300 bg-orange-50 rounded-lg shadow-sm transition-all
        ${selected ? 'ring-2 ring-orange-500 ring-offset-1' : ''}
      `}
    >
      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        style={{
          background: '#f59e0b',
        }}
        className="w-3 h-3"
      />

      {/* Node Header */}
      <div className="px-3 py-2 border-b border-orange-200 bg-orange-100 rounded-t-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <ArrowLeft className="w-4 h-4 text-orange-700" />
            <h3 className="text-sm font-medium text-orange-900">
              Output: {label}
            </h3>
          </div>
          
          <div className="flex items-center space-x-1">
            {preview && value && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handlePreview}
                className="w-6 h-6 p-0"
              >
                <Eye className="w-3 h-3" />
              </Button>
            )}
            
            {value && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDownload}
                className="w-6 h-6 p-0"
              >
                <Download className="w-3 h-3" />
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Node Body */}
      <div className="px-3 py-3 bg-white rounded-b-lg">
        <div className="mb-2">
          <span className="text-xs text-gray-600 capitalize">
            Format: {format}
          </span>
        </div>
        
        {/* Output Preview */}
        <div className="bg-gray-50 border border-gray-200 rounded p-2 min-h-[60px] max-h-[120px] overflow-auto">
          <pre className="text-xs text-gray-700 whitespace-pre-wrap break-words">
            {formatValue(value)}
          </pre>
        </div>
        
        {/* Status */}
        <div className="mt-2 text-xs">
          {value ? (
            <span className="text-green-600">✓ Output ready</span>
          ) : (
            <span className="text-gray-500">Waiting for input...</span>
          )}
        </div>
      </div>
    </div>
  );
};

export default OutputNode;