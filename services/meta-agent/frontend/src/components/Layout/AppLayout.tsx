'use client'

import { useState } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Activity,
  Bot,
  Brain,
  BrainCircuit,
  ChevronLeft,
  ChevronRight,
  Cog,
  Database,
  FolderOpen,
  GitBranch,
  Home,
  Menu,
  Network,
  Puzzle,
  Settings,
  Workflow,
  X,
  Zap
} from 'lucide-react'
import { Link, useLocation } from 'react-router-dom'

interface AppLayoutProps {
  children: React.ReactNode
}

const navigationItems = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: Home,
    description: 'Overview and analytics'
  },
  {
    title: 'Agents',
    href: '/agents',
    icon: Bot,
    description: 'AI agent management'
  },

  {
    title: 'Automations',
    href: '/automations',
    icon: Zap,
    description: 'Workflows, orchestrations & hybrid automations'
  },
  {
    title: 'Tasks',
    href: '/tasks',
    icon: Activity,
    description: 'Task management'
  },
  {
    title: 'Migration',
    href: '/migration',
    icon: GitBranch,
    description: 'App-to-agent migration',
    badge: 'New'
  },
  {
    title: 'Intelligence',
    href: '/intelligence',
    icon: Brain,
    description: 'AI gateway & models'
  },
  {
    title: 'Connections',
    href: '/connections',
    icon: Puzzle,
    description: 'Integrations, APIs & data sources'
  },
  {
    title: 'Projects',
    href: '/projects',
    icon: FolderOpen,
    description: 'Project management'
  },
  {
    title: 'Settings',
    href: '/settings',
    icon: Settings,
    description: 'Platform configuration'
  }
]

export function AppLayout({ children }: AppLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const location = useLocation()
  const pathname = location.pathname

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile menu overlay */}
      {mobileMenuOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={() => setMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside className={cn(
        "fixed left-0 top-0 z-50 h-screen border-r bg-sidebar border-sidebar-border transition-all duration-300 lg:translate-x-0",
        sidebarCollapsed ? "w-16" : "w-64",
        mobileMenuOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"
      )}>
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex h-16 items-center border-b border-sidebar-border px-4">
            {!sidebarCollapsed && (
              <div className="flex items-center space-x-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                  <BrainCircuit className="h-5 w-5" />
                </div>
                <span className="text-lg font-semibold text-sidebar-foreground">AI Platform</span>
              </div>
            )}
            <Button
              variant="ghost"
              size="icon"
              className={cn("ml-auto", sidebarCollapsed && "mx-auto")}
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            >
              {sidebarCollapsed ? (
                <ChevronRight className="h-4 w-4" />
              ) : (
                <ChevronLeft className="h-4 w-4" />
              )}
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="ml-2 lg:hidden"
              onClick={() => setMobileMenuOpen(false)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 space-y-2 p-3">
            {navigationItems.map((item) => {
              const isActive = pathname === item.href || pathname?.startsWith(item.href + '/')
              const Icon = item.icon

              return (
                <Link key={item.href} to={item.href}>
                  <div className={cn(
                    "sidebar-menu-item flex items-center space-x-3 rounded-xl px-4 py-3 text-sm",
                    isActive && "active"
                  )}>
                    <Icon className="h-5 w-5 flex-shrink-0" />
                    {!sidebarCollapsed && (
                      <>
                        <div className="flex-1 truncate">
                          <div className="font-medium">{item.title}</div>
                          <div className="text-xs text-muted-foreground truncate">
                            {item.description}
                          </div>
                        </div>
                        {item.badge && (
                          <Badge variant="secondary" className="ml-auto">
                            {item.badge}
                          </Badge>
                        )}
                      </>
                    )}
                  </div>
                </Link>
              )
            })}
          </nav>

          {/* Footer */}
          <div className="border-t border-sidebar-border p-4">
            {!sidebarCollapsed && (
              <div className="text-center text-xs text-sidebar-foreground/60">
                <div>AI Agent Platform v2.0</div>
                <div>Enhanced with ShadCN</div>
              </div>
            )}
          </div>
        </div>
      </aside>

      {/* Main content */}
      <div className={cn(
        "transition-all duration-300",
        sidebarCollapsed ? "lg:ml-16" : "lg:ml-64"
      )}>
        {/* Top bar */}
        <header className="sticky top-0 z-30 flex h-16 items-center border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex w-full items-center px-4">
            <Button
              variant="ghost"
              size="icon"
              className="lg:hidden"
              onClick={() => setMobileMenuOpen(true)}
            >
              <Menu className="h-5 w-5" />
            </Button>
            
            <div className="ml-auto flex items-center space-x-2">
              <Badge variant="outline" className="hidden sm:inline-flex">
                <Activity className="mr-1 h-3 w-3" />
                System Online
              </Badge>
              <Button variant="ghost" size="icon">
                <Cog className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="p-8 max-w-7xl mx-auto">
          <div className="space-y-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}