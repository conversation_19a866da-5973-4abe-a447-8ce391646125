/**
 * System Health Panel Component
 * Real-time system health monitoring with API integration
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Activity,
  CheckCircle2,
  XCircle,
  AlertTriangle,
  RefreshCw,
  Server,
  Database,
  Zap,
  Brain,
  Clock,
  TrendingUp,
  Users,
  Cpu,
  HardDrive
} from 'lucide-react';
import { useSystemApi } from '@/lib/api-client/hooks';
import type { SystemStatsResponse } from '@/api-generated/api';

type SystemStats = SystemStatsResponse;

interface HealthMetric {
  name: string;
  status: 'healthy' | 'warning' | 'error';
  value: string | number;
  description: string;
  icon: React.ComponentType<any>;
}

export function SystemHealthPanel() {
  const [systemStats, setSystemStats] = useState<SystemStats | null>(null);
  const [systemHealth, setSystemHealth] = useState<any>(null);
  const [aiHealth, setAiHealth] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const systemApi = useSystemApi();

  const fetchHealthData = async () => {
    try {
      setLoading(true);
      const [stats, health] = await Promise.all([
        systemApi.getSystemStatsApiV1RuntimeSystemStatsGet().then(response => response.data).catch(() => null),
        systemApi.getSystemHealthApiV1RuntimeSystemHealthGet().then(response => response.data).catch(() => null)
      ]);

      setSystemStats(stats);
      setSystemHealth(health);
      setAiHealth({ status: 'healthy', providers: ['OpenAI', 'Anthropic'] }); // Mock AI health
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to fetch health data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHealthData();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchHealthData, 30000);
    return () => clearInterval(interval);
  }, []);

  const getHealthMetrics = (): HealthMetric[] => {
    const metrics: HealthMetric[] = [];

    if (systemStats) {
      metrics.push(
        {
          name: 'Active Agents',
          status: (systemStats.active_agents ?? 0) > 0 ? 'healthy' : 'warning',
          value: systemStats.active_agents ?? 0,
          description: `${systemStats.total_agents} total agents`,
          icon: Users
        },
        {
          name: 'CPU Usage',
          status: (systemStats.cpu_usage ?? 0) < 80 ? 'healthy' : (systemStats.cpu_usage ?? 0) < 95 ? 'warning' : 'error',
          value: `${systemStats.cpu_usage ?? 0}%`,
          description: 'System CPU utilization',
          icon: Cpu
        },
        {
          name: 'Memory Usage',
          status: (systemStats.memory_usage ?? 0) < 80 ? 'healthy' : (systemStats.memory_usage ?? 0) < 95 ? 'warning' : 'error',
          value: `${systemStats.memory_usage ?? 0}%`,
          description: 'System memory utilization',
          icon: HardDrive
        },
        {
          name: 'Uptime',
          status: 'healthy',
          value: systemStats.uptime || '0s',
          description: 'System uptime',
          icon: Clock
        }
      );
    }

    if (aiHealth) {
      metrics.push({
        name: 'AI Providers',
        status: aiHealth.healthy_providers === aiHealth.total_providers ? 'healthy' : 
                aiHealth.healthy_providers > 0 ? 'warning' : 'error',
        value: `${aiHealth.healthy_providers}/${aiHealth.total_providers}`,
        description: 'Healthy AI providers',
        icon: Brain
      });
    }

    if (systemHealth) {
      metrics.push({
        name: 'System Status',
        status: systemHealth.status === 'healthy' ? 'healthy' : 
                systemHealth.status === 'degraded' ? 'warning' : 'error',
        value: systemHealth.status,
        description: 'Overall system health',
        icon: Activity
      });
    }

    return metrics;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return CheckCircle2;
      case 'warning': return AlertTriangle;
      case 'error': return XCircle;
      default: return Activity;
    }
  };

  const healthMetrics = getHealthMetrics();
  const overallStatus = healthMetrics.some(m => m.status === 'error') ? 'error' :
                       healthMetrics.some(m => m.status === 'warning') ? 'warning' : 'healthy';

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              System Health
              <Badge className={getStatusColor(overallStatus)}>
                {React.createElement(getStatusIcon(overallStatus), { className: "h-3 w-3 mr-1" })}
                {overallStatus}
              </Badge>
            </CardTitle>
            <CardDescription>
              Real-time system monitoring • Last updated: {lastUpdated.toLocaleTimeString()}
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchHealthData}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {loading && !systemStats ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            Loading health data...
          </div>
        ) : (
          <div className="space-y-4">
            {/* Health Metrics Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {healthMetrics.map((metric, index) => {
                const IconComponent = metric.icon;
                const StatusIcon = getStatusIcon(metric.status);
                
                return (
                  <div key={index} className="flex items-center space-x-3 p-3 rounded-lg border">
                    <div className="flex-shrink-0">
                      <IconComponent className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <p className="text-sm font-medium text-gray-900">{metric.name}</p>
                        <StatusIcon className={`h-4 w-4 ${
                          metric.status === 'healthy' ? 'text-green-600' :
                          metric.status === 'warning' ? 'text-yellow-600' : 'text-red-600'
                        }`} />
                      </div>
                      <p className="text-lg font-semibold">{metric.value}</p>
                      <p className="text-xs text-muted-foreground">{metric.description}</p>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Task Statistics */}
            {systemStats?.task_statistics && (
              <div className="pt-4 border-t">
                <h4 className="text-sm font-medium mb-3">Task Statistics</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <p className="text-2xl font-bold text-blue-600">
                      {systemStats.task_statistics.queued_tasks}
                    </p>
                    <p className="text-sm text-blue-800">Queued Tasks</p>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <p className="text-2xl font-bold text-green-600">
                      {systemStats.task_statistics.completed_tasks}
                    </p>
                    <p className="text-sm text-green-800">Completed Tasks</p>
                  </div>
                </div>
              </div>
            )}

            {/* Resource Usage */}
            {systemStats?.resource_usage && (
              <div className="pt-4 border-t">
                <h4 className="text-sm font-medium mb-3">Resource Usage</h4>
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>CPU Usage</span>
                      <span>{systemStats.resource_usage.total_cpu_usage}%</span>
                    </div>
                    <Progress value={systemStats.resource_usage.total_cpu_usage} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Memory Usage</span>
                      <span>{systemStats.resource_usage.total_memory_mb} MB</span>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Avg per agent: {systemStats.resource_usage.average_memory_per_agent} MB
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Alerts */}
            {overallStatus !== 'healthy' && (
              <Alert className={overallStatus === 'error' ? 'border-red-200 bg-red-50' : 'border-yellow-200 bg-yellow-50'}>
                <AlertTriangle className={`h-4 w-4 ${overallStatus === 'error' ? 'text-red-600' : 'text-yellow-600'}`} />
                <AlertDescription className={overallStatus === 'error' ? 'text-red-800' : 'text-yellow-800'}>
                  {overallStatus === 'error' 
                    ? 'System issues detected. Please check the metrics above and take appropriate action.'
                    : 'System performance warnings detected. Monitor closely and consider optimization.'
                  }
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
