'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Activity, 
  Cpu, 
  HardDrive, 
  MemoryStick, 
  Network, 
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Server
} from 'lucide-react';

interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
    temperature: number;
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  disk: {
    used: number;
    total: number;
    percentage: number;
  };
  network: {
    upload: number;
    download: number;
    latency: number;
  };
  uptime: string;
  lastUpdated: string;
}

export function SystemHealthAgent() {
  const [metrics, setMetrics] = useState<SystemMetrics>({
    cpu: { usage: 23, cores: 8, temperature: 45 },
    memory: { used: 8.2, total: 16, percentage: 51 },
    disk: { used: 256, total: 512, percentage: 50 },
    network: { upload: 1.2, download: 5.8, latency: 12 },
    uptime: '2 days, 14 hours',
    lastUpdated: new Date().toLocaleTimeString()
  });

  const [isRefreshing, setIsRefreshing] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Simulate real-time data updates
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      setMetrics(prev => ({
        cpu: {
          usage: Math.max(5, Math.min(95, prev.cpu.usage + (Math.random() - 0.5) * 10)),
          cores: prev.cpu.cores,
          temperature: Math.max(30, Math.min(80, prev.cpu.temperature + (Math.random() - 0.5) * 5))
        },
        memory: {
          used: Math.max(2, Math.min(15, prev.memory.used + (Math.random() - 0.5) * 2)),
          total: prev.memory.total,
          percentage: Math.max(12, Math.min(94, prev.memory.percentage + (Math.random() - 0.5) * 8))
        },
        disk: {
          used: prev.disk.used,
          total: prev.disk.total,
          percentage: Math.max(30, Math.min(85, prev.disk.percentage + (Math.random() - 0.5) * 3))
        },
        network: {
          upload: Math.max(0.1, Math.min(10, prev.network.upload + (Math.random() - 0.5) * 2)),
          download: Math.max(0.5, Math.min(20, prev.network.download + (Math.random() - 0.5) * 4)),
          latency: Math.max(5, Math.min(50, prev.network.latency + (Math.random() - 0.5) * 8))
        },
        uptime: prev.uptime,
        lastUpdated: new Date().toLocaleTimeString()
      }));
    }, 3000);

    return () => clearInterval(interval);
  }, [autoRefresh]);

  const refreshMetrics = async () => {
    setIsRefreshing(true);
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setMetrics(prev => ({
      ...prev,
      cpu: { ...prev.cpu, usage: Math.floor(Math.random() * 80) + 10 },
      memory: { ...prev.memory, percentage: Math.floor(Math.random() * 70) + 20 },
      disk: { ...prev.disk, percentage: Math.floor(Math.random() * 60) + 30 },
      network: {
        upload: Math.random() * 8 + 0.5,
        download: Math.random() * 15 + 2,
        latency: Math.floor(Math.random() * 30) + 8
      },
      lastUpdated: new Date().toLocaleTimeString()
    }));
    
    setIsRefreshing(false);
  };

  const getStatusColor = (percentage: number) => {
    if (percentage < 50) return 'text-green-600';
    if (percentage < 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStatusBadge = (percentage: number) => {
    if (percentage < 50) return <Badge className="bg-green-100 text-green-800">Good</Badge>;
    if (percentage < 80) return <Badge className="bg-yellow-100 text-yellow-800">Warning</Badge>;
    return <Badge className="bg-red-100 text-red-800">Critical</Badge>;
  };

  const overallHealth = () => {
    const avg = (metrics.cpu.usage + metrics.memory.percentage + metrics.disk.percentage) / 3;
    if (avg < 50) return { status: 'Healthy', color: 'text-green-600', icon: CheckCircle };
    if (avg < 80) return { status: 'Warning', color: 'text-yellow-600', icon: AlertTriangle };
    return { status: 'Critical', color: 'text-red-600', icon: AlertTriangle };
  };

  const health = overallHealth();
  const HealthIcon = health.icon;

  return (
    <div className="space-y-8 animate-fade-in">
      {/* Stunning Header with Live Indicators */}
      <div className="text-center relative">
        <div className="absolute inset-0 bg-gradient-to-r from-green-600 via-blue-600 to-purple-600 rounded-3xl opacity-10 blur-xl animate-gradient-shift"></div>
        <div className="relative bg-white rounded-2xl shadow-2xl border border-gray-100 p-8 mx-4 hover-lift">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-gradient-to-r from-green-500 to-blue-600 text-white p-4 rounded-full shadow-lg animate-pulse-glow">
              <Activity className="h-8 w-8" />
            </div>
          </div>
          <h2 className="text-4xl font-bold bg-gradient-to-r from-green-600 via-blue-600 to-purple-600 bg-clip-text text-transparent mb-3">
            🏥 System Health Monitor
          </h2>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto leading-relaxed">
            Advanced real-time monitoring with AI-powered analytics and predictive insights
          </p>
          <div className="flex justify-center mt-4 space-x-2">
            <div className="h-1 w-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-full animate-shimmer"></div>
            <div className="h-1 w-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-shimmer stagger-1"></div>
            <div className="h-1 w-6 bg-gradient-to-r from-purple-500 to-green-500 rounded-full animate-shimmer stagger-2"></div>
          </div>
          <div className="flex justify-center mt-4 space-x-4">
            <div className="flex items-center gap-2 text-sm text-green-600 font-medium">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              Live Monitoring
            </div>
            <div className="flex items-center gap-2 text-sm text-blue-600 font-medium">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              AI Analytics
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Overall Status */}
      <Card className="bg-gradient-to-r from-white to-gray-50 shadow-xl border-0 hover-lift">
        <CardHeader className="bg-gradient-to-r from-gray-900 to-gray-800 text-white rounded-t-lg">
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="bg-white/20 p-2 rounded-lg">
                <HealthIcon className={`h-6 w-6 ${health.color === 'text-green-600' ? 'text-green-300' : health.color === 'text-yellow-600' ? 'text-yellow-300' : 'text-red-300'}`} />
              </div>
              <div>
                <div className="text-xl font-bold">System Status: {health.status}</div>
                <div className="text-sm text-gray-300">Advanced monitoring dashboard</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button
                onClick={() => setAutoRefresh(!autoRefresh)}
                variant="outline"
                size="sm"
                className="bg-white/10 border-white/20 text-white hover:bg-white/20"
              >
                {autoRefresh ? '⏸️ Pause' : '▶️ Resume'} Auto-refresh
              </Button>
              <Button
                onClick={refreshMetrics}
                disabled={isRefreshing}
                size="sm"
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </CardTitle>
          <CardDescription className="text-gray-300 mt-2">
            🕒 Last updated: {metrics.lastUpdated} | ⏱️ Uptime: {metrics.uptime}
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Enhanced Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* CPU Usage */}
        <Card className="group hover:shadow-2xl transition-all duration-500 border-0 shadow-lg bg-gradient-to-br from-white to-red-50 hover-lift animate-scale-in">
          <CardHeader className="pb-3 bg-gradient-to-r from-red-500 to-orange-500 text-white rounded-t-lg">
            <CardTitle className="flex items-center gap-2 text-sm">
              <div className="bg-white/20 p-1 rounded">
                <Cpu className="h-4 w-4" />
              </div>
              CPU Performance
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className={`text-3xl font-bold ${getStatusColor(metrics.cpu.usage)} animate-bounce-in`}>
                  {metrics.cpu.usage.toFixed(1)}%
                </span>
                <div className="animate-fade-in stagger-1">
                  {getStatusBadge(metrics.cpu.usage)}
                </div>
              </div>
              <div className="relative">
                <Progress value={metrics.cpu.usage} className="h-3 bg-gray-200 rounded-full overflow-hidden" />
                <div className="absolute inset-0 bg-gradient-to-r from-red-400 to-orange-400 rounded-full animate-shimmer opacity-30"></div>
              </div>
              <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded-lg">
                🔧 {metrics.cpu.cores} cores • 🌡️ {metrics.cpu.temperature}°C
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Memory Usage */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2 text-sm">
              <MemoryStick className="h-4 w-4" />
              Memory Usage
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className={`text-2xl font-bold ${getStatusColor(metrics.memory.percentage)}`}>
                  {metrics.memory.percentage.toFixed(1)}%
                </span>
                {getStatusBadge(metrics.memory.percentage)}
              </div>
              <Progress value={metrics.memory.percentage} className="h-2" />
              <div className="text-xs text-gray-500">
                {metrics.memory.used.toFixed(1)} GB / {metrics.memory.total} GB
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Disk Usage */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2 text-sm">
              <HardDrive className="h-4 w-4" />
              Disk Usage
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className={`text-2xl font-bold ${getStatusColor(metrics.disk.percentage)}`}>
                  {metrics.disk.percentage.toFixed(1)}%
                </span>
                {getStatusBadge(metrics.disk.percentage)}
              </div>
              <Progress value={metrics.disk.percentage} className="h-2" />
              <div className="text-xs text-gray-500">
                {metrics.disk.used} GB / {metrics.disk.total} GB
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Network Activity */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2 text-sm">
              <Network className="h-4 w-4" />
              Network I/O
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-2xl font-bold text-blue-600">
                  {metrics.network.latency}ms
                </span>
                <Badge className="bg-blue-100 text-blue-800">Active</Badge>
              </div>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>↑ Upload:</span>
                  <span>{metrics.network.upload.toFixed(1)} MB/s</span>
                </div>
                <div className="flex justify-between">
                  <span>↓ Download:</span>
                  <span>{metrics.network.download.toFixed(1)} MB/s</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Monitoring */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* System Processes */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Server className="h-5 w-5" />
              Top Processes
            </CardTitle>
            <CardDescription>
              Most resource-intensive processes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                { name: 'node.js', cpu: 15.2, memory: 2.1 },
                { name: 'chrome.exe', cpu: 8.7, memory: 1.8 },
                { name: 'python.exe', cpu: 5.3, memory: 0.9 },
                { name: 'docker.exe', cpu: 3.1, memory: 0.7 },
                { name: 'vscode.exe', cpu: 2.8, memory: 1.2 }
              ].map((process, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <span className="font-medium text-sm">{process.name}</span>
                  <div className="flex gap-4 text-xs text-gray-600">
                    <span>CPU: {process.cpu}%</span>
                    <span>RAM: {process.memory} GB</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* System Alerts */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              System Alerts
            </CardTitle>
            <CardDescription>
              Recent system notifications and warnings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                { type: 'info', message: 'System backup completed successfully', time: '2 min ago' },
                { type: 'warning', message: 'High memory usage detected', time: '15 min ago' },
                { type: 'success', message: 'Security scan completed', time: '1 hour ago' },
                { type: 'info', message: 'System update available', time: '3 hours ago' }
              ].map((alert, index) => (
                <div key={index} className="flex items-start gap-3 p-2 bg-gray-50 rounded">
                  <div className={`w-2 h-2 rounded-full mt-2 ${
                    alert.type === 'success' ? 'bg-green-500' :
                    alert.type === 'warning' ? 'bg-yellow-500' :
                    alert.type === 'error' ? 'bg-red-500' : 'bg-blue-500'
                  }`} />
                  <div className="flex-1">
                    <p className="text-sm">{alert.message}</p>
                    <p className="text-xs text-gray-500">{alert.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Trends */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Performance Summary
          </CardTitle>
          <CardDescription>
            24-hour performance overview
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">98.7%</div>
              <div className="text-sm text-green-700">Uptime</div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">42.3%</div>
              <div className="text-sm text-blue-700">Avg CPU Usage</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">1.2 GB</div>
              <div className="text-sm text-purple-700">Peak Memory</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
