'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  MessageCircle, 
  Send, 
  Bot, 
  User, 
  Mic, 
  Settings,
  Download,
  Trash2
} from 'lucide-react';

interface ChatMessage {
  id: string;
  type: 'user' | 'agent';
  content: string;
  timestamp: string;
}

interface ChatAgentProps {
  agentName?: string;
  agentDescription?: string;
  capabilities?: string[];
}

export function ChatAgent({ 
  agentName = "AI Assistant", 
  agentDescription = "I'm here to help you with various tasks and questions.",
  capabilities = ["general_assistance", "information_retrieval", "task_automation"]
}: ChatAgentProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'agent',
      content: `Hello! I'm ${agentName}. ${agentDescription} How can I assist you today?`,
      timestamp: new Date().toISOString()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const generateResponse = (userMessage: string): string => {
    const responses = [
      `I understand you're asking about "${userMessage}". Let me help you with that.`,
      `That's an interesting question about "${userMessage}". Based on my knowledge, I can provide some insights.`,
      `Regarding "${userMessage}", I can assist you with several approaches to address this.`,
      `Thank you for asking about "${userMessage}". Here's what I can tell you about this topic.`,
      `I see you're interested in "${userMessage}". Let me break this down for you.`,
      `Great question about "${userMessage}"! I have some useful information that might help.`,
      `For "${userMessage}", I can provide you with detailed guidance and recommendations.`
    ];

    // Add capability-specific responses
    if (capabilities.includes('tax_calculation') && userMessage.toLowerCase().includes('tax')) {
      return `I can help you with tax calculations and advice. For detailed tax calculations, please use the tax calculator feature or ask specific questions about tax rates, deductions, or filing requirements.`;
    }

    if (capabilities.includes('system_monitoring') && userMessage.toLowerCase().includes('system')) {
      return `I can monitor system health and performance. Current system status shows all metrics are within normal ranges. Would you like me to check specific system components?`;
    }

    if (capabilities.includes('data_analysis') && userMessage.toLowerCase().includes('data')) {
      return `I can help analyze data and provide insights. Please share your data or describe what kind of analysis you need, and I'll guide you through the process.`;
    }

    return responses[Math.floor(Math.random() * responses.length)];
  };

  const sendMessage = async () => {
    if (!inputMessage.trim() || isTyping) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage.trim(),
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    const messageContent = inputMessage.trim();
    setInputMessage('');
    setIsTyping(true);

    // Simulate typing delay
    setTimeout(() => {
      const agentResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'agent',
        content: generateResponse(messageContent),
        timestamp: new Date().toISOString()
      };

      setMessages(prev => [...prev, agentResponse]);
      setIsTyping(false);
    }, 1000 + Math.random() * 2000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const clearChat = () => {
    setMessages([
      {
        id: '1',
        type: 'agent',
        content: `Hello! I'm ${agentName}. ${agentDescription} How can I assist you today?`,
        timestamp: new Date().toISOString()
      }
    ]);
  };

  const exportChat = () => {
    const chatText = messages.map(msg => 
      `[${new Date(msg.timestamp).toLocaleTimeString()}] ${msg.type === 'user' ? 'You' : agentName}: ${msg.content}`
    ).join('\n');
    
    const blob = new Blob([chatText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat-${agentName}-${new Date().toISOString().split('T')[0]}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const startVoiceInput = () => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition;
      const recognition = new SpeechRecognition();
      
      recognition.continuous = false;
      recognition.interimResults = false;
      recognition.lang = 'en-US';

      recognition.onstart = () => {
        setIsListening(true);
      };

      recognition.onresult = (event: any) => {
        const transcript = event.results[0][0].transcript;
        setInputMessage(transcript);
        setIsListening(false);
      };

      recognition.onerror = () => {
        setIsListening(false);
      };

      recognition.onend = () => {
        setIsListening(false);
      };

      recognition.start();
    } else {
      alert('Speech recognition is not supported in your browser');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          💬 {agentName}
        </h2>
        <p className="text-gray-600 mb-4">
          {agentDescription}
        </p>
        <div className="flex justify-center gap-2 flex-wrap">
          {capabilities.map((capability, index) => (
            <Badge key={index} variant="secondary" className="text-xs">
              {capability.replace('_', ' ')}
            </Badge>
          ))}
        </div>
      </div>

      {/* Chat Interface */}
      <Card className="h-[600px] flex flex-col">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <MessageCircle className="h-5 w-5" />
                Chat Interface
              </CardTitle>
              <CardDescription>
                Have a conversation with {agentName}
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button onClick={exportChat} variant="outline" size="sm">
                <Download className="h-4 w-4" />
              </Button>
              <Button onClick={clearChat} variant="outline" size="sm">
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        {/* Messages */}
        <CardContent className="flex-1 flex flex-col">
          <div className="flex-1 overflow-y-auto space-y-4 mb-4 p-2">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] rounded-lg px-4 py-3 ${
                    message.type === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-900 border'
                  }`}
                >
                  <div className="flex items-center gap-2 mb-1">
                    {message.type === 'user' ? (
                      <User className="h-3 w-3" />
                    ) : (
                      <Bot className="h-3 w-3" />
                    )}
                    <span className="text-xs opacity-75 font-medium">
                      {message.type === 'user' ? 'You' : agentName}
                    </span>
                    <span className="text-xs opacity-50">
                      {new Date(message.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                  <p className="text-sm leading-relaxed">{message.content}</p>
                </div>
              </div>
            ))}
            
            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-gray-100 border rounded-lg px-4 py-3 max-w-[80%]">
                  <div className="flex items-center gap-2 mb-1">
                    <Bot className="h-3 w-3" />
                    <span className="text-xs text-gray-500 font-medium">
                      {agentName} is typing...
                    </span>
                  </div>
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          <div className="border-t pt-4">
            <div className="flex gap-2">
              <Input
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={`Message ${agentName}...`}
                className="flex-1"
                disabled={isTyping}
              />
              <Button
                onClick={startVoiceInput}
                variant="outline"
                size="icon"
                disabled={isTyping || isListening}
                className={isListening ? 'bg-red-100 border-red-300' : ''}
              >
                <Mic className={`h-4 w-4 ${isListening ? 'text-red-600' : ''}`} />
              </Button>
              <Button 
                onClick={sendMessage} 
                disabled={!inputMessage.trim() || isTyping}
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
              <span>Press Enter to send, Shift+Enter for new line</span>
              <span>{messages.length - 1} messages</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Quick Actions
          </CardTitle>
          <CardDescription>
            Common tasks and questions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
            {[
              "What can you help me with?",
              "Show me your capabilities",
              "How do I get started?",
              "What are your main features?",
              "Can you explain how you work?",
              "What kind of tasks can you perform?"
            ].map((question, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                className="text-left justify-start h-auto p-3"
                onClick={() => setInputMessage(question)}
              >
                <span className="text-xs">{question}</span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
