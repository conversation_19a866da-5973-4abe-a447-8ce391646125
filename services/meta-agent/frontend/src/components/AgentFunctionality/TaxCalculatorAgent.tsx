'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Calculator, DollarSign, FileText, TrendingUp } from 'lucide-react';

interface TaxCalculation {
  grossIncome: number;
  deductions: number;
  taxableIncome: number;
  totalTax: number;
  afterTaxIncome: number;
  effectiveRate: number;
  details: string;
}

export function TaxCalculatorAgent() {
  const [country, setCountry] = useState<'india' | 'australia'>('india');
  const [income, setIncome] = useState<string>('');
  const [deductions, setDeductions] = useState<string>('');
  const [status, setStatus] = useState<'individual' | 'married' | 'senior'>('individual');
  const [result, setResult] = useState<TaxCalculation | null>(null);

  const calculateTax = () => {
    const grossIncome = parseFloat(income) || 0;
    const totalDeductions = parseFloat(deductions) || 0;

    if (grossIncome <= 0) {
      alert('Please enter a valid income amount');
      return;
    }

    const taxableIncome = Math.max(0, grossIncome - totalDeductions);
    let tax = 0;
    let details = '';

    if (country === 'india') {
      if (taxableIncome <= 250000) {
        tax = 0;
        details = 'No tax (income below ₹2.5 lakh threshold)';
      } else if (taxableIncome <= 500000) {
        tax = (taxableIncome - 250000) * 0.05;
        details = '5% on income above ₹2.5 lakh';
      } else if (taxableIncome <= 1000000) {
        tax = 12500 + (taxableIncome - 500000) * 0.20;
        details = '₹12,500 + 20% on income above ₹5 lakh';
      } else {
        tax = 112500 + (taxableIncome - 1000000) * 0.30;
        details = '₹1,12,500 + 30% on income above ₹10 lakh';
      }
    } else {
      if (taxableIncome <= 18200) {
        tax = 0;
        details = 'Tax-free threshold (no tax below AUD 18,200)';
      } else if (taxableIncome <= 45000) {
        tax = (taxableIncome - 18200) * 0.19;
        details = '19% on income above AUD 18,200';
      } else if (taxableIncome <= 120000) {
        tax = 5092 + (taxableIncome - 45000) * 0.325;
        details = 'AUD 5,092 + 32.5% on income above AUD 45,000';
      } else if (taxableIncome <= 180000) {
        tax = 29467 + (taxableIncome - 120000) * 0.37;
        details = 'AUD 29,467 + 37% on income above AUD 120,000';
      } else {
        tax = 51667 + (taxableIncome - 180000) * 0.45;
        details = 'AUD 51,667 + 45% on income above AUD 180,000';
      }
    }

    const afterTaxIncome = taxableIncome - tax;
    const effectiveRate = grossIncome > 0 ? (tax / grossIncome) * 100 : 0;

    setResult({
      grossIncome,
      deductions: totalDeductions,
      taxableIncome,
      totalTax: tax,
      afterTaxIncome,
      effectiveRate,
      details
    });
  };

  const clearCalculation = () => {
    setIncome('');
    setDeductions('');
    setResult(null);
  };

  const currency = country === 'india' ? '₹' : 'AUD ';

  return (
    <div className="space-y-8 animate-fade-in">
      {/* Stunning Header with Gradient */}
      <div className="text-center relative">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-green-600 rounded-3xl opacity-10 blur-xl"></div>
        <div className="relative bg-white rounded-2xl shadow-xl border border-gray-100 p-8 mx-4">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-full shadow-lg">
              <Calculator className="h-8 w-8" />
            </div>
          </div>
          <h2 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-green-600 bg-clip-text text-transparent mb-3">
            🇮🇳🇦🇺 India-Australia Tax Calculator
          </h2>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto leading-relaxed">
            Professional tax calculation engine with real-time processing and detailed breakdowns for both countries
          </p>
          <div className="flex justify-center mt-4 space-x-2">
            <div className="h-1 w-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
            <div className="h-1 w-8 bg-gradient-to-r from-purple-500 to-green-500 rounded-full"></div>
            <div className="h-1 w-6 bg-gradient-to-r from-green-500 to-blue-500 rounded-full"></div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Enhanced Input Form */}
        <Card className="group hover:shadow-2xl transition-all duration-500 border-0 shadow-xl bg-gradient-to-br from-white to-blue-50">
          <CardHeader className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-t-lg">
            <CardTitle className="flex items-center gap-3 text-xl">
              <div className="bg-white/20 p-2 rounded-lg">
                <Calculator className="h-6 w-6" />
              </div>
              Tax Calculation Engine
            </CardTitle>
            <CardDescription className="text-blue-100">
              Enter your income details for professional tax analysis
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6 p-6">
            <div className="space-y-3">
              <label className="block text-sm font-semibold text-gray-700 mb-2">Select Country</label>
              <div className="relative">
                <select
                  value={country}
                  onChange={(e) => setCountry(e.target.value as 'india' | 'australia')}
                  className="w-full p-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300 bg-white shadow-sm hover:shadow-md text-lg font-medium"
                >
                  <option value="india">🇮🇳 India - Income Tax Calculator</option>
                  <option value="australia">🇦🇺 Australia - Tax Assessment</option>
                </select>
                <div className="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-green-600" />
                Annual Income ({currency})
              </label>
              <div className="relative">
                <Input
                  type="number"
                  value={income}
                  onChange={(e) => setIncome(e.target.value)}
                  placeholder="Enter your annual income"
                  min="0"
                  className="pl-12 pr-4 py-4 text-lg font-medium border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-green-500/20 focus:border-green-500 transition-all duration-300 hover:shadow-md"
                />
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-green-600 font-bold">
                  {currency}
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
                <FileText className="h-4 w-4 text-blue-600" />
                Deductions ({currency})
              </label>
              <div className="relative">
                <Input
                  type="number"
                  value={deductions}
                  onChange={(e) => setDeductions(e.target.value)}
                  placeholder="Enter total deductions"
                  min="0"
                  className="pl-12 pr-4 py-4 text-lg font-medium border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300 hover:shadow-md"
                />
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-blue-600 font-bold">
                  {currency}
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <label className="block text-sm font-semibold text-gray-700 mb-2">Filing Status</label>
              <select
                value={status}
                onChange={(e) => setStatus(e.target.value as any)}
                className="w-full p-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-purple-500/20 focus:border-purple-500 transition-all duration-300 bg-white shadow-sm hover:shadow-md text-lg font-medium"
              >
                <option value="individual">👤 Individual Taxpayer</option>
                <option value="married">💑 Married Filing</option>
                <option value="senior">👴 Senior Citizen</option>
              </select>
            </div>

            <div className="flex gap-4 pt-4">
              <Button
                onClick={calculateTax}
                className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                size="lg"
              >
                <Calculator className="h-5 w-5 mr-2" />
                Calculate Tax
              </Button>
              <Button
                onClick={clearCalculation}
                variant="outline"
                className="px-6 py-4 rounded-xl border-2 hover:bg-gray-50 transition-all duration-300"
              >
                Clear
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Results */}
        <Card className="group hover:shadow-2xl transition-all duration-500 border-0 shadow-xl bg-gradient-to-br from-white to-green-50">
          <CardHeader className="bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-t-lg">
            <CardTitle className="flex items-center gap-3 text-xl">
              <div className="bg-white/20 p-2 rounded-lg">
                <TrendingUp className="h-6 w-6" />
              </div>
              Tax Calculation Results
            </CardTitle>
            <CardDescription className="text-green-100">
              Professional tax analysis with detailed breakdowns
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6">
            {result ? (
              <div className="space-y-6 animate-fade-in">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-2xl border border-blue-200 shadow-lg">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="bg-blue-600 text-white p-2 rounded-lg">
                      <DollarSign className="h-5 w-5" />
                    </div>
                    <h4 className="font-bold text-blue-900 text-lg">
                      Tax Calculation for {country.toUpperCase()}
                    </h4>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-white p-4 rounded-xl shadow-sm border border-blue-100">
                      <div className="text-xs text-blue-600 font-semibold mb-1">GROSS INCOME</div>
                      <div className="text-xl font-bold text-blue-900">
                        {currency}{result.grossIncome.toLocaleString()}
                      </div>
                    </div>
                    <div className="bg-white p-4 rounded-xl shadow-sm border border-blue-100">
                      <div className="text-xs text-blue-600 font-semibold mb-1">DEDUCTIONS</div>
                      <div className="text-xl font-bold text-blue-900">
                        {currency}{result.deductions.toLocaleString()}
                      </div>
                    </div>
                    <div className="bg-white p-4 rounded-xl shadow-sm border border-blue-100">
                      <div className="text-xs text-blue-600 font-semibold mb-1">TAXABLE INCOME</div>
                      <div className="text-xl font-bold text-blue-900">
                        {currency}{result.taxableIncome.toLocaleString()}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-6 rounded-2xl border border-yellow-200 shadow-lg">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="bg-yellow-600 text-white p-2 rounded-lg">
                      <Calculator className="h-5 w-5" />
                    </div>
                    <h4 className="font-bold text-yellow-900 text-lg">Tax Calculation Method</h4>
                  </div>
                  <div className="bg-white p-4 rounded-xl shadow-sm border border-yellow-100 mb-4">
                    <p className="text-sm text-yellow-800 leading-relaxed">{result.details}</p>
                  </div>
                  <div className="bg-gradient-to-r from-yellow-600 to-orange-600 text-white p-4 rounded-xl shadow-lg">
                    <div className="text-sm font-semibold mb-1">TOTAL TAX LIABILITY</div>
                    <div className="text-3xl font-bold">
                      {currency}{Math.round(result.totalTax).toLocaleString()}
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-2xl border border-green-200 shadow-lg">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="bg-green-600 text-white p-2 rounded-lg">
                      <TrendingUp className="h-5 w-5" />
                    </div>
                    <h4 className="font-bold text-green-900 text-lg">Final Tax Assessment</h4>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-white p-4 rounded-xl shadow-sm border border-green-100">
                      <div className="text-xs text-green-600 font-semibold mb-1">AFTER-TAX INCOME</div>
                      <div className="text-2xl font-bold text-green-700">
                        {currency}{Math.round(result.afterTaxIncome).toLocaleString()}
                      </div>
                    </div>
                    <div className="bg-white p-4 rounded-xl shadow-sm border border-green-100">
                      <div className="text-xs text-green-600 font-semibold mb-1">EFFECTIVE TAX RATE</div>
                      <div className="text-2xl font-bold text-green-700">
                        {result.effectiveRate.toFixed(2)}%
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded">
                  ⚠️ Note: This is a simplified calculation for demonstration purposes. 
                  Consult a qualified tax professional for accurate tax advice.
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Calculator className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Enter your income details and click "Calculate Tax" to see results</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Tax Rate Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              🇮🇳 India Tax Rates
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>₹0 - ₹2.5L:</span>
                <Badge variant="secondary">0%</Badge>
              </div>
              <div className="flex justify-between">
                <span>₹2.5L - ₹5L:</span>
                <Badge variant="secondary">5%</Badge>
              </div>
              <div className="flex justify-between">
                <span>₹5L - ₹10L:</span>
                <Badge variant="secondary">20%</Badge>
              </div>
              <div className="flex justify-between">
                <span>Above ₹10L:</span>
                <Badge variant="secondary">30%</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              🇦🇺 Australia Tax Rates
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>AUD 0 - 18.2K:</span>
                <Badge variant="secondary">0%</Badge>
              </div>
              <div className="flex justify-between">
                <span>AUD 18.2K - 45K:</span>
                <Badge variant="secondary">19%</Badge>
              </div>
              <div className="flex justify-between">
                <span>AUD 45K - 120K:</span>
                <Badge variant="secondary">32.5%</Badge>
              </div>
              <div className="flex justify-between">
                <span>AUD 120K - 180K:</span>
                <Badge variant="secondary">37%</Badge>
              </div>
              <div className="flex justify-between">
                <span>Above AUD 180K:</span>
                <Badge variant="secondary">45%</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
