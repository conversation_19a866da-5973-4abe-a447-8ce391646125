/**
 * System Health Monitoring Component
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  CheckCircle2,
  AlertTriangle,
  XCircle,
  Activity,
  RefreshCw,
  Wifi,
  WifiOff,
  Server,
  Database
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useSystemApi } from '@/lib/api-client/hooks';
import type { SystemHealthResponse } from '@/api-generated/api';

export function SystemHealth() {
  const [healthData, setHealthData] = useState<SystemHealthResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const systemApi = useSystemApi();

  useEffect(() => {
    loadHealthData();
    const interval = setInterval(loadHealthData, 30000); // Refresh every 30s
    return () => clearInterval(interval);
  }, []);

  const loadHealthData = async () => {
    try {
      setError(null);
      const response = await systemApi.getSystemHealthApiV1RuntimeSystemHealthGet();
      setHealthData(response.data);
    } catch (error) {
      console.error('Failed to load system health:', error);
      setError('Failed to load system health');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle2 className="h-5 w-5 text-green-500" />;
      case 'degraded':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'unhealthy':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'no_agents':
        return <Server className="h-5 w-5 text-gray-400" />;
      default:
        return <Activity className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'border-green-200 bg-green-50/50';
      case 'degraded':
        return 'border-yellow-200 bg-yellow-50/50';
      case 'unhealthy':
        return 'border-red-200 bg-red-50/50';
      case 'no_agents':
        return 'border-gray-200 bg-gray-50/50';
      default:
        return 'border-gray-200 bg-gray-50/50';
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'default';
      case 'degraded':
        return 'secondary';
      case 'unhealthy':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5 animate-pulse" />
            System Health
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-4 bg-muted rounded w-1/2"></div>
            <div className="h-4 bg-muted rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-700">
            <XCircle className="h-5 w-5" />
            System Health - Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={loadHealthData} size="sm" variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!healthData) {
    return null;
  }

  return (
    <Card className={cn("transition-colors", getStatusColor(healthData?.status || 'unknown'))}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getStatusIcon(healthData?.status || 'unknown')}
            System Health
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={getStatusVariant(healthData?.status || 'unknown') as any} className="capitalize">
              {healthData?.status?.replace('_', ' ') || 'Unknown'}
            </Badge>
            <Button onClick={loadHealthData} size="sm" variant="ghost">
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </CardTitle>
        <CardDescription>
          Real-time system and agent health monitoring
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Agent Health Summary */}
        <div>
          <h4 className="font-medium mb-3">Agent Health</h4>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {Object.values(healthData?.components || {}).filter(c => (c as any)?.status === 'healthy').length}
              </div>
              <div className="text-sm text-muted-foreground">Healthy</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {Object.values(healthData?.components || {}).filter(c => (c as any)?.status === 'unhealthy').length}
              </div>
              <div className="text-sm text-muted-foreground">Unhealthy</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {Object.keys(healthData?.components || {}).length}
              </div>
              <div className="text-sm text-muted-foreground">Total</div>
            </div>
          </div>
        </div>

        {/* Message Queue Status */}
        <div>
          <h4 className="font-medium mb-3">Infrastructure</h4>
          <div className="flex items-center justify-between p-3 border rounded-lg">
            <div className="flex items-center gap-2">
              {healthData?.components ? (
                <Wifi className="h-4 w-4 text-green-500" />
              ) : (
                <WifiOff className="h-4 w-4 text-red-500" />
              )}
              <span className="font-medium">Components</span>
            </div>
            <Badge
              variant={healthData?.components ? 'default' : 'destructive'}
              className="capitalize"
            >
              {healthData?.components ? 'operational' : 'unknown'}
            </Badge>
          </div>
        </div>

        {/* System Information */}
        <div>
          <h4 className="font-medium mb-3">System Information</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Version:</span>
              <span className="ml-2 font-mono">{healthData?.version || '1.0.0'}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Uptime:</span>
              <span className="ml-2 font-mono">{healthData?.uptime || 'N/A'}</span>
            </div>
            <div>
              <span className="text-muted-foreground">CPU Usage:</span>
              <span className="ml-2 font-mono">{typeof healthData?.cpu_usage === 'number' ? healthData.cpu_usage.toFixed(1) : (healthData?.cpu_usage || 0)}%</span>
            </div>
            <div>
              <span className="text-muted-foreground">Memory:</span>
              <span className="ml-2 font-mono">{healthData?.memory_mb || 0}MB ({typeof healthData?.memory_usage === 'number' ? healthData.memory_usage.toFixed(1) : (healthData?.memory_usage || 0)}%)</span>
            </div>
          </div>
        </div>

        {/* Health Summary Message */}
        <div className="pt-2 border-t text-sm text-muted-foreground">
          {healthData.status === 'healthy' && (
            <p>✅ All systems operational</p>
          )}
          {healthData.status === 'degraded' && (
            <p>⚠️ Some components experiencing issues</p>
          )}
          {healthData.status === 'unhealthy' && (
            <p>❌ Multiple system issues detected</p>
          )}
          {Object.keys(healthData?.components || {}).length === 0 && (
            <p>ℹ️ No components monitored</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}