/**
 * AI Status Component - Shows AI provider health and status
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Brain,
  CheckCircle2,
  XCircle,
  AlertCircle,
  RefreshCw,
  Loader2,
  Sparkles
} from 'lucide-react';
import type { SystemHealthResponse } from '@/api-generated/api';
import { useSystemApi } from '@/lib/api-client/hooks';

export function AIStatus() {
  const [healthData, setHealthData] = useState<SystemHealthResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const systemApi = useSystemApi();

  useEffect(() => {
    loadAIHealth();
    // Refresh every 30 seconds
    const interval = setInterval(loadAIHealth, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadAIHealth = async () => {
    try {
      setError(null);
      if (!loading) setLoading(true); // Only show loading on initial load

      // Mock data matching SystemHealthResponse schema - to be replaced with real API call
      const health: SystemHealthResponse = {
        status: 'healthy',
        version: '1.0.0',
        uptime: '5d 12h 34m',
        cpu_usage: 45.2,
        memory_usage: 68.5,
        memory_mb: 2048,
        components: {
          'openai': {},
          'anthropic': {}
        }
      };
      setHealthData(health);
    } catch (error) {
      console.error('Failed to load AI health:', error);
      setError('Failed to load AI status');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case 'unhealthy':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'degraded':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'unhealthy':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'degraded':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  if (loading && !healthData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI Providers
          </CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI Providers
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={loadAIHealth}
            className="mt-3"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!healthData) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5" />
          System Health
          <Badge variant="outline" className="ml-auto">
            v{healthData.version}
          </Badge>
        </CardTitle>
        <CardDescription>
          System health and service status
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Overall Status */}
        <div className={`flex items-center gap-2 p-3 rounded-lg border ${getStatusColor(healthData.status)}`}>
          {getStatusIcon(healthData.status)}
          <span className="font-medium capitalize">
            {healthData.status === 'healthy' ? 'All Systems Operational' : 
             healthData.status === 'degraded' ? 'Some Issues Detected' :
             'System Unhealthy'}
          </span>
        </div>

        {/* System Metrics */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <h4 className="text-sm font-medium">System Info</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Uptime:</span>
                <span>{healthData.uptime}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Version:</span>
                <span>{healthData.version}</span>
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Resource Usage</h4>
            <div className="space-y-1 text-sm">
              {healthData.cpu_usage && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">CPU:</span>
                  <span>{healthData.cpu_usage.toFixed(1)}%</span>
                </div>
              )}
              {healthData.memory_usage && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Memory:</span>
                  <span>{healthData.memory_usage.toFixed(1)}%</span>
                </div>
              )}
              {healthData.memory_mb && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Memory:</span>
                  <span>{healthData.memory_mb} MB</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Components Status */}
        {healthData.components && Object.keys(healthData.components).length > 0 ? (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Components</h4>
            {Object.keys(healthData.components).map((component) => (
              <div key={component} className="flex items-center justify-between p-2 rounded border bg-muted/20">
                <div className="flex items-center gap-2">
                  {getStatusIcon('healthy')}
                  <span className="font-medium capitalize">
                    {component}
                  </span>
                </div>
                <Badge variant="default" className="text-xs">
                  operational
                </Badge>
              </div>
            ))}
          </div>
        ) : (
          <Alert>
            <Sparkles className="h-4 w-4" />
            <AlertDescription>
              No components are currently monitored.
            </AlertDescription>
          </Alert>
        )}

        {/* Refresh Button */}
        <Button 
          variant="outline" 
          size="sm" 
          onClick={loadAIHealth}
          disabled={loading}
          className="w-full"
        >
          {loading ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4 mr-2" />
          )}
          Refresh Status
        </Button>
      </CardContent>
    </Card>
  );
}