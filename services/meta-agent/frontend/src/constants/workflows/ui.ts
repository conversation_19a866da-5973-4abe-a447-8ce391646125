export const UI_CONSTANTS = {
  SIDEBAR: {
    WIDTH: 300,
    MIN_WIDTH: 250,
    MAX_WIDTH: 500,
  },
  
  TOOLBAR: {
    HEIGHT: 60,
  },
  
  MINIMAP: {
    WIDTH: 200,
    HEIGHT: 150,
    POSITION: 'bottom-right' as const,
  },
  
  ZOOM_CONTROLS: {
    POSITION: 'bottom-left' as const,
  },
  
  KEYBOARD_SHORTCUTS: {
    SAVE: 'ctrl+s',
    UNDO: 'ctrl+z',
    REDO: 'ctrl+y',
    DELETE: 'delete',
    COPY: 'ctrl+c',
    PASTE: 'ctrl+v',
    SELECT_ALL: 'ctrl+a',
    FIT_VIEW: 'ctrl+shift+f',
    ZOOM_IN: 'ctrl+=',
    ZOOM_OUT: 'ctrl+-',
  } as const,
  
  ANIMATIONS: {
    DURATION: {
      SHORT: 150,
      MEDIUM: 300,
      LONG: 500,
    },
    EASING: 'ease-in-out',
  },
  
  BREAKPOINTS: {
    SM: 640,
    MD: 768,
    LG: 1024,
    XL: 1280,
  },
} as const;

export const THEME_COLORS = {
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    900: '#1e3a8a',
  },
  secondary: {
    50: '#f8fafc',
    100: '#f1f5f9',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    900: '#0f172a',
  },
  success: {
    50: '#f0fdf4',
    500: '#22c55e',
    600: '#16a34a',
  },
  warning: {
    50: '#fffbeb',
    500: '#f59e0b',
    600: '#d97706',
  },
  error: {
    50: '#fef2f2',
    500: '#ef4444',
    600: '#dc2626',
  },
} as const;