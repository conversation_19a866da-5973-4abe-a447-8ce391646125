export const NODE_CONSTANTS = {
  DEFAULT_SIZE: {
    width: 200,
    height: 100,
  },
  
  HANDLE_STYLES: {
    source: {
      background: '#555',
      width: 8,
      height: 8,
    },
    target: {
      background: '#555',
      width: 8,
      height: 8,
    },
  },
  
  VALIDATION_STATES: {
    VALID: 'valid',
    INVALID: 'invalid',
    WARNING: 'warning',
    BUILDING: 'building',
    BUILT: 'built',
  } as const,
  
  FIELD_TYPES: {
    TEXT: 'str',
    INT: 'int',
    FLOAT: 'float',
    BOOL: 'bool',
    FILE: 'file',
    CODE: 'code',
    PROMPT: 'prompt',
    NESTED_DICT: 'NestedDict',
    DICT: 'dict',
    MULTI_OPTION: 'multi_option',
  } as const,
  
  BASE_CLASSES: {
    COMPONENT: 'Component',
    CUSTOM_COMPONENT: 'CustomComponent',
    CHAIN: 'Chain',
    AGENT: 'Agent',
    TOOL: 'Tool',
    MEMORY: 'Memory',
    VECTORSTORE: 'VectorStore',
    EMBEDDINGS: 'Embeddings',
    LLM: 'LLM',
    RETRIEVER: 'Retriever',
    DOCUMENT_LOADER: 'DocumentLoader',
    TEXT_SPLITTER: 'TextSplitter',
    OUTPUT_PARSER: 'OutputParser',
    PROMPT: 'PromptTemplate',
  } as const,
} as const;

export const NODE_COLORS = {
  default: '#1a192b',
  selected: '#2d3748',
  invalid: '#e53e3e',
  building: '#ed8936',
  built: '#38a169',
  warning: '#d69e2e',
} as const;

export const HANDLE_COLORS = {
  default: '#cbd5e0',
  connected: '#4299e1',
  compatible: '#68d391',
  incompatible: '#fc8181',
} as const;