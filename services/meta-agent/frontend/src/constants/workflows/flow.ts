export const FLOW_CONSTANTS = {
  DEFAULT_VIEWPORT: {
    x: 0,
    y: 0,
    zoom: 1,
  },
  
  GRID_SIZE: 20,
  
  AUTO_SAVE_INTERVAL: 30000, // 30 seconds
  
  MAX_ZOOM: 2,
  MIN_ZOOM: 0.1,
  
  FLOW_VALIDATION: {
    MIN_NAME_LENGTH: 1,
    MAX_NAME_LENGTH: 100,
    MAX_DESCRIPTION_LENGTH: 500,
  },
  
  EXECUTION_STATUS: {
    RUNNING: 'running',
    COMPLETED: 'completed',
    FAILED: 'failed',
    CANCELLED: 'cancelled',
  } as const,
  
  FLOW_STATUS: {
    DRAFT: 'draft',
    PUBLISHED: 'published',
    ARCHIVED: 'archived',
  } as const,
} as const;

export const DEFAULT_FLOW_DATA = {
  nodes: [],
  edges: [],
  viewport: FLOW_CONSTANTS.DEFAULT_VIEWPORT,
};

export const FLOW_ERRORS = {
  INVALID_FLOW_DATA: 'Invalid flow data structure',
  MISSING_REQUIRED_FIELDS: 'Missing required fields',
  CIRCULAR_DEPENDENCY: 'Circular dependency detected',
  INVALID_CONNECTION: 'Invalid node connection',
} as const;