export const EDGE_CONSTANTS = {
  TYPES: {
    DEFAULT: 'default',
    CUSTOM: 'custom',
    SMOOTHSTEP: 'smoothstep',
    STEP: 'step',
    STRAIGHT: 'straight',
  } as const,
  
  STYLES: {
    default: {
      stroke: '#b1b1b7',
      strokeWidth: 2,
    },
    selected: {
      stroke: '#4299e1',
      strokeWidth: 3,
    },
    animated: {
      stroke: '#4299e1',
      strokeWidth: 2,
      strokeDasharray: '5,5',
    },
    invalid: {
      stroke: '#fc8181',
      strokeWidth: 2,
      strokeDasharray: '3,3',
    },
  },
  
  MARKERS: {
    arrow: {
      id: 'arrow',
      type: 'arrowclosed',
      markerWidth: 20,
      markerHeight: 20,
      orient: 'auto',
    },
  },
  
  CONNECTION_RULES: {
    ALLOW_SELF_CONNECTION: false,
    ALLOW_MULTIPLE_EDGES: false,
    VALIDATE_TYPES: true,
  },
} as const;

export const CONNECTION_STATES = {
  IDLE: 'idle',
  CONNECTING: 'connecting',
  VALID: 'valid',
  INVALID: 'invalid',
} as const;