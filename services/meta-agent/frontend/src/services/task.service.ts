import { apiService } from './api';

export interface Task {
  id: string;
  title: string;
  description?: string;
  type: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  agent_id?: string;
  agent_name?: string;
  orchestration_id?: string;
  orchestration_name?: string;
  progress: number;
  created_at: string;
  updated_at: string;
  estimated_duration?: number;
  actual_duration?: number;
  error_message?: string;
  result_summary?: string;
  input_data?: Record<string, any>;
  config?: Record<string, any>;
  auto_retry: boolean;
  max_retries: number;
  timeout_seconds: number;
  retry_count?: number;
}

export interface TaskCreateData {
  title: string;
  description?: string;
  type: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  agent_id?: string;
  orchestration_id?: string;
  estimated_duration: number;
  auto_retry: boolean;
  max_retries: number;
  timeout_seconds: number;
  input_data?: Record<string, any>;
  config?: Record<string, any>;
}

export interface TaskUpdateData {
  title?: string;
  description?: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  agent_id?: string;
  orchestration_id?: string;
  estimated_duration?: number;
  auto_retry?: boolean;
  max_retries?: number;
  timeout_seconds?: number;
  input_data?: Record<string, any>;
  config?: Record<string, any>;
}

export interface TasksResponse {
  tasks: Task[];
  total: number;
  page?: number;
  per_page?: number;
}

export interface TaskExecutionResult {
  task_id: string;
  status: string;
  result?: any;
  error?: string;
  execution_time?: number;
  memory_usage?: number;
  cpu_usage?: number;
}

class TaskService {
  private baseUrl = '/api/v1/tasks';

  async getTasks(params?: {
    page?: number;
    per_page?: number;
    status?: string;
    priority?: string;
    type?: string;
    agent_id?: string;
    orchestration_id?: string;
  }): Promise<TasksResponse> {
    const searchParams = new URLSearchParams();
    
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.per_page) searchParams.append('per_page', params.per_page.toString());
    if (params?.status) searchParams.append('status', params.status);
    if (params?.priority) searchParams.append('priority', params.priority);
    if (params?.type) searchParams.append('type', params.type);
    if (params?.agent_id) searchParams.append('agent_id', params.agent_id);
    if (params?.orchestration_id) searchParams.append('orchestration_id', params.orchestration_id);

    const url = searchParams.toString() ? `${this.baseUrl}?${searchParams}` : this.baseUrl;
    const response = await apiService.get(url);
    return response.data as TasksResponse;
  }

  async getTask(id: string): Promise<Task> {
    const response = await apiService.get(`${this.baseUrl}/${id}`);
    return response.data as Task;
  }

  async createTask(data: TaskCreateData): Promise<Task> {
    const response = await apiService.post(this.baseUrl, data);
    return response.data as Task;
  }

  async updateTask(id: string, data: TaskUpdateData): Promise<Task> {
    const response = await apiService.put(`${this.baseUrl}/${id}`, data);
    return response.data as Task;
  }

  async deleteTask(id: string): Promise<void> {
    await apiService.delete(`${this.baseUrl}/${id}`);
  }

  async startTask(id: string): Promise<{ success: boolean; message?: string }> {
    const response = await apiService.post(`${this.baseUrl}/${id}/start`);
    return response.data as { success: boolean; message?: string };
  }

  async pauseTask(id: string): Promise<{ success: boolean; message?: string }> {
    const response = await apiService.post(`${this.baseUrl}/${id}/pause`);
    return response.data as { success: boolean; message?: string };
  }

  async resumeTask(id: string): Promise<{ success: boolean; message?: string }> {
    const response = await apiService.post(`${this.baseUrl}/${id}/resume`);
    return response.data as { success: boolean; message?: string };
  }

  async stopTask(id: string): Promise<{ success: boolean; message?: string }> {
    const response = await apiService.post(`${this.baseUrl}/${id}/stop`);
    return response.data as { success: boolean; message?: string };
  }

  async cancelTask(id: string): Promise<{ success: boolean; message?: string }> {
    const response = await apiService.post(`${this.baseUrl}/${id}/cancel`);
    return response.data as { success: boolean; message?: string };
  }

  async retryTask(id: string): Promise<{ success: boolean; message?: string }> {
    const response = await apiService.post(`${this.baseUrl}/${id}/retry`);
    return response.data as { success: boolean; message?: string };
  }

  async getTaskProgress(id: string): Promise<{
    progress: number;
    status: string;
    current_step?: string;
    total_steps?: number;
    completed_steps?: number;
    estimated_remaining?: number;
  }> {
    const response = await apiService.get(`${this.baseUrl}/${id}/progress`);
    return response.data as any;
  }

  async getTaskLogs(id: string, params?: {
    level?: 'debug' | 'info' | 'warning' | 'error';
    limit?: number;
    offset?: number;
  }): Promise<{
    logs: Array<{
      timestamp: string;
      level: string;
      message: string;
      source?: string;
    }>;
    total: number;
  }> {
    const searchParams = new URLSearchParams();
    if (params?.level) searchParams.append('level', params.level);
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.offset) searchParams.append('offset', params.offset.toString());

    const url = searchParams.toString() 
      ? `${this.baseUrl}/${id}/logs?${searchParams}` 
      : `${this.baseUrl}/${id}/logs`;
    
    const response = await apiService.get(url);
    return response.data as any;
  }

  async getTaskResult(id: string): Promise<TaskExecutionResult> {
    const response = await apiService.get(`${this.baseUrl}/${id}/result`);
    return response.data as any;
  }

  async getTaskMetrics(id: string): Promise<{
    execution_time: number;
    memory_usage: number;
    cpu_usage: number;
    network_io?: number;
    disk_io?: number;
    error_count: number;
    retry_count: number;
  }> {
    const response = await apiService.get(`${this.baseUrl}/${id}/metrics`);
    return response.data as any;
  }

  async cloneTask(id: string, overrides?: Partial<TaskCreateData>): Promise<Task> {
    const response = await apiService.post(`${this.baseUrl}/${id}/clone`, overrides || {});
    return response.data as any;
  }

  async batchUpdateTasks(taskIds: string[], data: TaskUpdateData): Promise<{
    success: boolean;
    updated: number;
    failed: number;
    errors?: Array<{ task_id: string; error: string }>;
  }> {
    const response = await apiService.put(`${this.baseUrl}/batch`, {
      task_ids: taskIds,
      ...data,
    });
    return response.data as any;
  }

  async batchDeleteTasks(taskIds: string[]): Promise<{
    success: boolean;
    deleted: number;
    failed: number;
    errors?: Array<{ task_id: string; error: string }>;
  }> {
    const response = await apiService.post(`${this.baseUrl}/batch/delete`, { task_ids: taskIds });
    return response.data as any;
  }

  async getTaskTemplates(): Promise<Array<{
    id: string;
    name: string;
    description: string;
    type: string;
    default_config: Record<string, any>;
    input_schema?: Record<string, any>;
  }>> {
    const response = await apiService.get(`${this.baseUrl}/templates`);
    return (response.data as any).templates;
  }

  async createTaskFromTemplate(templateId: string, data: {
    title: string;
    description?: string;
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    agent_id?: string;
    orchestration_id?: string;
    input_data?: Record<string, any>;
    config_overrides?: Record<string, any>;
  }): Promise<Task> {
    const response = await apiService.post(`${this.baseUrl}/templates/${templateId}/create`, data);
    return response.data as any;
  }

  async getTaskStatistics(params?: {
    start_date?: string;
    end_date?: string;
    agent_id?: string;
    orchestration_id?: string;
  }): Promise<{
    total_tasks: number;
    completed_tasks: number;
    failed_tasks: number;
    running_tasks: number;
    pending_tasks: number;
    cancelled_tasks: number;
    average_execution_time: number;
    success_rate: number;
    task_types: Record<string, number>;
    priority_distribution: Record<string, number>;
    hourly_distribution: Array<{ hour: number; count: number }>;
    daily_distribution: Array<{ date: string; count: number }>;
  }> {
    const searchParams = new URLSearchParams();
    if (params?.start_date) searchParams.append('start_date', params.start_date);
    if (params?.end_date) searchParams.append('end_date', params.end_date);
    if (params?.agent_id) searchParams.append('agent_id', params.agent_id);
    if (params?.orchestration_id) searchParams.append('orchestration_id', params.orchestration_id);

    const url = searchParams.toString() 
      ? `${this.baseUrl}/statistics?${searchParams}` 
      : `${this.baseUrl}/statistics`;
    
    const response = await apiService.get(url);
    return response.data as any;
  }
}

export const taskService = new TaskService();