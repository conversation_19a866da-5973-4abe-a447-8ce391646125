/**
 * Workflow Service Hooks - React Query integration for workflow API
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getWorkflowApi } from './workflowApi';
import type {
  Workflow,
  WorkflowTemplate,
  WorkflowExecution,
  WorkflowComponent,
  CreateWorkflowRequest,
  UpdateWorkflowRequest,
  ExecuteWorkflowRequest,
} from '@/types/api';

// Query Keys
export const WORKFLOW_QUERY_KEYS = {
  all: ['workflows'] as const,
  lists: () => [...WORKFLOW_QUERY_KEYS.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...WORKFLOW_QUERY_KEYS.lists(), filters] as const,
  details: () => [...WORKFLOW_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...WORKFLOW_QUERY_KEYS.details(), id] as const,
  executions: (workflowId?: string) => [...WORKFLOW_QUERY_KEYS.all, 'executions', workflowId] as const,
  templates: () => ['workflow-templates'] as const,
  templateList: (filters: Record<string, any>) => [...WORKFLOW_QUERY_KEYS.templates(), 'list', filters] as const,
  components: () => ['workflow-components'] as const,
  componentList: (filters: Record<string, any>) => [...WORKFLOW_QUERY_KEYS.components(), 'list', filters] as const,
} as const;

// Workflow CRUD Hooks

export const useWorkflows = (params?: {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  tags?: string[];
  status?: string;
  enabled?: boolean;
}) => {
  const { enabled = true, ...queryParams } = params || {};
  
  return useQuery({
    queryKey: WORKFLOW_QUERY_KEYS.list(queryParams),
    queryFn: () => getWorkflowApi().getWorkflows(queryParams),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useWorkflow = (id: string, enabled = true) => {
  return useQuery({
    queryKey: WORKFLOW_QUERY_KEYS.detail(id),
    queryFn: () => getWorkflowApi().getWorkflow(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000,
  });
};

export const useCreateWorkflow = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateWorkflowRequest) => getWorkflowApi().createWorkflow(data),
    onSuccess: (newWorkflow) => {
      // Invalidate and update workflows list
      queryClient.invalidateQueries({ queryKey: WORKFLOW_QUERY_KEYS.lists() });
      
      // Add the new workflow to the cache
      queryClient.setQueryData(WORKFLOW_QUERY_KEYS.detail(newWorkflow.id), newWorkflow);
    },
  });
};

export const useUpdateWorkflow = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateWorkflowRequest }) =>
      getWorkflowApi().updateWorkflow(id, data),
    onSuccess: (updatedWorkflow) => {
      // Update the specific workflow in cache
      queryClient.setQueryData(WORKFLOW_QUERY_KEYS.detail(updatedWorkflow.id), updatedWorkflow);
      
      // Invalidate workflows list to reflect changes
      queryClient.invalidateQueries({ queryKey: WORKFLOW_QUERY_KEYS.lists() });
    },
  });
};

export const useDeleteWorkflow = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => getWorkflowApi().deleteWorkflow(id),
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: WORKFLOW_QUERY_KEYS.detail(deletedId) });
      
      // Invalidate lists to reflect deletion
      queryClient.invalidateQueries({ queryKey: WORKFLOW_QUERY_KEYS.lists() });
    },
  });
};

export const useDuplicateWorkflow = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, name }: { id: string; name?: string }) =>
      getWorkflowApi().duplicateWorkflow(id, name),
    onSuccess: (duplicatedWorkflow) => {
      // Add duplicated workflow to cache
      queryClient.setQueryData(WORKFLOW_QUERY_KEYS.detail(duplicatedWorkflow.id), duplicatedWorkflow);
      
      // Invalidate lists to show new workflow
      queryClient.invalidateQueries({ queryKey: WORKFLOW_QUERY_KEYS.lists() });
    },
  });
};

// Workflow Execution Hooks

export const useExecuteWorkflow = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (request: ExecuteWorkflowRequest) => getWorkflowApi().executeWorkflow(request),
    onSuccess: (execution) => {
      // Invalidate executions list
      queryClient.invalidateQueries({ 
        queryKey: WORKFLOW_QUERY_KEYS.executions(execution.workflow_id) 
      });
    },
  });
};

export const useWorkflowExecutions = (workflowId?: string, params?: {
  page?: number;
  limit?: number;
  status?: string;
  enabled?: boolean;
}) => {
  const { enabled = true, ...queryParams } = params || {};
  
  return useQuery({
    queryKey: WORKFLOW_QUERY_KEYS.executions(workflowId),
    queryFn: () => getWorkflowApi().getWorkflowExecutions(workflowId, queryParams),
    enabled,
    refetchInterval: 5000, // Refetch every 5 seconds for real-time updates
  });
};

export const useWorkflowExecution = (id: string, enabled = true) => {
  return useQuery({
    queryKey: [...WORKFLOW_QUERY_KEYS.all, 'execution', id],
    queryFn: () => getWorkflowApi().getWorkflowExecution(id),
    enabled: enabled && !!id,
    refetchInterval: 3000, // 3 seconds for all active executions - we'll handle completion logic elsewhere
  });
};

export const useCancelWorkflowExecution = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => getWorkflowApi().cancelWorkflowExecution(id),
    onSuccess: (_, executionId) => {
      // Invalidate executions to refresh status
      queryClient.invalidateQueries({ queryKey: WORKFLOW_QUERY_KEYS.executions() });
      queryClient.invalidateQueries({ queryKey: [...WORKFLOW_QUERY_KEYS.all, 'execution', executionId] });
    },
  });
};

// Template Hooks

export const useWorkflowTemplates = (params?: {
  category?: string;
  tags?: string[];
  difficulty?: string;
  search?: string;
  enabled?: boolean;
}) => {
  const { enabled = true, ...queryParams } = params || {};
  
  return useQuery({
    queryKey: WORKFLOW_QUERY_KEYS.templateList(queryParams),
    queryFn: () => getWorkflowApi().getWorkflowTemplates(queryParams),
    enabled,
    staleTime: 10 * 60 * 1000, // 10 minutes (templates don't change often)
  });
};

export const useWorkflowTemplate = (id: string, enabled = true) => {
  return useQuery({
    queryKey: [...WORKFLOW_QUERY_KEYS.templates(), 'detail', id],
    queryFn: () => getWorkflowApi().getWorkflowTemplate(id),
    enabled: enabled && !!id,
    staleTime: 10 * 60 * 1000,
  });
};

export const useCreateWorkflowFromTemplate = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ templateId, data }: { 
      templateId: string; 
      data: { name: string; description?: string } 
    }) => getWorkflowApi().createWorkflowFromTemplate(templateId, data),
    onSuccess: (newWorkflow) => {
      // Add new workflow to cache
      queryClient.setQueryData(WORKFLOW_QUERY_KEYS.detail(newWorkflow.id), newWorkflow);
      
      // Invalidate workflows list
      queryClient.invalidateQueries({ queryKey: WORKFLOW_QUERY_KEYS.lists() });
    },
  });
};

// Component Hooks

export const useWorkflowComponents = (params?: {
  category?: string;
  search?: string;
  enabled?: boolean;
}) => {
  const { enabled = true, ...queryParams } = params || {};
  
  return useQuery({
    queryKey: WORKFLOW_QUERY_KEYS.componentList(queryParams),
    queryFn: () => getWorkflowApi().getWorkflowComponents(queryParams),
    enabled,
    staleTime: 30 * 60 * 1000, // 30 minutes (components are relatively stable)
  });
};

export const useWorkflowComponent = (type: string, enabled = true) => {
  return useQuery({
    queryKey: [...WORKFLOW_QUERY_KEYS.components(), 'detail', type],
    queryFn: () => getWorkflowApi().getWorkflowComponent(type),
    enabled: enabled && !!type,
    staleTime: 30 * 60 * 1000,
  });
};

// Validation Hook

export const useValidateWorkflow = () => {
  return useMutation({
    mutationFn: (workflowData: { nodes: any[]; edges: any[] }) =>
      getWorkflowApi().validateWorkflow(workflowData),
  });
};

// Import/Export Hooks

export const useExportWorkflow = () => {
  return useMutation({
    mutationFn: ({ id, format }: { id: string; format?: 'json' | 'yaml' }) =>
      getWorkflowApi().exportWorkflow(id, format),
  });
};

export const useImportWorkflow = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (file: File) => getWorkflowApi().importWorkflow(file),
    onSuccess: (importedWorkflow) => {
      // Add imported workflow to cache
      queryClient.setQueryData(WORKFLOW_QUERY_KEYS.detail(importedWorkflow.id), importedWorkflow);
      
      // Invalidate workflows list
      queryClient.invalidateQueries({ queryKey: WORKFLOW_QUERY_KEYS.lists() });
    },
  });
};

// Agent-Workflow Integration Hooks

export const useAgentWorkflows = (agentId: string, enabled = true) => {
  return useQuery({
    queryKey: ['agents', agentId, 'workflows'],
    queryFn: () => getWorkflowApi().getAgentWorkflows(agentId),
    enabled: enabled && !!agentId,
    staleTime: 5 * 60 * 1000,
  });
};

export const useAttachWorkflowToAgent = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ agentId, workflowId, options }: {
      agentId: string;
      workflowId: string;
      options?: { is_primary?: boolean; auto_execute?: boolean };
    }) => getWorkflowApi().attachWorkflowToAgent(agentId, workflowId, options),
    onSuccess: (_, { agentId }) => {
      // Invalidate agent workflows
      queryClient.invalidateQueries({ queryKey: ['agents', agentId, 'workflows'] });
    },
  });
};

export const useDetachWorkflowFromAgent = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ agentId, workflowId }: { agentId: string; workflowId: string }) =>
      getWorkflowApi().detachWorkflowFromAgent(agentId, workflowId),
    onSuccess: (_, { agentId }) => {
      // Invalidate agent workflows
      queryClient.invalidateQueries({ queryKey: ['agents', agentId, 'workflows'] });
    },
  });
};

// Sharing Hooks

export const useShareWorkflow = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: {
      id: string;
      data: { is_public: boolean; permissions?: string[] };
    }) => getWorkflowApi().shareWorkflow(id, data),
    onSuccess: (_, { id }) => {
      // Invalidate workflow detail to refresh sharing status
      queryClient.invalidateQueries({ queryKey: WORKFLOW_QUERY_KEYS.detail(id) });
    },
  });
};

export const useWorkflowPermissions = (id: string, enabled = true) => {
  return useQuery({
    queryKey: [...WORKFLOW_QUERY_KEYS.all, 'permissions', id],
    queryFn: () => getWorkflowApi().getWorkflowPermissions(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000,
  });
};