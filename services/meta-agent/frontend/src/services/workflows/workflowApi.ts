/**
 * Workflow API Service - Integrates with meta-agent API-first approach
 */

import type {
  Workflow,
  WorkflowTemplate,
  WorkflowExecution,
  WorkflowComponent,
  CreateWorkflowRequest,
  UpdateWorkflowRequest,
  ExecuteWorkflowRequest,
  ListResponse,
  SuccessResponse,
} from '@/types/api';

// Import the main API service
import { apiService, type ApiService } from '@/services/api';

export class WorkflowApiService {
  private api: ApiService;

  constructor(apiServiceInstance: ApiService) {
    this.api = apiServiceInstance;
  }

  // Workflow CRUD operations
  async getWorkflows(params?: {
    page?: number;
    limit?: number;
    search?: string;
    category?: string;
    tags?: string[];
    status?: string;
  }): Promise<ListResponse<Workflow>> {
    const response = await this.api.get<ListResponse<Workflow>>('/workflows', params);
    return response.data;
  }

  async getWorkflow(id: string): Promise<Workflow> {
    const response = await this.api.get<Workflow>(`/workflows/${id}`);
    return response.data;
  }

  async createWorkflow(data: CreateWorkflowRequest): Promise<Workflow> {
    const response = await this.api.post<Workflow>('/workflows', data);
    return response.data;
  }

  async updateWorkflow(id: string, data: UpdateWorkflowRequest): Promise<Workflow> {
    const response = await this.api.put<Workflow>(`/workflows/${id}`, data);
    return response.data;
  }

  async deleteWorkflow(id: string): Promise<SuccessResponse> {
    const response = await this.api.delete<SuccessResponse>(`/workflows/${id}`);
    return response.data;
  }

  async duplicateWorkflow(id: string, name?: string): Promise<Workflow> {
    const response = await this.api.post<Workflow>(`/workflows/${id}/duplicate`, { name });
    return response.data;
  }

  // Workflow execution operations
  async executeWorkflow(request: ExecuteWorkflowRequest): Promise<WorkflowExecution> {
    const response = await this.api.post<WorkflowExecution>('/workflows/execute', request);
    return response.data;
  }

  async getWorkflowExecutions(workflowId?: string, params?: {
    page?: number;
    limit?: number;
    status?: string;
  }): Promise<ListResponse<WorkflowExecution>> {
    const url = workflowId 
      ? `/workflows/${workflowId}/executions`
      : '/workflow-executions';
    const response = await this.api.get<ListResponse<WorkflowExecution>>(url, params);
    return response.data;
  }

  async getWorkflowExecution(id: string): Promise<WorkflowExecution> {
    const response = await this.api.get<WorkflowExecution>(`/workflow-executions/${id}`);
    return response.data;
  }

  async cancelWorkflowExecution(id: string): Promise<SuccessResponse> {
    const response = await this.api.post<SuccessResponse>(`/workflow-executions/${id}/cancel`);
    return response.data;
  }

  // Workflow templates
  async getWorkflowTemplates(params?: {
    category?: string;
    tags?: string[];
    difficulty?: string;
    search?: string;
  }): Promise<ListResponse<WorkflowTemplate>> {
    const response = await this.api.get<ListResponse<WorkflowTemplate>>('/workflow-templates', params);
    return response.data;
  }

  async getWorkflowTemplate(id: string): Promise<WorkflowTemplate> {
    const response = await this.api.get<WorkflowTemplate>(`/workflow-templates/${id}`);
    return response.data;
  }

  async createWorkflowFromTemplate(templateId: string, data: {
    name: string;
    description?: string;
  }): Promise<Workflow> {
    const response = await this.api.post<Workflow>(`/workflow-templates/${templateId}/create`, data);
    return response.data;
  }

  // Workflow components/node types
  async getWorkflowComponents(params?: {
    category?: string;
    search?: string;
  }): Promise<ListResponse<WorkflowComponent>> {
    const response = await this.api.get<ListResponse<WorkflowComponent>>('/workflow-components', params);
    return response.data;
  }

  async getWorkflowComponent(type: string): Promise<WorkflowComponent> {
    const response = await this.api.get<WorkflowComponent>(`/workflow-components/${type}`);
    return response.data;
  }

  // Workflow validation
  async validateWorkflow(workflowData: { nodes: any[]; edges: any[] }): Promise<{
    valid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const response = await this.api.post<{
      valid: boolean;
      errors: string[];
      warnings: string[];
    }>('/workflows/validate', workflowData);
    return response.data;
  }

  // Import/Export workflows
  async exportWorkflow(id: string, format: 'json' | 'yaml' = 'json'): Promise<Blob> {
    const response = await this.api.get<Blob>(`/workflows/${id}/export?format=${format}`);
    return response.data;
  }

  async importWorkflow(file: File): Promise<Workflow> {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await this.api.post<Workflow>('/workflows/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  // Agent-Workflow integration
  async getAgentWorkflows(agentId: string): Promise<ListResponse<Workflow>> {
    const response = await this.api.get<ListResponse<Workflow>>(`/agents/${agentId}/workflows`);
    return response.data;
  }

  async attachWorkflowToAgent(agentId: string, workflowId: string, options?: {
    is_primary?: boolean;
    auto_execute?: boolean;
  }): Promise<SuccessResponse> {
    const response = await this.api.post<SuccessResponse>(
      `/agents/${agentId}/workflows/${workflowId}`,
      options
    );
    return response.data;
  }

  async detachWorkflowFromAgent(agentId: string, workflowId: string): Promise<SuccessResponse> {
    const response = await this.api.delete<SuccessResponse>(
      `/agents/${agentId}/workflows/${workflowId}`
    );
    return response.data;
  }

  // Workflow sharing and permissions
  async shareWorkflow(id: string, data: {
    is_public: boolean;
    permissions?: string[];
  }): Promise<SuccessResponse> {
    const response = await this.api.put<SuccessResponse>(`/workflows/${id}/share`, data);
    return response.data;
  }

  async getWorkflowPermissions(id: string): Promise<{
    can_read: boolean;
    can_write: boolean;
    can_execute: boolean;
    can_share: boolean;
  }> {
    const response = await this.api.get<{
      can_read: boolean;
      can_write: boolean;
      can_execute: boolean;
      can_share: boolean;
    }>(`/workflows/${id}/permissions`);
    return response.data;
  }
}

// Create and export singleton instance using the existing API service
const workflowApiService = new WorkflowApiService(apiService);

export const getWorkflowApi = (): WorkflowApiService => {
  return workflowApiService;
};

// For backward compatibility and easier imports
export { WorkflowApiService as default };