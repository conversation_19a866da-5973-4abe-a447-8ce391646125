/**
 * AI Agent Platform - Authentication Service
 */

import { apiService } from './api';
import {
  User,
  LoginRequest,
  RegisterRequest,
  TokenResponse,
} from '@/types/api';

class AuthService {
  async login(credentials: LoginRequest): Promise<User> {
    const params = new URLSearchParams();
    params.append('username', credentials.username);
    params.append('password', credentials.password);

    const response = await apiService.post<TokenResponse>(
      '/auth/login',
      params,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );
    apiService.setTokens(response.data);

    // Get user data after login
    const userResponse = await apiService.get<User>('/auth/me');
    return userResponse.data;
  }

  async register(data: RegisterRequest): Promise<User> {
    const response = await apiService.post<User>('/auth/register', data);
    return response.data;
  }

  async getCurrentUser(): Promise<User> {
    const response = await apiService.get<User>('/auth/me');
    return response.data;
  }

  async updateProfile(data: Partial<User>): Promise<User> {
    const response = await apiService.put<User>('/auth/me', data);
    return response.data;
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    await apiService.post('/auth/change-password', {
      current_password: currentPassword,
      new_password: newPassword,
    });
  }

  logout() {
    apiService.logout();
  }

  isAuthenticated(): boolean {
    return !!apiService.getToken();
  }
}

export const authService = new AuthService();