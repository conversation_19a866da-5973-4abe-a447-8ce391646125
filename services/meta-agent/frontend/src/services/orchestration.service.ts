/**
 * AI Agent Platform - Orchestration Service
 */

import { apiService } from './api';
import {
  Orchestration,
  CreateOrchestrationRequest,
  ListResponse,
  SuccessResponse,
  OrchestrationPattern,
} from '@/types/api';

class OrchestrationService {
  async listOrchestrations(params?: {
    status?: string;
    pattern?: OrchestrationPattern;
    limit?: number;
    offset?: number;
  }): Promise<ListResponse<Orchestration>> {
    const response = await apiService.get<{
      orchestrations: Orchestration[];
      total: number;
      limit: number;
      offset: number;
    }>('/orchestrations', params);

    return {
      items: response.data.orchestrations,
      total: response.data.total,
      limit: response.data.limit,
      offset: response.data.offset,
    };
  }

  async getOrchestration(id: string): Promise<Orchestration> {
    const response = await apiService.get<Orchestration>(`/orchestrations/${id}`);
    return response.data;
  }

  async createOrchestration(data: CreateOrchestrationRequest): Promise<Orchestration> {
    const response = await apiService.post<Orchestration>('/orchestrations', data);
    return response.data;
  }

  async updateOrchestration(id: string, data: Partial<Orchestration>): Promise<Orchestration> {
    const response = await apiService.put<Orchestration>(`/orchestrations/${id}`, data);
    return response.data;
  }

  async deleteOrchestration(id: string): Promise<void> {
    await apiService.delete(`/orchestrations/${id}`);
  }

  async startOrchestration(id: string): Promise<SuccessResponse> {
    const response = await apiService.post<SuccessResponse>(`/orchestrations/${id}/start`);
    return response.data;
  }

  async stopOrchestration(id: string, graceful: boolean = true): Promise<SuccessResponse> {
    const response = await apiService.post<SuccessResponse>(
      `/orchestrations/${id}/stop?graceful=${graceful}`
    );
    return response.data;
  }

  async pauseOrchestration(id: string): Promise<SuccessResponse> {
    const response = await apiService.post<SuccessResponse>(`/orchestrations/${id}/pause`);
    return response.data;
  }

  async resumeOrchestration(id: string): Promise<SuccessResponse> {
    const response = await apiService.post<SuccessResponse>(`/orchestrations/${id}/resume`);
    return response.data;
  }

  async getOrchestrationProgress(id: string): Promise<any> {
    const response = await apiService.get(`/orchestrations/${id}/progress`);
    return response.data;
  }

  async addAgentToOrchestration(id: string, agentId: string, role: string = 'worker'): Promise<SuccessResponse> {
    const response = await apiService.post<SuccessResponse>(
      `/orchestrations/${id}/agents`,
      { agent_id: agentId, role }
    );
    return response.data;
  }

  async removeAgentFromOrchestration(id: string, agentId: string): Promise<void> {
    await apiService.delete(`/orchestrations/${id}/agents/${agentId}`);
  }
}

export const orchestrationService = new OrchestrationService();