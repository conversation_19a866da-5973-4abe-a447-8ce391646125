/**
 * AI Agent Platform - API Service for Tests
 * Node.js compatible version that stores tokens in memory
 */

import axios, { AxiosInstance, AxiosError } from 'axios';
import { TokenResponse, ErrorResponse } from '@/types/api';

const API_URL = process.env.VITE_API_URL || 'http://localhost:8000';

// In-memory token storage for Node.js environment
class TokenStorage {
  private tokens: { [key: string]: string } = {};

  set(key: string, value: string) {
    this.tokens[key] = value;
  }

  get(key: string): string | undefined {
    return this.tokens[key];
  }

  remove(key: string) {
    delete this.tokens[key];
  }

  clear() {
    this.tokens = {};
  }
}

const storage = new TokenStorage();
const TOKEN_KEY = 'access_token';
const REFRESH_TOKEN_KEY = 'refresh_token';

class ApiServiceTest {
  private api: AxiosInstance;
  private refreshPromise: Promise<void> | null = null;

  constructor() {
    this.api = axios.create({
      baseURL: `${API_URL}/api/v1`,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add token
    this.api.interceptors.request.use(
      (config) => {
        const token = this.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        
        // If data is FormData or URLSearchParams, remove the default content-type to let axios set it automatically
        if (config.data instanceof FormData || config.data instanceof URLSearchParams) {
          delete config.headers['Content-Type'];
        }
        
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for token refresh
    this.api.interceptors.response.use(
      (response) => response,
      async (error: AxiosError) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && originalRequest) {
          // Check if we're already refreshing
          if (!this.refreshPromise) {
            this.refreshPromise = this.refreshToken();
          }

          try {
            await this.refreshPromise;
            this.refreshPromise = null;
            
            // Retry original request with new token
            const token = this.getToken();
            if (token && originalRequest.headers) {
              originalRequest.headers.Authorization = `Bearer ${token}`;
            }
            return this.api(originalRequest);
          } catch (refreshError) {
            this.refreshPromise = null;
            this.logout();
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      }
    );
  }

  // Token management
  getToken(): string | null {
    return storage.get(TOKEN_KEY) || null;
  }

  getRefreshToken(): string | null {
    return storage.get(REFRESH_TOKEN_KEY) || null;
  }

  setTokens(tokens: TokenResponse) {
    storage.set(TOKEN_KEY, tokens.access_token);
    storage.set(REFRESH_TOKEN_KEY, tokens.refresh_token);
  }

  clearTokens() {
    storage.remove(TOKEN_KEY);
    storage.remove(REFRESH_TOKEN_KEY);
  }

  async refreshToken(): Promise<void> {
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await axios.post<TokenResponse>(
        `${API_URL}/api/v1/auth/refresh`,
        { refresh_token: refreshToken }
      );
      
      this.setTokens(response.data);
    } catch (error) {
      this.clearTokens();
      throw error;
    }
  }

  logout() {
    this.clearTokens();
  }

  // API methods
  get<T>(url: string, params?: any) {
    return this.api.get<T>(url, { params });
  }

  post<T>(url: string, data?: any) {
    return this.api.post<T>(url, data);
  }

  put<T>(url: string, data?: any) {
    return this.api.put<T>(url, data);
  }

  delete<T>(url: string) {
    return this.api.delete<T>(url);
  }

  // Handle errors
  isApiError(error: any): error is AxiosError<ErrorResponse> {
    return error.isAxiosError === true;
  }

  getErrorMessage(error: any): string {
    if (this.isApiError(error)) {
      const apiError = error.response?.data;
      return apiError?.detail || apiError?.error || 'An error occurred';
    }
    return error.message || 'An unexpected error occurred';
  }
}

export const apiService = new ApiServiceTest();