import { Routes, Route, Navigate } from 'react-router-dom'
import { Providers } from '@/components/providers'
import { useAuth } from '@/contexts/AuthContext'

// Import pages
import HomePage from '@/pages/HomePage'
import DashboardPage from '@/pages/DashboardPage'
import AgentsPage from '@/pages/AgentsPage'
import AgentDetailPage from '@/pages/AgentDetailPage'
import CreateAgentPage from '@/pages/CreateAgentPage'
import TasksPage from '@/pages/TasksPage'
import TaskDetailPage from '@/pages/TaskDetailPage'
import CreateTaskPage from '@/pages/CreateTaskPage'
import WorkflowsPage from '@/pages/WorkflowsPage'
import CreateWorkflowPage from '@/pages/CreateWorkflowPage'
import AutomationsPage from '@/pages/AutomationsPage'
import OrchestrationsPage from '@/pages/OrchestrationsPage'
import ConnectionsPage from '@/pages/ConnectionsPage'
import IntelligencePage from '@/pages/IntelligencePage'
import SettingsPage from '@/pages/SettingsPage'
import GeneratorPage from '@/pages/GeneratorPage'
import MigrationPage from '@/pages/MigrationPage'
import TestPage from '@/pages/TestPage'
import TestEmbeddedPage from '@/pages/TestEmbeddedPage'
import DeployedAgentPage from '@/pages/DeployedAgentPage'

// Protected route wrapper
function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, loading } = useAuth()

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="space-y-4 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="text-lg">Loading AI Agent Platform...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return <Navigate to="/" replace />
  }

  return <>{children}</>
}

function App() {
  return (
    <Providers>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route
          path="/dashboard"
          element={<DashboardPage />}
        />
        <Route path="/agents" element={<AgentsPage />} />
        <Route path="/agents/create" element={<CreateAgentPage />} />
        <Route path="/agents/:id" element={<AgentDetailPage />} />
        <Route path="/tasks" element={<TasksPage />} />
        <Route path="/tasks/create" element={<CreateTaskPage />} />
        <Route path="/tasks/:id" element={<TaskDetailPage />} />
        <Route path="/workflows" element={<WorkflowsPage />} />
        <Route path="/workflows/create" element={<CreateWorkflowPage />} />
        <Route path="/automations" element={<AutomationsPage />} />
        <Route path="/orchestrations" element={<OrchestrationsPage />} />
        <Route path="/connections" element={<ConnectionsPage />} />
        <Route path="/intelligence" element={<IntelligencePage />} />
        <Route path="/settings" element={<SettingsPage />} />
        <Route path="/generator" element={<GeneratorPage />} />
        <Route path="/migration" element={<MigrationPage />} />
        <Route path="/test" element={<TestPage />} />
        <Route path="/test-embedded" element={<TestEmbeddedPage />} />
        <Route path="/deployed/:agentId" element={<DeployedAgentPage />} />
        {/* Catch all route */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Providers>
  )
}

export default App