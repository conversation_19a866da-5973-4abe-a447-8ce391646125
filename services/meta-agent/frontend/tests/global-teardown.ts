/**
 * Global teardown for browser tests
 * Cleanup test data and resources
 */

import { chromium, FullConfig } from '@playwright/test';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global teardown...');

  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // Clean up any test agents created during testing
    console.log('🗑️ Cleaning up test agents...');
    
    // Get list of agents that match test patterns
    try {
      const agentsResponse = await page.request.get('http://localhost:8000/api/v1/agents');
      if (agentsResponse.ok()) {
        const data = await agentsResponse.json();
        const agents = Array.isArray(data) ? data : (data.agents || []);
        
        for (const agent of agents) {
          // Delete agents with test names
          if (agent.name && (
            agent.name.includes('critical-test-') ||
            agent.name.includes('fullstack-critical-') ||
            agent.name.includes('progress-test-') ||
            agent.name.includes('test-agent-') ||
            agent.name.includes('journey-test-') ||
            agent.name.includes('download-test-')
          )) {
            console.log(`🗑️ Cleaning up test agent: ${agent.name}`);
            await page.request.delete(`http://localhost:8000/api/v1/agents/${agent.id}`);
          }
        }
      }
    } catch (error) {
      console.warn('⚠️ Could not clean up test agents:', error);
    }

    // Clean up any test tasks
    console.log('🗑️ Cleaning up test tasks...');
    
    // Clean up generation artifacts
    console.log('🗑️ Cleaning up generation artifacts...');

    console.log('✅ Global teardown completed successfully');

  } catch (error) {
    console.warn('⚠️ Global teardown encountered issues:', error);
    // Don't fail the entire test suite if cleanup fails
  } finally {
    await browser.close();
  }
}

export default globalTeardown;