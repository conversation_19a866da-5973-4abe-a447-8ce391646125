/**
 * CRITICAL TEST: Agent Generation Workflow
 * Tests the most critical functionality - agent generation from start to finish
 */

import { test, expect, Page } from '@playwright/test';

test.describe('CRITICAL: Agent Generation Workflow', () => {
  test.describe.configure({ mode: 'serial' });

  let page: Page;

  test.beforeAll(async ({ browser }) => {
    page = await browser.newPage();
  });

  test.afterAll(async () => {
    await page.close();
  });

  test('should load generator page with all required elements', async () => {
    await page.goto('/generator');
    await page.waitForLoadState('networkidle');

    // Critical form elements must be present
    await expect(page.locator('h1')).toContainText('Generator');
    await expect(page.locator('input[name="agent_name"], input[id*="agent-name"]')).toBeVisible();
    await expect(page.locator('textarea[name="description"], textarea[id*="description"]')).toBeVisible();
    await expect(page.locator('select[name="agent_type"], select[id*="agent-type"]')).toBeVisible();
    await expect(page.locator('select[name="language"], select[id*="language"]')).toBeVisible();
    await expect(page.locator('button:has-text("Generate")')).toBeVisible();
  });

  test('should load templates and examples successfully', async () => {
    await page.goto('/generator');
    await page.waitForLoadState('networkidle');

    // Wait for examples section to load from API
    await page.waitForSelector('text=Quick Start Examples', { timeout: 10000 });

    // Check that agent types are populated
    const agentTypeSelect = page.locator('select[name="agent_type"], select[id*="agent-type"]');
    const options = await agentTypeSelect.locator('option').count();
    expect(options).toBeGreaterThan(1); // At least default + others

    // Check that capabilities are loaded
    const capabilityCheckboxes = page.locator('input[type="checkbox"]');
    const checkboxCount = await capabilityCheckboxes.count();
    expect(checkboxCount).toBeGreaterThan(0);
  });

  test('should validate form before submission', async () => {
    await page.goto('/generator');
    await page.waitForLoadState('networkidle');

    // Try to submit empty form
    const generateButton = page.locator('button:has-text("Generate")');
    
    // Check if button is disabled for empty form
    const isDisabled = await generateButton.isDisabled();
    if (!isDisabled) {
      // If not disabled, click should show validation errors
      await generateButton.click();
      
      // Look for validation messages
      const validationSelectors = [
        '.error',
        '[class*="error"]',
        '[role="alert"]',
        'text=required',
        'text=Required'
      ];

      let validationFound = false;
      for (const selector of validationSelectors) {
        try {
          await page.waitForSelector(selector, { timeout: 2000 });
          validationFound = true;
          break;
        } catch (e) {
          // Continue checking
        }
      }

      // Either validation messages appear OR button stays disabled
      expect(validationFound || isDisabled).toBeTruthy();
    }
  });

  test('should complete assistant agent generation - FULL WORKFLOW', async () => {
    await page.goto('/generator');
    await page.waitForLoadState('networkidle');

    const testAgentName = `critical-test-${Date.now()}`;

    // Fill required fields
    await page.fill('input[name="agent_name"], input[id*="agent-name"]', testAgentName);
    await page.fill('textarea[name="description"], textarea[id*="description"]', 
      'Critical test agent for browser automation testing');

    // Select assistant type
    await page.selectOption('select[name="agent_type"], select[id*="agent-type"]', 'assistant');

    // Select Python language
    await page.selectOption('select[name="language"], select[id*="language"]', 'python');

    // Select conversation capability (most basic)
    const conversationCheckbox = page.locator('input[type="checkbox"][value*="conversation"], input[type="checkbox"] + label:has-text("conversation")').first();
    if (await conversationCheckbox.isVisible()) {
      await conversationCheckbox.check();
    } else {
      // Select any available checkbox
      const firstCheckbox = page.locator('input[type="checkbox"]').first();
      await firstCheckbox.check();
    }

    // Submit form
    const generateButton = page.locator('button:has-text("Generate")');
    await expect(generateButton).toBeEnabled();
    await generateButton.click();

    // Wait for generation to start - look for status indicators
    const statusIndicators = [
      'text=pending',
      'text=generating',
      'text=started',
      '[data-testid="generation-status"]',
      '.generation-status'
    ];

    let generationStarted = false;
    for (const selector of statusIndicators) {
      try {
        await page.waitForSelector(selector, { timeout: 5000 });
        generationStarted = true;
        console.log(`✅ Generation started: ${selector}`);
        break;
      } catch (e) {
        // Continue trying other selectors
      }
    }

    if (!generationStarted) {
      // If no status indicators found, check if there's a progress indication or wait a bit
      console.log('⚠️ No explicit generation status found, checking for other progress signs...');
      await page.waitForTimeout(2000); // Wait 2 seconds for potential async updates
    }

    // Wait for completion with extended timeout - more flexible completion detection
    const completionIndicators = [
      'text=completed',
      'text=success',
      'text=generated',
      'text=ready',
      'text=finished',
      'button:has-text("Download")',
      '[data-testid="download-button"]'
    ];

    let generationCompleted = false;
    for (const selector of completionIndicators) {
      try {
        await page.waitForSelector(selector, { timeout: 120000 });
        generationCompleted = true;
        console.log(`✅ Generation completed: ${selector}`);
        break;
      } catch (e) {
        // Continue trying other selectors
      }
    }

    if (!generationCompleted) {
      // Check if there's any error message
      const errorSelectors = ['text=error', 'text=failed', '.error', '[role="alert"]'];
      for (const selector of errorSelectors) {
        if (await page.locator(selector).isVisible()) {
          throw new Error(`Generation failed with error: ${await page.locator(selector).textContent()}`);
        }
      }
      throw new Error('Generation did not complete within timeout');
    }

    // Verify download button appears
    const downloadButton = page.locator('button:has-text("Download")');
    await expect(downloadButton).toBeVisible();
    await expect(downloadButton).toBeEnabled();
    console.log('✅ Download button available');

    // Test download functionality
    const downloadPromise = page.waitForEvent('download');
    await downloadButton.click();
    const download = await downloadPromise;
    
    expect(download.suggestedFilename()).toContain('.zip');
    console.log(`✅ Download successful: ${download.suggestedFilename()}`);

    console.log(`🎉 CRITICAL TEST PASSED: Full agent generation workflow completed for ${testAgentName}`);
  });

  test('should complete fullstack agent generation - ADVANCED WORKFLOW', async () => {
    await page.goto('/generator');
    await page.waitForLoadState('networkidle');

    const testAgentName = `fullstack-critical-${Date.now()}`;

    // Fill required fields
    await page.fill('input[name="agent_name"], input[id*="agent-name"]', testAgentName);
    await page.fill('textarea[name="description"], textarea[id*="description"]', 
      'Critical fullstack test agent with complete feature set');

    // Select fullstack type (if available)
    const agentTypeSelect = page.locator('select[name="agent_type"], select[id*="agent-type"]');
    const fullstackOption = agentTypeSelect.locator('option[value="fullstack"]');
    
    if (await fullstackOption.count() > 0) {
      await agentTypeSelect.selectOption('fullstack');
      console.log('✅ Selected fullstack agent type');
    } else {
      await agentTypeSelect.selectOption('coordinator');
      console.log('✅ Selected coordinator agent type (fallback)');
    }

    // Select Python language
    await page.selectOption('select[name="language"], select[id*="language"]', 'python');

    // Select multiple capabilities for fullstack
    const capabilities = [
      'conversation',
      'api_integration', 
      'web_integration',
      'agent_coordination'
    ];

    for (const capability of capabilities) {
      const checkbox = page.locator(`input[type="checkbox"][value*="${capability}"], input[type="checkbox"] + label:has-text("${capability}")`).first();
      if (await checkbox.isVisible()) {
        await checkbox.check();
      }
    }

    // If no specific capabilities found, select first 4 checkboxes
    const checkedCount = await page.locator('input[type="checkbox"]:checked').count();
    if (checkedCount === 0) {
      const checkboxes = page.locator('input[type="checkbox"]');
      const totalCheckboxes = await checkboxes.count();
      for (let i = 0; i < Math.min(4, totalCheckboxes); i++) {
        await checkboxes.nth(i).check();
      }
    }

    console.log(`✅ Selected ${await page.locator('input[type="checkbox"]:checked').count()} capabilities`);

    // Enable advanced settings if available
    const advancedToggle = page.locator('button:has-text("Advanced"), button:has-text("Show Advanced")');
    if (await advancedToggle.isVisible()) {
      await advancedToggle.click();
      console.log('✅ Enabled advanced settings');
    }

    // Submit form
    const generateButton = page.locator('button:has-text("Generate")');
    await expect(generateButton).toBeEnabled();
    await generateButton.click();

    // Wait for generation with longer timeout for fullstack - use same logic as basic test
    const statusIndicators = [
      'text=pending',
      'text=generating',
      'text=started',
      '[data-testid="generation-status"]',
      '.generation-status'
    ];

    let generationStarted = false;
    for (const selector of statusIndicators) {
      try {
        await page.waitForSelector(selector, { timeout: 5000 });
        generationStarted = true;
        console.log(`✅ Fullstack generation started: ${selector}`);
        break;
      } catch (e) {
        // Continue trying other selectors
      }
    }

    if (!generationStarted) {
      console.log('⚠️ No explicit fullstack generation status found, waiting...');
      await page.waitForTimeout(2000);
    }

    // Wait for completion with extended timeout for fullstack
    const completionIndicators = [
      'text=completed',
      'text=success',
      'text=generated',
      'text=ready',
      'text=finished',
      'button:has-text("Download")',
      '[data-testid="download-button"]'
    ];

    let generationCompleted = false;
    for (const selector of completionIndicators) {
      try {
        await page.waitForSelector(selector, { timeout: 180000 }); // 3 minutes for fullstack
        generationCompleted = true;
        console.log(`✅ Fullstack generation completed: ${selector}`);
        break;
      } catch (e) {
        // Continue trying other selectors
      }
    }

    if (!generationCompleted) {
      throw new Error('Fullstack generation did not complete within timeout');
    }

    // Verify download
    const downloadButton = page.locator('button:has-text("Download")');
    await expect(downloadButton).toBeVisible();

    const downloadPromise = page.waitForEvent('download');
    await downloadButton.click();
    const download = await downloadPromise;
    
    expect(download.suggestedFilename()).toContain('.zip');
    console.log(`✅ Fullstack download successful: ${download.suggestedFilename()}`);

    console.log(`🎉 CRITICAL FULLSTACK TEST PASSED for ${testAgentName}`);
  });

  test('should handle generation errors gracefully', async () => {
    await page.goto('/generator');
    await page.waitForLoadState('networkidle');

    // Create an invalid configuration
    await page.fill('input[name="agent_name"], input[id*="agent-name"]', 'invalid@agent$name#');
    await page.fill('textarea[name="description"], textarea[id*="description"]', '');

    const generateButton = page.locator('button:has-text("Generate")');
    
    if (await generateButton.isEnabled()) {
      await generateButton.click();
      
      // Should show validation error or handle gracefully
      const errorSelectors = [
        'text=error',
        'text=invalid',
        'text=required',
        '.error',
        '[role="alert"]'
      ];

      let errorHandled = false;
      for (const selector of errorSelectors) {
        try {
          await page.waitForSelector(selector, { timeout: 5000 });
          errorHandled = true;
          console.log('✅ Validation error handled properly');
          break;
        } catch (e) {
          // Continue checking
        }
      }

      // If no validation shown, form should remain interactive
      if (!errorHandled) {
        await expect(page.locator('input[name="agent_name"], input[id*="agent-name"]')).toBeVisible();
        console.log('✅ Form remains interactive after invalid submission');
      }
    } else {
      console.log('✅ Generate button properly disabled for invalid input');
    }
  });

  test('should track generation progress and status', async () => {
    await page.goto('/generator');
    await page.waitForLoadState('networkidle');

    const testAgentName = `progress-test-${Date.now()}`;

    // Quick valid form
    await page.fill('input[name="agent_name"], input[id*="agent-name"]', testAgentName);
    await page.fill('textarea[name="description"], textarea[id*="description"]', 'Progress tracking test');
    await page.selectOption('select[name="agent_type"], select[id*="agent-type"]', 'assistant');
    await page.selectOption('select[name="language"], select[id*="language"]', 'python');
    
    const firstCheckbox = page.locator('input[type="checkbox"]').first();
    await firstCheckbox.check();

    // Start generation
    await page.locator('button:has-text("Generate")').click();

    // Look for progress indicators
    const progressIndicators = [
      '[data-testid="progress"]',
      '.progress',
      'text=generating',
      'text=%',
      'text=pending',
      'text=started'
    ];

    let progressFound = false;
    for (const selector of progressIndicators) {
      try {
        await page.waitForSelector(selector, { timeout: 10000 });
        progressFound = true;
        console.log(`✅ Progress indicator found: ${selector}`);
        break;
      } catch (e) {
        // Continue checking
      }
    }

    expect(progressFound).toBeTruthy();

    // Wait for completion
    await page.waitForSelector('text=completed, text=success', { timeout: 120000 });
    console.log('✅ Progress tracking completed successfully');
  });
});