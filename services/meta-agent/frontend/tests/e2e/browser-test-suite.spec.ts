/**
 * Comprehensive Browser Testing Suite for AI Agent Platform
 * Tests all critical features including agent generation and running operations
 */

import { test, expect, Page, Browser } from '@playwright/test';

// Test configuration
const BASE_URL = 'http://localhost:3000';
const API_BASE_URL = 'http://localhost:8000';

// Test utilities
class TestUtils {
  static async waitForNetworkIdle(page: Page, timeout = 5000) {
    await page.waitForLoadState('networkidle', { timeout });
  }

  static async waitForElement(page: Page, selector: string, timeout = 10000) {
    await page.waitForSelector(selector, { timeout });
  }

  static async scrollToElement(page: Page, selector: string) {
    await page.locator(selector).scrollIntoViewIfNeeded();
  }

  static generateTestAgentName() {
    return `test-agent-${Date.now()}`;
  }
}

test.describe('AI Agent Platform - Comprehensive Browser Tests', () => {
  let page: Page;
  let browser: Browser;

  test.beforeAll(async ({ browser: b }) => {
    browser = b;
  });

  test.beforeEach(async () => {
    page = await browser.newPage();
    
    // Set viewport for consistent testing
    await page.setViewportSize({ width: 1440, height: 900 });
    
    // Navigate to base URL
    await page.goto(BASE_URL);
    await TestUtils.waitForNetworkIdle(page);
  });

  test.afterEach(async () => {
    await page.close();
  });

  test.describe('Navigation and Layout Tests', () => {
    test('should load homepage and verify main navigation', async () => {
      // Verify homepage loads
      await expect(page).toHaveTitle(/AI Agent Platform/);
      
      // Check main navigation elements
      const navLinks = [
        { text: 'Dashboard', href: '/dashboard' },
        { text: 'Agents', href: '/agents' },
        { text: 'Tasks', href: '/tasks' },
        { text: 'Generator', href: '/generator' },
        { text: 'Migration', href: '/migration' }
      ];

      for (const link of navLinks) {
        const navElement = page.locator(`nav a[href="${link.href}"]`);
        await expect(navElement).toBeVisible();
        await expect(navElement).toContainText(link.text);
      }
    });

    test('should navigate to all main pages without errors', async () => {
      const pages = [
        '/dashboard',
        '/agents', 
        '/tasks',
        '/generator',
        '/migration'
      ];

      for (const pagePath of pages) {
        console.log(`Testing navigation to ${pagePath}`);
        
        await page.goto(`${BASE_URL}${pagePath}`);
        await TestUtils.waitForNetworkIdle(page);
        
        // Check no error messages
        const errorElements = page.locator('[data-testid="error"], .error, [class*="error"]');
        await expect(errorElements).toHaveCount(0);
        
        // Check page has loaded content
        await expect(page.locator('main, [role="main"], .container')).toBeVisible();
      }
    });

    test('should handle responsive design', async () => {
      // Test mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      await page.reload();
      await TestUtils.waitForNetworkIdle(page);
      
      // Navigation should still be accessible
      await expect(page.locator('nav')).toBeVisible();
      
      // Test tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 });
      await page.reload();
      await TestUtils.waitForNetworkIdle(page);
      
      await expect(page.locator('nav')).toBeVisible();
      
      // Reset to desktop
      await page.setViewportSize({ width: 1440, height: 900 });
    });
  });

  test.describe('Dashboard Functionality Tests', () => {
    test('should load dashboard with system stats', async () => {
      await page.goto(`${BASE_URL}/dashboard`);
      await TestUtils.waitForNetworkIdle(page);

      // Check dashboard cards load
      const dashboardCards = [
        'System Status',
        'Active Agents', 
        'Running Tasks',
        'System Health'
      ];

      for (const cardTitle of dashboardCards) {
        await expect(page.locator(`text=${cardTitle}`)).toBeVisible();
      }

      // Check charts/graphs are present
      await expect(page.locator('[data-testid="chart"], .chart, canvas')).toBeVisible();
    });

    test('should display real-time system metrics', async () => {
      await page.goto(`${BASE_URL}/dashboard`);
      await TestUtils.waitForNetworkIdle(page);

      // Check for metric values
      const metricSelectors = [
        '[data-testid="cpu-usage"]',
        '[data-testid="memory-usage"]', 
        '[data-testid="active-agents-count"]',
        '[data-testid="running-tasks-count"]'
      ];

      for (const selector of metricSelectors) {
        const element = page.locator(selector);
        if (await element.isVisible()) {
          await expect(element).not.toBeEmpty();
        }
      }
    });
  });

  test.describe('Agent Management Tests', () => {
    test('should load agents page and display agent list', async () => {
      await page.goto(`${BASE_URL}/agents`);
      await TestUtils.waitForNetworkIdle(page);

      // Check agents page elements
      await expect(page.locator('h1')).toContainText('Agents');
      
      // Check for create agent button
      const createButton = page.locator('button:has-text("Create Agent"), a:has-text("Create Agent")');
      await expect(createButton).toBeVisible();

      // Check agents table/list loads
      await expect(page.locator('[data-testid="agents-table"], .agents-list, table')).toBeVisible();
    });

    test('should handle agent operations (start/stop/delete)', async () => {
      await page.goto(`${BASE_URL}/agents`);
      await TestUtils.waitForNetworkIdle(page);

      // Look for existing agents
      const agentRows = page.locator('[data-testid="agent-row"], tr:has(td)');
      const agentCount = await agentRows.count();

      if (agentCount > 0) {
        // Test agent operations on first agent
        const firstAgent = agentRows.first();
        
        // Check for action buttons
        const actionButtons = [
          'button:has-text("Start")',
          'button:has-text("Stop")', 
          'button:has-text("Delete")',
          'button:has-text("View")'
        ];

        for (const buttonSelector of actionButtons) {
          const button = firstAgent.locator(buttonSelector);
          if (await button.isVisible()) {
            await expect(button).toBeEnabled();
          }
        }
      }
    });

    test('should navigate to agent details page', async () => {
      await page.goto(`${BASE_URL}/agents`);
      await TestUtils.waitForNetworkIdle(page);

      // Look for agent detail links
      const detailLinks = page.locator('a[href*="/agents/"], button:has-text("View")');
      const linkCount = await detailLinks.count();

      if (linkCount > 0) {
        await detailLinks.first().click();
        await TestUtils.waitForNetworkIdle(page);
        
        // Verify we're on an agent detail page
        await expect(page.url()).toContain('/agents/');
        await expect(page.locator('h1, h2')).toBeVisible();
      }
    });
  });

  test.describe('Agent Generator Tests - CRITICAL', () => {
    test('should load generator page with all form elements', async () => {
      await page.goto(`${BASE_URL}/generator`);
      await TestUtils.waitForNetworkIdle(page);

      // Check page title
      await expect(page.locator('h1')).toContainText('Agent Generator');

      // Check form elements are present
      const formElements = [
        'input[name="agent_name"], input[id*="agent-name"]',
        'textarea[name="description"], textarea[id*="description"]',
        'select[name="agent_type"], select[id*="agent-type"]',
        'select[name="language"], select[id*="language"]'
      ];

      for (const selector of formElements) {
        await expect(page.locator(selector)).toBeVisible();
      }

      // Check capabilities section
      await expect(page.locator('text=Capabilities')).toBeVisible();
      
      // Check generate button
      await expect(page.locator('button:has-text("Generate")')).toBeVisible();
    });

    test('should load example configurations', async () => {
      await page.goto(`${BASE_URL}/generator`);
      await TestUtils.waitForNetworkIdle(page);

      // Check for examples section
      await expect(page.locator('text=Examples, text=Quick Start')).toBeVisible();

      // Look for example cards
      const exampleCards = page.locator('[data-testid="example-card"], .example-card');
      const cardCount = await exampleCards.count();
      
      expect(cardCount).toBeGreaterThan(0);

      // Test loading an example
      if (cardCount > 0) {
        const loadButton = exampleCards.first().locator('button:has-text("Load")');
        if (await loadButton.isVisible()) {
          await loadButton.click();
          
          // Verify form is populated
          const agentNameInput = page.locator('input[name="agent_name"], input[id*="agent-name"]');
          await expect(agentNameInput).not.toHaveValue('');
        }
      }
    });

    test('should validate form inputs', async () => {
      await page.goto(`${BASE_URL}/generator`);
      await TestUtils.waitForNetworkIdle(page);

      // Try to generate without filling required fields
      const generateButton = page.locator('button:has-text("Generate")');
      await generateButton.click();

      // Check for validation messages
      const validationMessages = page.locator('.error, [class*="error"], [role="alert"]');
      const messageCount = await validationMessages.count();
      
      if (messageCount > 0) {
        await expect(validationMessages.first()).toBeVisible();
      } else {
        // Button should be disabled for invalid form
        await expect(generateButton).toBeDisabled();
      }
    });

    test('should complete full agent generation workflow - CRITICAL', async () => {
      await page.goto(`${BASE_URL}/generator`);
      await TestUtils.waitForNetworkIdle(page);

      const testAgentName = TestUtils.generateTestAgentName();

      // Fill out the form
      await page.fill('input[name="agent_name"], input[id*="agent-name"]', testAgentName);
      await page.fill('textarea[name="description"], textarea[id*="description"]', 
        'Test agent created by browser automation');

      // Select agent type
      const agentTypeSelect = page.locator('select[name="agent_type"], select[id*="agent-type"]');
      await agentTypeSelect.selectOption('assistant');

      // Select language
      const languageSelect = page.locator('select[name="language"], select[id*="language"]');
      await languageSelect.selectOption('python');

      // Select capabilities
      const capabilityCheckboxes = page.locator('input[type="checkbox"]');
      const checkboxCount = await capabilityCheckboxes.count();
      
      if (checkboxCount > 0) {
        // Select first few capabilities
        for (let i = 0; i < Math.min(3, checkboxCount); i++) {
          await capabilityCheckboxes.nth(i).check();
        }
      }

      // Submit form
      const generateButton = page.locator('button:has-text("Generate")');
      await expect(generateButton).toBeEnabled();
      await generateButton.click();

      // Wait for generation to start
      await page.waitForSelector('text=Generation Started, text=generating, [data-testid="generation-progress"]', 
        { timeout: 10000 });

      // Wait for completion (with longer timeout for generation)
      await page.waitForSelector('text=completed, text=success, button:has-text("Download")', 
        { timeout: 60000 });

      // Verify download button appears
      const downloadButton = page.locator('button:has-text("Download")');
      await expect(downloadButton).toBeVisible();
      await expect(downloadButton).toBeEnabled();

      console.log(`✅ Agent generation completed successfully: ${testAgentName}`);
    });

    test('should handle generation progress and status updates', async () => {
      await page.goto(`${BASE_URL}/generator`);
      await TestUtils.waitForNetworkIdle(page);

      // Fill minimum required fields
      const testAgentName = TestUtils.generateTestAgentName();
      await page.fill('input[name="agent_name"], input[id*="agent-name"]', testAgentName);
      await page.fill('textarea[name="description"], textarea[id*="description"]', 'Progress test agent');
      
      // Select first available options
      await page.locator('select[name="agent_type"], select[id*="agent-type"]').selectOption({ index: 0 });
      await page.locator('select[name="language"], select[id*="language"]').selectOption({ index: 0 });

      // Select at least one capability
      const firstCheckbox = page.locator('input[type="checkbox"]').first();
      if (await firstCheckbox.isVisible()) {
        await firstCheckbox.check();
      }

      // Start generation
      await page.locator('button:has-text("Generate")').click();

      // Check for progress indicators
      const progressElements = [
        '[data-testid="progress-bar"]',
        '.progress',
        'text=generating',
        'text=%'
      ];

      let progressFound = false;
      for (const selector of progressElements) {
        try {
          await page.waitForSelector(selector, { timeout: 5000 });
          progressFound = true;
          break;
        } catch (e) {
          // Continue checking other selectors
        }
      }

      if (progressFound) {
        console.log('✅ Generation progress indicator found');
      }

      // Wait for completion
      await page.waitForSelector('text=completed, text=success, button:has-text("Download")', 
        { timeout: 60000 });
    });

    test('should allow download of generated agent', async () => {
      await page.goto(`${BASE_URL}/generator`);
      await TestUtils.waitForNetworkIdle(page);

      // Quick generation for download test
      const testAgentName = `download-test-${Date.now()}`;
      await page.fill('input[name="agent_name"], input[id*="agent-name"]', testAgentName);
      await page.fill('textarea[name="description"], textarea[id*="description"]', 'Download test agent');
      await page.locator('select[name="agent_type"], select[id*="agent-type"]').selectOption('assistant');
      await page.locator('select[name="language"], select[id*="language"]').selectOption('python');
      
      const firstCheckbox = page.locator('input[type="checkbox"]').first();
      if (await firstCheckbox.isVisible()) {
        await firstCheckbox.check();
      }

      await page.locator('button:has-text("Generate")').click();
      
      // Wait for completion
      await page.waitForSelector('button:has-text("Download")', { timeout: 60000 });

      // Set up download handler
      const downloadPromise = page.waitForEvent('download');
      
      // Click download
      await page.locator('button:has-text("Download")').click();
      
      // Wait for download
      const download = await downloadPromise;
      
      // Verify download
      expect(download.suggestedFilename()).toContain('.zip');
      expect(download.suggestedFilename()).toContain('agent');
      
      console.log(`✅ Agent download successful: ${download.suggestedFilename()}`);
    });
  });

  test.describe('Tasks Page Tests', () => {
    test('should load tasks page and display task list', async () => {
      await page.goto(`${BASE_URL}/tasks`);
      await TestUtils.waitForNetworkIdle(page);

      // Check page loads
      await expect(page.locator('h1')).toContainText('Tasks');
      
      // Check for tasks table or list
      await expect(page.locator('[data-testid="tasks-table"], .tasks-list, table')).toBeVisible();
    });

    test('should handle task operations', async () => {
      await page.goto(`${BASE_URL}/tasks`);
      await TestUtils.waitForNetworkIdle(page);

      // Look for task action buttons
      const actionButtons = page.locator('button:has-text("Start"), button:has-text("Stop"), button:has-text("Cancel")');
      const buttonCount = await actionButtons.count();

      if (buttonCount > 0) {
        // Verify buttons are interactive
        for (let i = 0; i < Math.min(3, buttonCount); i++) {
          const button = actionButtons.nth(i);
          await expect(button).toBeVisible();
        }
      }
    });
  });

  test.describe('Migration Page Tests', () => {
    test('should load migration page with analysis tools', async () => {
      await page.goto(`${BASE_URL}/migration`);
      await TestUtils.waitForNetworkIdle(page);

      // Check page loads
      await expect(page.locator('h1')).toContainText('Migration');
      
      // Check for migration tools
      const migrationElements = [
        'button:has-text("Analyze")',
        'button:has-text("Plan")',
        'button:has-text("Execute")',
        'text=Analysis',
        'text=Plans'
      ];

      for (const selector of migrationElements) {
        const element = page.locator(selector);
        if (await element.isVisible()) {
          await expect(element).toBeVisible();
        }
      }
    });
  });

  test.describe('API Integration Tests', () => {
    test('should handle API errors gracefully', async () => {
      // Test with invalid API calls
      await page.route('**/api/**', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Test error' })
        });
      });

      await page.goto(`${BASE_URL}/dashboard`);
      await TestUtils.waitForNetworkIdle(page);

      // Check that error states are handled
      const errorStates = page.locator('text=Error, text=Failed, .error-state');
      // Should either show error states or loading states
      const loadingStates = page.locator('text=Loading, .loading, .spinner');
      
      const errorCount = await errorStates.count();
      const loadingCount = await loadingStates.count();
      
      expect(errorCount + loadingCount).toBeGreaterThanOrEqual(0);
    });

    test('should maintain functionality during slow API responses', async () => {
      // Slow down API responses
      await page.route('**/api/**', async route => {
        await new Promise(resolve => setTimeout(resolve, 2000));
        route.continue();
      });

      await page.goto(`${BASE_URL}/agents`);
      
      // Page should still be navigable during loading
      await expect(page.locator('nav')).toBeVisible();
      await expect(page.locator('h1')).toBeVisible();
      
      // Loading states should be shown
      await page.waitForSelector('text=Loading, .loading, .spinner', { timeout: 1000 });
    });
  });

  test.describe('Full User Journey Tests', () => {
    test('should complete full user workflow: Dashboard → Generator → Agent Creation → Management', async () => {
      // Step 1: Start at dashboard
      await page.goto(`${BASE_URL}/dashboard`);
      await TestUtils.waitForNetworkIdle(page);
      await expect(page.locator('h1')).toContainText('Dashboard');

      // Step 2: Navigate to generator
      await page.click('a[href="/generator"], nav a:has-text("Generator")');
      await TestUtils.waitForNetworkIdle(page);
      await expect(page.locator('h1')).toContainText('Generator');

      // Step 3: Create an agent
      const testAgentName = `journey-test-${Date.now()}`;
      await page.fill('input[name="agent_name"], input[id*="agent-name"]', testAgentName);
      await page.fill('textarea[name="description"], textarea[id*="description"]', 
        'Full journey test agent');
      await page.locator('select[name="agent_type"], select[id*="agent-type"]').selectOption('assistant');
      await page.locator('select[name="language"], select[id*="language"]').selectOption('python');
      
      const firstCheckbox = page.locator('input[type="checkbox"]').first();
      if (await firstCheckbox.isVisible()) {
        await firstCheckbox.check();
      }

      await page.locator('button:has-text("Generate")').click();
      await page.waitForSelector('button:has-text("Download")', { timeout: 60000 });

      // Step 4: Navigate to agents page
      await page.click('a[href="/agents"], nav a:has-text("Agents")');
      await TestUtils.waitForNetworkIdle(page);
      await expect(page.locator('h1')).toContainText('Agents');

      // Step 5: Verify the created agent appears in the list
      await page.waitForSelector(`text=${testAgentName}`, { timeout: 10000 });
      await expect(page.locator(`text=${testAgentName}`)).toBeVisible();

      console.log(`✅ Full user journey completed successfully with agent: ${testAgentName}`);
    });
  });

  test.describe('Performance and Accessibility Tests', () => {
    test('should load pages within acceptable time limits', async () => {
      const pages = ['/dashboard', '/agents', '/generator', '/tasks', '/migration'];
      
      for (const pagePath of pages) {
        const startTime = Date.now();
        await page.goto(`${BASE_URL}${pagePath}`);
        await TestUtils.waitForNetworkIdle(page);
        const loadTime = Date.now() - startTime;
        
        expect(loadTime).toBeLessThan(10000); // 10 second max load time
        console.log(`Page ${pagePath} loaded in ${loadTime}ms`);
      }
    });

    test('should be keyboard navigable', async () => {
      await page.goto(`${BASE_URL}/generator`);
      await TestUtils.waitForNetworkIdle(page);

      // Test tab navigation
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      
      // Check that focus is visible
      const focusedElement = page.locator(':focus');
      await expect(focusedElement).toBeVisible();
    });

    test('should handle concurrent operations', async () => {
      // Open multiple pages simultaneously
      const pages = await Promise.all([
        browser.newPage(),
        browser.newPage(),
        browser.newPage()
      ]);

      try {
        await Promise.all([
          pages[0].goto(`${BASE_URL}/dashboard`),
          pages[1].goto(`${BASE_URL}/agents`),
          pages[2].goto(`${BASE_URL}/generator`)
        ]);

        await Promise.all([
          TestUtils.waitForNetworkIdle(pages[0]),
          TestUtils.waitForNetworkIdle(pages[1]),
          TestUtils.waitForNetworkIdle(pages[2])
        ]);

        // All pages should load successfully
        for (const p of pages) {
          await expect(p.locator('h1')).toBeVisible();
        }
      } finally {
        await Promise.all(pages.map(p => p.close()));
      }
    });
  });
});