/**
 * Global setup for browser tests
 * Ensures backend services are running and database is prepared
 */

import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup for browser tests...');

  // Start a browser to check if services are available
  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // Check if frontend is accessible
    console.log('✅ Checking frontend availability...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
    console.log('✅ Frontend is accessible');

    // Check if backend API is accessible
    console.log('✅ Checking backend API availability...');
    const apiResponse = await page.request.get('http://localhost:8000/health');
    if (apiResponse.ok()) {
      console.log('✅ Backend API is accessible');
    } else {
      console.warn('⚠️ Backend API may not be fully ready');
    }

    // Check if agent generation API is available
    console.log('✅ Checking agent generation API...');
    const genResponse = await page.request.get('http://localhost:8000/api/v1/generation/templates');
    if (genResponse.ok()) {
      console.log('✅ Agent generation API is accessible');
    } else {
      console.warn('⚠️ Agent generation API may not be ready');
    }

    // Set up test data if needed
    console.log('✅ Setting up test environment...');
    
    // Store global test state
    process.env.PLAYWRIGHT_TEST_BASE_URL = 'http://localhost:3000';
    process.env.PLAYWRIGHT_API_BASE_URL = 'http://localhost:8000';

    console.log('✅ Global setup completed successfully');

  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

export default globalSetup;