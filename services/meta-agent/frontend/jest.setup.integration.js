// Integration test setup - Node environment with real HTTP calls

// Set test environment variables
process.env.VITE_API_URL = 'http://localhost:8000'
process.env.NODE_ENV = 'test-integration'

// Global test utilities for integration tests
global.testUtils = {
  // Helper to wait for async operations
  waitFor: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Helper to generate unique test data
  generateTestUser: () => ({
    username: `testuser_${Date.now()}_${Math.random().toString(36).substring(7)}`,
    email: `test_${Date.now()}_${Math.random().toString(36).substring(7)}@example.com`,
    password: 'TestPassword123!',
    full_name: 'Test User'
  }),
  
  generateTestAgent: () => ({
    name: `Test Agent ${Date.now()}_${Math.random().toString(36).substring(7)}`,
    description: 'A test agent for integration testing',
    type: 'assistant',
    config: {
      max_concurrent_tasks: 5,
      timeout_seconds: 300
    },
    capabilities: ['text_analysis', 'data_processing']
  })
}

// Global setup for all integration tests
beforeAll(async () => {
  // Wait a bit to ensure backend is ready
  await global.testUtils.waitFor(10000)
  
  console.log('🧪 Starting integration tests against:', process.env.VITE_API_URL)
})

afterAll(() => {
  console.log('🏁 Integration tests completed')
})

// Setup for each test
beforeEach(() => {
  // Reset any global state if needed
})

afterEach(() => {
  // Cleanup after each test
})