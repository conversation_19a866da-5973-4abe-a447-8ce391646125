{"permissions": {"allow": ["Bash(find:*)", "Bash(npm run dev:*)", "Bash(npm run build:*)", "Bash(git log:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(bazel run:*)", "Bash(cp:*)", "Bash(bazel build:*)", "Bash(npm install:*)", "Bash(ls:*)", "Bash(./services/meta-agent/rest-api/update_generated.sh:*)", "<PERSON><PERSON>(bazel:*)", "Bash(rm:*)", "<PERSON><PERSON>(cat:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(python:*)", "Bash(pip install:*)", "<PERSON><PERSON>(source:*)", "Bash(./scripts/generate-client.sh:*)", "Bash(npm run type-check:*)", "Bash(npm run lint)", "Bash(grep:*)"], "deny": []}}