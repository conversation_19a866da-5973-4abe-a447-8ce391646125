#!/bin/bash
set -euo pipefail

# Navigate to frontend directory
cd "$(dirname "$0")/.."

echo "🧪 Running Meta-Agent Frontend E2E Tests..."

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Ensure Playwright browsers are installed
echo "🎭 Installing Playwright browsers if needed..."
npx playwright install

# Run Playwright E2E tests
echo "🎯 Running Playwright E2E tests..."
npm run test:e2e

echo "✅ E2E tests completed successfully!"