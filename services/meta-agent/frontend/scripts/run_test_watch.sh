#!/bin/bash
set -euo pipefail

# Navigate to frontend directory
cd "$(dirname "$0")/.."

echo "🧪 Running Meta-Agent Frontend Tests in Watch Mode..."

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Run Jest in watch mode
echo "👀 Starting Jest in watch mode..."
echo "   Press 'q' to quit, 'a' to run all tests"
npm run test:watch