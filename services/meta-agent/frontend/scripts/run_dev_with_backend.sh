#!/bin/bash
set -euo pipefail

# Navigate to frontend directory
cd "$(dirname "$0")/.."

echo "🚀 Starting Meta-Agent Frontend with Backend Integration..."

# Step 1: Generate API client to ensure latest backend API
echo "1️⃣ Generating latest API client..."
./scripts/generate-client.sh

# Step 2: Check if backend is running
echo "2️⃣ Checking backend connectivity..."
if ! curl -f http://localhost:8001/health 2>/dev/null; then
    echo "⚠️ Backend doesn't seem to be running on http://localhost:8001"
    echo "   Please start the backend service first:"
    echo "   bazel run //services/meta-agent/backend:backend"
    echo ""
    echo "   Or start both frontend and backend together:"
    echo "   bazel run //services/meta-agent:dev"
    echo ""
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Step 3: Start frontend development server
echo "3️⃣ Starting frontend development server..."
echo "🔥 Starting Vite dev server on http://localhost:3000"
echo "🔗 Backend API expected at http://localhost:8001"
npm run dev