#!/bin/bash
set -euo pipefail

# Navigate to frontend directory
cd "$(dirname "$0")/.."

echo "🔍 Running TypeScript Type Check..."

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Run TypeScript type checking
echo "🎯 Checking types with TypeScript compiler..."
npm run type-check

echo "✅ Type check completed successfully!"