#!/bin/bash
set -euo pipefail

# Navigate to frontend directory
cd "$(dirname "$0")/.."

echo "🧪 Running Meta-Agent Frontend Integration Tests..."

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Run Jest integration tests
echo "🎯 Running Jest integration tests..."
npm run test:integration

echo "✅ Integration tests completed successfully!"