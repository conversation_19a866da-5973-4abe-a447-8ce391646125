#!/bin/bash
# Generate and install API client for meta-agent frontend

set -e

echo "🔄 Generating TypeScript API client for Meta Agent..."

DEST_DIR="src/api-generated"

# Navigate to frontend directory and remember the actual path
cd "$(dirname "$0")/.."
FRONTEND_DIR="$PWD"

# When in Bazel context, we need to copy to the actual source directory
if [[ "$PWD" == *"/execroot/_main/"* ]] && [[ -n "$BUILD_WORKSPACE_DIRECTORY" ]]; then
    # Use <PERSON>zel's workspace directory with relative path
    DEST_DIR="$BUILD_WORKSPACE_DIRECTORY/services/meta-agent/frontend/src/api-generated"
    echo "🔧 Using Bazel workspace directory for destination"
fi

# Check if we're in a Bazel context by looking at current directory
if [[ "$PWD" == *"bazel-bin"* ]] || [[ "$PWD" == *"bazel-out"* ]]; then
    echo "🏗️  Using existing Bazel build (already in Bazel context)..."
else
    echo "🏗️  Building OpenAPI client with <PERSON><PERSON>..."
    bazel build //services/meta-agent/rest-api:generate_typescript_client
fi

# Copy generated files to src/api-generated
echo "📋 Copying generated client files to $DEST_DIR..."
rm -rf "$DEST_DIR"
mkdir -p "$DEST_DIR"

# Copy all generated TypeScript files using relative paths
# Debug: show current working directory
echo "📍 Current working directory: $PWD"

# Extract execroot path and construct relative path
if [[ "$PWD" == *"/execroot/_main/"* ]]; then
    # We're in Bazel execroot, use execroot-relative path
    EXECROOT_PATH="${PWD%/bazel-out/*}/bazel-out/darwin_arm64-fastbuild/bin/services/meta-agent/rest-api/generated/typescript"
    POSSIBLE_PATHS=(
        "../../rest-api/generated/typescript"
        "../../../../../services/meta-agent/rest-api/generated/typescript"
        "${EXECROOT_PATH}"
    )
else
    # Fallback for non-Bazel context
    POSSIBLE_PATHS=(
        "../../../bazel-bin/services/meta-agent/rest-api/generated/typescript"
    )
fi

GENERATED_DIR=""
for path in "${POSSIBLE_PATHS[@]}"; do
    if [ -d "$path" ]; then
        GENERATED_DIR="$path"
        echo "📂 Found generated files at: $GENERATED_DIR"
        break
    fi
done

if [ -n "$GENERATED_DIR" ] && [ -d "$GENERATED_DIR" ]; then
    cp "$GENERATED_DIR"/*.ts "$DEST_DIR/"
    echo "✅ Copied all TypeScript client files to $DEST_DIR/"
    echo "   - api.ts (main API classes)"
    echo "   - base.ts (base API functionality)" 
    echo "   - configuration.ts (API configuration)"
    echo "   - index.ts (exports)"
else
    echo "❌ Error: Generated client directory not found"
    echo "Tried the following paths:"
    for path in "${POSSIBLE_PATHS[@]}"; do
        echo "   - $path"
    done
    exit 1
fi

echo "✨ API client generation complete!"
echo "📁 Generated files are now available in $DEST_DIR"