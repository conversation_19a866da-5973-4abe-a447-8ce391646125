#!/bin/bash
set -euo pipefail

# Navigate to frontend directory
cd "$(dirname "$0")/.."

echo "🔄 Building Meta-Agent Frontend with API Client Generation..."

# Step 1: Generate API client first
echo "1️⃣ Generating API client..."
./scripts/generate-client.sh

# Step 2: Build the application
echo "2️⃣ Building frontend application..."
./scripts/run_build.sh

echo "✅ Frontend build with API client completed successfully!"
echo "📁 Built files are in the 'dist' directory"