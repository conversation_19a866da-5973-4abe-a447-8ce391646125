#!/bin/bash
set -euo pipefail

# Navigate to frontend directory
cd "$(dirname "$0")/.."

echo "🏗️ Building Meta-Agent Frontend for Production..."

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Type check first
echo "🔍 Running TypeScript type check..."
npm run type-check

# Build the application
echo "📦 Building application..."
npm run build

echo "✅ Frontend build completed successfully!"
echo "📁 Built files are in the 'dist' directory"