#!/bin/bash
set -euo pipefail

# Navigate to frontend directory
cd "$(dirname "$0")/.."

echo "🧹 Cleaning Meta-Agent Frontend Build Artifacts..."

# Remove build outputs
if [ -d "dist" ]; then
    echo "🗑️ Removing dist directory..."
    rm -rf dist
fi

# Remove TypeScript build info
if [ -f "tsconfig.tsbuildinfo" ]; then
    echo "🗑️ Removing TypeScript build info..."
    rm -f tsconfig.tsbuildinfo
fi

# Remove test coverage
if [ -d "coverage" ]; then
    echo "🗑️ Removing test coverage..."
    rm -rf coverage
fi

# Remove Playwright test results
if [ -d "test-results" ]; then
    echo "🗑️ Removing test results..."
    rm -rf test-results
fi

if [ -d "playwright-report" ]; then
    echo "🗑️ Removing Playwright reports..."
    rm -rf playwright-report
fi

# Remove generated API client (optional - uncomment if needed)
# if [ -d "src/lib/api-client/generated" ]; then
#     echo "🗑️ Removing generated API client..."
#     rm -rf src/lib/api-client/generated
# fi

echo "✅ Cleanup completed successfully!"
echo "💡 To clean node_modules as well, run: rm -rf node_modules"