#!/usr/bin/env python3
"""
Test script for agent creation functionality
Tests both backend API and frontend integration
"""

import requests
import json
import time
import sys

BASE_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:3000"

def test_backend_agent_creation():
    """Test backend agent creation API"""
    print("🔧 Testing Backend Agent Creation")
    print("=" * 40)
    
    # Test 1: Create a simple agent
    print("\n1️⃣ Testing simple agent creation...")
    agent_data = {
        "name": "Backend Test Agent",
        "description": "Testing backend agent creation",
        "type": "assistant",
        "capabilities": ["natural_language"],
        "config": {}
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/v1/public/agents", json=agent_data)
        response.raise_for_status()
        agent = response.json()
        print(f"✅ Agent created successfully!")
        print(f"   ID: {agent['id']}")
        print(f"   Name: {agent['name']}")
        print(f"   Status: {agent['status']}")
        return agent['id']
    except Exception as e:
        print(f"❌ Failed to create agent: {e}")
        return None

def test_complex_agent_creation():
    """Test creating agent with complex configuration"""
    print("\n2️⃣ Testing complex agent creation...")
    
    complex_agent = {
        "name": "Advanced AI Assistant",
        "description": "A sophisticated AI assistant with multiple capabilities",
        "type": "assistant",
        "capabilities": [
            "natural_language",
            "conversation",
            "task_execution",
            "data_analysis",
            "coding",
            "problem_solving"
        ],
        "config": {
            "model": "gpt-4",
            "temperature": 0.7,
            "max_tokens": 1000,
            "system_prompt": "You are a helpful AI assistant."
        }
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/v1/public/agents", json=complex_agent)
        response.raise_for_status()
        agent = response.json()
        print(f"✅ Complex agent created successfully!")
        print(f"   ID: {agent['id']}")
        print(f"   Name: {agent['name']}")
        print(f"   Capabilities: {len(agent['capabilities'])} capabilities")
        print(f"   Config keys: {list(agent.get('config', {}).keys())}")
        return agent['id']
    except Exception as e:
        print(f"❌ Failed to create complex agent: {e}")
        return None

def test_agent_listing():
    """Test agent listing functionality"""
    print("\n3️⃣ Testing agent listing...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/v1/public/agents")
        response.raise_for_status()
        agents_data = response.json()
        
        print(f"✅ Agent listing successful!")
        print(f"   Total agents: {agents_data.get('total', 0)}")
        print(f"   Agents in response: {len(agents_data.get('items', []))}")
        
        # Show recent agents
        for i, agent in enumerate(agents_data.get('items', [])[:3]):
            print(f"   Agent {i+1}: {agent['name']} ({agent['status']})")
            
        return True
    except Exception as e:
        print(f"❌ Failed to list agents: {e}")
        return False

def test_frontend_accessibility():
    """Test frontend page accessibility"""
    print("\n4️⃣ Testing frontend accessibility...")
    
    pages_to_test = [
        ("/agents", "Agents List"),
        ("/agents/create", "Agent Creation"),
        ("/dashboard", "Dashboard")
    ]
    
    accessible_pages = 0
    
    for path, name in pages_to_test:
        try:
            response = requests.get(f"{FRONTEND_URL}{path}", timeout=10)
            if response.status_code == 200:
                print(f"   ✅ {name}: Accessible")
                accessible_pages += 1
            else:
                print(f"   ⚠️  {name}: Status {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name}: Error - {e}")
    
    print(f"\n   📊 Accessible pages: {accessible_pages}/{len(pages_to_test)}")
    return accessible_pages == len(pages_to_test)

def test_agent_deployment(agent_id):
    """Test agent deployment functionality"""
    if not agent_id:
        print("\n⏭️  Skipping deployment test (no agent ID)")
        return False
        
    print(f"\n5️⃣ Testing agent deployment...")
    
    try:
        # Deploy the agent
        response = requests.post(f"{BASE_URL}/api/v1/public/agents/{agent_id}/deploy")
        response.raise_for_status()
        deployment = response.json()
        
        print(f"✅ Agent deployed successfully!")
        print(f"   URL: {deployment['deployment_url']}")
        print(f"   Port: {deployment['port']}")
        
        # Test deployment status
        response = requests.get(f"{BASE_URL}/api/v1/public/agents/{agent_id}/deployment")
        response.raise_for_status()
        status = response.json()
        
        print(f"✅ Deployment status retrieved!")
        print(f"   Deployed: {status['deployed']}")
        print(f"   Health: {status['health_status']}")
        
        return True
    except Exception as e:
        print(f"❌ Failed to deploy agent: {e}")
        return False

def main():
    """Run comprehensive agent creation tests"""
    print("🧪 COMPREHENSIVE AGENT CREATION TESTING")
    print("=" * 60)
    
    # Test backend functionality
    agent_id = test_backend_agent_creation()
    complex_agent_id = test_complex_agent_creation()
    listing_success = test_agent_listing()
    
    # Test frontend accessibility
    frontend_success = test_frontend_accessibility()
    
    # Test deployment with one of the created agents
    deployment_success = test_agent_deployment(agent_id or complex_agent_id)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    tests = [
        ("Backend Agent Creation", agent_id is not None),
        ("Complex Agent Creation", complex_agent_id is not None),
        ("Agent Listing", listing_success),
        ("Frontend Accessibility", frontend_success),
        ("Agent Deployment", deployment_success)
    ]
    
    passed_tests = sum(1 for _, passed in tests if passed)
    
    for test_name, passed in tests:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    print(f"\n📈 Overall Success Rate: {passed_tests}/{len(tests)} ({passed_tests/len(tests)*100:.1f}%)")
    
    if passed_tests >= len(tests) * 0.8:  # 80% success rate
        print("\n🎉 AGENT CREATION IS WORKING!")
        print("✅ Backend API: Functional")
        print("✅ Frontend Pages: Accessible")
        print("✅ Agent Management: Working")
        
        print(f"\n📋 USER INSTRUCTIONS:")
        print(f"1. Go to: {FRONTEND_URL}/agents/create")
        print(f"2. Fill in agent details")
        print(f"3. Click 'Create Agent'")
        print(f"4. Check agents list: {FRONTEND_URL}/agents")
        
        if agent_id:
            print(f"\n🔗 Test the created agent:")
            print(f"   Agent ID: {agent_id}")
            print(f"   Deploy and test: {FRONTEND_URL}/agents")
        
        return True
    else:
        print("\n❌ AGENT CREATION HAS ISSUES")
        print("   Please check the failed tests above")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
