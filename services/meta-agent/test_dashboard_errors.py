#!/usr/bin/env python3
"""
Test script to identify dashboard and agent creation errors
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8000"

def test_endpoint(url, method="GET", data=None, description=""):
    """Test an API endpoint and return results"""
    try:
        print(f"\n🔍 Testing: {description}")
        print(f"   URL: {url}")
        
        if method == "GET":
            response = requests.get(url, timeout=10)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=10)
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"   ✅ Success: {type(result).__name__}")
                if isinstance(result, dict):
                    print(f"   Keys: {list(result.keys())}")
                elif isinstance(result, list):
                    print(f"   Items: {len(result)}")
                return True, result
            except:
                print(f"   ✅ Success: {response.text[:100]}...")
                return True, response.text
        else:
            print(f"   ❌ Error: {response.text}")
            return False, response.text
            
    except Exception as e:
        print(f"   💥 Exception: {e}")
        return False, str(e)

def main():
    print("🚀 Testing Dashboard and Agent Creation APIs")
    print("=" * 60)
    
    # Test basic endpoints
    endpoints = [
        (f"{BASE_URL}/", "GET", None, "Root endpoint"),
        (f"{BASE_URL}/health", "GET", None, "Health check"),
        (f"{BASE_URL}/api/v1/public/agents", "GET", None, "List agents"),
        (f"{BASE_URL}/api/v1/public/runtime/system/stats", "GET", None, "System stats"),
        (f"{BASE_URL}/api/v1/public/runtime/system/health", "GET", None, "System health"),
    ]
    
    results = {}
    
    for url, method, data, description in endpoints:
        success, result = test_endpoint(url, method, data, description)
        results[description] = {"success": success, "result": result}
    
    # Test agent creation
    print(f"\n🤖 Testing Agent Creation")
    print("-" * 40)
    
    agent_data = {
        "name": "Dashboard Test Agent",
        "description": "Test agent for dashboard debugging",
        "type": "assistant",
        "config": {"test": True},
        "capabilities": ["conversation", "natural_language"]
    }
    
    success, result = test_endpoint(
        f"{BASE_URL}/api/v1/public/agents", 
        "POST", 
        agent_data, 
        "Create agent"
    )
    results["Create agent"] = {"success": success, "result": result}
    
    if success:
        agent_id = result.get("id")
        print(f"   Created agent ID: {agent_id}")
        
        # Test agent deployment
        if agent_id:
            success, deploy_result = test_endpoint(
                f"{BASE_URL}/api/v1/public/agents/{agent_id}/deploy",
                "POST",
                None,
                "Deploy agent"
            )
            results["Deploy agent"] = {"success": success, "result": deploy_result}
    
    # Summary
    print(f"\n📊 Test Results Summary")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result["success"] else "❌ FAIL"
        print(f"{status} {test_name}")
    
    # Check for specific issues
    print(f"\n🔍 Issue Analysis")
    print("-" * 40)
    
    failed_tests = [name for name, result in results.items() if not result["success"]]
    
    if not failed_tests:
        print("✅ All tests passed! Backend APIs are working correctly.")
        print("🔍 The issue is likely in the frontend:")
        print("   - Frontend not running")
        print("   - CORS issues")
        print("   - API URL configuration")
        print("   - PowerShell execution policy blocking npm")
    else:
        print(f"❌ Failed tests: {', '.join(failed_tests)}")
        for name in failed_tests:
            print(f"   {name}: {results[name]['result']}")
    
    print(f"\n💡 Next Steps:")
    print("1. Fix PowerShell execution policy to run frontend")
    print("2. Check frontend API configuration")
    print("3. Verify CORS settings")
    print("4. Test frontend-backend connectivity")

if __name__ == "__main__":
    main()
