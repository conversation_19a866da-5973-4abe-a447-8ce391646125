{"openapi": "3.0.3", "info": {"title": "AI Agent Platform API", "description": "Comprehensive API for managing AI agents, workflows, and platform operations", "version": "2.0.0", "contact": {"name": "AI Agent Platform Team", "url": "https://github.com/ai-agent-platform/platform", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "http://localhost:8000", "description": "Development server"}, {"url": "https://api.ai-agent-platform.com", "description": "Production server"}], "tags": [{"name": "Agents", "description": "AI agent management operations"}, {"name": "Migration", "description": "Application-to-agent migration operations"}, {"name": "Tasks", "description": "Task management and monitoring"}, {"name": "System", "description": "System health and statistics"}], "paths": {"/api/v1/agents": {"get": {"summary": "List all agents", "description": "Retrieve a paginated list of all agents with optional filtering", "tags": ["Agents"], "operationId": "getAgents", "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "size", "in": "query", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "status", "in": "query", "schema": {"type": "string", "enum": ["created", "building", "ready", "running", "paused", "error", "stopped"]}}, {"name": "type", "in": "query", "schema": {"type": "string", "enum": ["api_service", "background_worker", "scheduled_task", "data_processor"]}}, {"name": "search", "in": "query", "schema": {"type": "string", "description": "Search in name and description"}}], "responses": {"200": {"description": "List of agents", "content": {"application/json": {"schema": {"type": "object", "properties": {"agents": {"type": "array", "items": {"$ref": "#/components/schemas/Agent"}}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "post": {"summary": "Create a new agent", "description": "Generate and create a new AI agent based on requirements", "tags": ["Agents"], "operationId": "createAgents", "parameters": [], "responses": {"201": {"description": "Agent created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"agent": {"$ref": "#/components/schemas/Agent"}, "task_id": {"type": "string", "format": "uuid", "description": "ID of the generation task"}}}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerationRequest"}}}}}}, "/api/v1/agents/{agent_id}": {"get": {"summary": "Get agent details", "description": "Retrieve detailed information about a specific agent", "tags": ["Agents"], "operationId": "getByIdAgents", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Agent details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Agent"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "put": {"summary": "Update agent", "description": "Update agent configuration and settings", "tags": ["Agents"], "operationId": "updateAgents", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Agent updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Agent"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "configuration": {"type": "object", "additionalProperties": true}, "deployment_config": {"$ref": "#/components/schemas/DeploymentConfig"}}}}}}}, "delete": {"summary": "Delete agent", "description": "Delete an agent and stop all associated processes", "tags": ["Agents"], "operationId": "deleteAgents", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"204": {"description": "Agent deleted successfully"}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/agents/{agent_id}/start": {"post": {"summary": "Start agent", "description": "Start a deployed agent", "tags": ["Agents"], "operationId": "startStart", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Agent started successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string"}, "port": {"type": "integer"}, "process_id": {"type": "integer"}}}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/agents/{agent_id}/stop": {"post": {"summary": "Stop agent", "description": "Stop a running agent", "tags": ["Agents"], "operationId": "stopStop", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Agent stopped successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string"}, "message": {"type": "string"}}}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/migration/analyze": {"post": {"summary": "Analyze application for migration", "description": "Analyze an existing application to assess migration feasibility", "tags": ["Migration"], "operationId": "analyzeAnalyze", "parameters": [], "responses": {"200": {"description": "Analysis completed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplicationAnalysis"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"app_path": {"type": "string", "description": "Path to the application to analyze"}}, "required": ["app_path"]}}}}}}, "/api/v1/migration/plan": {"post": {"summary": "Create migration plan", "description": "Create a detailed migration plan based on application analysis", "tags": ["Migration"], "operationId": "createPlanPlan", "parameters": [], "responses": {"200": {"description": "Migration plan created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MigrationPlan"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"analysis_id": {"type": "string", "format": "uuid"}, "custom_strategy": {"type": "string", "enum": ["wrapper", "rewrite", "hybrid", "interface", "decompose"]}}, "required": ["analysis_id"]}}}}}}, "/api/v1/migration/start": {"post": {"summary": "Start migration", "description": "Start executing a migration plan", "tags": ["Migration"], "operationId": "startStart", "parameters": [], "responses": {"200": {"description": "Migration started", "content": {"application/json": {"schema": {"type": "object", "properties": {"project_id": {"type": "string", "format": "uuid"}, "status": {"type": "string"}, "estimated_timeline_days": {"type": "integer"}}}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"plan_id": {"type": "string", "format": "uuid"}}, "required": ["plan_id"]}}}}}}, "/api/v1/tasks": {"get": {"summary": "List tasks", "description": "Retrieve a list of platform tasks with optional filtering", "tags": ["Tasks"], "operationId": "getTasks", "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "size", "in": "query", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "status", "in": "query", "schema": {"type": "string", "enum": ["pending", "running", "completed", "failed", "cancelled"]}}, {"name": "type", "in": "query", "schema": {"type": "string", "enum": ["generation", "deployment", "execution", "monitoring"]}}, {"name": "agent_id", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "List of tasks", "content": {"application/json": {"schema": {"type": "object", "properties": {"tasks": {"type": "array", "items": {"$ref": "#/components/schemas/Task"}}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/tasks/{task_id}": {"get": {"summary": "Get task details", "description": "Retrieve detailed information about a specific task", "tags": ["Tasks"], "operationId": "getByIdTasks", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Task details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Task"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/system/stats": {"get": {"summary": "Get system statistics", "description": "Retrieve current system performance and usage statistics", "tags": ["System"], "operationId": "getStats", "parameters": [], "responses": {"200": {"description": "System statistics", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemStats"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/system/health": {"get": {"summary": "Health check", "description": "Check system health and availability", "tags": ["System"], "operationId": "getHealth", "parameters": [], "responses": {"200": {"description": "System is healthy", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "enum": ["healthy", "degraded", "unhealthy"]}, "version": {"type": "string"}, "uptime": {"type": "string"}, "components": {"type": "object", "additionalProperties": {"type": "object", "properties": {"status": {"type": "string"}, "message": {"type": "string"}}}}}}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}}, "components": {"schemas": {"Agent": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique agent identifier"}, "name": {"type": "string", "description": "Agent display name"}, "description": {"type": "string", "description": "Agent description"}, "type": {"type": "string", "enum": ["api_service", "background_worker", "scheduled_task", "data_processor"], "description": "Agent type"}, "status": {"type": "string", "enum": ["created", "building", "ready", "running", "paused", "error", "stopped"], "description": "Current status"}, "framework": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "flask", "django", "express", "custom"], "description": "Framework used"}, "language": {"type": "string", "enum": ["python", "javascript", "typescript", "go", "rust"], "description": "Programming language"}, "capabilities": {"type": "array", "items": {"type": "string"}, "description": "Agent capabilities"}, "configuration": {"type": "object", "additionalProperties": true, "description": "Agent configuration"}, "deployment_config": {"$ref": "#/components/schemas/DeploymentConfig"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "version": {"type": "string", "description": "Agent version"}}, "required": ["id", "name", "type", "status", "framework", "language"], "description": "AI Agent definition"}, "DeploymentConfig": {"type": "object", "properties": {"port": {"type": "integer", "minimum": 1024, "maximum": 65535}, "environment": {"type": "object", "additionalProperties": {"type": "string"}}, "resources": {"$ref": "#/components/schemas/ResourceLimits"}, "auto_scale": {"type": "boolean", "default": false}, "replicas": {"type": "integer", "minimum": 1, "default": 1}}, "description": "Agent deployment configuration"}, "ResourceLimits": {"type": "object", "properties": {"cpu_limit": {"type": "string", "pattern": "^\\d+m?$", "description": "CPU limit (e.g., '500m', '1')"}, "memory_limit": {"type": "string", "pattern": "^\\d+[KMG]i?$", "description": "Memory limit (e.g., '512Mi', '1Gi')"}, "disk_limit": {"type": "string", "pattern": "^\\d+[KMG]i?$", "description": "Disk limit"}}, "description": "Resource limits for agent deployment"}, "GenerationRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1, "maxLength": 100}, "description": {"type": "string", "maxLength": 500}, "requirements": {"type": "string", "description": "Natural language requirements"}, "type": {"type": "string", "enum": ["api_service", "background_worker", "data_processor", "integration"]}, "framework": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "flask", "express", "custom"], "default": "<PERSON><PERSON><PERSON>"}, "language": {"type": "string", "enum": ["python", "javascript", "typescript"], "default": "python"}, "capabilities": {"type": "array", "items": {"type": "string"}}, "deployment": {"$ref": "#/components/schemas/DeploymentConfig"}, "advanced_options": {"$ref": "#/components/schemas/AdvancedOptions"}}, "required": ["name", "description", "requirements", "type"], "description": "Request to generate a new agent"}, "AdvancedOptions": {"type": "object", "properties": {"use_ai_workflow": {"type": "boolean", "default": true}, "custom_templates": {"type": "array", "items": {"type": "string"}}, "integration_endpoints": {"type": "array", "items": {"type": "string"}}, "testing_enabled": {"type": "boolean", "default": true}, "documentation_level": {"type": "string", "enum": ["minimal", "standard", "comprehensive"], "default": "standard"}}, "description": "Advanced generation options"}, "Task": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "agent_id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string", "enum": ["generation", "deployment", "execution", "monitoring"]}, "status": {"type": "string", "enum": ["pending", "running", "completed", "failed", "cancelled"]}, "priority": {"type": "string", "enum": ["low", "medium", "high", "critical"], "default": "medium"}, "progress": {"type": "number", "minimum": 0, "maximum": 100}, "metadata": {"type": "object", "additionalProperties": true}, "result": {"type": "object", "additionalProperties": true}, "error": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "started_at": {"type": "string", "format": "date-time"}, "completed_at": {"type": "string", "format": "date-time"}, "logs": {"type": "array", "items": {"$ref": "#/components/schemas/TaskLog"}}}, "required": ["id", "name", "type", "status"], "description": "Platform task definition"}, "TaskLog": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time"}, "level": {"type": "string", "enum": ["debug", "info", "warning", "error"]}, "message": {"type": "string"}, "details": {"type": "object", "additionalProperties": true}}, "required": ["timestamp", "level", "message"], "description": "Task execution log entry"}, "ApplicationAnalysis": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "app_path": {"type": "string"}, "app_type": {"type": "string", "enum": ["web_app", "api_service", "cli_tool", "microservice", "monolith"]}, "technology_stack": {"type": "array", "items": {"type": "string"}}, "complexity_score": {"type": "number", "minimum": 0, "maximum": 10}, "migration_feasibility": {"type": "number", "minimum": 0, "maximum": 10}, "recommended_strategy": {"type": "string", "enum": ["wrapper", "rewrite", "hybrid", "interface", "decompose"]}, "estimated_effort_hours": {"type": "integer", "minimum": 0}, "endpoints_count": {"type": "integer", "minimum": 0}, "models_count": {"type": "integer", "minimum": 0}, "dependencies_count": {"type": "integer", "minimum": 0}, "external_integrations_count": {"type": "integer", "minimum": 0}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["id", "app_path", "app_type", "complexity_score", "migration_feasibility"], "description": "Application analysis results for migration"}, "MigrationPlan": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "analysis_id": {"type": "string", "format": "uuid"}, "strategy": {"type": "string", "enum": ["wrapper", "rewrite", "hybrid", "interface", "decompose"]}, "estimated_timeline_days": {"type": "integer", "minimum": 1}, "phases_count": {"type": "integer", "minimum": 1}, "agents_to_generate": {"type": "integer", "minimum": 1}, "phases": {"type": "array", "items": {"$ref": "#/components/schemas/MigrationPhase"}}, "success_criteria": {"type": "array", "items": {"type": "string"}}, "risk_assessment": {"type": "array", "items": {"$ref": "#/components/schemas/RiskItem"}}, "resource_requirements": {"type": "object", "additionalProperties": true}}, "required": ["id", "analysis_id", "strategy", "estimated_timeline_days"], "description": "Detailed migration plan"}, "MigrationPhase": {"type": "object", "properties": {"phase": {"type": "integer", "minimum": 1}, "name": {"type": "string"}, "description": {"type": "string"}, "tasks": {"type": "array", "items": {"type": "string"}}, "duration_days": {"type": "integer", "minimum": 1}}, "required": ["phase", "name", "duration_days"], "description": "Migration phase definition"}, "RiskItem": {"type": "object", "properties": {"risk": {"type": "string"}, "probability": {"type": "string", "enum": ["low", "medium", "high"]}, "impact": {"type": "string", "enum": ["low", "medium", "high"]}, "mitigation": {"type": "string"}}, "required": ["risk", "probability", "impact", "mitigation"], "description": "Risk assessment item"}, "SystemStats": {"type": "object", "properties": {"total_agents": {"type": "integer", "minimum": 0}, "active_agents": {"type": "integer", "minimum": 0}, "queued_tasks": {"type": "integer", "minimum": 0}, "completed_tasks": {"type": "integer", "minimum": 0}, "failed_tasks": {"type": "integer", "minimum": 0}, "avg_response_time": {"type": "number", "minimum": 0}, "system_load": {"type": "number", "minimum": 0, "maximum": 100}, "memory_usage": {"type": "number", "minimum": 0, "maximum": 100}, "disk_usage": {"type": "number", "minimum": 0, "maximum": 100}, "uptime": {"type": "string"}, "success_rate": {"type": "number", "minimum": 0, "maximum": 100}}, "description": "System performance statistics"}, "Error": {"type": "object", "properties": {"error": {"type": "string", "description": "Error message"}, "error_code": {"type": "string", "description": "Machine-readable error code"}, "details": {"type": "object", "additionalProperties": true, "description": "Additional error details"}, "timestamp": {"type": "string", "format": "date-time"}, "request_id": {"type": "string", "format": "uuid"}}, "required": ["error"], "description": "Standard error response"}, "PaginationInfo": {"type": "object", "properties": {"page": {"type": "integer", "minimum": 1}, "size": {"type": "integer", "minimum": 1, "maximum": 100}, "total": {"type": "integer", "minimum": 0}, "pages": {"type": "integer", "minimum": 0}, "has_next": {"type": "boolean"}, "has_prev": {"type": "boolean"}}, "required": ["page", "size", "total", "pages", "has_next", "has_prev"], "description": "Pagination information"}}, "securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}, "ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-API-Key"}}}, "security": [{"BearerAuth": []}, {"ApiKeyAuth": []}]}