#!/usr/bin/env python3
"""
Test network connection fix
"""

import requests
import time

def main():
    print('🔧 Testing Network Connection Fix')
    print('=' * 50)

    # Test backend
    try:
        backend = requests.get('http://localhost:8000/health', timeout=5)
        print(f'✅ Backend: {backend.status_code} - {backend.json()}')
    except Exception as e:
        print(f'❌ Backend: {e}')
        return

    # Test frontend
    try:
        frontend = requests.get('http://localhost:3001', timeout=10)
        print(f'✅ Frontend: {frontend.status_code} - Running')
    except Exception as e:
        print(f'❌ Frontend: {e}')
        return

    # Test CORS with frontend origin
    print('\n🌐 Testing CORS...')
    headers = {
        'Origin': 'http://localhost:3001',
        'Content-Type': 'application/json'
    }

    try:
        # Test agents endpoint
        agents_response = requests.get('http://localhost:8000/api/v1/public/agents', headers=headers, timeout=5)
        print(f'✅ CORS Agents: {agents_response.status_code}')
        
        # Test system stats
        stats_response = requests.get('http://localhost:8000/api/v1/public/runtime/system/stats', headers=headers, timeout=5)
        print(f'✅ CORS Stats: {stats_response.status_code}')
        
        # Test agent creation
        agent_data = {
            'name': 'CORS Test Agent',
            'description': 'Testing CORS functionality',
            'type': 'assistant',
            'config': {},
            'capabilities': ['conversation']
        }
        
        create_response = requests.post('http://localhost:8000/api/v1/public/agents', json=agent_data, headers=headers, timeout=10)
        print(f'✅ CORS Create: {create_response.status_code}')
        
        if create_response.status_code == 200:
            agent = create_response.json()
            print(f'   Created: {agent["name"]} (ID: {agent["id"]})')
        
    except Exception as e:
        print(f'❌ CORS Error: {e}')

    print('\n🎯 Network connection should be fixed!')
    print('🌐 Frontend should now connect to backend properly')
    print('\n📱 Test these URLs:')
    print('   Frontend: http://localhost:3001')
    print('   Dashboard: http://localhost:3001/dashboard')
    print('   Create Agent: http://localhost:3001/agents/create')

if __name__ == "__main__":
    main()
