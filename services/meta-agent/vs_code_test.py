#!/usr/bin/env python3
"""
VS Code Python Terminal Complete Test
"""

import subprocess
import time
import requests
import threading
import os

def start_backend():
    """Start backend server"""
    print("🚀 Starting Backend Server...")
    os.chdir("D:/mono/services/meta-agent/backend/src")
    
    try:
        # Start uvicorn server
        result = subprocess.run([
            "python", "-m", "uvicorn", "main_simple:app", 
            "--host", "0.0.0.0", "--port", "8000", "--reload"
        ], capture_output=False, text=True)
    except Exception as e:
        print(f"❌ Backend Error: {e}")

def start_frontend():
    """Start frontend server"""
    print("🌐 Starting Frontend Server...")
    os.chdir("D:/mono/services/meta-agent/frontend")
    
    try:
        result = subprocess.run(["npm", "run", "dev"], capture_output=False, text=True)
    except Exception as e:
        print(f"❌ Frontend Error: {e}")

def test_servers():
    """Test both servers"""
    print("🧪 Testing Servers...")
    time.sleep(10)  # Wait for servers to start
    
    # Test Backend
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        print(f"✅ Backend: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"❌ Backend Test: {e}")
    
    # Test Frontend
    try:
        response = requests.get("http://localhost:3000", timeout=10)
        print(f"✅ Frontend: {response.status_code} - Running")
    except Exception as e:
        print(f"❌ Frontend Test: {e}")
    
    # Test Agents API
    try:
        response = requests.get("http://localhost:8000/api/v1/public/agents", timeout=5)
        data = response.json()
        print(f"✅ Agents API: {response.status_code} - Total: {data.get('total', 0)}")
    except Exception as e:
        print(f"❌ Agents API Test: {e}")
    
    print("\n🎯 VS Code Python Terminal Setup Complete!")
    print("🌐 URLs:")
    print("   Backend: http://localhost:8000")
    print("   Frontend: http://localhost:3000")
    print("   Dashboard: http://localhost:3000/dashboard")
    print("   Create Agent: http://localhost:3000/agents/create")

def main():
    print("🎉 VS Code Python Terminal Setup")
    print("=" * 50)
    
    # Start backend in thread
    backend_thread = threading.Thread(target=start_backend, daemon=True)
    backend_thread.start()
    
    # Wait a bit
    time.sleep(5)
    
    # Start frontend in thread
    frontend_thread = threading.Thread(target=start_frontend, daemon=True)
    frontend_thread.start()
    
    # Test servers
    test_servers()
    
    # Keep running
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n👋 Stopping servers...")

if __name__ == "__main__":
    main()
