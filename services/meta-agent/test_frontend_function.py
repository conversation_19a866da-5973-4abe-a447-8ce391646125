#!/usr/bin/env python3
"""
Test that frontend can access the deployed agent interface
"""

import requests
import time

def test_frontend_deployed_agent():
    """Test frontend access to deployed agent"""
    
    print('🌐 Testing Frontend Deployed Agent Access')
    print('=' * 50)
    
    # Test that frontend is running
    try:
        response = requests.get('http://localhost:3000', timeout=5)
        print(f'✅ Frontend: {response.status_code} - Running')
    except Exception as e:
        print(f'❌ Frontend not accessible: {e}')
        return False
    
    # Test that backend is running
    try:
        response = requests.get('http://localhost:8000/health', timeout=5)
        print(f'✅ Backend: {response.status_code} - Running')
    except Exception as e:
        print(f'❌ Backend not accessible: {e}')
        return False
    
    # Get a deployed agent ID (use the one we just created)
    agent_id = '846135af-2da4-4e84-8c20-74ff9ca94896'
    
    # Test the deployed agent page directly
    try:
        response = requests.get(f'http://localhost:3000/deployed/{agent_id}', timeout=10)
        print(f'✅ Deployed Agent Page: {response.status_code} - Accessible')
        
        # Check if the page contains expected content
        if 'Interface Test Agent' in response.text or 'Deployed Agent Interface' in response.text:
            print('✅ Page Content: Contains expected agent information')
        else:
            print('⚠️  Page Content: May not be fully loaded yet')
            
    except Exception as e:
        print(f'❌ Deployed Agent Page: {e}')
        return False
    
    # Test the API endpoint that the frontend calls
    try:
        response = requests.get(f'http://localhost:8000/api/v1/public/deployed/{agent_id}', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f'✅ API Endpoint: Working - {data["interface"]["title"]}')
        else:
            print(f'❌ API Endpoint: {response.status_code} - {response.text}')
            return False
    except Exception as e:
        print(f'❌ API Endpoint: {e}')
        return False
    
    print(f'\n🎯 Test Results:')
    print(f'✅ Frontend server is running')
    print(f'✅ Backend server is running')
    print(f'✅ Deployed agent page is accessible')
    print(f'✅ API endpoint is working')
    print(f'✅ getDeployedAgentInterface function should now work')
    
    print(f'\n💡 Next Steps:')
    print(f'1. Open http://localhost:3000/deployed/{agent_id}')
    print(f'2. Check browser console for any JavaScript errors')
    print(f'3. Test the chat functionality')
    print(f'4. Verify agent deployment status')
    
    return True

if __name__ == "__main__":
    success = test_frontend_deployed_agent()
    if success:
        print('\n🎉 Frontend deployed agent access is working!')
    else:
        print('\n❌ There are still issues with frontend access.')
