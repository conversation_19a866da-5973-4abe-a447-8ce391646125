# AI Agent Platform 🤖

**The Next-Generation AI-Powered Agent Development and Management Platform**

A comprehensive, enterprise-grade platform for creating, deploying, and managing AI agents with advanced capabilities including multi-provider AI integration, self-healing infrastructure, and intelligent workflow automation.

[![CI/CD](https://github.com/your-org/ai-agent-platform/actions/workflows/ci.yml/badge.svg)](https://github.com/your-org/ai-agent-platform/actions/workflows/ci.yml)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Python](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![Node.js](https://img.shields.io/badge/node.js-18+-green.svg)](https://nodejs.org/)
[![Docker](https://img.shields.io/badge/docker-ready-blue.svg)](https://www.docker.com/)

## 🚀 Features

### 🎯 Core Capabilities
- **AI-Powered Agent Generation**: Create intelligent agents from natural language prompts
- **Multi-Provider AI Gateway**: Support for OpenAI, Anthropic, Google AI with intelligent routing
- **Auto-Deployment System**: Automatic containerization and deployment with dynamic port management (30000-32000)
- **Self-Healing Infrastructure**: AI-powered monitoring and automatic recovery with 8 healing actions
- **Visual Workflow Builder**: n8n-style drag-and-drop interface for complex workflows
- **Platform Self-Evolution**: Continuous improvement through usage pattern analysis

### 🔧 Advanced Features
- **LangChain Integration**: Enhanced AI capabilities with chains, tools, and memory management
- **MCP Server Integration**: Extensible tool ecosystem via Model Context Protocol (7 built-in servers)
- **Compound Agents**: JSON-based multi-agent workflow definitions with 10 node types
- **Application Migration**: Automated conversion of existing apps to AI agents with 5 migration strategies
- **Multi-Protocol Communication**: REST, gRPC, WebSocket, TCP, UDP, and Message Queue support
- **Intelligence Layer**: Advanced AI processing with context management and embeddings

### 🎨 User Experience
- **Modern Frontend**: Built with Next.js, Tailwind CSS, and ShadCN UI components
- **Type-Safe API**: OpenAPI-generated TypeScript client with React Query
- **Real-Time Updates**: WebSocket integration for live monitoring
- **Responsive Design**: Mobile-first approach with dark/light theme support

### 🛠️ Technical Stack
- **Backend**: Python 3.11+, FastAPI, SQLAlchemy, PostgreSQL, Redis
- **Frontend**: Next.js 14, React 18, TypeScript, Tailwind CSS, ShadCN UI
- **AI Integration**: OpenAI, Anthropic, Google AI, LangChain
- **Infrastructure**: Docker, Kubernetes, Nginx, Prometheus, Grafana
- **Protocols**: REST, GraphQL, WebSocket, gRPC, TCP/UDP, Message Queues

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   AI Services   │
│   (Next.js)     │◄──►│   (FastAPI)     │◄──►│   (Multi-provider)
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │   Message Queue │              │
         └──────────────┤    (Kafka)      │──────────────┘
                        └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Database      │
                    │ (PostgreSQL +   │
                    │    Redis)       │
                    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Python 3.8+ with pip
- Node.js 18+ with npm
- Docker and Docker Compose (optional but recommended)
- Git

### 1. Clone and Setup
```bash
git clone https://github.com/your-org/ai-agent-platform.git
cd ai-agent-platform

# Quick setup with our automated script
make setup
# or
./scripts/dev-setup.sh
```

### 2. Configure API Keys
```bash
# Copy environment templates
cp backend/.env.example backend/.env
cp frontend/.env.local.example frontend/.env.local

# Edit backend/.env and add your API keys:
# OPENAI_API_KEY=your_openai_key_here
# ANTHROPIC_API_KEY=your_anthropic_key_here
# GOOGLE_API_KEY=your_google_key_here
```

### 3. Start Development Environment
```bash
# Start all services (database, cache, backend, frontend)
make dev
# or
./scripts/start-dev.sh
```

### 4. Access the Platform
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Monitoring**: http://localhost:3001 (Grafana - admin/admin)
- **Metrics**: http://localhost:9090 (Prometheus)

### Production Deployment Options

#### Docker Compose (Recommended for Single Server)
```bash
# Configure environment
cp .env.prod.template .env.prod
# Edit .env.prod with production values

# Deploy
./scripts/deploy.sh --method docker-compose --environment production
# or
make deploy-production DOMAIN=your-domain.com
```

#### Kubernetes (Recommended for Scalability)
```bash
# Deploy to Kubernetes
./scripts/deploy.sh --method kubernetes \
  --namespace ai-platform \
  --domain your-domain.com \
  --registry your-registry.com
# or
make deploy-k8s NAMESPACE=ai-platform DOMAIN=your-domain.com
```

## 📚 Documentation

### API Documentation
- **Interactive API Docs**: `/docs` (Swagger UI)
- **OpenAPI Spec**: `/openapi.json`
- **Authentication**: JWT-based with refresh tokens

### Key Endpoints
- `POST /api/v1/auth/login` - User authentication
- `GET /api/v1/agents` - List agents
- `POST /api/v1/agents` - Create agent
- `GET /api/v1/orchestrations` - List orchestrations
- `POST /api/v1/orchestrations` - Create orchestration
- `GET /api/v1/tasks` - List tasks
- `POST /api/v1/tasks` - Create task
- `POST /api/v1/ai/chat` - AI chat completion
- `POST /api/v1/ai/code` - AI code generation

### Architecture Documentation
- [API Architecture](docs/api/README.md)
- [Database Schema](docs/architecture/database.md)
- [Deployment Guide](docs/deployment/README.md)

## 🛠️ Development

### Development Workflow

#### 1. Setting up Development Environment
```bash
# Clone repository
git clone https://github.com/your-org/ai-agent-platform.git
cd ai-agent-platform

# Setup environment
make setup

# Start development services
make dev
```

#### 2. Making Changes
```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Make changes and test
make test
make lint
make type-check

# Run full CI pipeline locally
make ci
```

#### 3. Available Make Commands
```bash
make help           # Show all available commands
make install        # Install dependencies
make dev           # Start development environment
make test          # Run tests
make build         # Build applications
make deploy        # Deploy to production
make backup        # Create backup
make docs          # Generate documentation
```

### Code Quality
We maintain high code quality standards:

```bash
# Format code
make format

# Lint code
make lint

# Type checking
make type-check

# Security scanning
make security-scan

# Run all quality checks
make quality
```

### Testing
```bash
# Run all tests
make test

# Run specific test suites
make test-backend
make test-frontend
make test-integration

# Run with coverage
make test-coverage

# End-to-end tests
make test-e2e
```

### Useful Scripts
- `./scripts/dev-setup.sh` - Development environment setup
- `./scripts/deploy.sh` - Production deployment
- `./scripts/backup-restore.sh` - Backup and restore operations
- `./scripts/check-quality.sh` - Code quality checks

## 🔧 Configuration

### Environment Variables
Key configuration options:

- `ENVIRONMENT`: development/staging/production
- `DEBUG`: Enable debug mode
- `SECRET_KEY`: Application secret key
- `DATABASE_URL`: PostgreSQL connection string
- `REDIS_URL`: Redis connection string
- `KAFKA_BOOTSTRAP_SERVERS`: Kafka broker addresses
- `OPENAI_API_KEY`: OpenAI API key
- `ANTHROPIC_API_KEY`: Anthropic API key

See [`.env.example`](.env.example) for full configuration options.

### Scaling Configuration
```yaml
# docker-compose.prod.yml
backend:
  deploy:
    replicas: 4
    resources:
      limits:
        cpus: '1.0'
        memory: 1G
```

## 📊 Monitoring

### Metrics and Monitoring
- **Prometheus**: Metrics collection at `/metrics`
- **Grafana**: Visualization dashboards
- **Health Checks**: Service health at `/health`
- **Structured Logging**: JSON logs with correlation IDs

### Key Metrics
- Agent performance and resource usage
- Task execution times and success rates
- API response times and error rates
- System resource utilization
- AI provider usage and costs

## 🔒 Security

### Security Features
- JWT authentication with refresh tokens
- Role-based access control (RBAC)
- API rate limiting
- SQL injection prevention
- XSS protection headers
- HTTPS/TLS encryption
- Container security best practices

### Security Headers
```
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000
```

## 🚀 Performance

### Performance Features
- **Async/Await**: Non-blocking I/O operations
- **Connection Pooling**: Database and Redis connection pooling
- **Caching**: Redis caching for frequently accessed data
- **Load Balancing**: Nginx reverse proxy with upstream servers
- **Compression**: Gzip compression for API responses
- **CDN Ready**: Static asset optimization

### Performance Targets
- **API Response Time**: < 200ms (95th percentile)
- **Agent Startup Time**: < 5 seconds
- **Concurrent Agents**: 1000+ per node
- **Task Throughput**: 100+ tasks/second

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Run tests (`make test`)
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

### Development Guidelines
- Follow PEP 8 for Python code
- Use TypeScript for all new frontend code
- Write tests for new features
- Update documentation for API changes
- Use conventional commits

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Getting Help
- **Documentation**: Check the `/docs` directory
- **Issues**: Create GitHub issues for bugs
- **Discussions**: Use GitHub discussions for questions

### Troubleshooting
- **Logs**: `docker-compose logs <service>`
- **Health**: Check `/health` endpoints
- **Metrics**: Monitor Grafana dashboards
- **Database**: Connect to PostgreSQL for debugging

## 🏆 Acknowledgments

Built with these amazing technologies:
- [FastAPI](https://fastapi.tiangolo.com/) - Modern Python web framework
- [Next.js](https://nextjs.org/) - React production framework
- [PostgreSQL](https://www.postgresql.org/) - Advanced relational database
- [Apache Kafka](https://kafka.apache.org/) - Event streaming platform
- [Redis](https://redis.io/) - In-memory data structure store
- [Prometheus](https://prometheus.io/) - Monitoring and alerting toolkit