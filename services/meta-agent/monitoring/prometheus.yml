# AI Agent Platform - Prometheus Development Configuration

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'ai-agent-platform'
    environment: 'development'

rule_files:
  - "/etc/prometheus/rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 15s
    metrics_path: '/metrics'

  # AI Agent Platform Backend
  - job_name: 'ai-agent-backend'
    static_configs:
      - targets: ['backend:8000']
    scrape_interval: 30s
    metrics_path: '/metrics'
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # PostgreSQL Database
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s
    metrics_path: '/metrics'
    # Note: Requires postgres_exporter sidecar container

  # Redis Cache
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
    metrics_path: '/metrics'
    # Note: Requires redis_exporter sidecar container

  # Apache Kafka
  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka:9092']
    scrape_interval: 30s
    metrics_path: '/metrics'
    # Note: Requires kafka JMX exporter

  # Node.js Frontend (if metrics enabled)
  - job_name: 'ai-agent-frontend'
    static_configs:
      - targets: ['frontend:3000']
    scrape_interval: 60s
    metrics_path: '/api/metrics'
    scrape_timeout: 10s

  # Nginx Reverse Proxy
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:8080']
    scrape_interval: 30s
    metrics_path: '/nginx_status'
    # Note: Requires nginx-prometheus-exporter

  # System metrics (if node_exporter is deployed)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['host.docker.internal:9100']
    scrape_interval: 30s
    metrics_path: '/metrics'

  # Docker container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metrics_path: '/metrics'

# Storage configuration
storage:
  tsdb:
    path: /prometheus
    retention.time: 15d
    retention.size: 10GB