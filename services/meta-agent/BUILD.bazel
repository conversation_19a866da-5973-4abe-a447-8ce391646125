load("@rules_multirun//:defs.bzl", "command", "multirun")

package(default_visibility = ["//visibility:public"])

# Meta-Agent platform build target that includes all components
filegroup(
    name = "meta_agent",
    srcs = [
        "//services/meta-agent/backend",
        "//services/meta-agent/frontend:build",
    ],
    visibility = ["//visibility:public"],
)

# Platform services binary targets
filegroup(
    name = "meta_agent_services", 
    srcs = [
        "//services/meta-agent/backend",
    ],
    visibility = ["//visibility:public"],
)

# Web application build targets
filegroup(
    name = "meta_agent_web",
    srcs = [
        "//services/meta-agent/frontend:build",
        "//services/meta-agent/frontend:dev",
        "//services/meta-agent/frontend:generate_client",
    ],
    visibility = ["//visibility:public"],
)

# API clients and OpenAPI specifications
filegroup(
    name = "meta_agent_api",
    srcs = [
        "//services/meta-agent/backend/openapi:openapi_specs",
    ],
    visibility = ["//visibility:public"],
)

# Database setup for meta-agent (when implemented)
filegroup(
    name = "meta_agent_db",
    srcs = [
        # "//services/meta-agent/db:setup",
        # "//services/meta-agent/db:migrate",
    ],
    visibility = ["//visibility:public"],
)

# Test suites for meta-agent
test_suite(
    name = "meta_agent_unit_tests",
    tags = [
        "meta-agent",
        "unit",
    ],
)

test_suite(
    name = "meta_agent_integration_tests", 
    tags = [
        "integration",
        "meta-agent",
    ],
    tests = [
        "//services/meta-agent/frontend:test_integration",
        "//services/meta-agent/backend:backend_tests",
    ],
)

test_suite(
    name = "meta_agent_e2e_tests",
    tags = [
        "e2e",
        "meta-agent",
    ],
    tests = [
        "//services/meta-agent/frontend:test_e2e",
    ],
)

# All meta-agent tests
test_suite(
    name = "meta_agent_tests",
    tags = ["meta-agent"],
    tests = [
        ":meta_agent_unit_tests",
        ":meta_agent_integration_tests", 
        ":meta_agent_e2e_tests",
    ],
)

# Multi-run commands for convenient service startup
command(
    name = "start_backend",
    command = "//services/meta-agent/backend:backend",
)

command(
    name = "start_frontend",
    command = "//services/meta-agent/frontend:dev",
)

# Backend services only
multirun(
    name = "backend",
    commands = [
        "start_backend",
    ],
    jobs = 0,  # Run in parallel
    visibility = ["//visibility:public"],
)

# Frontend only
multirun(
    name = "frontend", 
    commands = [
        "start_frontend",
    ],
    jobs = 0,
    visibility = ["//visibility:public"],
)

# All services (backend + frontend)
multirun(
    name = "local_all",
    commands = [
        "start_backend",
        "start_frontend", 
    ],
    jobs = 0,  # Run in parallel
    visibility = ["//visibility:public"],
)

# Development workflow commands
multirun(
    name = "dev",
    commands = [
        "start_backend",
        "start_frontend",
    ],
    jobs = 0,
    visibility = ["//visibility:public"],
)

# Convenient aliases for common operations
alias(
    name = "build_all",
    actual = ":meta_agent",
    visibility = ["//visibility:public"],
)

alias(
    name = "test_all", 
    actual = ":meta_agent_tests",
    visibility = ["//visibility:public"],
)

alias(
    name = "generate_client",
    actual = "//services/meta-agent/frontend/api-client:generate_client",
    visibility = ["//visibility:public"],
)