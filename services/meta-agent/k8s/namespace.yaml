apiVersion: v1
kind: Namespace
metadata:
  name: ai-agent-platform
  labels:
    name: ai-agent-platform
    app.kubernetes.io/name: ai-agent-platform
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/component: namespace
    app.kubernetes.io/part-of: ai-agent-platform
---
# Network Policy for namespace isolation
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: ai-agent-platform-network-policy
  namespace: ai-agent-platform
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ai-agent-platform
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - namespaceSelector:
        matchLabels:
          name: monitoring
  egress:
  - {}  # Allow all egress traffic