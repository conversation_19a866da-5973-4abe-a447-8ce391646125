apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-agent-platform-config
  namespace: ai-agent-platform
  labels:
    app.kubernetes.io/name: ai-agent-platform
    app.kubernetes.io/component: config
data:
  # Application configuration
  APP_NAME: "AI Agent Platform"
  APP_VERSION: "1.0.0"
  APP_DESCRIPTION: "Enterprise AI Agent Platform"
  ENVIRONMENT: "production"
  DEBUG: "false"
  
  # Server settings
  HOST: "0.0.0.0"
  PORT: "8000"
  WORKERS: "4"
  
  # URLs
  BACKEND_URL: "https://api.ai-agent-platform.com"
  FRONTEND_URL: "https://ai-agent-platform.com"
  
  # Database settings
  POSTGRES_SERVER: "postgresql-service"
  POSTGRES_PORT: "5432"
  POSTGRES_DB: "ai_agent_platform"
  
  # Redis settings
  REDIS_SERVER: "redis-service"
  REDIS_PORT: "6379"
  REDIS_DB: "0"
  
  # Kafka settings
  KAFKA_BOOTSTRAP_SERVERS: "kafka-service:9092"
  KAFKA_GROUP_ID: "ai_agent_platform"
  KAFKA_AUTO_OFFSET_RESET: "earliest"
  
  # Kafka topics
  KAFKA_AGENT_EVENTS_TOPIC: "agent.events"
  KAFKA_ORCHESTRATION_TOPIC: "orchestration.commands"
  KAFKA_INTELLIGENCE_TOPIC: "intelligence.requests"
  
  # Vector database settings
  QDRANT_HOST: "qdrant"
  QDRANT_PORT: "6333"
  QDRANT_TIMEOUT: "30.0"
  
  # Embedding model settings
  EMBEDDING_MODEL: "all-MiniLM-L6-v2"
  EMBEDDING_DIMENSION: "384"
  VECTOR_COLLECTION_PREFIX: "ai_agent_platform_"
  
  # JWT settings
  JWT_ALGORITHM: "HS256"
  ACCESS_TOKEN_EXPIRE_MINUTES: "60"
  REFRESH_TOKEN_EXPIRE_DAYS: "7"
  
  # MFA settings
  SMS_PROVIDER: "aws_sns"
  AWS_REGION: "us-east-1"
  TOTP_ISSUER: "AI Agent Platform"
  BACKUP_CODES_COUNT: "10"
  
  # AI service settings
  DEFAULT_AI_MODEL: "gpt-4"
  MAX_AI_TOKENS: "4096"
  AI_TEMPERATURE: "0.7"
  GOOGLE_AI_LOCATION: "us-central1"
  
  # Agent settings
  MAX_CONCURRENT_AGENTS: "1000"
  AGENT_STARTUP_TIMEOUT: "30"
  AGENT_IDLE_TIMEOUT: "300"
  AGENT_MAX_MEMORY_MB: "512"
  AGENT_MAX_CPU_PERCENT: "50.0"
  
  # Monitoring settings
  LOG_LEVEL: "INFO"
  LOG_FORMAT: "json"
  PROMETHEUS_ENABLED: "true"
  PROMETHEUS_PORT: "8000"
  SENTRY_ENVIRONMENT: "production"
  
  # CORS settings
  ALLOWED_HOSTS: "ai-agent-platform.com,api.ai-agent-platform.com"
  CORS_ORIGINS: "https://ai-agent-platform.com,https://app.ai-agent-platform.com"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: ai-agent-platform
  labels:
    app.kubernetes.io/name: nginx
    app.kubernetes.io/component: config
data:
  nginx.conf: |
    upstream backend {
        server ai-agent-backend-service:8000;
    }
    
    upstream frontend {
        server ai-agent-frontend-service:3000;
    }
    
    server {
        listen 80;
        server_name _;
        return 301 https://$server_name$request_uri;
    }
    
    server {
        listen 443 ssl http2;
        server_name ai-agent-platform.com;
        
        ssl_certificate /etc/ssl/certs/tls.crt;
        ssl_certificate_key /etc/ssl/private/tls.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        
        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self' https:; frame-ancestors 'none';";
        
        # Rate limiting
        limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
        limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;
        
        # Frontend
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }
        
        # API endpoints
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
        }
        
        # Auth endpoints with stricter rate limiting
        location /auth/ {
            limit_req zone=auth burst=10 nodelay;
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Health checks
        location /health {
            proxy_pass http://backend;
            access_log off;
        }
        
        # Metrics endpoint (restricted access)
        location /metrics {
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;
            proxy_pass http://backend;
        }
    }