# Kubernetes Secrets for AI Agent Platform
# NOTE: These are template values. Replace with actual encrypted values in production.
# Use: kube<PERSON>l create secret generic <secret-name> --from-literal=key=value

apiVersion: v1
kind: Secret
metadata:
  name: ai-agent-platform-secrets
  namespace: ai-agent-platform
type: Opaque
stringData:
  # Database credentials
  DATABASE_PASSWORD: "your-secure-db-password-here"
  
  # JWT Secret Key (generate with: openssl rand -hex 32)
  SECRET_KEY: "your-jwt-secret-key-here-should-be-at-least-32-characters-long"
  
  # MFA Encryption Key (generate with: from cryptography.fernet import Fernet; Fernet.generate_key())
  MFA_ENCRYPTION_KEY: "your-mfa-encryption-key-here"
  
  # OAuth Credentials
  OAUTH_GOOGLE_CLIENT_ID: "your-google-oauth-client-id"
  OAUTH_GOOGLE_CLIENT_SECRET: "your-google-oauth-client-secret"
  OAUTH_GITHUB_CLIENT_ID: "your-github-oauth-client-id"
  OAUTH_GITHUB_CLIENT_SECRET: "your-github-oauth-client-secret"
  OAUTH_MICROSOFT_CLIENT_ID: "your-microsoft-oauth-client-id"
  OAUTH_MICROSOFT_CLIENT_SECRET: "your-microsoft-oauth-client-secret"
  
  # AI Service API Keys
  OPENAI_API_KEY: "your-openai-api-key"
  ANTHROPIC_API_KEY: "your-anthropic-api-key"
  
  # SMS Service Configuration (if using Twilio)
  TWILIO_ACCOUNT_SID: "your-twilio-account-sid"
  TWILIO_AUTH_TOKEN: "your-twilio-auth-token"
  TWILIO_PHONE_NUMBER: "your-twilio-phone-number"
  
  # Email Service Configuration (if using SMTP)
  SMTP_SERVER: "smtp.gmail.com"
  SMTP_PORT: "587"
  SMTP_USERNAME: "<EMAIL>"
  SMTP_PASSWORD: "your-email-app-password"
  
  # Monitoring and Analytics (optional)
  SENTRY_DSN: "your-sentry-dsn-for-error-tracking"
  GRAFANA_ADMIN_PASSWORD: "your-grafana-admin-password"

---
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secret
  namespace: ai-agent-platform
type: Opaque
stringData:
  POSTGRES_DB: "ai_agent_platform"
  POSTGRES_USER: "ai_agent_user"
  POSTGRES_PASSWORD: "your-secure-db-password-here"

---
apiVersion: v1
kind: Secret
metadata:
  name: redis-secret
  namespace: ai-agent-platform
type: Opaque
stringData:
  REDIS_PASSWORD: "your-redis-password-here"

---
# TLS Secret for HTTPS (create this when you have SSL certificates)
# apiVersion: v1
# kind: Secret
# metadata:
#   name: tls-secret
#   namespace: ai-agent-platform
# type: kubernetes.io/tls
# data:
#   tls.crt: <base64-encoded-certificate>
#   tls.key: <base64-encoded-private-key>