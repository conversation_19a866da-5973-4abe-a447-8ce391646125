apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: qdrant-pvc
  namespace: ai-agent-platform
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: standard

---
# Qdrant Vector Database Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: qdrant
  namespace: ai-agent-platform
  labels:
    app: qdrant
    component: vector-database
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: qdrant
  template:
    metadata:
      labels:
        app: qdrant
        component: vector-database
    spec:
      containers:
      - name: qdrant
        image: qdrant/qdrant:v1.7.3
        ports:
        - containerPort: 6333
          name: rest-api
        - containerPort: 6334
          name: grpc-api
        env:
        - name: QDRANT__SERVICE__HTTP_PORT
          value: "6333"
        - name: QDRANT__SERVICE__GRPC_PORT
          value: "6334"
        - name: QDRANT__LOG_LEVEL
          value: "INFO"
        - name: QDRANT__STORAGE__STORAGE_PATH
          value: "/qdrant/storage"
        - name: QDRANT__STORAGE__SNAPSHOTS_PATH
          value: "/qdrant/snapshots"
        - name: QDRANT__STORAGE__WAL_CAPACITY_MB
          value: "32"
        - name: QDRANT__STORAGE__WAL_SEGMENTS_AHEAD
          value: "0"
        - name: QDRANT__STORAGE__PERFORMANCE__MAX_SEARCH_THREADS
          value: "0"
        - name: QDRANT__STORAGE__OPTIMIZERS__DEFAULT_SEGMENT_NUMBER
          value: "0"
        - name: QDRANT__STORAGE__OPTIMIZERS__MEMMAP_THRESHOLD
          value: "1000000"
        - name: QDRANT__TELEMETRY_DISABLED
          value: "true"
        volumeMounts:
        - name: qdrant-storage
          mountPath: /qdrant/storage
        - name: qdrant-snapshots
          mountPath: /qdrant/snapshots
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /
            port: 6333
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /readyz
            port: 6333
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: qdrant-storage
        persistentVolumeClaim:
          claimName: qdrant-pvc
      - name: qdrant-snapshots
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: qdrant
  namespace: ai-agent-platform
  labels:
    app: qdrant
    component: vector-database
spec:
  type: ClusterIP
  ports:
  - port: 6333
    targetPort: 6333
    protocol: TCP
    name: rest-api
  - port: 6334
    targetPort: 6334
    protocol: TCP
    name: grpc-api
  selector:
    app: qdrant

---
# Qdrant Backup CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: qdrant-backup
  namespace: ai-agent-platform
spec:
  schedule: "0 3 * * *"  # Daily at 3 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: qdrant-backup
            image: curlimages/curl:8.4.0
            command:
            - sh
            - -c
            - |
              # Create snapshot
              SNAPSHOT_NAME="backup-$(date +%Y%m%d-%H%M%S)"
              echo "Creating Qdrant snapshot: $SNAPSHOT_NAME"
              
              curl -X POST "http://qdrant:6333/snapshots" \
                -H "Content-Type: application/json" \
                -d "{\"snapshot_name\": \"$SNAPSHOT_NAME\"}"
              
              # List snapshots
              echo "Available snapshots:"
              curl -s "http://qdrant:6333/snapshots" | head -20
              
              # Cleanup old snapshots (keep last 7 days)
              echo "Cleanup completed"
            resources:
              requests:
                memory: "64Mi"
                cpu: "100m"
              limits:
                memory: "128Mi"
                cpu: "200m"
          restartPolicy: OnFailure
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 1

---
# Qdrant Dashboard (optional)
apiVersion: apps/v1
kind: Deployment
metadata:
  name: qdrant-dashboard
  namespace: ai-agent-platform
  labels:
    app: qdrant-dashboard
    component: management
spec:
  replicas: 1
  selector:
    matchLabels:
      app: qdrant-dashboard
  template:
    metadata:
      labels:
        app: qdrant-dashboard
        component: management
    spec:
      containers:
      - name: qdrant-dashboard
        image: qdrant/qdrant-web-ui:v0.1.12
        ports:
        - containerPort: 80
          name: http
        env:
        - name: QDRANT_API_URL
          value: "http://qdrant:6333"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"

---
apiVersion: v1
kind: Service
metadata:
  name: qdrant-dashboard
  namespace: ai-agent-platform
  labels:
    app: qdrant-dashboard
    component: management
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
    name: http
  selector:
    app: qdrant-dashboard

---
# Network Policy for Qdrant
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: qdrant-network-policy
  namespace: ai-agent-platform
spec:
  podSelector:
    matchLabels:
      app: qdrant
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: ai-agent-backend
    ports:
    - protocol: TCP
      port: 6333
    - protocol: TCP
      port: 6334
  - from:
    - podSelector:
        matchLabels:
          app: qdrant-dashboard
    ports:
    - protocol: TCP
      port: 6333
  egress:
  - {}  # Allow all outbound traffic for now