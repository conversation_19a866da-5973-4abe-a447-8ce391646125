# AI Agent Platform - Kubernetes Deployment

This directory contains Kubernetes manifests and deployment scripts for the AI Agent Platform, providing enterprise-grade orchestration, monitoring, and scalability.

## 🏗️ Architecture Overview

The platform consists of the following components:

### Core Services
- **Backend**: FastAPI application with OAuth, MFA, RBAC, and AI integrations
- **Frontend**: Next.js React application with real-time UI
- **Database**: PostgreSQL with automated backups
- **Cache**: Redis with persistence and monitoring
- **Message Queue**: Apache Kafka with Zookeeper

### Infrastructure
- **Load Balancer**: Nginx with SSL/TLS, rate limiting, and caching
- **Monitoring**: Prometheus, Grafana, AlertManager
- **Security**: Network policies, RBAC, service accounts

## 📋 Prerequisites

### Required Tools
- `kubectl` (v1.24+)
- Kubernetes cluster (v1.24+)
- Docker images built and available
- At least 8GB RAM and 4 CPU cores available in cluster

### Storage Requirements
- PostgreSQL: 20Gi
- Kafka: 10Gi
- Redis: 5Gi
- Prometheus: 50Gi
- Grafana: 10Gi
- Backups: 10Gi

### Supported Kubernetes Distributions
- Google Kubernetes Engine (GKE)
- Amazon Elastic Kubernetes Service (EKS)
- Azure Kubernetes Service (AKS)
- Self-managed Kubernetes
- Minikube (for development)

## 🚀 Quick Deployment

### 1. Prepare Secrets
Copy the secrets template and fill in actual values:

```bash
cp secrets.yaml secrets-production.yaml
# Edit secrets-production.yaml with your actual credentials
```

**Important**: Never commit actual secrets to version control!

### 2. Update Configuration
Edit `configmap.yaml` to match your environment:
- Domain names
- CORS origins
- OAuth redirect URIs
- Database settings

### 3. Build and Push Images
```bash
# Build backend image
docker build -t ai-agent-platform/backend:latest ../backend/

# Build frontend image  
docker build -t ai-agent-platform/frontend:latest ../frontend/

# Push to your container registry
docker tag ai-agent-platform/backend:latest your-registry/ai-agent-backend:latest
docker push your-registry/ai-agent-backend:latest

docker tag ai-agent-platform/frontend:latest your-registry/ai-agent-frontend:latest
docker push your-registry/ai-agent-frontend:latest
```

### 4. Deploy
```bash
# Deploy everything
./deploy.sh deploy

# Check status
./deploy.sh status

# View logs
kubectl logs -f deployment/ai-agent-backend -n ai-agent-platform
```

## 📁 File Structure

```
k8s/
├── deploy.sh              # Deployment script
├── README.md              # This file
├── namespace.yaml         # Kubernetes namespaces
├── secrets.yaml           # Secret templates (DO NOT commit real secrets)
├── configmap.yaml         # Application configuration
├── postgres.yaml          # PostgreSQL database
├── redis.yaml             # Redis cache
├── kafka.yaml             # Kafka message queue
├── backend.yaml           # Backend application
├── frontend.yaml          # Frontend application
├── nginx.yaml             # Load balancer
└── monitoring.yaml        # Monitoring stack
```

## 🔧 Configuration

### Environment-Specific Settings

For different environments, create separate configuration files:

```bash
# Development
configmap-dev.yaml
secrets-dev.yaml

# Staging  
configmap-staging.yaml
secrets-staging.yaml

# Production
configmap-prod.yaml
secrets-prod.yaml
```

### Resource Requirements

| Component | CPU Request | CPU Limit | Memory Request | Memory Limit |
|-----------|-------------|-----------|----------------|--------------|
| Backend   | 250m        | 1000m     | 512Mi          | 2Gi          |
| Frontend  | 100m        | 500m      | 256Mi          | 1Gi          |
| PostgreSQL| 250m        | 1000m     | 512Mi          | 2Gi          |
| Redis     | 100m        | 500m      | 256Mi          | 1Gi          |
| Kafka     | 500m        | 1000m     | 1Gi            | 2Gi          |
| Nginx     | 100m        | 200m      | 128Mi          | 256Mi        |

### Auto-Scaling Configuration

The platform includes Horizontal Pod Autoscalers (HPA):

- **Backend**: Scales 3-20 pods based on CPU (70%) and memory (80%)
- **Frontend**: Scales 2-10 pods based on CPU (70%) and memory (80%)

## 🔐 Security

### Network Policies
- Strict ingress/egress rules between components
- External access only through Nginx load balancer
- Database and cache accessible only from backend

### RBAC
- Service accounts with minimal required permissions
- Separate namespaces for application and monitoring
- Pod security contexts with non-root users

### Secrets Management
- Kubernetes secrets for sensitive data
- Encrypted at rest (if supported by cluster)
- Separate secrets for different environments

## 📊 Monitoring

### Prometheus Metrics
- Application metrics on port 8090
- Infrastructure metrics via exporters
- Custom business metrics

### Grafana Dashboards
Access Grafana:
```bash
kubectl port-forward service/grafana 3000:3000 -n ai-agent-platform-monitoring
# Visit http://localhost:3000
# Default login: admin/admin123
```

### Alerts
Configured alerts for:
- Service availability
- High CPU/memory usage
- Database connectivity
- Disk space
- Error rates

## 🔄 Operations

### Scaling

Scale individual components:
```bash
# Scale backend
kubectl scale deployment ai-agent-backend --replicas=5 -n ai-agent-platform

# Scale frontend
kubectl scale deployment ai-agent-frontend --replicas=3 -n ai-agent-platform
```

### Updates

Perform rolling updates:
```bash
# Update backend image
kubectl set image deployment/ai-agent-backend backend=your-registry/ai-agent-backend:v2.0.0 -n ai-agent-platform

# Check rollout status
kubectl rollout status deployment/ai-agent-backend -n ai-agent-platform

# Rollback if needed
kubectl rollout undo deployment/ai-agent-backend -n ai-agent-platform
```

### Backups

PostgreSQL automated backups run daily at 2 AM:
```bash
# Check backup job
kubectl get cronjob postgres-backup -n ai-agent-platform

# Manually trigger backup
kubectl create job --from=cronjob/postgres-backup manual-backup-$(date +%Y%m%d) -n ai-agent-platform
```

### Logs

View logs from different components:
```bash
# Backend logs
kubectl logs -f deployment/ai-agent-backend -n ai-agent-platform

# Frontend logs  
kubectl logs -f deployment/ai-agent-frontend -n ai-agent-platform

# PostgreSQL logs
kubectl logs -f deployment/postgres -n ai-agent-platform

# Kafka logs
kubectl logs -f deployment/kafka -n ai-agent-platform
```

## 🚨 Troubleshooting

### Common Issues

**1. Pods not starting**
```bash
kubectl describe pod <pod-name> -n ai-agent-platform
kubectl logs <pod-name> -n ai-agent-platform
```

**2. Services not accessible**
```bash
kubectl get svc -n ai-agent-platform
kubectl describe svc <service-name> -n ai-agent-platform
```

**3. Database connection issues**
```bash
# Test database connectivity
kubectl run --rm -it --restart=Never test-pg --image=postgres:15-alpine -- psql -h postgres -U ai_agent_user -d ai_agent_platform
```

**4. High memory usage**
```bash
# Check resource usage
kubectl top pods -n ai-agent-platform
kubectl top nodes
```

### Debug Mode

Enable debug mode for troubleshooting:
```yaml
# In configmap.yaml
DEBUG: "true"
LOG_LEVEL: "DEBUG"
```

Then restart deployments:
```bash
kubectl rollout restart deployment/ai-agent-backend -n ai-agent-platform
```

## 📈 Performance Tuning

### Database Optimization
```bash
# Connect to PostgreSQL
kubectl exec -it deployment/postgres -n ai-agent-platform -- psql -U ai_agent_user -d ai_agent_platform

-- Check slow queries
SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;

-- Check table sizes
SELECT schemaname,tablename,pg_size_pretty(size) as size FROM
(SELECT schemaname,tablename,pg_relation_size(schemaname||'.'||tablename) as size
FROM pg_tables WHERE schemaname NOT IN ('information_schema','pg_catalog')) t
ORDER BY size DESC;
```

### Redis Optimization
```bash
# Check Redis stats
kubectl exec -it deployment/redis -n ai-agent-platform -- redis-cli info memory
kubectl exec -it deployment/redis -n ai-agent-platform -- redis-cli info stats
```

### Kafka Optimization
```bash
# Check Kafka topics
kubectl exec -it deployment/kafka -n ai-agent-platform -- kafka-topics --bootstrap-server localhost:9092 --list

# Check consumer lag
kubectl exec -it deployment/kafka -n ai-agent-platform -- kafka-consumer-groups --bootstrap-server localhost:9092 --list
```

## 🔄 Disaster Recovery

### Backup Strategy
1. **Database**: Automated daily backups to persistent volume
2. **Configuration**: Version-controlled Kubernetes manifests
3. **Persistent Data**: Regular volume snapshots (cloud provider dependent)

### Recovery Procedure
1. Restore database from backup
2. Deploy latest manifests
3. Verify service connectivity
4. Run health checks

### High Availability
- Multi-replica deployments
- Pod disruption budgets
- Anti-affinity rules (for production)
- Cross-zone distribution (cloud provider dependent)

## 🎯 Production Checklist

Before deploying to production:

- [ ] Update all default passwords and secrets
- [ ] Configure proper domain names and SSL certificates
- [ ] Set up proper monitoring and alerting
- [ ] Configure backup and disaster recovery
- [ ] Review and test security policies
- [ ] Configure log aggregation
- [ ] Set up CI/CD pipelines
- [ ] Perform load testing
- [ ] Create runbooks and documentation
- [ ] Train operations team

## 📞 Support

For deployment issues:
1. Check logs and pod status
2. Review this documentation
3. Check monitoring dashboards
4. Consult application logs
5. Create GitHub issue with details

## 📄 License

This deployment configuration is part of the AI Agent Platform project and follows the same licensing terms.