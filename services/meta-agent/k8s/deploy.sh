#!/bin/bash

# AI Agent Platform Kubernetes Deployment Script
# This script deploys the complete AI Agent Platform to Kubernetes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="ai-agent-platform"
MONITORING_NAMESPACE="ai-agent-platform-monitoring"

# Functions
log() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed. Please install kubectl first."
        exit 1
    fi
    
    # Check if kubectl can connect to cluster
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster. Please check your kubeconfig."
        exit 1
    fi
    
    # Check if Docker images exist (optional)
    # You can uncomment these if you want to check for images
    # docker images | grep -q "ai-agent-platform/backend" || warning "Backend image not found locally"
    # docker images | grep -q "ai-agent-platform/frontend" || warning "Frontend image not found locally"
    
    success "Prerequisites check passed"
}

# Create namespaces
create_namespaces() {
    log "Creating namespaces..."
    kubectl apply -f namespace.yaml
    success "Namespaces created"
}

# Deploy secrets (with validation)
deploy_secrets() {
    log "Deploying secrets..."
    
    # Check if secrets file exists
    if [[ ! -f "secrets.yaml" ]]; then
        error "secrets.yaml not found. Please create it from secrets.yaml.template"
        exit 1
    fi
    
    # Validate that secrets don't contain template values
    if grep -q "your-.*-here" secrets.yaml; then
        warning "Found template values in secrets.yaml. Please replace them with actual values."
        read -p "Continue anyway? (y/N) " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    kubectl apply -f secrets.yaml
    success "Secrets deployed"
}

# Deploy configuration
deploy_config() {
    log "Deploying configuration..."
    kubectl apply -f configmap.yaml
    success "Configuration deployed"
}

# Deploy databases
deploy_databases() {
    log "Deploying PostgreSQL..."
    kubectl apply -f postgres.yaml
    
    log "Waiting for PostgreSQL to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/postgres -n $NAMESPACE
    
    log "Deploying Redis..."
    kubectl apply -f redis.yaml
    
    log "Waiting for Redis to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/redis -n $NAMESPACE
    
    success "Databases deployed"
}

# Deploy vector database
deploy_vector_db() {
    log "Deploying Qdrant vector database..."
    kubectl apply -f qdrant.yaml
    
    log "Waiting for Qdrant to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/qdrant -n $NAMESPACE
    
    success "Vector database deployed"
}

# Deploy message queue
deploy_messaging() {
    log "Deploying Kafka and Zookeeper..."
    kubectl apply -f kafka.yaml
    
    log "Waiting for Zookeeper to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/zookeeper -n $NAMESPACE
    
    log "Waiting for Kafka to be ready..."
    kubectl wait --for=condition=available --timeout=600s deployment/kafka -n $NAMESPACE
    
    success "Message queue deployed"
}

# Deploy backend
deploy_backend() {
    log "Deploying backend application..."
    kubectl apply -f backend.yaml
    
    log "Waiting for backend to be ready..."
    kubectl wait --for=condition=available --timeout=600s deployment/ai-agent-backend -n $NAMESPACE
    
    success "Backend deployed"
}

# Deploy frontend
deploy_frontend() {
    log "Deploying frontend application..."
    kubectl apply -f frontend.yaml
    
    log "Waiting for frontend to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/ai-agent-frontend -n $NAMESPACE
    
    success "Frontend deployed"
}

# Deploy load balancer
deploy_loadbalancer() {
    log "Deploying Nginx load balancer..."
    kubectl apply -f nginx.yaml
    
    log "Waiting for Nginx to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/nginx -n $NAMESPACE
    
    success "Load balancer deployed"
}

# Deploy monitoring
deploy_monitoring() {
    log "Deploying monitoring stack..."
    kubectl apply -f monitoring.yaml
    
    log "Waiting for Prometheus to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/prometheus -n $MONITORING_NAMESPACE
    
    log "Waiting for Grafana to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/grafana -n $MONITORING_NAMESPACE
    
    success "Monitoring deployed"
}

# Get service information
get_service_info() {
    log "Getting service information..."
    
    echo
    echo "=== AI Agent Platform Deployment Complete ==="
    echo
    
    # Get LoadBalancer IP/hostname
    EXTERNAL_IP=$(kubectl get service nginx -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
    EXTERNAL_HOSTNAME=$(kubectl get service nginx -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "")
    
    if [[ -n "$EXTERNAL_IP" ]]; then
        success "Application available at: http://$EXTERNAL_IP"
    elif [[ -n "$EXTERNAL_HOSTNAME" ]]; then
        success "Application available at: http://$EXTERNAL_HOSTNAME"
    else
        warning "External IP/hostname not yet assigned. Check with:"
        echo "  kubectl get service nginx -n $NAMESPACE"
        echo
        echo "Or use port-forward for testing:"
        echo "  kubectl port-forward service/nginx 8080:80 -n $NAMESPACE"
        echo "  Then visit: http://localhost:8080"
    fi
    
    echo
    echo "Monitoring dashboards:"
    echo "  Grafana: kubectl port-forward service/grafana 3000:3000 -n $MONITORING_NAMESPACE"
    echo "  Prometheus: kubectl port-forward service/prometheus 9090:9090 -n $MONITORING_NAMESPACE"
    echo
    
    echo "Useful commands:"
    echo "  Check pod status: kubectl get pods -n $NAMESPACE"
    echo "  Check logs: kubectl logs -f deployment/ai-agent-backend -n $NAMESPACE"
    echo "  Scale backend: kubectl scale deployment ai-agent-backend --replicas=5 -n $NAMESPACE"
    echo
}

# Cleanup function
cleanup() {
    log "Cleaning up resources..."
    kubectl delete -f . --ignore-not-found=true
    kubectl delete namespace $NAMESPACE --ignore-not-found=true
    kubectl delete namespace $MONITORING_NAMESPACE --ignore-not-found=true
    success "Cleanup complete"
}

# Upgrade function
upgrade() {
    log "Upgrading deployment..."
    
    # Rolling update for backend
    kubectl rollout restart deployment/ai-agent-backend -n $NAMESPACE
    kubectl rollout status deployment/ai-agent-backend -n $NAMESPACE
    
    # Rolling update for frontend
    kubectl rollout restart deployment/ai-agent-frontend -n $NAMESPACE
    kubectl rollout status deployment/ai-agent-frontend -n $NAMESPACE
    
    success "Upgrade complete"
}

# Status function
status() {
    echo "=== AI Agent Platform Status ==="
    echo
    
    echo "Namespaces:"
    kubectl get namespaces | grep -E "(ai-agent-platform|NAME)"
    echo
    
    echo "Pods in $NAMESPACE:"
    kubectl get pods -n $NAMESPACE
    echo
    
    echo "Services in $NAMESPACE:"
    kubectl get services -n $NAMESPACE
    echo
    
    echo "Monitoring pods in $MONITORING_NAMESPACE:"
    kubectl get pods -n $MONITORING_NAMESPACE
    echo
    
    echo "Persistent Volume Claims:"
    kubectl get pvc -n $NAMESPACE
    echo
}

# Help function
show_help() {
    echo "AI Agent Platform Kubernetes Deployment Script"
    echo
    echo "Usage: $0 [COMMAND]"
    echo
    echo "Commands:"
    echo "  deploy     Deploy the complete platform (default)"
    echo "  cleanup    Remove all resources"
    echo "  upgrade    Perform rolling update"
    echo "  status     Show deployment status"
    echo "  help       Show this help message"
    echo
    echo "Examples:"
    echo "  $0 deploy    # Deploy everything"
    echo "  $0 status    # Check status"
    echo "  $0 cleanup   # Remove everything"
    echo
}

# Main deployment function
deploy_all() {
    log "Starting AI Agent Platform deployment..."
    
    check_prerequisites
    create_namespaces
    deploy_secrets
    deploy_config
    deploy_databases
    deploy_vector_db
    deploy_messaging
    deploy_backend
    deploy_frontend
    deploy_loadbalancer
    deploy_monitoring
    get_service_info
    
    success "Deployment completed successfully!"
}

# Main script logic
case "${1:-deploy}" in
    deploy)
        deploy_all
        ;;
    cleanup)
        cleanup
        ;;
    upgrade)
        upgrade
        ;;
    status)
        status
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        error "Unknown command: $1"
        echo
        show_help
        exit 1
        ;;
esac