#!/usr/bin/env python3
"""
Test script to identify which API calls are causing frontend loading issues
"""

import requests
import json
import time
import sys
from concurrent.futures import ThreadPoolExecutor, as_completed

BASE_URL = "http://localhost:8000"

def test_api_call(endpoint, description, timeout=5):
    """Test a single API endpoint with timeout"""
    try:
        start_time = time.time()
        response = requests.get(f"{BASE_URL}{endpoint}", timeout=timeout)
        end_time = time.time()
        
        duration = end_time - start_time
        
        if response.status_code == 200:
            return {
                "endpoint": endpoint,
                "description": description,
                "status": "SUCCESS",
                "duration": f"{duration:.2f}s",
                "status_code": response.status_code
            }
        else:
            return {
                "endpoint": endpoint,
                "description": description,
                "status": "ERROR",
                "duration": f"{duration:.2f}s",
                "status_code": response.status_code
            }
    except requests.exceptions.Timeout:
        return {
            "endpoint": endpoint,
            "description": description,
            "status": "TIMEOUT",
            "duration": f">{timeout}s",
            "status_code": "TIMEOUT"
        }
    except Exception as e:
        return {
            "endpoint": endpoint,
            "description": description,
            "status": "EXCEPTION",
            "duration": "N/A",
            "error": str(e)
        }

def test_frontend_api_calls():
    """Test all API calls that frontend pages make"""
    print("🔍 Testing Frontend API Calls")
    print("=" * 50)
    
    # API calls that frontend pages make
    api_calls = [
        # Agents page calls
        ("/api/v1/public/agents", "List Agents"),
        ("/api/v1/public/agents?limit=50", "List Agents with Limit"),
        
        # Dashboard page calls
        ("/api/v1/public/runtime/system/stats", "System Stats"),
        ("/api/v1/public/runtime/system/health", "System Health"),
        ("/api/v1/public/runtime/agents/active", "Active Agents"),
        
        # Agent deployment calls (for existing agents)
        ("/api/v1/public/agents/1ce153ca-6b35-4ff6-80a4-0cfed0dd2df8/deployment", "Deployment Status"),
        
        # Other potential calls
        ("/api/v1/public/deployed/1ce153ca-6b35-4ff6-80a4-0cfed0dd2df8", "Deployed Agent Interface"),
    ]
    
    print(f"Testing {len(api_calls)} API endpoints...")
    print()
    
    # Test all endpoints concurrently with timeout
    results = []
    with ThreadPoolExecutor(max_workers=5) as executor:
        future_to_call = {
            executor.submit(test_api_call, endpoint, description, 10): (endpoint, description)
            for endpoint, description in api_calls
        }
        
        for future in as_completed(future_to_call):
            result = future.result()
            results.append(result)
            
            # Print result immediately
            status_icon = {
                "SUCCESS": "✅",
                "ERROR": "❌", 
                "TIMEOUT": "⏰",
                "EXCEPTION": "💥"
            }.get(result["status"], "❓")
            
            print(f"{status_icon} {result['description']}")
            print(f"   {result['endpoint']}")
            print(f"   Status: {result['status']} ({result.get('status_code', 'N/A')})")
            print(f"   Duration: {result['duration']}")
            if 'error' in result:
                print(f"   Error: {result['error']}")
            print()
    
    # Summary
    success_count = sum(1 for r in results if r["status"] == "SUCCESS")
    timeout_count = sum(1 for r in results if r["status"] == "TIMEOUT")
    error_count = sum(1 for r in results if r["status"] in ["ERROR", "EXCEPTION"])
    
    print("=" * 50)
    print("📊 API Test Summary")
    print("=" * 50)
    print(f"✅ Successful: {success_count}/{len(api_calls)}")
    print(f"⏰ Timeouts: {timeout_count}/{len(api_calls)}")
    print(f"❌ Errors: {error_count}/{len(api_calls)}")
    print()
    
    # Identify problematic endpoints
    problematic = [r for r in results if r["status"] in ["TIMEOUT", "ERROR", "EXCEPTION"]]
    if problematic:
        print("🚨 Problematic Endpoints:")
        for result in problematic:
            print(f"   • {result['description']}: {result['status']}")
            print(f"     {result['endpoint']}")
        print()
    
    # Recommendations
    if timeout_count > 0:
        print("💡 Recommendations:")
        print("   • Add loading timeouts to frontend API calls")
        print("   • Implement proper error handling for slow endpoints")
        print("   • Consider adding loading states for slow operations")
        print()
    
    return success_count >= len(api_calls) * 0.8

def test_frontend_pages():
    """Test frontend page loading"""
    print("🌐 Testing Frontend Page Loading")
    print("=" * 40)
    
    pages = [
        ("http://localhost:3000/", "Home"),
        ("http://localhost:3000/dashboard", "Dashboard"),
        ("http://localhost:3000/agents", "Agents"),
        ("http://localhost:3000/agents/create", "Create Agent")
    ]
    
    for url, name in pages:
        try:
            start_time = time.time()
            response = requests.get(url, timeout=15)
            end_time = time.time()
            duration = end_time - start_time
            
            if response.status_code == 200:
                print(f"✅ {name}: {duration:.2f}s")
            else:
                print(f"❌ {name}: {response.status_code} ({duration:.2f}s)")
        except requests.exceptions.Timeout:
            print(f"⏰ {name}: Timeout (>15s)")
        except Exception as e:
            print(f"💥 {name}: {e}")
    
    print()

if __name__ == "__main__":
    print("🔍 FRONTEND LOADING ISSUE DIAGNOSIS")
    print("=" * 60)
    
    # Test API endpoints
    api_success = test_frontend_api_calls()
    
    # Test frontend pages
    test_frontend_pages()
    
    print("=" * 60)
    if api_success:
        print("✅ Most API endpoints are working")
        print("💡 Frontend loading issues may be due to:")
        print("   • Slow API calls without proper timeouts")
        print("   • Missing error handling in frontend")
        print("   • React state management issues")
        print("   • Network connectivity problems")
    else:
        print("❌ Multiple API endpoints have issues")
        print("💡 Fix the failing API endpoints first")
    
    print("\n🔧 Next Steps:")
    print("1. Add API call timeouts in frontend")
    print("2. Implement proper loading states")
    print("3. Add error boundaries for failed API calls")
    print("4. Consider using React Query for better API state management")
