import { Router, Route } from 'preact-router'
import { Header } from '@/components/Header'
import { HomePage } from '@/pages/HomePage'
import { ApiInfoPage } from '@/pages/ApiInfoPage'
import Dashboard from '@/components/Dashboard'
import InvoiceTable from '@/components/InvoiceTable'
import ARChart from '@/components/ARChart'
import EmailTemplateEditor from '@/components/EmailTemplateEditor'
import ScheduleConfigurator from '@/components/ScheduleConfigurator'
import ThresholdSettings from '@/components/ThresholdSettings'
import ResponseTracker from '@/components/ResponseTracker'
import ManualOverrideControls from '@/components/ManualOverrideControls'
import AgentInfo from '@/components/AgentInfo'

export function App() {
  return (
    <div class="min-h-screen bg-gray-50">
      <Header />
      <main class="container mx-auto px-4 py-8">
        <Router>
          <Route path="/" component={HomePage} />
          <Route path="/api-info" component={ApiInfoPage} />
          <Route path="/dashboard" component={Dashboard} />
          <Route path="/invoice-table" component={InvoiceTable} />
          <Route path="/ar-chart" component={ARChart} />
          <Route path="/email-template-editor" component={EmailTemplateEditor} />
          <Route path="/schedule-configurator" component={ScheduleConfigurator} />
          <Route path="/threshold-settings" component={ThresholdSettings} />
          <Route path="/response-tracker" component={ResponseTracker} />
          <Route path="/manual-override-controls" component={ManualOverrideControls} />
          <Route path="/agent-info" component={AgentInfo} />
        </Router>
      </main>
    </div>
  )
}