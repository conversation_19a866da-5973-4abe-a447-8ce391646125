import { useEffect, useState } from 'preact/hooks'
import { apiClient, AgentInfo } from '@/services/api'

export function HomePage() {
  const [agentInfo, setAgentInfo] = useState<AgentInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchAgentInfo()
  }, [])

  const fetchAgentInfo = async () => {
    try {
      const info = await apiClient.getAgentInfo()
      setAgentInfo(info)
    } catch (err) {
      setError(err.message || 'Failed to fetch agent info')
    } finally {
      setLoading(false)
    }
  }

  if (loading) return <div class="text-center py-8">Loading...</div>
  if (error) return <div class="text-red-600 text-center py-8">Error: {error}</div>

  return (
    <div class="max-w-4xl mx-auto">
      <div class="bg-white shadow rounded-lg p-6">
        <h1 class="text-2xl font-bold mb-4">{agentInfo?.name || 'ar-automation-agent'}</h1>
        <p class="text-gray-600 mb-6">{agentInfo?.description}</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h2 class="text-lg font-semibold mb-2">Agent Details</h2>
            <dl class="space-y-1">
              <div class="flex justify-between">
                <dt class="text-gray-500">ID:</dt>
                <dd class="font-mono text-sm">{agentInfo?.agent_id}</dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-gray-500">Type:</dt>
                <dd>{agentInfo?.type}</dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-gray-500">Version:</dt>
                <dd>{agentInfo?.version}</dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-gray-500">Status:</dt>
                <dd class="text-green-600">{agentInfo?.status}</dd>
              </div>
            </dl>
          </div>
          
          <div>
            <h2 class="text-lg font-semibold mb-2">Capabilities</h2>
            <ul class="space-y-1">
              {agentInfo?.capabilities.map(cap => (
                <li key={cap} class="text-gray-600">• {cap}</li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}