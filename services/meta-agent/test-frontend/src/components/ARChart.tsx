import { FunctionComponent, h } from 'preact';
import { useState, useEffect } from 'preact/hooks';
import { BarChart, Bar, XAxi<PERSON>, <PERSON>A<PERSON><PERSON>, CartesianGrid, Tooltip, Legend, LineChart, Line, PieC<PERSON>, Pie, Cell } from 'recharts';
import { api } from '@/services/api';

interface ARChartData {
  name: string;
  value: number;
}

interface ARChartProps {
  /** Data for the chart */
  data: ARChartData[];
}


const ARChart: FunctionComponent<ARChartProps> = ({ data }) => {
  const [chartType, setChartType] = useState<'bar' | 'line' | 'pie'>('bar');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

  const handleChartTypeChange = (type: 'bar' | 'line' | 'pie') => {
    setChartType(type);
  };

  useEffect(() => {
    // Placeholder for future API calls
    // Example:
    // const fetchData = async () => {
    //   setLoading(true);
    //   try {
    //     const response = await api.get('/some-endpoint');
    //     // Process response and update data
    //   } catch (err) {
    //     setError('Error fetching data');
    //   } finally {
    //     setLoading(false);
    //   }
    // };
    // fetchData();
  }, []);

  if (loading) {
    return <div class="text-center py-4">Loading...</div>;
  }

  if (error) {
    return <div class="text-center py-4 text-red-500">Error: {error}</div>;
  }

  if (!data || data.length === 0) {
    return <div class="text-center py-4">No data available.</div>;
  }

  const renderChart = () => {
    switch (chartType) {
      case 'bar':
        return (
          <BarChart width={730} height={250} data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="value" fill="#8884d8" />
          </BarChart>
        );
      case 'line':
        return (
          <LineChart width={730} height={250} data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line type="monotone" dataKey="value" stroke="#8884d8" />
          </LineChart>
        );
      case 'pie':
        return (
          <PieChart width={730} height={250}>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
              label
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip />
          </PieChart>
        );
      default:
        return null;
    }
  };


  return (
    <div class="bg-white p-4 rounded shadow-md">
      <div class="flex justify-end">
        <button class={`mr-2 ${chartType === 'bar' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'}`} onClick={() => handleChartTypeChange('bar')}>Bar</button>
        <button class={`mr-2 ${chartType === 'line' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'}`} onClick={() => handleChartTypeChange('line')}>Line</button>
        <button class={` ${chartType === 'pie' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'}`} onClick={() => handleChartTypeChange('pie')}>Pie</button>
      </div>
      {renderChart()}
    </div>
  );
};

export default ARChart;