import { h, FunctionalComponent } from 'preact';
import { useState, useEffect } from 'preact/hooks';
import { api, Invoice } from '@/services/api';

/**
 * @description Displays a list of invoices.
 * @param {object} props - Component props.
 */
const InvoiceTable: FunctionalComponent = (props) => {
  const [invoices, setInvoices] = useState<Invoice[] | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchInvoices = async () => {
      try {
        // Replace with actual API call
        const data = await api.getInvoices(); 
        setInvoices(data);
      } catch (err) {
        setError('Failed to fetch invoices.');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchInvoices();
  }, []);

  if (loading) {
    return <div class="text-center py-4">Loading invoices...</div>;
  }

  if (error) {
    return <div class="text-center py-4 text-red-500">{error}</div>;
  }

  if (!invoices || invoices.length === 0) {
    return <div class="text-center py-4">No invoices found.</div>;
  }

  return (
    <div class="overflow-x-auto">
      <table class="w-full table-auto">
        <thead>
          <tr>
            {/* Placeholder for sortable columns */}
            <th class="px-4 py-2">Invoice Number</th>
            <th class="px-4 py-2">Amount</th>
            <th class="px-4 py-2">Due Date</th>
            <th class="px-4 py-2">Status</th>
          </tr>
        </thead>
        <tbody>
          {invoices.map((invoice) => (
            <tr key={invoice.id}> {/* Assuming invoice has an 'id' property */}
              <td class="border px-4 py-2">{invoice.invoiceNumber}</td>
              <td class="border px-4 py-2">{invoice.amount}</td>
              <td class="border px-4 py-2">{invoice.dueDate}</td>
              <td class="border px-4 py-2">{invoice.status}</td>
            </tr>
          ))}
        </tbody>
      </table>
      {/* Placeholder for pagination */}
    </div>
  );
};

export default InvoiceTable;
