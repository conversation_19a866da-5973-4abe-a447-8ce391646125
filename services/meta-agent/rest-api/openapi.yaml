openapi: 3.0.3
info:
  title: Meta Agent API
  description: Complete API specification for the Meta Agent system
  version: 1.0.0
  contact:
    name: Meta Agent API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.meta-agent.com/v1
    description: Production server
  - url: https://staging-api.meta-agent.com/v1
    description: Staging server
  - url: http://localhost:8001/api/v1
    description: Development server

security:
  - bearerAuth: []

tags:
  - name: Authentication
    description: User authentication and session management
  - name: Agents
    description: AI Agent management operations
  - name: Tasks
    description: Task execution and management
  - name: Workflows
    description: Workflow orchestration
  - name: User Management
    description: User profile and settings

paths:
  # =============================================================================
  # AUTHENTICATION ENDPOINTS
  # =============================================================================
  /auth/session:
    get:
      tags: [Authentication]
      summary: Get current session
      description: Retrieve the current user session
      security: []
      responses:
        '200':
          description: Session information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Session'
        '401':
          description: Not authenticated

  /auth/signin:
    post:
      tags: [Authentication]
      summary: Sign in with email/password
      description: Authenticate user with email and password
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              title: SignInRequest
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
                  minLength: 6
              required:
                - email
                - password
      responses:
        '200':
          description: Authentication successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          description: Invalid credentials

  /auth/signout:
    post:
      tags: [Authentication]
      summary: Sign out
      description: End the current user session
      responses:
        '200':
          description: Successfully signed out
        '401':
          description: Not authenticated

  /auth/user:
    get:
      tags: [Authentication]
      summary: Get current user
      description: Retrieve current authenticated user information
      responses:
        '200':
          description: User information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '401':
          description: Not authenticated

  # =============================================================================
  # AGENT ENDPOINTS
  # =============================================================================
  /agents:
    get:
      tags: [Agents]
      summary: List agents
      description: Retrieve a list of all AI agents
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [active, inactive, error]
          description: Filter by agent status
        - name: type
          in: query
          schema:
            type: string
            enum: [chat, task, workflow, analysis]
          description: Filter by agent type
      responses:
        '200':
          description: List of agents
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Agent'

    post:
      tags: [Agents]
      summary: Create agent
      description: Create a new AI agent
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AgentCreate'
      responses:
        '201':
          description: Agent created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Agent'
        '400':
          description: Invalid input

  /agents/{id}:
    get:
      tags: [Agents]
      summary: Get agent
      description: Retrieve a specific agent by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Agent details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Agent'
        '404':
          description: Agent not found

    put:
      tags: [Agents]
      summary: Update agent
      description: Update an existing agent
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AgentUpdate'
      responses:
        '200':
          description: Agent updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Agent'
        '404':
          description: Agent not found

    delete:
      tags: [Agents]
      summary: Delete agent
      description: Delete an agent
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Agent deleted successfully
        '404':
          description: Agent not found

  # =============================================================================
  # TASK ENDPOINTS
  # =============================================================================
  /tasks:
    get:
      tags: [Tasks]
      summary: List tasks
      description: Retrieve a list of all tasks
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [pending, running, completed, failed]
          description: Filter by task status
        - name: agent_id
          in: query
          schema:
            type: string
            format: uuid
          description: Filter by agent ID
      responses:
        '200':
          description: List of tasks
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Task'

    post:
      tags: [Tasks]
      summary: Create task
      description: Create a new task
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskCreate'
      responses:
        '201':
          description: Task created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
        '400':
          description: Invalid input

  /tasks/{id}:
    get:
      tags: [Tasks]
      summary: Get task
      description: Retrieve a specific task by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Task details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
        '404':
          description: Task not found

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    # =============================================================================
    # AUTHENTICATION SCHEMAS
    # =============================================================================
    Session:
      type: object
      title: Session
      properties:
        user:
          $ref: '#/components/schemas/User'
        expires_at:
          type: string
          format: date-time
        created_at:
          type: string
          format: date-time

    AuthResponse:
      type: object
      title: AuthResponse
      properties:
        user:
          $ref: '#/components/schemas/User'
        token:
          type: string
        expires_at:
          type: string
          format: date-time

    User:
      type: object
      title: User
      properties:
        id:
          type: string
          format: uuid
        email:
          type: string
          format: email
        name:
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    # =============================================================================
    # AGENT SCHEMAS
    # =============================================================================
    Agent:
      type: object
      title: Agent
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        description:
          type: string
        type:
          type: string
          enum: [chat, task, workflow, analysis]
        status:
          type: string
          enum: [active, inactive, error]
        configuration:
          type: object
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    AgentCreate:
      type: object
      title: AgentCreate
      properties:
        name:
          type: string
        description:
          type: string
        type:
          type: string
          enum: [chat, task, workflow, analysis]
        configuration:
          type: object
      required:
        - name
        - type

    AgentUpdate:
      type: object
      title: AgentUpdate
      properties:
        name:
          type: string
        description:
          type: string
        configuration:
          type: object

    # =============================================================================
    # TASK SCHEMAS
    # =============================================================================
    Task:
      type: object
      title: Task
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        description:
          type: string
        agent_id:
          type: string
          format: uuid
        status:
          type: string
          enum: [pending, running, completed, failed]
        input:
          type: object
        output:
          type: object
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    TaskCreate:
      type: object
      title: TaskCreate
      properties:
        name:
          type: string
        description:
          type: string
        agent_id:
          type: string
          format: uuid
        input:
          type: object
      required:
        - name
        - agent_id

    # =============================================================================
    # SHARED ERROR SCHEMAS
    # =============================================================================
    Error:
      type: object
      title: Error
      properties:
        error:
          type: string
        message:
          type: string
        details:
          type: object
    
    # =============================================================================
    # SYSTEM MONITORING SCHEMAS
    # =============================================================================
    SystemStats:
      type: object
      title: SystemStats
      properties:
        agents:
          type: object
          properties:
            total:
              type: integer
            active:
              type: integer
            inactive:
              type: integer
        tasks:
          type: object
          properties:
            total:
              type: integer
            pending:
              type: integer
            running:
              type: integer
            completed:
              type: integer
            failed:
              type: integer
        resources:
          type: object
          properties:
            cpu_percent:
              type: number
            memory_percent:
              type: number
            disk_percent:
              type: number
    
    SystemHealth:
      type: object
      title: SystemHealth
      properties:
        status:
          type: string
          enum: [healthy, degraded, unhealthy]
        checks:
          type: object
          properties:
            database:
              type: boolean
            api:
              type: boolean
            workers:
              type: boolean
        uptime:
          type: integer
          description: Uptime in seconds
    
    # =============================================================================
    # DEPLOYMENT SCHEMAS
    # =============================================================================
    DeploymentConfig:
      type: object
      title: DeploymentConfig
      properties:
        environment:
          type: string
          enum: [development, staging, production]
        resources:
          type: object
          properties:
            cpu:
              type: string
            memory:
              type: string
        replicas:
          type: integer
          minimum: 1
    
    # =============================================================================
    # MIGRATION SCHEMAS
    # =============================================================================
    ApplicationAnalysis:
      type: object
      title: ApplicationAnalysis
      properties:
        id:
          type: string
          format: uuid
        application_name:
          type: string
        technology_stack:
          type: array
          items:
            type: string
        complexity_score:
          type: number
          minimum: 0
          maximum: 10
        recommendations:
          type: array
          items:
            type: string
        created_at:
          type: string
          format: date-time
    
    MigrationPlan:
      type: object
      title: MigrationPlan
      properties:
        id:
          type: string
          format: uuid
        analysis_id:
          type: string
          format: uuid
        strategy:
          type: string
          enum: [rehost, replatform, refactor, rebuild, replace]
        phases:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
              duration_days:
                type: integer
              tasks:
                type: array
                items:
                  type: string
        estimated_cost:
          type: number
        risk_level:
          type: string
          enum: [low, medium, high]
        created_at:
          type: string
          format: date-time
    
    MigrationProject:
      type: object
      title: MigrationProject
      properties:
        id:
          type: string
          format: uuid
        plan_id:
          type: string
          format: uuid
        name:
          type: string
        status:
          type: string
          enum: [planning, in_progress, completed, failed, paused]
        progress:
          type: number
          minimum: 0
          maximum: 100
        started_at:
          type: string
          format: date-time
        completed_at:
          type: string
          format: date-time
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time