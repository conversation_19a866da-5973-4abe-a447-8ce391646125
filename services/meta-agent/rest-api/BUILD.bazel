package(default_visibility = ["//visibility:public"])

# OpenAPI specification files
exports_files([
    "openapi.yaml",
    "backend_openapi.yaml",
])

# Validate OpenAPI spec using Redocly
genrule(
    name = "validate_spec",
    srcs = ["backend_openapi.yaml"],
    outs = ["validation_result.txt"],
    cmd = """
        redocly lint $(location backend_openapi.yaml) > $@ 2>&1 || echo "Validation completed with warnings" > $@
    """,
)




# Generate TypeScript client with full API methods
genrule(
    name = "generate_typescript_client",
    srcs = ["backend_openapi.yaml"],
    outs = [
        "generated/typescript/api.ts",
        "generated/typescript/base.ts", 
        "generated/typescript/common.ts",
        "generated/typescript/configuration.ts",
        "generated/typescript/index.ts"
    ],
    cmd = """
        # Generate TypeScript client with full API methods using axios generator
        npx @openapitools/openapi-generator-cli generate \\
            -i $(location backend_openapi.yaml) \\
            -g typescript-axios \\
            -o client_temp \\
            --additional-properties=supportsES6=true,withInterfaces=true,npmName=meta-agent-client
        
        # Create output directory and copy generated files
        mkdir -p $$(dirname $(location generated/typescript/api.ts))
        cp client_temp/api.ts $(location generated/typescript/api.ts)
        cp client_temp/base.ts $(location generated/typescript/base.ts)
        cp client_temp/common.ts $(location generated/typescript/common.ts)
        cp client_temp/configuration.ts $(location generated/typescript/configuration.ts)
        cp client_temp/index.ts $(location generated/typescript/index.ts)
    """,
)

# Generate Python client directly to structured location
genrule(
    name = "generate_python_client",
    srcs = ["backend_openapi.yaml"],
    outs = ["generated/python/client/client.py"],
    cmd = """
        # Generate Python client
        npx @openapitools/openapi-generator-cli generate \
            -i $(location backend_openapi.yaml) \
            -g python \
            -o /tmp/python_client \
            --additional-properties=packageName=meta_agent_client
        
        # Create output directory and copy
        mkdir -p $$(dirname $(location generated/python/client/client.py))
        cp /tmp/python_client/meta_agent_client/api_client.py $(location generated/python/client/client.py)
    """,
)

# Generate Python server directly to structured location
genrule(
    name = "generate_python_server",
    srcs = ["backend_openapi.yaml"],
    outs = ["generated/python/server/server.py"],
    cmd = """
        # Generate Python server
        npx @openapitools/openapi-generator-cli generate \
            -i $(location backend_openapi.yaml) \
            -g python-fastapi \
            -o /tmp/python_server \
            --additional-properties=packageName=meta_agent_server
        
        # Create output directory and copy
        mkdir -p $$(dirname $(location generated/python/server/server.py))
        cp /tmp/python_server/src/meta_agent_server/main.py $(location generated/python/server/server.py)
    """,
)

# Copy generated files to the generated/ directory (manual script - not needed with new approach)
sh_binary(
    name = "update_generated",
    srcs = ["update_generated.sh"],
    data = [
        ":generate_typescript_client",
    ],
)

# All clients target - generates directly to structured output locations
filegroup(
    name = "all_clients",
    srcs = [
        ":generate_typescript_client",
        ":generate_python_client", 
        ":generate_python_server",
        ":validate_spec",
    ],
)

# Clean target to remove generated files
sh_binary(
    name = "clean",
    srcs = ["clean.sh"],
)

