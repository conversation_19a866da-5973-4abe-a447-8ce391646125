#!/bin/bash
# <PERSON><PERSON>t to copy generated files to the generated/ directory

set -e

# Get the workspace root
WORKSPACE_ROOT="/Users/<USER>/metaspaces/mono"

# Create directories if they don't exist
mkdir -p "$WORKSPACE_ROOT/generated/meta-agent/python/server"
mkdir -p "$WORKSPACE_ROOT/generated/meta-agent/python/client"
mkdir -p "$WORKSPACE_ROOT/generated/meta-agent/typescript"

# Copy generated Python server code
if [ -f bazel-bin/services/meta-agent/rest-api/server.py ]; then
    cp bazel-bin/services/meta-agent/rest-api/server.py "$WORKSPACE_ROOT/generated/meta-agent/python/server/"
    echo "Copied Python server code to generated/meta-agent/python/server/"
fi

# Copy generated Python client code
if [ -f bazel-bin/services/meta-agent/rest-api/client.py ]; then
    cp bazel-bin/services/meta-agent/rest-api/client.py "$WORKSPACE_ROOT/generated/meta-agent/python/client/"
    echo "Copied Python client code to generated/meta-agent/python/client/"
fi

# Copy generated TypeScript client code
if [ -f bazel-bin/services/meta-agent/rest-api/client.ts ]; then
    cp bazel-bin/services/meta-agent/rest-api/client.ts "$WORKSPACE_ROOT/generated/meta-agent/typescript/"
    echo "Copied TypeScript client code to generated/meta-agent/typescript/"
fi

echo "All generated files have been copied to the generated/ directory"