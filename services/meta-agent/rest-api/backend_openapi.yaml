openapi: 3.1.0
info:
  title: AI Agent Platform
  description: Enterprise AI Agent Platform
  version: 1.0.0
paths:
  /api/v1/auth/register:
    post:
      tags:
      - authentication
      summary: Register
      description: Register a new user
      operationId: register_api_v1_auth_register_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserCreate'
        required: true
      responses:
        '201':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/auth/login:
    post:
      tags:
      - authentication
      summary: Login
      description: Login with username/email and password
      operationId: login_api_v1_auth_login_post
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Body_login_api_v1_auth_login_post'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Token'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/auth/refresh:
    post:
      tags:
      - authentication
      summary: Refresh Token
      description: Refresh access token using refresh token
      operationId: refresh_token_api_v1_auth_refresh_post
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Body_refresh_token_api_v1_auth_refresh_post'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Token'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/auth/me:
    get:
      tags:
      - authentication
      summary: Get Current User Info
      description: Get current user information
      operationId: get_current_user_info_api_v1_auth_me_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
      security:
      - HTTPBearer: []
    put:
      tags:
      - authentication
      summary: Update Current User
      description: Update current user information
      operationId: update_current_user_api_v1_auth_me_put
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserUpdate'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/auth/change-password:
    post:
      tags:
      - authentication
      summary: Change Password
      description: Change user password
      operationId: change_password_api_v1_auth_change_password_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordChangeRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/auth/create-admin:
    post:
      tags:
      - authentication
      summary: Create Admin User
      description: Create admin user (superuser only)
      operationId: create_admin_user_api_v1_auth_create_admin_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserCreate'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/auth/oauth/providers:
    get:
      tags:
      - authentication
      summary: Get Oauth Providers
      description: Get available OAuth providers
      operationId: get_oauth_providers_api_v1_auth_oauth_providers_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/auth/oauth/init:
    post:
      tags:
      - authentication
      summary: Init Oauth Flow
      description: Initialize OAuth authentication flow
      operationId: init_oauth_flow_api_v1_auth_oauth_init_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OAuthInitRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OAuthInitResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/auth/oauth/callback:
    post:
      tags:
      - authentication
      summary: Oauth Callback
      description: Handle OAuth callback and complete authentication
      operationId: oauth_callback_api_v1_auth_oauth_callback_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OAuthCallbackRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/auth/login/mfa:
    post:
      tags:
      - authentication
      summary: Login With Mfa
      description: Login with MFA support
      operationId: login_with_mfa_api_v1_auth_login_mfa_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginWithMFARequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/auth/mfa/setup/totp:
    post:
      tags:
      - authentication
      summary: Setup Totp Mfa
      description: Setup TOTP (authenticator app) MFA device
      operationId: setup_totp_mfa_api_v1_auth_mfa_setup_totp_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MFASetupTOTPRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/auth/mfa/setup/sms:
    post:
      tags:
      - authentication
      summary: Setup Sms Mfa
      description: Setup SMS MFA device
      operationId: setup_sms_mfa_api_v1_auth_mfa_setup_sms_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MFASetupSMSRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/auth/mfa/verify/setup:
    post:
      tags:
      - authentication
      summary: Verify Mfa Setup
      description: Verify and complete MFA device setup
      operationId: verify_mfa_setup_api_v1_auth_mfa_verify_setup_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MFAVerifyRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/auth/mfa/devices:
    get:
      tags:
      - authentication
      summary: Get Mfa Devices
      description: Get user's MFA devices
      operationId: get_mfa_devices_api_v1_auth_mfa_devices_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
      - HTTPBearer: []
  /api/v1/auth/mfa/devices/{device_id}:
    delete:
      tags:
      - authentication
      summary: Remove Mfa Device
      description: Remove MFA device
      operationId: remove_mfa_device_api_v1_auth_mfa_devices__device_id__delete
      security:
      - HTTPBearer: []
      parameters:
      - name: device_id
        in: path
        required: true
        schema:
          type: string
          title: Device Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/auth/mfa/send-sms:
    post:
      tags:
      - authentication
      summary: Send Sms Code
      description: Send SMS verification code
      operationId: send_sms_code_api_v1_auth_mfa_send_sms_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MFATokenRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/auth/permissions:
    get:
      tags:
      - authentication
      summary: Get User Permissions
      description: Get current user's permissions and roles
      operationId: get_user_permissions_api_v1_auth_permissions_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserPermissionsResponse'
      security:
      - HTTPBearer: []
  /api/v1/auth/roles:
    get:
      tags:
      - authentication
      summary: Get All Roles
      description: Get all available roles (admin only)
      operationId: get_all_roles_api_v1_auth_roles_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
      - HTTPBearer: []
  /api/v1/auth/permissions/all:
    get:
      tags:
      - authentication
      summary: Get All Permissions
      description: Get all available permissions (admin only)
      operationId: get_all_permissions_api_v1_auth_permissions_all_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
      - HTTPBearer: []
  /api/v1/auth/roles/create:
    post:
      tags:
      - authentication
      summary: Create Role
      description: Create new role (admin only)
      operationId: create_role_api_v1_auth_roles_create_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateRoleRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/auth/roles/assign:
    post:
      tags:
      - authentication
      summary: Assign Role
      description: Assign role to user (admin only)
      operationId: assign_role_api_v1_auth_roles_assign_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoleAssignmentRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/auth/roles/remove:
    delete:
      tags:
      - authentication
      summary: Remove Role
      description: Remove role from user (admin only)
      operationId: remove_role_api_v1_auth_roles_remove_delete
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoleAssignmentRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/auth/security/login-attempts:
    get:
      tags:
      - authentication
      summary: Get Login Attempts
      description: Get user's recent login attempts
      operationId: get_login_attempts_api_v1_auth_security_login_attempts_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
      - HTTPBearer: []
  /api/v1/agents/:
    post:
      tags:
      - agents
      summary: Create Agent
      description: Create a new AI agent with full-stack code generation and deployment
      operationId: create_agent_api_v1_agents__post
      security:
      - HTTPBearer: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AgentCreate'
      responses:
        '201':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    get:
      tags:
      - agents
      summary: List Agents
      description: List agents with optional filters
      operationId: list_agents_api_v1_agents__get
      parameters:
      - name: status
        in: query
        required: false
        schema:
          anyOf:
          - $ref: '#/components/schemas/AgentStatus'
          - type: 'null'
          title: Status
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          maximum: 1000
          minimum: 1
          default: 100
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentListResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/agents/{agent_id}:
    get:
      tags:
      - agents
      summary: Get Agent
      description: Get agent by ID
      operationId: get_agent_api_v1_agents__agent_id__get
      parameters:
      - name: agent_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Agent Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    put:
      tags:
      - agents
      summary: Update Agent
      description: Update an existing agent
      operationId: update_agent_api_v1_agents__agent_id__put
      parameters:
      - name: agent_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Agent Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AgentUpdate'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    delete:
      tags:
      - agents
      summary: Delete Agent
      description: Delete an agent
      operationId: delete_agent_api_v1_agents__agent_id__delete
      parameters:
      - name: agent_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Agent Id
      responses:
        '204':
          description: Successful Response
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/agents/{agent_id}/start:
    post:
      tags:
      - agents
      summary: Start Agent
      description: Start an agent
      operationId: start_agent_api_v1_agents__agent_id__start_post
      parameters:
      - name: agent_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Agent Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentStartResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/agents/{agent_id}/stop:
    post:
      tags:
      - agents
      summary: Stop Agent
      description: Stop an agent
      operationId: stop_agent_api_v1_agents__agent_id__stop_post
      parameters:
      - name: agent_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Agent Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentStartResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/agents/{agent_id}/deployment:
    get:
      tags:
      - agents
      summary: Get Agent Deployment
      description: Get agent deployment status and information
      operationId: get_agent_deployment_api_v1_agents__agent_id__deployment_get
      parameters:
      - name: agent_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Agent Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    delete:
      tags:
      - agents
      summary: Stop Agent Deployment
      description: Stop and remove agent deployment
      operationId: stop_agent_deployment_api_v1_agents__agent_id__deployment_delete
      parameters:
      - name: agent_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Agent Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/agents/{agent_id}/deploy:
    post:
      tags:
      - agents
      summary: Deploy Agent Endpoint
      description: Deploy an existing agent (if not already deployed)
      operationId: deploy_agent_endpoint_api_v1_agents__agent_id__deploy_post
      parameters:
      - name: agent_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Agent Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/agents/{agent_id}/heartbeat:
    post:
      tags:
      - agents
      summary: Update Heartbeat
      description: Update agent heartbeat
      operationId: update_heartbeat_api_v1_agents__agent_id__heartbeat_post
      parameters:
      - name: agent_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Agent Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/tasks/:
    get:
      tags:
      - tasks
      summary: List Tasks
      description: List tasks with optional filters
      operationId: list_tasks_api_v1_tasks__get
      parameters:
      - name: status
        in: query
        required: false
        schema:
          anyOf:
          - $ref: '#/components/schemas/TaskStatus'
          - type: 'null'
          title: Status
      - name: type
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Type
      - name: agent_id
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: uuid
          - type: 'null'
          title: Agent Id
      - name: size
        in: query
        required: false
        schema:
          type: integer
          maximum: 1000
          minimum: 1
          default: 100
          title: Size
      - name: page
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Page
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskListResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/tasks/{task_id}:
    get:
      tags:
      - tasks
      summary: Get Task
      description: Get task by ID
      operationId: get_task_api_v1_tasks__task_id__get
      parameters:
      - name: task_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Task Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/orchestrations/:
    post:
      tags:
      - orchestrations
      summary: Create Orchestration
      description: Create a new orchestration
      operationId: create_orchestration_api_v1_orchestrations__post
      security:
      - HTTPBearer: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrchestrationCreate'
      responses:
        '201':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrchestrationResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    get:
      tags:
      - orchestrations
      summary: List Orchestrations
      description: List orchestrations for current user
      operationId: list_orchestrations_api_v1_orchestrations__get
      security:
      - HTTPBearer: []
      parameters:
      - name: status
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Status
      - name: pattern
        in: query
        required: false
        schema:
          anyOf:
          - $ref: '#/components/schemas/OrchestrationPattern'
          - type: 'null'
          title: Pattern
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          maximum: 1000
          minimum: 1
          default: 100
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          minimum: 0
          default: 0
          title: Offset
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrchestrationListResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/orchestrations/{orchestration_id}:
    get:
      tags:
      - orchestrations
      summary: Get Orchestration
      description: Get orchestration by ID
      operationId: get_orchestration_api_v1_orchestrations__orchestration_id__get
      security:
      - HTTPBearer: []
      parameters:
      - name: orchestration_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Orchestration Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrchestrationResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    put:
      tags:
      - orchestrations
      summary: Update Orchestration
      description: Update an orchestration
      operationId: update_orchestration_api_v1_orchestrations__orchestration_id__put
      security:
      - HTTPBearer: []
      parameters:
      - name: orchestration_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Orchestration Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrchestrationUpdate'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrchestrationResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    delete:
      tags:
      - orchestrations
      summary: Delete Orchestration
      description: Delete an orchestration
      operationId: delete_orchestration_api_v1_orchestrations__orchestration_id__delete
      security:
      - HTTPBearer: []
      parameters:
      - name: orchestration_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Orchestration Id
      responses:
        '204':
          description: Successful Response
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/orchestrations/{orchestration_id}/start:
    post:
      tags:
      - orchestrations
      summary: Start Orchestration
      description: Start an orchestration
      operationId: start_orchestration_api_v1_orchestrations__orchestration_id__start_post
      security:
      - HTTPBearer: []
      parameters:
      - name: orchestration_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Orchestration Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrchestrationStartResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/orchestrations/{orchestration_id}/stop:
    post:
      tags:
      - orchestrations
      summary: Stop Orchestration
      description: Stop an orchestration
      operationId: stop_orchestration_api_v1_orchestrations__orchestration_id__stop_post
      security:
      - HTTPBearer: []
      parameters:
      - name: orchestration_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Orchestration Id
      - name: graceful
        in: query
        required: false
        schema:
          type: boolean
          default: true
          title: Graceful
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrchestrationStartResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/orchestrations/{orchestration_id}/pause:
    post:
      tags:
      - orchestrations
      summary: Pause Orchestration
      description: Pause an orchestration
      operationId: pause_orchestration_api_v1_orchestrations__orchestration_id__pause_post
      security:
      - HTTPBearer: []
      parameters:
      - name: orchestration_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Orchestration Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/orchestrations/{orchestration_id}/resume:
    post:
      tags:
      - orchestrations
      summary: Resume Orchestration
      description: Resume an orchestration
      operationId: resume_orchestration_api_v1_orchestrations__orchestration_id__resume_post
      security:
      - HTTPBearer: []
      parameters:
      - name: orchestration_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Orchestration Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/orchestrations/{orchestration_id}/progress:
    get:
      tags:
      - orchestrations
      summary: Get Orchestration Progress
      description: Get orchestration progress
      operationId: get_orchestration_progress_api_v1_orchestrations__orchestration_id__progress_get
      security:
      - HTTPBearer: []
      parameters:
      - name: orchestration_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Orchestration Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrchestrationProgressResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/orchestrations/{orchestration_id}/agents:
    post:
      tags:
      - orchestrations
      summary: Add Agent To Orchestration
      description: Add an agent to orchestration
      operationId: add_agent_to_orchestration_api_v1_orchestrations__orchestration_id__agents_post
      security:
      - HTTPBearer: []
      parameters:
      - name: orchestration_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Orchestration Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddAgentRequest'
      responses:
        '201':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/orchestrations/{orchestration_id}/agents/{agent_id}:
    delete:
      tags:
      - orchestrations
      summary: Remove Agent From Orchestration
      description: Remove an agent from orchestration
      operationId: remove_agent_from_orchestration_api_v1_orchestrations__orchestration_id__agents__agent_id__delete
      security:
      - HTTPBearer: []
      parameters:
      - name: orchestration_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Orchestration Id
      - name: agent_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Agent Id
      responses:
        '204':
          description: Successful Response
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/runtime/agents:
    get:
      tags:
      - runtime
      summary: List Active Agents
      description: List all active agent runtimes
      operationId: list_active_agents_api_v1_runtime_agents_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/RuntimeInfoResponse'
                type: array
                title: Response List Active Agents Api V1 Runtime Agents Get
  /api/v1/runtime/agents/{agent_id}/info:
    get:
      tags:
      - runtime
      summary: Get Agent Runtime Info
      description: Get runtime information for a specific agent
      operationId: get_agent_runtime_info_api_v1_runtime_agents__agent_id__info_get
      parameters:
      - name: agent_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Agent Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RuntimeInfoResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/runtime/agents/{agent_id}/pause:
    post:
      tags:
      - runtime
      summary: Pause Agent Runtime
      description: Pause an agent runtime
      operationId: pause_agent_runtime_api_v1_runtime_agents__agent_id__pause_post
      parameters:
      - name: agent_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Agent Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/runtime/agents/{agent_id}/resume:
    post:
      tags:
      - runtime
      summary: Resume Agent Runtime
      description: Resume an agent runtime
      operationId: resume_agent_runtime_api_v1_runtime_agents__agent_id__resume_post
      parameters:
      - name: agent_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Agent Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/runtime/agents/{agent_id}/execute-task:
    post:
      tags:
      - runtime
      summary: Execute Task On Agent
      description: Execute a task on a specific agent
      operationId: execute_task_on_agent_api_v1_runtime_agents__agent_id__execute_task_post
      parameters:
      - name: agent_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Agent Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskExecutionRequest'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskExecutionResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/runtime/agents/{agent_id}/queue-task:
    post:
      tags:
      - runtime
      summary: Queue Task For Agent
      description: Add a task to an agent's execution queue
      operationId: queue_task_for_agent_api_v1_runtime_agents__agent_id__queue_task_post
      parameters:
      - name: agent_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
          title: Agent Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskExecutionRequest'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/runtime/system/stats:
    get:
      tags:
      - runtime
      summary: Get System Stats
      description: Get system statistics
      operationId: get_system_stats_api_v1_runtime_system_stats_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SystemStatsResponse'
  /api/v1/runtime/system/health:
    get:
      tags:
      - runtime
      summary: Get System Health
      description: Get system health status
      operationId: get_system_health_api_v1_runtime_system_health_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SystemHealthResponse'
  /api/v1/runtime/system/shutdown:
    post:
      tags:
      - runtime
      summary: Shutdown All Agents
      description: Shutdown all agent runtimes (admin only)
      operationId: shutdown_all_agents_api_v1_runtime_system_shutdown_post
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/runtime/messaging/status:
    get:
      tags:
      - runtime
      summary: Get Messaging Status
      description: Get message queue service status
      operationId: get_messaging_status_api_v1_runtime_messaging_status_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/runtime/messaging/connect:
    post:
      tags:
      - runtime
      summary: Connect Message Queue
      description: Connect to message queue service
      operationId: connect_message_queue_api_v1_runtime_messaging_connect_post
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/api/v1/ai/chat:
    post:
      tags:
      - AI Integration
      summary: Chat Completion
      description: Generate chat completion using AI providers
      operationId: chat_completion_api_v1_api_v1_ai_chat_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/api__ai__ChatRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AIResponseSchema'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/api/v1/ai/completion:
    post:
      tags:
      - AI Integration
      summary: Text Completion
      description: Generate text completion using AI providers
      operationId: text_completion_api_v1_api_v1_ai_completion_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompletionRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AIResponseSchema'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/api/v1/ai/code:
    post:
      tags:
      - AI Integration
      summary: Generate Code
      description: Generate code using AI providers
      operationId: generate_code_api_v1_api_v1_ai_code_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/api__ai__CodeGenerationRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AIResponseSchema'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/api/v1/ai/embeddings:
    post:
      tags:
      - AI Integration
      summary: Generate Embeddings
      description: Generate text embeddings using AI providers
      operationId: generate_embeddings_api_v1_api_v1_ai_embeddings_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/api__ai__EmbeddingRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AIResponseSchema'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/api/v1/ai/process:
    post:
      tags:
      - AI Integration
      summary: Process Ai Request
      description: Process generic AI request
      operationId: process_ai_request_api_v1_api_v1_ai_process_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AIRequestSchema'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AIResponseSchema'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/api/v1/ai/models:
    get:
      tags:
      - AI Integration
      summary: Get Available Models
      description: Get all available AI models by provider
      operationId: get_available_models_api_v1_api_v1_ai_models_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties:
                  items:
                    type: string
                  type: array
                type: object
                title: Response Get Available Models Api V1 Api V1 Ai Models Get
      security:
      - HTTPBearer: []
  /api/v1/api/v1/ai/providers:
    get:
      tags:
      - AI Integration
      summary: Get Providers
      description: Get list of configured AI providers
      operationId: get_providers_api_v1_api_v1_ai_providers_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                items:
                  type: string
                type: array
                title: Response Get Providers Api V1 Api V1 Ai Providers Get
      security:
      - HTTPBearer: []
  /api/v1/api/v1/ai/health:
    get:
      tags:
      - AI Integration
      summary: Health Check
      description: Check health status of all AI providers
      operationId: health_check_api_v1_api_v1_ai_health_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties:
                  type: boolean
                type: object
                title: Response Health Check Api V1 Api V1 Ai Health Get
      security:
      - HTTPBearer: []
  /api/v1/api/v1/ai/capabilities:
    get:
      tags:
      - AI Integration
      summary: Get Capabilities
      description: Get AI capabilities by task type
      operationId: get_capabilities_api_v1_api_v1_ai_capabilities_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties:
                  items:
                    type: string
                  type: array
                type: object
                title: Response Get Capabilities Api V1 Api V1 Ai Capabilities Get
      security:
      - HTTPBearer: []
  /api/v1/api/v1/ai/batch:
    post:
      tags:
      - AI Integration
      summary: Batch Process
      description: Process multiple AI requests in batch
      operationId: batch_process_api_v1_api_v1_ai_batch_post
      requestBody:
        content:
          application/json:
            schema:
              items:
                $ref: '#/components/schemas/AIRequestSchema'
              type: array
              title: Requests
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/generation/templates:
    get:
      tags:
      - agent-generation
      summary: Get Available Templates
      description: Get all available agent templates
      operationId: get_available_templates_api_v1_generation_templates_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/TemplateInfo'
                type: array
                title: Response Get Available Templates Api V1 Generation Templates
                  Get
  /api/v1/generation/templates/{agent_type}:
    get:
      tags:
      - agent-generation
      summary: Get Templates By Type
      description: Get templates filtered by agent type
      operationId: get_templates_by_type_api_v1_generation_templates__agent_type__get
      parameters:
      - name: agent_type
        in: path
        required: true
        schema:
          $ref: '#/components/schemas/AgentType'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TemplateInfo'
                title: Response Get Templates By Type Api V1 Generation Templates  Agent
                  Type  Get
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/generation/templates/language/{language}:
    get:
      tags:
      - agent-generation
      summary: Get Templates By Language
      description: Get templates filtered by programming language
      operationId: get_templates_by_language_api_v1_generation_templates_language__language__get
      parameters:
      - name: language
        in: path
        required: true
        schema:
          $ref: '#/components/schemas/ProgrammingLanguage'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TemplateInfo'
                title: Response Get Templates By Language Api V1 Generation Templates
                  Language  Language  Get
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/generation/validate:
    post:
      tags:
      - agent-generation
      summary: Validate Generation Request
      description: Validate an agent generation request
      operationId: validate_generation_request_api_v1_generation_validate_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerationRequestModel'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/generation/generate:
    post:
      tags:
      - agent-generation
      summary: Generate Agent
      description: Generate a new agent
      operationId: generate_agent_api_v1_generation_generate_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerationRequestModel'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenerationResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/generation/status/{generation_id}:
    get:
      tags:
      - agent-generation
      summary: Get Generation Status
      description: Get the status of an agent generation
      operationId: get_generation_status_api_v1_generation_status__generation_id__get
      parameters:
      - name: generation_id
        in: path
        required: true
        schema:
          type: string
          title: Generation Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenerationStatus'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/generation/download/{generation_id}:
    get:
      tags:
      - agent-generation
      summary: Download Generated Agent
      description: Download the generated agent code as a ZIP file
      operationId: download_generated_agent_api_v1_generation_download__generation_id__get
      parameters:
      - name: generation_id
        in: path
        required: true
        schema:
          type: string
          title: Generation Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/generation/cleanup/{generation_id}:
    delete:
      tags:
      - agent-generation
      summary: Cleanup Generation
      description: Clean up generation files and status
      operationId: cleanup_generation_api_v1_generation_cleanup__generation_id__delete
      parameters:
      - name: generation_id
        in: path
        required: true
        schema:
          type: string
          title: Generation Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/generation/capabilities:
    get:
      tags:
      - agent-generation
      summary: Get Available Capabilities
      description: Get list of all available capabilities
      operationId: get_available_capabilities_api_v1_generation_capabilities_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/generation/build-deploy/{generation_id}:
    post:
      tags:
      - agent-generation
      summary: Build Deploy Agent
      description: Build, deploy and test A2A communication for a generated agent
      operationId: build_deploy_agent_api_v1_generation_build_deploy__generation_id__post
      parameters:
      - name: generation_id
        in: path
        required: true
        schema:
          type: string
          title: Generation Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/generation/build-status/{generation_id}:
    get:
      tags:
      - agent-generation
      summary: Get Build Status
      description: Get build and deployment status
      operationId: get_build_status_api_v1_generation_build_status__generation_id__get
      parameters:
      - name: generation_id
        in: path
        required: true
        schema:
          type: string
          title: Generation Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/generation/examples:
    get:
      tags:
      - agent-generation
      summary: Get Generation Examples
      description: Get example agent generation configurations
      operationId: get_generation_examples_api_v1_generation_examples_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/vector/knowledge/store:
    post:
      tags:
      - vector-db
      summary: Store Agent Knowledge
      description: Store knowledge for an agent
      operationId: store_agent_knowledge_api_v1_vector_knowledge_store_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StoreKnowledgeRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StoreKnowledgeResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/vector/knowledge/search:
    post:
      tags:
      - vector-db
      summary: Search Agent Knowledge
      description: Search agent knowledge base
      operationId: search_agent_knowledge_api_v1_vector_knowledge_search_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchKnowledgeRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchKnowledgeResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/vector/conversation/store:
    post:
      tags:
      - vector-db
      summary: Store Conversation Context
      description: Store conversation context for an agent
      operationId: store_conversation_context_api_v1_vector_conversation_store_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StoreConversationRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StoreConversationResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/vector/conversation/context:
    post:
      tags:
      - vector-db
      summary: Get Conversation Context
      description: Get relevant conversation context for current query
      operationId: get_conversation_context_api_v1_vector_conversation_context_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetContextRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetContextResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/vector/code/store:
    post:
      tags:
      - vector-db
      summary: Store Code Snippet
      description: Store code snippet for agent reference
      operationId: store_code_snippet_api_v1_vector_code_store_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StoreCodeSnippetRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StoreCodeSnippetResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/vector/code/search:
    post:
      tags:
      - vector-db
      summary: Search Code Snippets
      description: Search code snippets
      operationId: search_code_snippets_api_v1_vector_code_search_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchCodeRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchCodeResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/vector/data:
    delete:
      tags:
      - vector-db
      summary: Delete Collection Data
      description: Delete data from collection based on filter conditions
      operationId: delete_collection_data_api_v1_vector_data_delete
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteDataRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteDataResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/vector/collections/{collection_name}/info:
    get:
      tags:
      - vector-db
      summary: Get Collection Info
      description: Get information about a collection
      operationId: get_collection_info_api_v1_vector_collections__collection_name__info_get
      security:
      - HTTPBearer: []
      parameters:
      - name: collection_name
        in: path
        required: true
        schema:
          type: string
          title: Collection Name
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CollectionInfoResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/vector/health:
    get:
      tags:
      - vector-db
      summary: Vector Db Health Check
      description: Check vector database health
      operationId: vector_db_health_check_api_v1_vector_health_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
  /api/v1/a2a/register:
    post:
      tags:
      - a2a-protocol
      summary: Register Agent
      description: Register an agent with the A2A protocol
      operationId: register_agent_api_v1_a2a_register_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterAgentRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterAgentResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/a2a/unregister/{agent_id}:
    delete:
      tags:
      - a2a-protocol
      summary: Unregister Agent
      description: Unregister an agent from the A2A protocol
      operationId: unregister_agent_api_v1_a2a_unregister__agent_id__delete
      security:
      - HTTPBearer: []
      parameters:
      - name: agent_id
        in: path
        required: true
        schema:
          type: string
          title: Agent Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterAgentResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/a2a/send/{sender_id}:
    post:
      tags:
      - a2a-protocol
      summary: Send Message
      description: Send a message from one agent to another
      operationId: send_message_api_v1_a2a_send__sender_id__post
      security:
      - HTTPBearer: []
      parameters:
      - name: sender_id
        in: path
        required: true
        schema:
          type: string
          title: Sender Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendMessageRequest'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/A2AResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/a2a/broadcast/{sender_id}:
    post:
      tags:
      - a2a-protocol
      summary: Broadcast Message
      description: Broadcast a message to multiple agents
      operationId: broadcast_message_api_v1_a2a_broadcast__sender_id__post
      security:
      - HTTPBearer: []
      parameters:
      - name: sender_id
        in: path
        required: true
        schema:
          type: string
          title: Sender Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BroadcastMessageRequest'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/A2AResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/a2a/conversation/establish/{initiator_id}:
    post:
      tags:
      - a2a-protocol
      summary: Establish Conversation
      description: Establish a conversation between multiple agents
      operationId: establish_conversation_api_v1_a2a_conversation_establish__initiator_id__post
      security:
      - HTTPBearer: []
      parameters:
      - name: initiator_id
        in: path
        required: true
        schema:
          type: string
          title: Initiator Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EstablishConversationRequest'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EstablishConversationResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/a2a/conversation/{conversation_id}/history:
    get:
      tags:
      - a2a-protocol
      summary: Get Conversation History
      description: Get conversation history
      operationId: get_conversation_history_api_v1_a2a_conversation__conversation_id__history_get
      security:
      - HTTPBearer: []
      parameters:
      - name: conversation_id
        in: path
        required: true
        schema:
          type: string
          title: Conversation Id
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          maximum: 200
          minimum: 1
          default: 50
          title: Limit
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConversationHistoryResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/a2a/capabilities/query:
    post:
      tags:
      - a2a-protocol
      summary: Query Capabilities
      description: Query agent capabilities
      operationId: query_capabilities_api_v1_a2a_capabilities_query_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QueryCapabilitiesRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QueryCapabilitiesResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/a2a/messages/search:
    post:
      tags:
      - a2a-protocol
      summary: Search Messages
      description: Search through A2A messages
      operationId: search_messages_api_v1_a2a_messages_search_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchMessagesRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchMessagesResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/a2a/agents/active:
    get:
      tags:
      - a2a-protocol
      summary: Get Active Agents
      description: Get list of active agents
      operationId: get_active_agents_api_v1_a2a_agents_active_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ActiveAgentsResponse'
      security:
      - HTTPBearer: []
  /api/v1/a2a/agents/{agent_id}/status:
    get:
      tags:
      - a2a-protocol
      summary: Get Agent Status
      description: Get status of a specific agent
      operationId: get_agent_status_api_v1_a2a_agents__agent_id__status_get
      security:
      - HTTPBearer: []
      parameters:
      - name: agent_id
        in: path
        required: true
        schema:
          type: string
          title: Agent Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentStatusResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/google-adk/generate/text:
    post:
      tags:
      - google-adk
      summary: Generate Text
      description: Generate text using Google's generative AI models
      operationId: generate_text_api_v1_google_adk_generate_text_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TextGenerationRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TextGenerationResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/google-adk/generate/code:
    post:
      tags:
      - google-adk
      summary: Generate Code
      description: Generate code using Google's code generation models
      operationId: generate_code_api_v1_google_adk_generate_code_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/api__google_adk__CodeGenerationRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CodeGenerationResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/google-adk/analyze/image:
    post:
      tags:
      - google-adk
      summary: Analyze Image Base64
      description: Analyze image using Google's vision models (base64 input)
      operationId: analyze_image_base64_api_v1_google_adk_analyze_image_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ImageAnalysisRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImageAnalysisResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/google-adk/analyze/image/upload:
    post:
      tags:
      - google-adk
      summary: Analyze Image Upload
      description: Analyze uploaded image using Google's vision models
      operationId: analyze_image_upload_api_v1_google_adk_analyze_image_upload_post
      security:
      - HTTPBearer: []
      parameters:
      - name: prompt
        in: query
        required: true
        schema:
          type: string
          title: Prompt
      - name: model
        in: query
        required: false
        schema:
          type: string
          default: gemini-pro-vision
          title: Model
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Body_analyze_image_upload_api_v1_google_adk_analyze_image_upload_post'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImageAnalysisResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/google-adk/embeddings:
    post:
      tags:
      - google-adk
      summary: Generate Embeddings
      description: Generate embeddings using Google's embedding models
      operationId: generate_embeddings_api_v1_google_adk_embeddings_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/api__google_adk__EmbeddingRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmbeddingResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/google-adk/chat:
    post:
      tags:
      - google-adk
      summary: Chat
      description: Chat with Google's conversational AI models
      operationId: chat_api_v1_google_adk_chat_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/api__google_adk__ChatRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/google-adk/agent/create:
    post:
      tags:
      - google-adk
      summary: Create Agent With Adk
      description: Create an AI agent configuration optimized for Google ADK
      operationId: create_agent_with_adk_api_v1_google_adk_agent_create_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AgentADKRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentADKResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/google-adk/agent/enhance-knowledge:
    post:
      tags:
      - google-adk
      summary: Enhance Agent Knowledge
      description: Enhance agent with knowledge using Google's AI capabilities
      operationId: enhance_agent_knowledge_api_v1_google_adk_agent_enhance_knowledge_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/KnowledgeEnhancementRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/KnowledgeEnhancementResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/google-adk/agent/analyze-performance:
    post:
      tags:
      - google-adk
      summary: Analyze Agent Performance
      description: Analyze agent performance using Google's AI
      operationId: analyze_agent_performance_api_v1_google_adk_agent_analyze_performance_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PerformanceAnalysisRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PerformanceAnalysisResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/google-adk/models/{model}/info:
    get:
      tags:
      - google-adk
      summary: Get Model Info
      description: Get information about a specific Google AI model
      operationId: get_model_info_api_v1_google_adk_models__model__info_get
      security:
      - HTTPBearer: []
      parameters:
      - name: model
        in: path
        required: true
        schema:
          type: string
          title: Model
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ModelInfoResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/google-adk/health:
    get:
      tags:
      - google-adk
      summary: Health Check
      description: Check Google ADK service health
      operationId: health_check_api_v1_google_adk_health_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthCheckResponse'
  /api/v1/deployment/deploy:
    post:
      tags:
      - deployment
      summary: Deploy Agent
      description: Deploy an agent with specified configuration.
      operationId: deploy_agent_api_v1_deployment_deploy_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeploymentRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeploymentResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/deployment/status/{deployment_id}:
    get:
      tags:
      - deployment
      summary: Get Deployment Status
      description: Get detailed status of a specific deployment.
      operationId: get_deployment_status_api_v1_deployment_status__deployment_id__get
      security:
      - HTTPBearer: []
      parameters:
      - name: deployment_id
        in: path
        required: true
        schema:
          type: string
          title: Deployment Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeploymentStatusResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/deployment/list:
    get:
      tags:
      - deployment
      summary: List Deployments
      description: List deployments with optional filtering.
      operationId: list_deployments_api_v1_deployment_list_get
      security:
      - HTTPBearer: []
      parameters:
      - name: status
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Status
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          default: 0
          title: Offset
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeploymentListResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/deployment/rollback/{deployment_id}:
    post:
      tags:
      - deployment
      summary: Rollback Deployment
      description: Rollback a deployment to the previous version.
      operationId: rollback_deployment_api_v1_deployment_rollback__deployment_id__post
      security:
      - HTTPBearer: []
      parameters:
      - name: deployment_id
        in: path
        required: true
        schema:
          type: string
          title: Deployment Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/deployment/terminate/{deployment_id}:
    delete:
      tags:
      - deployment
      summary: Terminate Deployment
      description: Terminate a deployment.
      operationId: terminate_deployment_api_v1_deployment_terminate__deployment_id__delete
      security:
      - HTTPBearer: []
      parameters:
      - name: deployment_id
        in: path
        required: true
        schema:
          type: string
          title: Deployment Id
      - name: force
        in: query
        required: false
        schema:
          type: boolean
          default: false
          title: Force
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/deployment/scale/{deployment_id}:
    post:
      tags:
      - deployment
      summary: Scale Deployment
      description: Scale a deployment to the specified number of replicas.
      operationId: scale_deployment_api_v1_deployment_scale__deployment_id__post
      security:
      - HTTPBearer: []
      parameters:
      - name: deployment_id
        in: path
        required: true
        schema:
          type: string
          title: Deployment Id
      - name: replicas
        in: query
        required: true
        schema:
          type: integer
          title: Replicas
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/deployment/logs/{deployment_id}:
    get:
      tags:
      - deployment
      summary: Get Deployment Logs
      description: Get logs for a specific deployment.
      operationId: get_deployment_logs_api_v1_deployment_logs__deployment_id__get
      security:
      - HTTPBearer: []
      parameters:
      - name: deployment_id
        in: path
        required: true
        schema:
          type: string
          title: Deployment Id
      - name: lines
        in: query
        required: false
        schema:
          type: integer
          default: 100
          title: Lines
      - name: follow
        in: query
        required: false
        schema:
          type: boolean
          default: false
          title: Follow
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/deployment/metrics/{deployment_id}:
    get:
      tags:
      - deployment
      summary: Get Deployment Metrics
      description: Get metrics for a specific deployment.
      operationId: get_deployment_metrics_api_v1_deployment_metrics__deployment_id__get
      security:
      - HTTPBearer: []
      parameters:
      - name: deployment_id
        in: path
        required: true
        schema:
          type: string
          title: Deployment Id
      - name: time_range
        in: query
        required: false
        schema:
          type: string
          default: 1h
          title: Time Range
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/deployment/strategies:
    get:
      tags:
      - deployment
      summary: Get Deployment Strategies
      description: Get available deployment strategies and their descriptions.
      operationId: get_deployment_strategies_api_v1_deployment_strategies_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/deployment/health:
    get:
      tags:
      - deployment
      summary: Deployment Service Health
      description: Check health of the deployment service.
      operationId: deployment_service_health_api_v1_deployment_health_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/evolution/metrics:
    get:
      tags:
      - platform-evolution
      summary: Get Evolution Metrics
      description: Get platform evolution metrics and statistics.
      operationId: get_evolution_metrics_api_v1_evolution_metrics_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EvolutionMetrics'
      security:
      - HTTPBearer: []
  /api/v1/evolution/initiatives:
    get:
      tags:
      - platform-evolution
      summary: Get Improvement Initiatives
      description: Get list of improvement initiatives with optional filtering.
      operationId: get_improvement_initiatives_api_v1_evolution_initiatives_get
      security:
      - HTTPBearer: []
      parameters:
      - name: status
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Status
      - name: category
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Category
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          default: 50
          title: Limit
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ImprovementInitiative'
                title: Response Get Improvement Initiatives Api V1 Evolution Initiatives
                  Get
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/evolution/patterns:
    get:
      tags:
      - platform-evolution
      summary: Get Usage Patterns
      description: Get identified usage patterns and trends.
      operationId: get_usage_patterns_api_v1_evolution_patterns_get
      security:
      - HTTPBearer: []
      parameters:
      - name: time_range
        in: query
        required: false
        schema:
          type: string
          default: 7d
          title: Time Range
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UsagePattern'
                title: Response Get Usage Patterns Api V1 Evolution Patterns Get
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/evolution/insights:
    get:
      tags:
      - platform-evolution
      summary: Get Platform Insights
      description: Get AI-generated platform insights and recommendations.
      operationId: get_platform_insights_api_v1_evolution_insights_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlatformInsights'
      security:
      - HTTPBearer: []
  /api/v1/evolution/initiatives/{initiative_id}/approve:
    post:
      tags:
      - platform-evolution
      summary: Approve Initiative
      description: Approve an improvement initiative for implementation.
      operationId: approve_initiative_api_v1_evolution_initiatives__initiative_id__approve_post
      security:
      - HTTPBearer: []
      parameters:
      - name: initiative_id
        in: path
        required: true
        schema:
          type: string
          title: Initiative Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/evolution/initiatives/{initiative_id}/reject:
    post:
      tags:
      - platform-evolution
      summary: Reject Initiative
      description: Reject an improvement initiative with reason.
      operationId: reject_initiative_api_v1_evolution_initiatives__initiative_id__reject_post
      security:
      - HTTPBearer: []
      parameters:
      - name: initiative_id
        in: path
        required: true
        schema:
          type: string
          title: Initiative Id
      - name: reason
        in: query
        required: true
        schema:
          type: string
          minLength: 1
          title: Reason
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/evolution/scan:
    post:
      tags:
      - platform-evolution
      summary: Trigger Evolution Scan
      description: Manually trigger a platform evolution analysis scan.
      operationId: trigger_evolution_scan_api_v1_evolution_scan_post
      security:
      - HTTPBearer: []
      parameters:
      - name: force_full_scan
        in: query
        required: false
        schema:
          type: boolean
          default: false
          title: Force Full Scan
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/evolution/scan/{scan_id}/status:
    get:
      tags:
      - platform-evolution
      summary: Get Scan Status
      description: Get status of an evolution scan.
      operationId: get_scan_status_api_v1_evolution_scan__scan_id__status_get
      security:
      - HTTPBearer: []
      parameters:
      - name: scan_id
        in: path
        required: true
        schema:
          type: string
          title: Scan Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/evolution/history:
    get:
      tags:
      - platform-evolution
      summary: Get Evolution History
      description: Get platform evolution history and changes over time.
      operationId: get_evolution_history_api_v1_evolution_history_get
      security:
      - HTTPBearer: []
      parameters:
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          default: 100
          title: Limit
      - name: days
        in: query
        required: false
        schema:
          type: integer
          default: 30
          title: Days
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/evolution/config:
    get:
      tags:
      - platform-evolution
      summary: Get Evolution Config
      description: Get platform evolution system configuration.
      operationId: get_evolution_config_api_v1_evolution_config_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
      - HTTPBearer: []
    post:
      tags:
      - platform-evolution
      summary: Update Evolution Config
      description: Update platform evolution system configuration.
      operationId: update_evolution_config_api_v1_evolution_config_post
      requestBody:
        content:
          application/json:
            schema:
              type: object
              title: Config
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/evolution/health:
    get:
      tags:
      - platform-evolution
      summary: Evolution Service Health
      description: Check health of the evolution service.
      operationId: evolution_service_health_api_v1_evolution_health_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/workflows/create:
    post:
      tags:
      - workflows
      summary: Create Workflow
      description: Create a new workflow.
      operationId: create_workflow_api_v1_workflows_create_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WorkflowDefinition'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - HTTPBearer: []
  /api/v1/workflows/list:
    get:
      tags:
      - workflows
      summary: List Workflows
      description: List workflows with optional filtering.
      operationId: list_workflows_api_v1_workflows_list_get
      security:
      - HTTPBearer: []
      parameters:
      - name: status
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Status
      - name: tag
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Tag
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          default: 0
          title: Offset
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/WorkflowResponse'
                title: Response List Workflows Api V1 Workflows List Get
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/workflows/{workflow_id}:
    get:
      tags:
      - workflows
      summary: Get Workflow
      description: Get a specific workflow definition.
      operationId: get_workflow_api_v1_workflows__workflow_id__get
      security:
      - HTTPBearer: []
      parameters:
      - name: workflow_id
        in: path
        required: true
        schema:
          type: string
          title: Workflow Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowDefinition'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    put:
      tags:
      - workflows
      summary: Update Workflow
      description: Update an existing workflow.
      operationId: update_workflow_api_v1_workflows__workflow_id__put
      security:
      - HTTPBearer: []
      parameters:
      - name: workflow_id
        in: path
        required: true
        schema:
          type: string
          title: Workflow Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WorkflowDefinition'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    delete:
      tags:
      - workflows
      summary: Delete Workflow
      description: Delete a workflow.
      operationId: delete_workflow_api_v1_workflows__workflow_id__delete
      security:
      - HTTPBearer: []
      parameters:
      - name: workflow_id
        in: path
        required: true
        schema:
          type: string
          title: Workflow Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/workflows/{workflow_id}/execute:
    post:
      tags:
      - workflows
      summary: Execute Workflow
      description: Execute a workflow.
      operationId: execute_workflow_api_v1_workflows__workflow_id__execute_post
      security:
      - HTTPBearer: []
      parameters:
      - name: workflow_id
        in: path
        required: true
        schema:
          type: string
          title: Workflow Id
      requestBody:
        content:
          application/json:
            schema:
              type: object
              title: Trigger Data
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowExecutionResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/workflows/{workflow_id}/executions:
    get:
      tags:
      - workflows
      summary: Get Workflow Executions
      description: Get execution history for a workflow.
      operationId: get_workflow_executions_api_v1_workflows__workflow_id__executions_get
      security:
      - HTTPBearer: []
      parameters:
      - name: workflow_id
        in: path
        required: true
        schema:
          type: string
          title: Workflow Id
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          default: 0
          title: Offset
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/WorkflowExecutionResponse'
                title: Response Get Workflow Executions Api V1 Workflows  Workflow
                  Id  Executions Get
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/workflows/executions/{execution_id}:
    get:
      tags:
      - workflows
      summary: Get Execution Status
      description: Get detailed status of a workflow execution.
      operationId: get_execution_status_api_v1_workflows_executions__execution_id__get
      security:
      - HTTPBearer: []
      parameters:
      - name: execution_id
        in: path
        required: true
        schema:
          type: string
          title: Execution Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowExecutionResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/workflows/executions/{execution_id}/nodes:
    get:
      tags:
      - workflows
      summary: Get Execution Node Details
      description: Get detailed execution status for each node in the workflow.
      operationId: get_execution_node_details_api_v1_workflows_executions__execution_id__nodes_get
      security:
      - HTTPBearer: []
      parameters:
      - name: execution_id
        in: path
        required: true
        schema:
          type: string
          title: Execution Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/NodeExecutionResponse'
                title: Response Get Execution Node Details Api V1 Workflows Executions  Execution
                  Id  Nodes Get
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/workflows/executions/{execution_id}/cancel:
    post:
      tags:
      - workflows
      summary: Cancel Execution
      description: Cancel a running workflow execution.
      operationId: cancel_execution_api_v1_workflows_executions__execution_id__cancel_post
      security:
      - HTTPBearer: []
      parameters:
      - name: execution_id
        in: path
        required: true
        schema:
          type: string
          title: Execution Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/workflows/node-types:
    get:
      tags:
      - workflows
      summary: Get Available Node Types
      description: Get all available node types and their configurations.
      operationId: get_available_node_types_api_v1_workflows_node_types_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/workflows/{workflow_id}/activate:
    post:
      tags:
      - workflows
      summary: Activate Workflow
      description: Activate a workflow for automatic execution.
      operationId: activate_workflow_api_v1_workflows__workflow_id__activate_post
      security:
      - HTTPBearer: []
      parameters:
      - name: workflow_id
        in: path
        required: true
        schema:
          type: string
          title: Workflow Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/workflows/{workflow_id}/deactivate:
    post:
      tags:
      - workflows
      summary: Deactivate Workflow
      description: Deactivate a workflow.
      operationId: deactivate_workflow_api_v1_workflows__workflow_id__deactivate_post
      security:
      - HTTPBearer: []
      parameters:
      - name: workflow_id
        in: path
        required: true
        schema:
          type: string
          title: Workflow Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/workflows/executions/{execution_id}/stream:
    get:
      tags:
      - workflows
      summary: Stream Execution Logs
      description: Stream real-time execution logs.
      operationId: stream_execution_logs_api_v1_workflows_executions__execution_id__stream_get
      security:
      - HTTPBearer: []
      parameters:
      - name: execution_id
        in: path
        required: true
        schema:
          type: string
          title: Execution Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/workflows/health:
    get:
      tags:
      - workflows
      summary: Workflow Service Health
      description: Check health of the workflow service.
      operationId: workflow_service_health_api_v1_workflows_health_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/ai/providers:
    get:
      tags:
      - ai-assistant
      summary: Get Available Providers
      description: Get list of available AI providers
      operationId: get_available_providers_api_v1_ai_providers_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/ai/parse-requirements:
    post:
      tags:
      - ai-assistant
      summary: Parse Requirements
      description: Parse natural language requirements into agent configuration
      operationId: parse_requirements_api_v1_ai_parse_requirements_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RequirementsParseRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequirementsParseResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/ai/analyze-code:
    post:
      tags:
      - ai-assistant
      summary: Analyze Code
      description: Analyze code for quality, security, and performance
      operationId: analyze_code_api_v1_ai_analyze_code_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CodeAnalysisRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CodeAnalysisResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/ai/generate-documentation:
    post:
      tags:
      - ai-assistant
      summary: Generate Documentation
      description: Generate documentation for code
      operationId: generate_documentation_api_v1_ai_generate_documentation_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentationRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentationResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/ai/generate:
    post:
      tags:
      - ai-assistant
      summary: Generate Ai Content
      description: Generate AI content using specified capability
      operationId: generate_ai_content_api_v1_ai_generate_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AIGenerationRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AIGenerationResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/ai/capabilities:
    get:
      tags:
      - ai-assistant
      summary: Get Ai Capabilities
      description: Get available AI capabilities
      operationId: get_ai_capabilities_api_v1_ai_capabilities_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/ai/health:
    get:
      tags:
      - ai-assistant
      summary: Ai Service Health
      description: Check AI service health and provider status
      operationId: ai_service_health_api_v1_ai_health_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /:
    get:
      summary: Root
      description: Root endpoint
      operationId: root__get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /health:
    get:
      summary: Health Check
      description: Health check endpoint
      operationId: health_check_health_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /metrics:
    get:
      summary: Metrics
      description: Prometheus metrics endpoint
      operationId: metrics_metrics_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
components:
  schemas:
    A2AResponse:
      properties:
        success:
          type: boolean
          title: Success
        message_id:
          type: string
          title: Message Id
        data:
          anyOf:
          - type: object
          - type: 'null'
          title: Data
        error:
          anyOf:
          - type: string
          - type: 'null'
          title: Error
        timestamp:
          type: string
          format: date-time
          title: Timestamp
      type: object
      required:
      - success
      - message_id
      title: A2AResponse
      description: Response structure for A2A communications
    AIGenerationRequest:
      properties:
        prompt:
          type: string
          title: Prompt
          description: AI prompt
        capability:
          type: string
          title: Capability
          description: AI capability to use
        max_tokens:
          type: integer
          title: Max Tokens
          description: Maximum tokens to generate
          default: 4000
        temperature:
          type: number
          title: Temperature
          description: Temperature for generation
          default: 0.7
        system_message:
          anyOf:
          - type: string
          - type: 'null'
          title: System Message
          description: System message
        preferred_provider:
          anyOf:
          - type: string
          - type: 'null'
          title: Preferred Provider
          description: Preferred AI provider
      type: object
      required:
      - prompt
      - capability
      title: AIGenerationRequest
    AIGenerationResponse:
      properties:
        content:
          type: string
          title: Content
        provider:
          type: string
          title: Provider
        model:
          type: string
          title: Model
        usage:
          type: object
          title: Usage
        metadata:
          type: object
          title: Metadata
      type: object
      required:
      - content
      - provider
      - model
      - usage
      - metadata
      title: AIGenerationResponse
    AIProvider:
      type: string
      enum:
      - openai
      - anthropic
      - google
      - azure
      - huggingface
      - local
      title: AIProvider
      description: Supported AI providers
    AIRequestSchema:
      properties:
        task_type:
          $ref: '#/components/schemas/TaskType'
        provider:
          anyOf:
          - $ref: '#/components/schemas/AIProvider'
          - type: 'null'
        model:
          type: string
          title: Model
        input_data:
          type: object
          title: Input Data
        parameters:
          anyOf:
          - type: object
          - type: 'null'
          title: Parameters
        timeout:
          anyOf:
          - type: integer
          - type: 'null'
          title: Timeout
          default: 300
      type: object
      required:
      - task_type
      - model
      - input_data
      title: AIRequestSchema
      description: AI request schema
    AIResponseSchema:
      properties:
        task_id:
          type: string
          title: Task Id
        provider:
          type: string
          title: Provider
        model:
          type: string
          title: Model
        result:
          title: Result
        metadata:
          type: object
          title: Metadata
        usage:
          anyOf:
          - type: object
          - type: 'null'
          title: Usage
        execution_time:
          type: number
          title: Execution Time
        success:
          type: boolean
          title: Success
        error:
          anyOf:
          - type: string
          - type: 'null'
          title: Error
      type: object
      required:
      - task_id
      - provider
      - model
      - result
      - metadata
      - execution_time
      - success
      title: AIResponseSchema
      description: AI response schema
    ActiveAgentsResponse:
      properties:
        agents:
          items:
            $ref: '#/components/schemas/AgentStatusResponse'
          type: array
          title: Agents
        total_active:
          type: integer
          title: Total Active
        total_inactive:
          type: integer
          title: Total Inactive
      type: object
      required:
      - agents
      - total_active
      - total_inactive
      title: ActiveAgentsResponse
    AddAgentRequest:
      properties:
        agent_id:
          type: string
          format: uuid
          title: Agent Id
        role:
          type: string
          maxLength: 100
          title: Role
          default: worker
      type: object
      required:
      - agent_id
      title: AddAgentRequest
    AdvancedOptions:
      properties:
        use_ai_workflow:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Use Ai Workflow
          description: Use AI-powered workflow
          default: false
        custom_templates:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Custom Templates
          description: Custom templates
        integration_endpoints:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Integration Endpoints
          description: Integration endpoints
        testing_enabled:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Testing Enabled
          description: Enable testing
          default: true
        documentation_level:
          anyOf:
          - type: string
            enum:
            - minimal
            - standard
            - comprehensive
          - type: 'null'
          title: Documentation Level
          description: Documentation level
          default: standard
      type: object
      title: AdvancedOptions
      description: Advanced configuration options
    AgentADKRequest:
      properties:
        agent_type:
          type: string
          title: Agent Type
          description: Type of agent
        capabilities:
          items:
            type: string
          type: array
          title: Capabilities
          description: Agent capabilities
        model_preferences:
          additionalProperties:
            type: string
          type: object
          title: Model Preferences
          description: Model preferences
        metadata:
          anyOf:
          - type: object
          - type: 'null'
          title: Metadata
          description: Additional metadata
      type: object
      required:
      - agent_type
      - capabilities
      title: AgentADKRequest
    AgentADKResponse:
      properties:
        success:
          type: boolean
          title: Success
        agent_config:
          type: object
          title: Agent Config
        recommended_models:
          additionalProperties:
            type: string
          type: object
          title: Recommended Models
        features:
          additionalProperties:
            type: boolean
          type: object
          title: Features
      type: object
      required:
      - success
      - agent_config
      - recommended_models
      - features
      title: AgentADKResponse
    AgentCapability:
      properties:
        name:
          type: string
          title: Name
        description:
          type: string
          title: Description
        version:
          type: string
          title: Version
        parameters:
          type: object
          title: Parameters
        constraints:
          type: object
          title: Constraints
      type: object
      required:
      - name
      - description
      - version
      title: AgentCapability
      description: Agent capability definition
    AgentCreate:
      properties:
        name:
          type: string
          maxLength: 255
          minLength: 1
          title: Name
        description:
          anyOf:
          - type: string
            maxLength: 1000
          - type: 'null'
          title: Description
        type:
          type: string
          enum:
          - assistant
          - analyst
          - specialist
          - coordinator
          - processor
          - monitor
          - fullstack
          - api_service
          - background_worker
          - data_processor
          - integration
          title: Type
          description: Agent type
          default: assistant
        config:
          type: object
          title: Config
        capabilities:
          items:
            type: string
          type: array
          title: Capabilities
        requirements:
          anyOf:
          - type: string
          - type: 'null'
          title: Requirements
          description: Natural language requirements for AI generation
        framework:
          anyOf:
          - type: string
            enum:
            - fastapi
            - flask
            - express
            - custom
          - type: 'null'
          title: Framework
          description: Framework to use for the agent
        language:
          anyOf:
          - type: string
            enum:
            - python
            - javascript
            - typescript
          - type: 'null'
          title: Language
          description: Programming language for the agent
        deployment:
          anyOf:
          - $ref: '#/components/schemas/DeploymentConfig'
          - type: 'null'
          description: Deployment configuration
        advanced_options:
          anyOf:
          - $ref: '#/components/schemas/AdvancedOptions'
          - type: 'null'
          description: Advanced configuration options
      type: object
      required:
      - name
      title: AgentCreate
      description: Comprehensive agent creation schema that accepts all GenerationRequest
        fields
    AgentListResponse:
      properties:
        agents:
          items:
            $ref: '#/components/schemas/AgentResponse'
          type: array
          title: Agents
        total:
          type: integer
          title: Total
        limit:
          type: integer
          title: Limit
        offset:
          type: integer
          title: Offset
      type: object
      required:
      - agents
      - total
      - limit
      - offset
      title: AgentListResponse
    AgentResponse:
      properties:
        name:
          type: string
          maxLength: 255
          minLength: 1
          title: Name
        description:
          anyOf:
          - type: string
            maxLength: 1000
          - type: 'null'
          title: Description
        type:
          type: string
          enum:
          - assistant
          - analyst
          - specialist
          - coordinator
          - processor
          - monitor
          - fullstack
          - api_service
          - background_worker
          - data_processor
          - integration
          title: Type
          description: Agent type
          default: assistant
        config:
          type: object
          title: Config
        capabilities:
          items:
            type: string
          type: array
          title: Capabilities
        id:
          type: string
          format: uuid
          title: Id
        status:
          $ref: '#/components/schemas/AgentStatus'
        version:
          type: string
          title: Version
        owner_id:
          type: string
          format: uuid
          title: Owner Id
        created_at:
          type: string
          format: date-time
          title: Created At
        updated_at:
          type: string
          format: date-time
          title: Updated At
        last_heartbeat:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Last Heartbeat
        cpu_usage:
          anyOf:
          - type: number
          - type: 'null'
          title: Cpu Usage
        memory_usage:
          anyOf:
          - type: integer
          - type: 'null'
          title: Memory Usage
      type: object
      required:
      - name
      - id
      - status
      - version
      - owner_id
      - created_at
      - updated_at
      - last_heartbeat
      - cpu_usage
      - memory_usage
      title: AgentResponse
    AgentStartResponse:
      properties:
        agent_id:
          type: string
          format: uuid
          title: Agent Id
        success:
          type: boolean
          title: Success
        message:
          type: string
          title: Message
      type: object
      required:
      - agent_id
      - success
      - message
      title: AgentStartResponse
    AgentStatus:
      type: string
      enum:
      - created
      - starting
      - running
      - paused
      - stopped
      - error
      - terminated
      title: AgentStatus
      description: Agent status enumeration
    AgentStatusResponse:
      properties:
        agent_id:
          type: string
          title: Agent Id
        status:
          type: string
          title: Status
        agent_type:
          type: string
          title: Agent Type
        capabilities:
          items:
            $ref: '#/components/schemas/AgentCapability'
          type: array
          title: Capabilities
        last_heartbeat:
          type: string
          title: Last Heartbeat
        registered_at:
          type: string
          title: Registered At
        metadata:
          type: object
          title: Metadata
      type: object
      required:
      - agent_id
      - status
      - agent_type
      - capabilities
      - last_heartbeat
      - registered_at
      - metadata
      title: AgentStatusResponse
    AgentType:
      type: string
      enum:
      - assistant
      - analyst
      - specialist
      - coordinator
      - processor
      - monitor
      - fullstack
      title: AgentType
      description: Supported agent types for generation
    AgentUpdate:
      properties:
        name:
          anyOf:
          - type: string
            maxLength: 255
            minLength: 1
          - type: 'null'
          title: Name
        description:
          anyOf:
          - type: string
            maxLength: 1000
          - type: 'null'
          title: Description
        config:
          anyOf:
          - type: object
          - type: 'null'
          title: Config
        capabilities:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Capabilities
        constraints:
          anyOf:
          - type: object
          - type: 'null'
          title: Constraints
      type: object
      title: AgentUpdate
    Body_analyze_image_upload_api_v1_google_adk_analyze_image_upload_post:
      properties:
        file:
          type: string
          format: binary
          title: File
      type: object
      required:
      - file
      title: Body_analyze_image_upload_api_v1_google_adk_analyze_image_upload_post
    Body_login_api_v1_auth_login_post:
      properties:
        grant_type:
          anyOf:
          - type: string
            pattern: password
          - type: 'null'
          title: Grant Type
        username:
          type: string
          title: Username
        password:
          type: string
          title: Password
        scope:
          type: string
          title: Scope
          default: ''
        client_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Client Id
        client_secret:
          anyOf:
          - type: string
          - type: 'null'
          title: Client Secret
      type: object
      required:
      - username
      - password
      title: Body_login_api_v1_auth_login_post
    Body_refresh_token_api_v1_auth_refresh_post:
      properties:
        refresh_token:
          type: string
          title: Refresh Token
      type: object
      required:
      - refresh_token
      title: Body_refresh_token_api_v1_auth_refresh_post
    BroadcastMessageRequest:
      properties:
        subject:
          type: string
          title: Subject
          description: Broadcast subject
        payload:
          type: object
          title: Payload
          description: Message payload
        filter_agent_types:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Filter Agent Types
          description: Filter by agent types
        filter_capabilities:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Filter Capabilities
          description: Filter by capabilities
        priority:
          allOf:
          - $ref: '#/components/schemas/MessagePriority'
          default: medium
      type: object
      required:
      - subject
      - payload
      title: BroadcastMessageRequest
    CodeAnalysisRequest:
      properties:
        code:
          type: string
          title: Code
          description: Code to analyze
        language:
          type: string
          title: Language
          description: Programming language
        analysis_type:
          type: string
          title: Analysis Type
          description: Type of analysis to perform
          default: full
      type: object
      required:
      - code
      - language
      title: CodeAnalysisRequest
    CodeAnalysisResponse:
      properties:
        quality_score:
          type: integer
          title: Quality Score
        issues:
          items:
            type: string
          type: array
          title: Issues
        suggestions:
          items:
            type: string
          type: array
          title: Suggestions
        complexity:
          type: string
          title: Complexity
        maintainability:
          type: string
          title: Maintainability
        security_concerns:
          items:
            type: string
          type: array
          title: Security Concerns
        performance_notes:
          items:
            type: string
          type: array
          title: Performance Notes
      type: object
      required:
      - quality_score
      - issues
      - suggestions
      - complexity
      - maintainability
      - security_concerns
      - performance_notes
      title: CodeAnalysisResponse
    CodeGenerationResponse:
      properties:
        success:
          type: boolean
          title: Success
        code:
          type: string
          title: Code
        language:
          type: string
          title: Language
        model:
          type: string
          title: Model
        metadata:
          type: object
          title: Metadata
      type: object
      required:
      - success
      - code
      - language
      - model
      title: CodeGenerationResponse
    CodeResult:
      properties:
        id:
          type: string
          title: Id
        score:
          type: number
          title: Score
        title:
          type: string
          title: Title
        code:
          type: string
          title: Code
        language:
          type: string
          title: Language
        description:
          type: string
          title: Description
        tags:
          items:
            type: string
          type: array
          title: Tags
        created_at:
          type: string
          title: Created At
        metadata:
          type: object
          title: Metadata
      type: object
      required:
      - id
      - score
      - title
      - code
      - language
      - description
      - tags
      - created_at
      - metadata
      title: CodeResult
    CollectionInfoResponse:
      properties:
        success:
          type: boolean
          title: Success
        info:
          type: object
          title: Info
      type: object
      required:
      - success
      - info
      title: CollectionInfoResponse
    CompletionRequest:
      properties:
        prompt:
          type: string
          title: Prompt
        model:
          anyOf:
          - type: string
          - type: 'null'
          title: Model
          default: gpt-3.5-turbo
        provider:
          anyOf:
          - $ref: '#/components/schemas/AIProvider'
          - type: 'null'
        temperature:
          anyOf:
          - type: number
          - type: 'null'
          title: Temperature
          default: 0.7
        max_tokens:
          anyOf:
          - type: integer
          - type: 'null'
          title: Max Tokens
          default: 1000
      type: object
      required:
      - prompt
      title: CompletionRequest
      description: Text completion request
    ContextResult:
      properties:
        id:
          type: string
          title: Id
        score:
          type: number
          title: Score
        message:
          type: string
          title: Message
        role:
          type: string
          title: Role
        conversation_id:
          type: string
          title: Conversation Id
        created_at:
          type: string
          title: Created At
        metadata:
          type: object
          title: Metadata
      type: object
      required:
      - id
      - score
      - message
      - role
      - conversation_id
      - created_at
      - metadata
      title: ContextResult
    ConversationHistoryResponse:
      properties:
        success:
          type: boolean
          title: Success
        conversation_id:
          type: string
          title: Conversation Id
        messages:
          items:
            $ref: '#/components/schemas/ConversationMessage'
          type: array
          title: Messages
        total_messages:
          type: integer
          title: Total Messages
      type: object
      required:
      - success
      - conversation_id
      - messages
      - total_messages
      title: ConversationHistoryResponse
    ConversationMessage:
      properties:
        id:
          type: string
          title: Id
        type:
          $ref: '#/components/schemas/MessageType'
        sender_id:
          type: string
          title: Sender Id
        receiver_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Receiver Id
        subject:
          type: string
          title: Subject
        payload:
          type: object
          title: Payload
        timestamp:
          type: string
          title: Timestamp
      type: object
      required:
      - id
      - type
      - sender_id
      - receiver_id
      - subject
      - payload
      - timestamp
      title: ConversationMessage
    CreateRoleRequest:
      properties:
        name:
          type: string
          maxLength: 100
          minLength: 1
          title: Name
        display_name:
          type: string
          maxLength: 200
          minLength: 1
          title: Display Name
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
        permissions:
          items:
            type: string
          type: array
          minItems: 1
          title: Permissions
      type: object
      required:
      - name
      - display_name
      - permissions
      title: CreateRoleRequest
    DeleteDataRequest:
      properties:
        collection_name:
          type: string
          title: Collection Name
          description: Collection name (without prefix)
        filter_conditions:
          type: object
          title: Filter Conditions
          description: Filter conditions for deletion
      type: object
      required:
      - collection_name
      - filter_conditions
      title: DeleteDataRequest
    DeleteDataResponse:
      properties:
        success:
          type: boolean
          title: Success
        message:
          type: string
          title: Message
      type: object
      required:
      - success
      - message
      title: DeleteDataResponse
    DeploymentConfig:
      properties:
        port:
          anyOf:
          - type: integer
            maximum: 65535.0
            minimum: 1.0
          - type: 'null'
          title: Port
          description: Port number
        environment:
          anyOf:
          - additionalProperties:
              type: string
            type: object
          - type: 'null'
          title: Environment
          description: Environment variables
        resources:
          anyOf:
          - $ref: '#/components/schemas/ResourceLimits'
          - type: 'null'
          description: Resource limits
        auto_scale:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Auto Scale
          description: Enable auto-scaling
          default: false
        replicas:
          anyOf:
          - type: integer
            minimum: 1.0
          - type: 'null'
          title: Replicas
          description: Number of replicas
          default: 1
      type: object
      title: DeploymentConfig
      description: Deployment configuration for agents
    DeploymentListResponse:
      properties:
        deployments:
          items:
            $ref: '#/components/schemas/DeploymentStatusResponse'
          type: array
          title: Deployments
        total:
          type: integer
          title: Total
        active:
          type: integer
          title: Active
        failed:
          type: integer
          title: Failed
      type: object
      required:
      - deployments
      - total
      - active
      - failed
      title: DeploymentListResponse
    DeploymentRequest:
      properties:
        agent_definition:
          type: object
          title: Agent Definition
          description: Agent definition to deploy
        strategy:
          allOf:
          - $ref: '#/components/schemas/DeploymentStrategy'
          description: Deployment strategy
          default: rolling
        replicas:
          type: integer
          maximum: 10.0
          minimum: 1.0
          title: Replicas
          description: Number of replicas
          default: 1
        resources:
          anyOf:
          - type: object
          - type: 'null'
          title: Resources
          description: Resource requirements
        environment_variables:
          anyOf:
          - additionalProperties:
              type: string
            type: object
          - type: 'null'
          title: Environment Variables
          description: Environment variables
        health_check_path:
          type: string
          title: Health Check Path
          description: Health check endpoint path
          default: /health
        port_range_start:
          anyOf:
          - type: integer
            maximum: 32000.0
            minimum: 30000.0
          - type: 'null'
          title: Port Range Start
          description: Port range start
        port_range_end:
          anyOf:
          - type: integer
            maximum: 32000.0
            minimum: 30000.0
          - type: 'null'
          title: Port Range End
          description: Port range end
      type: object
      required:
      - agent_definition
      title: DeploymentRequest
    DeploymentResponse:
      properties:
        deployment_id:
          type: string
          title: Deployment Id
        status:
          type: string
          title: Status
        port:
          anyOf:
          - type: integer
          - type: 'null'
          title: Port
        endpoints:
          items:
            type: string
          type: array
          title: Endpoints
        created_at:
          type: string
          title: Created At
      type: object
      required:
      - deployment_id
      - status
      - port
      - endpoints
      - created_at
      title: DeploymentResponse
    DeploymentStatusResponse:
      properties:
        deployment_id:
          type: string
          title: Deployment Id
        status:
          type: string
          title: Status
        stage:
          type: string
          title: Stage
        progress:
          type: number
          title: Progress
        logs:
          items:
            type: string
          type: array
          title: Logs
        health_status:
          type: string
          title: Health Status
        endpoints:
          items:
            type: string
          type: array
          title: Endpoints
        metrics:
          type: object
          title: Metrics
        updated_at:
          type: string
          title: Updated At
      type: object
      required:
      - deployment_id
      - status
      - stage
      - progress
      - logs
      - health_status
      - endpoints
      - metrics
      - updated_at
      title: DeploymentStatusResponse
    DeploymentStrategy:
      type: string
      enum:
      - blue_green
      - rolling
      - canary
      - recreate
      title: DeploymentStrategy
      description: Deployment strategies.
    DocumentationRequest:
      properties:
        code:
          type: string
          title: Code
          description: Code to document
        language:
          type: string
          title: Language
          description: Programming language
        doc_type:
          type: string
          title: Doc Type
          description: Type of documentation
          default: api
      type: object
      required:
      - code
      - language
      title: DocumentationRequest
    DocumentationResponse:
      properties:
        documentation:
          type: string
          title: Documentation
        format:
          type: string
          title: Format
          default: markdown
      type: object
      required:
      - documentation
      title: DocumentationResponse
    EmbeddingResponse:
      properties:
        success:
          type: boolean
          title: Success
        embeddings:
          items:
            items:
              type: number
            type: array
          type: array
          title: Embeddings
        model:
          type: string
          title: Model
        dimensions:
          type: integer
          title: Dimensions
        metadata:
          type: object
          title: Metadata
      type: object
      required:
      - success
      - embeddings
      - model
      - dimensions
      title: EmbeddingResponse
    EstablishConversationRequest:
      properties:
        participant_ids:
          items:
            type: string
          type: array
          title: Participant Ids
          description: List of participant agent IDs
        context:
          type: object
          title: Context
          description: Conversation context
      type: object
      required:
      - participant_ids
      - context
      title: EstablishConversationRequest
    EstablishConversationResponse:
      properties:
        success:
          type: boolean
          title: Success
        conversation_id:
          type: string
          title: Conversation Id
        participants:
          items:
            type: string
          type: array
          title: Participants
      type: object
      required:
      - success
      - conversation_id
      - participants
      title: EstablishConversationResponse
    EvolutionMetrics:
      properties:
        total_improvements:
          type: integer
          title: Total Improvements
        active_initiatives:
          type: integer
          title: Active Initiatives
        completed_initiatives:
          type: integer
          title: Completed Initiatives
        performance_improvements:
          type: number
          title: Performance Improvements
        capability_enhancements:
          type: integer
          title: Capability Enhancements
        usability_improvements:
          type: integer
          title: Usability Improvements
        last_update:
          type: string
          title: Last Update
      type: object
      required:
      - total_improvements
      - active_initiatives
      - completed_initiatives
      - performance_improvements
      - capability_enhancements
      - usability_improvements
      - last_update
      title: EvolutionMetrics
    GenerationRequestModel:
      properties:
        agent_name:
          type: string
          maxLength: 100
          minLength: 1
          title: Agent Name
          description: Name of the agent
        agent_type:
          allOf:
          - $ref: '#/components/schemas/AgentType'
          description: Type of agent to generate
        language:
          allOf:
          - $ref: '#/components/schemas/ProgrammingLanguage'
          description: Programming language for the agent
        description:
          type: string
          maxLength: 500
          minLength: 1
          title: Description
          description: Description of the agent
        capabilities:
          items:
            type: string
          type: array
          minItems: 1
          title: Capabilities
          description: List of agent capabilities
        configuration:
          type: object
          title: Configuration
          description: Custom configuration
        custom_logic:
          anyOf:
          - type: string
          - type: 'null'
          title: Custom Logic
          description: Custom logic code
        deployment_config:
          anyOf:
          - type: object
          - type: 'null'
          title: Deployment Config
          description: Deployment configuration
      type: object
      required:
      - agent_name
      - agent_type
      - language
      - description
      - capabilities
      title: GenerationRequestModel
    GenerationResponse:
      properties:
        generation_id:
          type: string
          title: Generation Id
        status:
          type: string
          title: Status
          default: pending
        message:
          type: string
          title: Message
          default: Agent generation started
        download_url:
          anyOf:
          - type: string
          - type: 'null'
          title: Download Url
      type: object
      required:
      - generation_id
      title: GenerationResponse
    GenerationStatus:
      properties:
        generation_id:
          type: string
          title: Generation Id
        status:
          type: string
          title: Status
        progress:
          type: integer
          title: Progress
        message:
          type: string
          title: Message
        download_url:
          anyOf:
          - type: string
          - type: 'null'
          title: Download Url
        error:
          anyOf:
          - type: string
          - type: 'null'
          title: Error
      type: object
      required:
      - generation_id
      - status
      - progress
      - message
      title: GenerationStatus
    GetContextRequest:
      properties:
        agent_id:
          type: string
          title: Agent Id
          description: Agent ID
        query:
          type: string
          maxLength: 1000
          minLength: 1
          title: Query
          description: Context query
        limit:
          type: integer
          maximum: 20.0
          minimum: 1.0
          title: Limit
          description: Maximum context items
          default: 5
        score_threshold:
          type: number
          maximum: 1.0
          minimum: 0.0
          title: Score Threshold
          description: Minimum similarity score
          default: 0.6
      type: object
      required:
      - agent_id
      - query
      title: GetContextRequest
    GetContextResponse:
      properties:
        success:
          type: boolean
          title: Success
        context:
          items:
            $ref: '#/components/schemas/ContextResult'
          type: array
          title: Context
        total_found:
          type: integer
          title: Total Found
      type: object
      required:
      - success
      - context
      - total_found
      title: GetContextResponse
    HTTPValidationError:
      properties:
        detail:
          items:
            $ref: '#/components/schemas/ValidationError'
          type: array
          title: Detail
      type: object
      title: HTTPValidationError
    HealthCheckResponse:
      properties:
        status:
          type: string
          title: Status
        vertex_ai:
          type: object
          title: Vertex Ai
        generative_ai:
          type: object
          title: Generative Ai
        available_models:
          items:
            type: string
          type: array
          title: Available Models
        timestamp:
          type: string
          title: Timestamp
      type: object
      required:
      - status
      - vertex_ai
      - generative_ai
      - available_models
      - timestamp
      title: HealthCheckResponse
    HealthResponse:
      properties:
        success:
          type: boolean
          title: Success
        status:
          type: string
          title: Status
        details:
          type: object
          title: Details
      type: object
      required:
      - success
      - status
      - details
      title: HealthResponse
    ImageAnalysisRequest:
      properties:
        prompt:
          type: string
          maxLength: 1000
          minLength: 1
          title: Prompt
          description: Analysis prompt
        image_base64:
          anyOf:
          - type: string
          - type: 'null'
          title: Image Base64
          description: Base64 encoded image
        model:
          type: string
          title: Model
          description: Model to use
          default: gemini-pro-vision
        temperature:
          anyOf:
          - type: number
            maximum: 1.0
            minimum: 0.0
          - type: 'null'
          title: Temperature
          description: Temperature
      type: object
      required:
      - prompt
      title: ImageAnalysisRequest
    ImageAnalysisResponse:
      properties:
        success:
          type: boolean
          title: Success
        analysis:
          type: string
          title: Analysis
        model:
          type: string
          title: Model
        metadata:
          type: object
          title: Metadata
      type: object
      required:
      - success
      - analysis
      - model
      title: ImageAnalysisResponse
    ImprovementInitiative:
      properties:
        id:
          type: string
          title: Id
        title:
          type: string
          title: Title
        description:
          type: string
          title: Description
        category:
          type: string
          title: Category
        priority:
          type: string
          title: Priority
        status:
          type: string
          title: Status
        impact_score:
          type: number
          title: Impact Score
        effort_estimate:
          type: integer
          title: Effort Estimate
        created_at:
          type: string
          title: Created At
        updated_at:
          type: string
          title: Updated At
        completion_date:
          anyOf:
          - type: string
          - type: 'null'
          title: Completion Date
        metrics:
          type: object
          title: Metrics
      type: object
      required:
      - id
      - title
      - description
      - category
      - priority
      - status
      - impact_score
      - effort_estimate
      - created_at
      - updated_at
      - metrics
      title: ImprovementInitiative
    KnowledgeEnhancementRequest:
      properties:
        agent_id:
          type: string
          title: Agent Id
          description: Agent ID to enhance
        knowledge_sources:
          items:
            type: object
          type: array
          title: Knowledge Sources
          description: Knowledge sources
      type: object
      required:
      - agent_id
      - knowledge_sources
      title: KnowledgeEnhancementRequest
    KnowledgeEnhancementResponse:
      properties:
        success:
          type: boolean
          title: Success
        sources_processed:
          type: integer
          title: Sources Processed
        message:
          type: string
          title: Message
      type: object
      required:
      - success
      - sources_processed
      - message
      title: KnowledgeEnhancementResponse
    KnowledgeResult:
      properties:
        id:
          type: string
          title: Id
        score:
          type: number
          title: Score
        content:
          type: string
          title: Content
        agent_id:
          type: string
          title: Agent Id
        category:
          type: string
          title: Category
        metadata:
          type: object
          title: Metadata
        created_at:
          type: string
          title: Created At
      type: object
      required:
      - id
      - score
      - content
      - agent_id
      - category
      - metadata
      - created_at
      title: KnowledgeResult
    LoginWithMFARequest:
      properties:
        email:
          type: string
          format: email
          title: Email
        password:
          type: string
          title: Password
        mfa_token:
          anyOf:
          - type: string
          - type: 'null'
          title: Mfa Token
      type: object
      required:
      - email
      - password
      title: LoginWithMFARequest
    MFASetupSMSRequest:
      properties:
        device_name:
          type: string
          maxLength: 100
          minLength: 1
          title: Device Name
        phone_number:
          type: string
          maxLength: 20
          minLength: 10
          title: Phone Number
      type: object
      required:
      - device_name
      - phone_number
      title: MFASetupSMSRequest
    MFASetupTOTPRequest:
      properties:
        device_name:
          type: string
          maxLength: 100
          minLength: 1
          title: Device Name
      type: object
      required:
      - device_name
      title: MFASetupTOTPRequest
    MFATokenRequest:
      properties:
        token:
          type: string
          title: Token
      type: object
      required:
      - token
      title: MFATokenRequest
    MFAVerifyRequest:
      properties:
        device_id:
          type: string
          title: Device Id
        code:
          type: string
          title: Code
      type: object
      required:
      - device_id
      - code
      title: MFAVerifyRequest
    MessagePriority:
      type: string
      enum:
      - low
      - medium
      - high
      - critical
      title: MessagePriority
      description: Message priority levels
    MessageSearchResult:
      properties:
        id:
          type: string
          title: Id
        score:
          type: number
          title: Score
        content:
          type: string
          title: Content
        agent_id:
          type: string
          title: Agent Id
        metadata:
          type: object
          title: Metadata
        created_at:
          type: string
          title: Created At
      type: object
      required:
      - id
      - score
      - content
      - agent_id
      - metadata
      - created_at
      title: MessageSearchResult
    MessageType:
      type: string
      enum:
      - request
      - response
      - broadcast
      - handshake
      - heartbeat
      - terminate
      - error
      title: MessageType
      description: A2A message types
    ModelInfoResponse:
      properties:
        model:
          type: string
          title: Model
        name:
          type: string
          title: Name
        description:
          type: string
          title: Description
        capabilities:
          items:
            type: string
          type: array
          title: Capabilities
        metadata:
          type: object
          title: Metadata
      type: object
      required:
      - model
      - name
      - description
      - capabilities
      - metadata
      title: ModelInfoResponse
    NodeExecutionResponse:
      properties:
        node_id:
          type: string
          title: Node Id
        node_name:
          type: string
          title: Node Name
        status:
          type: string
          title: Status
        started_at:
          anyOf:
          - type: string
          - type: 'null'
          title: Started At
        completed_at:
          anyOf:
          - type: string
          - type: 'null'
          title: Completed At
        duration:
          anyOf:
          - type: number
          - type: 'null'
          title: Duration
        input_data:
          type: object
          title: Input Data
        output_data:
          type: object
          title: Output Data
        error:
          anyOf:
          - type: string
          - type: 'null'
          title: Error
      type: object
      required:
      - node_id
      - node_name
      - status
      - started_at
      - completed_at
      - duration
      - input_data
      - output_data
      - error
      title: NodeExecutionResponse
    OAuthCallbackRequest:
      properties:
        provider:
          type: string
          title: Provider
        code:
          type: string
          title: Code
        state:
          type: string
          title: State
      type: object
      required:
      - provider
      - code
      - state
      title: OAuthCallbackRequest
    OAuthInitRequest:
      properties:
        provider:
          type: string
          title: Provider
          description: OAuth provider (google, github, microsoft)
        redirect_url:
          anyOf:
          - type: string
          - type: 'null'
          title: Redirect Url
          description: URL to redirect after successful auth
      type: object
      required:
      - provider
      title: OAuthInitRequest
    OAuthInitResponse:
      properties:
        authorization_url:
          type: string
          title: Authorization Url
        state:
          type: string
          title: State
      type: object
      required:
      - authorization_url
      - state
      title: OAuthInitResponse
    OrchestrationCreate:
      properties:
        name:
          type: string
          maxLength: 255
          minLength: 1
          title: Name
        description:
          anyOf:
          - type: string
            maxLength: 1000
          - type: 'null'
          title: Description
        pattern:
          allOf:
          - $ref: '#/components/schemas/OrchestrationPattern'
          default: sequential
        config:
          type: object
          title: Config
        execution_plan:
          type: object
          title: Execution Plan
        agent_ids:
          items:
            type: string
            format: uuid
          type: array
          minItems: 1
          title: Agent Ids
      type: object
      required:
      - name
      - agent_ids
      title: OrchestrationCreate
    OrchestrationListResponse:
      properties:
        orchestrations:
          items:
            $ref: '#/components/schemas/OrchestrationResponse'
          type: array
          title: Orchestrations
        total:
          type: integer
          title: Total
        limit:
          type: integer
          title: Limit
        offset:
          type: integer
          title: Offset
      type: object
      required:
      - orchestrations
      - total
      - limit
      - offset
      title: OrchestrationListResponse
    OrchestrationMemberResponse:
      properties:
        agent_id:
          type: string
          format: uuid
          title: Agent Id
        role:
          type: string
          title: Role
        join_order:
          type: integer
          title: Join Order
      type: object
      required:
      - agent_id
      - role
      - join_order
      title: OrchestrationMemberResponse
    OrchestrationPattern:
      type: string
      enum:
      - sequential
      - parallel
      - hierarchical
      - peer_to_peer
      - event_driven
      title: OrchestrationPattern
      description: Orchestration pattern enumeration
    OrchestrationProgressResponse:
      properties:
        orchestration_id:
          type: string
          title: Orchestration Id
        status:
          type: string
          title: Status
        pattern:
          type: string
          title: Pattern
        progress_percentage:
          type: number
          title: Progress Percentage
        total_steps:
          type: integer
          title: Total Steps
        completed_steps:
          type: integer
          title: Completed Steps
        start_time:
          anyOf:
          - type: string
          - type: 'null'
          title: Start Time
        end_time:
          anyOf:
          - type: string
          - type: 'null'
          title: End Time
        duration_seconds:
          anyOf:
          - type: number
          - type: 'null'
          title: Duration Seconds
        agent_count:
          type: integer
          title: Agent Count
        results:
          type: object
          title: Results
      type: object
      required:
      - orchestration_id
      - status
      - pattern
      - progress_percentage
      - total_steps
      - completed_steps
      - start_time
      - end_time
      - duration_seconds
      - agent_count
      - results
      title: OrchestrationProgressResponse
    OrchestrationResponse:
      properties:
        name:
          type: string
          maxLength: 255
          minLength: 1
          title: Name
        description:
          anyOf:
          - type: string
            maxLength: 1000
          - type: 'null'
          title: Description
        pattern:
          allOf:
          - $ref: '#/components/schemas/OrchestrationPattern'
          default: sequential
        config:
          type: object
          title: Config
        execution_plan:
          type: object
          title: Execution Plan
        id:
          type: string
          format: uuid
          title: Id
        status:
          type: string
          title: Status
        progress_percentage:
          type: integer
          title: Progress Percentage
        owner_id:
          type: string
          format: uuid
          title: Owner Id
        created_at:
          type: string
          format: date-time
          title: Created At
        updated_at:
          type: string
          format: date-time
          title: Updated At
        started_at:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Started At
        completed_at:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Completed At
        members:
          items:
            $ref: '#/components/schemas/OrchestrationMemberResponse'
          type: array
          title: Members
      type: object
      required:
      - name
      - id
      - status
      - progress_percentage
      - owner_id
      - created_at
      - updated_at
      - started_at
      - completed_at
      - members
      title: OrchestrationResponse
    OrchestrationStartResponse:
      properties:
        orchestration_id:
          type: string
          format: uuid
          title: Orchestration Id
        success:
          type: boolean
          title: Success
        message:
          type: string
          title: Message
      type: object
      required:
      - orchestration_id
      - success
      - message
      title: OrchestrationStartResponse
    OrchestrationUpdate:
      properties:
        name:
          anyOf:
          - type: string
            maxLength: 255
            minLength: 1
          - type: 'null'
          title: Name
        description:
          anyOf:
          - type: string
            maxLength: 1000
          - type: 'null'
          title: Description
        config:
          anyOf:
          - type: object
          - type: 'null'
          title: Config
        execution_plan:
          anyOf:
          - type: object
          - type: 'null'
          title: Execution Plan
      type: object
      title: OrchestrationUpdate
    PasswordChangeRequest:
      properties:
        current_password:
          type: string
          minLength: 8
          title: Current Password
        new_password:
          type: string
          minLength: 8
          title: New Password
      type: object
      required:
      - current_password
      - new_password
      title: PasswordChangeRequest
    PerformanceAnalysisRequest:
      properties:
        agent_id:
          type: string
          title: Agent Id
          description: Agent ID to analyze
        metrics:
          type: object
          title: Metrics
          description: Performance metrics
      type: object
      required:
      - agent_id
      - metrics
      title: PerformanceAnalysisRequest
    PerformanceAnalysisResponse:
      properties:
        success:
          type: boolean
          title: Success
        analysis:
          type: string
          title: Analysis
        recommendations:
          items:
            type: string
          type: array
          title: Recommendations
        metrics_summary:
          type: object
          title: Metrics Summary
        timestamp:
          type: string
          title: Timestamp
      type: object
      required:
      - success
      - analysis
      - recommendations
      - metrics_summary
      - timestamp
      title: PerformanceAnalysisResponse
    PlatformInsights:
      properties:
        overall_health_score:
          type: number
          title: Overall Health Score
        performance_trends:
          additionalProperties:
            type: number
          type: object
          title: Performance Trends
        usage_patterns:
          items:
            $ref: '#/components/schemas/UsagePattern'
          type: array
          title: Usage Patterns
        improvement_suggestions:
          items:
            type: string
          type: array
          title: Improvement Suggestions
        bottlenecks:
          items:
            type: string
          type: array
          title: Bottlenecks
        optimization_opportunities:
          items:
            type: string
          type: array
          title: Optimization Opportunities
        generated_at:
          type: string
          title: Generated At
      type: object
      required:
      - overall_health_score
      - performance_trends
      - usage_patterns
      - improvement_suggestions
      - bottlenecks
      - optimization_opportunities
      - generated_at
      title: PlatformInsights
    ProgrammingLanguage:
      type: string
      enum:
      - python
      - typescript
      - javascript
      title: ProgrammingLanguage
      description: Supported programming languages
    QueryCapabilitiesRequest:
      properties:
        agent_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Agent Id
          description: Specific agent ID to query
        capability_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Capability Name
          description: Specific capability to search for
      type: object
      title: QueryCapabilitiesRequest
    QueryCapabilitiesResponse:
      properties:
        capabilities:
          additionalProperties:
            items:
              $ref: '#/components/schemas/AgentCapability'
            type: array
          type: object
          title: Capabilities
        total_agents:
          type: integer
          title: Total Agents
      type: object
      required:
      - capabilities
      - total_agents
      title: QueryCapabilitiesResponse
    RegisterAgentRequest:
      properties:
        agent_id:
          type: string
          title: Agent Id
          description: Agent ID to register
        agent_type:
          type: string
          title: Agent Type
          description: Type of agent
        capabilities:
          items:
            $ref: '#/components/schemas/AgentCapability'
          type: array
          title: Capabilities
          description: Agent capabilities
        metadata:
          anyOf:
          - type: object
          - type: 'null'
          title: Metadata
          description: Additional metadata
      type: object
      required:
      - agent_id
      - agent_type
      - capabilities
      title: RegisterAgentRequest
    RegisterAgentResponse:
      properties:
        success:
          type: boolean
          title: Success
        message:
          type: string
          title: Message
      type: object
      required:
      - success
      - message
      title: RegisterAgentResponse
    RequirementsParseRequest:
      properties:
        requirements:
          type: string
          title: Requirements
          description: Natural language requirements for agent
        context:
          anyOf:
          - type: object
          - type: 'null'
          title: Context
          description: Additional context
      type: object
      required:
      - requirements
      title: RequirementsParseRequest
    RequirementsParseResponse:
      properties:
        name:
          type: string
          title: Name
        type:
          type: string
          title: Type
        language:
          type: string
          title: Language
        framework:
          type: string
          title: Framework
        capabilities:
          items:
            type: string
          type: array
          title: Capabilities
        deployment:
          type: object
          title: Deployment
        advanced_options:
          type: object
          title: Advanced Options
        reasoning:
          type: string
          title: Reasoning
        confidence:
          type: number
          title: Confidence
          description: Confidence in parsing accuracy
          default: 0.8
      type: object
      required:
      - name
      - type
      - language
      - framework
      - capabilities
      - deployment
      - advanced_options
      - reasoning
      title: RequirementsParseResponse
    ResourceLimits:
      properties:
        cpu_limit:
          anyOf:
          - type: string
          - type: 'null'
          title: Cpu Limit
          description: CPU limit (e.g., '500m', '1')
        memory_limit:
          anyOf:
          - type: string
          - type: 'null'
          title: Memory Limit
          description: Memory limit (e.g., '512Mi', '1Gi')
        disk_limit:
          anyOf:
          - type: string
          - type: 'null'
          title: Disk Limit
          description: Disk limit
      type: object
      title: ResourceLimits
      description: Resource limits for agent deployment
    ResourceUsageResponse:
      properties:
        total_cpu_usage:
          type: number
          title: Total Cpu Usage
        total_memory_mb:
          type: integer
          title: Total Memory Mb
        average_cpu_per_agent:
          type: number
          title: Average Cpu Per Agent
        average_memory_per_agent:
          type: number
          title: Average Memory Per Agent
      type: object
      required:
      - total_cpu_usage
      - total_memory_mb
      - average_cpu_per_agent
      - average_memory_per_agent
      title: ResourceUsageResponse
    RoleAssignmentRequest:
      properties:
        user_id:
          type: string
          title: User Id
        role_name:
          type: string
          title: Role Name
        expires_at:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Expires At
      type: object
      required:
      - user_id
      - role_name
      title: RoleAssignmentRequest
    RuntimeInfoResponse:
      properties:
        agent_id:
          type: string
          title: Agent Id
        status:
          type: string
          title: Status
        process_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Process Id
        start_time:
          anyOf:
          - type: string
          - type: 'null'
          title: Start Time
        last_heartbeat:
          anyOf:
          - type: string
          - type: 'null'
          title: Last Heartbeat
        cpu_usage:
          type: number
          title: Cpu Usage
        memory_usage:
          type: integer
          title: Memory Usage
        current_task:
          anyOf:
          - type: object
          - type: 'null'
          title: Current Task
        queue_size:
          type: integer
          title: Queue Size
        task_history_count:
          type: integer
          title: Task History Count
        capabilities:
          items:
            type: string
          type: array
          title: Capabilities
        constraints:
          type: object
          title: Constraints
      type: object
      required:
      - agent_id
      - status
      - process_id
      - start_time
      - last_heartbeat
      - cpu_usage
      - memory_usage
      - current_task
      - queue_size
      - task_history_count
      - capabilities
      - constraints
      title: RuntimeInfoResponse
    SearchCodeRequest:
      properties:
        query:
          type: string
          maxLength: 1000
          minLength: 1
          title: Query
          description: Search query
        language:
          anyOf:
          - type: string
          - type: 'null'
          title: Language
          description: Filter by programming language
        tags:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Tags
          description: Filter by tags
        limit:
          type: integer
          maximum: 50.0
          minimum: 1.0
          title: Limit
          description: Maximum results to return
          default: 10
        score_threshold:
          type: number
          maximum: 1.0
          minimum: 0.0
          title: Score Threshold
          description: Minimum similarity score
          default: 0.6
      type: object
      required:
      - query
      title: SearchCodeRequest
    SearchCodeResponse:
      properties:
        success:
          type: boolean
          title: Success
        results:
          items:
            $ref: '#/components/schemas/CodeResult'
          type: array
          title: Results
        total_found:
          type: integer
          title: Total Found
      type: object
      required:
      - success
      - results
      - total_found
      title: SearchCodeResponse
    SearchKnowledgeRequest:
      properties:
        query:
          type: string
          maxLength: 1000
          minLength: 1
          title: Query
          description: Search query
        agent_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Agent Id
          description: Filter by agent ID
        category:
          anyOf:
          - type: string
          - type: 'null'
          title: Category
          description: Filter by category
        limit:
          type: integer
          maximum: 50.0
          minimum: 1.0
          title: Limit
          description: Maximum results to return
          default: 10
        score_threshold:
          type: number
          maximum: 1.0
          minimum: 0.0
          title: Score Threshold
          description: Minimum similarity score
          default: 0.7
      type: object
      required:
      - query
      title: SearchKnowledgeRequest
    SearchKnowledgeResponse:
      properties:
        success:
          type: boolean
          title: Success
        results:
          items:
            $ref: '#/components/schemas/KnowledgeResult'
          type: array
          title: Results
        total_found:
          type: integer
          title: Total Found
      type: object
      required:
      - success
      - results
      - total_found
      title: SearchKnowledgeResponse
    SearchMessagesRequest:
      properties:
        query:
          type: string
          maxLength: 1000
          minLength: 1
          title: Query
          description: Search query
        agent_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Agent Id
          description: Filter by agent ID
        message_type:
          anyOf:
          - $ref: '#/components/schemas/MessageType'
          - type: 'null'
          description: Filter by message type
        limit:
          type: integer
          maximum: 100.0
          minimum: 1.0
          title: Limit
          description: Maximum results
          default: 20
      type: object
      required:
      - query
      title: SearchMessagesRequest
    SearchMessagesResponse:
      properties:
        success:
          type: boolean
          title: Success
        results:
          items:
            $ref: '#/components/schemas/MessageSearchResult'
          type: array
          title: Results
        total_found:
          type: integer
          title: Total Found
      type: object
      required:
      - success
      - results
      - total_found
      title: SearchMessagesResponse
    SendMessageRequest:
      properties:
        receiver_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Receiver Id
          description: Target agent ID (null for broadcast)
        receiver_type:
          anyOf:
          - type: string
          - type: 'null'
          title: Receiver Type
          description: Target agent type
        subject:
          type: string
          title: Subject
          description: Message subject
        payload:
          type: object
          title: Payload
          description: Message payload
        priority:
          allOf:
          - $ref: '#/components/schemas/MessagePriority'
          default: medium
        type:
          allOf:
          - $ref: '#/components/schemas/MessageType'
          default: request
        reply_to:
          anyOf:
          - type: string
          - type: 'null'
          title: Reply To
          description: Message ID to reply to
        correlation_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Correlation Id
          description: Conversation correlation ID
        expires_in_seconds:
          anyOf:
          - type: integer
            maximum: 3600.0
            minimum: 1.0
          - type: 'null'
          title: Expires In Seconds
          description: Message expiration time
        wait_for_response:
          type: boolean
          title: Wait For Response
          description: Wait for response
          default: false
        timeout:
          type: integer
          maximum: 300.0
          minimum: 1.0
          title: Timeout
          description: Response timeout in seconds
          default: 30
      type: object
      required:
      - subject
      - payload
      title: SendMessageRequest
    StoreCodeSnippetRequest:
      properties:
        title:
          type: string
          maxLength: 200
          minLength: 1
          title: Title
          description: Code snippet title
        code:
          type: string
          maxLength: 20000
          minLength: 1
          title: Code
          description: Code content
        language:
          type: string
          title: Language
          description: Programming language
        description:
          type: string
          maxLength: 1000
          minLength: 1
          title: Description
          description: Code description
        tags:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Tags
          description: Tags for categorization
        metadata:
          anyOf:
          - type: object
          - type: 'null'
          title: Metadata
          description: Additional metadata
      type: object
      required:
      - title
      - code
      - language
      - description
      title: StoreCodeSnippetRequest
    StoreCodeSnippetResponse:
      properties:
        success:
          type: boolean
          title: Success
        point_id:
          type: string
          title: Point Id
        message:
          type: string
          title: Message
      type: object
      required:
      - success
      - point_id
      - message
      title: StoreCodeSnippetResponse
    StoreConversationRequest:
      properties:
        agent_id:
          type: string
          title: Agent Id
          description: Agent ID
        user_id:
          type: string
          title: User Id
          description: User ID
        conversation_id:
          type: string
          title: Conversation Id
          description: Conversation ID
        message:
          type: string
          maxLength: 5000
          minLength: 1
          title: Message
          description: Message content
        role:
          type: string
          title: Role
          description: Message role (user or assistant)
        metadata:
          anyOf:
          - type: object
          - type: 'null'
          title: Metadata
          description: Additional metadata
      type: object
      required:
      - agent_id
      - user_id
      - conversation_id
      - message
      - role
      title: StoreConversationRequest
    StoreConversationResponse:
      properties:
        success:
          type: boolean
          title: Success
        point_id:
          type: string
          title: Point Id
        message:
          type: string
          title: Message
      type: object
      required:
      - success
      - point_id
      - message
      title: StoreConversationResponse
    StoreKnowledgeRequest:
      properties:
        agent_id:
          type: string
          title: Agent Id
          description: Agent ID to store knowledge for
        content:
          type: string
          maxLength: 10000
          minLength: 1
          title: Content
          description: Knowledge content
        category:
          type: string
          title: Category
          description: Knowledge category
          default: general
        metadata:
          anyOf:
          - type: object
          - type: 'null'
          title: Metadata
          description: Additional metadata
      type: object
      required:
      - agent_id
      - content
      title: StoreKnowledgeRequest
    StoreKnowledgeResponse:
      properties:
        success:
          type: boolean
          title: Success
        point_id:
          type: string
          title: Point Id
        message:
          type: string
          title: Message
      type: object
      required:
      - success
      - point_id
      - message
      title: StoreKnowledgeResponse
    SystemHealthResponse:
      properties:
        status:
          type: string
          enum:
          - healthy
          - degraded
          - unhealthy
          title: Status
        version:
          type: string
          title: Version
        uptime:
          type: string
          title: Uptime
        cpu_usage:
          anyOf:
          - type: number
          - type: 'null'
          title: Cpu Usage
        memory_usage:
          anyOf:
          - type: number
          - type: 'null'
          title: Memory Usage
        memory_mb:
          anyOf:
          - type: integer
          - type: 'null'
          title: Memory Mb
        components:
          additionalProperties:
            type: object
          type: object
          title: Components
      type: object
      required:
      - status
      - version
      - uptime
      title: SystemHealthResponse
      description: System health check response
    SystemStatsResponse:
      properties:
        total_agents:
          anyOf:
          - type: integer
          - type: 'null'
          title: Total Agents
        active_agents:
          anyOf:
          - type: integer
          - type: 'null'
          title: Active Agents
        cpu_usage:
          anyOf:
          - type: number
          - type: 'null'
          title: Cpu Usage
        memory_usage:
          anyOf:
          - type: number
          - type: 'null'
          title: Memory Usage
        disk_usage:
          anyOf:
          - type: number
          - type: 'null'
          title: Disk Usage
        uptime:
          anyOf:
          - type: string
          - type: 'null'
          title: Uptime
        success_rate:
          anyOf:
          - type: number
          - type: 'null'
          title: Success Rate
        resource_usage:
          anyOf:
          - $ref: '#/components/schemas/ResourceUsageResponse'
          - type: 'null'
        task_statistics:
          anyOf:
          - $ref: '#/components/schemas/TaskStatisticsResponse'
          - type: 'null'
      type: object
      title: SystemStatsResponse
    TaskExecutionRequest:
      properties:
        task_id:
          anyOf:
          - type: string
            format: uuid
          - type: 'null'
          title: Task Id
        task_type:
          type: string
          maxLength: 100
          title: Task Type
        input_data:
          type: object
          title: Input Data
        metadata:
          anyOf:
          - type: object
          - type: 'null'
          title: Metadata
      type: object
      required:
      - task_type
      title: TaskExecutionRequest
    TaskExecutionResponse:
      properties:
        task_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Task Id
        agent_id:
          type: string
          format: uuid
          title: Agent Id
        status:
          type: string
          title: Status
        result:
          type: object
          title: Result
        message:
          type: string
          title: Message
      type: object
      required:
      - task_id
      - agent_id
      - status
      - result
      - message
      title: TaskExecutionResponse
    TaskListResponse:
      properties:
        tasks:
          items:
            $ref: '#/components/schemas/TaskResponse'
          type: array
          title: Tasks
        total:
          type: integer
          title: Total
        limit:
          type: integer
          title: Limit
        offset:
          type: integer
          title: Offset
      type: object
      required:
      - tasks
      - total
      - limit
      - offset
      title: TaskListResponse
    TaskResponse:
      properties:
        title:
          type: string
          maxLength: 500
          minLength: 1
          title: Title
        description:
          anyOf:
          - type: string
            maxLength: 2000
          - type: 'null'
          title: Description
        type:
          type: string
          maxLength: 100
          title: Type
        priority:
          type: integer
          maximum: 10.0
          minimum: 1.0
          title: Priority
          default: 5
        input_data:
          anyOf:
          - type: object
          - type: 'null'
          title: Input Data
        metadata:
          type: object
          title: Metadata
        deadline:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Deadline
        id:
          type: string
          format: uuid
          title: Id
        status:
          $ref: '#/components/schemas/TaskStatus'
        progress_percentage:
          type: integer
          title: Progress Percentage
        steps_completed:
          type: integer
          title: Steps Completed
        total_steps:
          anyOf:
          - type: integer
          - type: 'null'
          title: Total Steps
        assigned_agent_id:
          anyOf:
          - type: string
            format: uuid
          - type: 'null'
          title: Assigned Agent Id
        orchestration_id:
          anyOf:
          - type: string
            format: uuid
          - type: 'null'
          title: Orchestration Id
        parent_task_id:
          anyOf:
          - type: string
            format: uuid
          - type: 'null'
          title: Parent Task Id
        created_at:
          type: string
          format: date-time
          title: Created At
        updated_at:
          type: string
          format: date-time
          title: Updated At
        started_at:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Started At
        completed_at:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Completed At
        output_data:
          anyOf:
          - type: object
          - type: 'null'
          title: Output Data
      type: object
      required:
      - title
      - type
      - id
      - status
      - progress_percentage
      - steps_completed
      - total_steps
      - assigned_agent_id
      - orchestration_id
      - parent_task_id
      - created_at
      - updated_at
      - started_at
      - completed_at
      - output_data
      title: TaskResponse
    TaskStatisticsResponse:
      properties:
        queued_tasks:
          type: integer
          title: Queued Tasks
        completed_tasks:
          type: integer
          title: Completed Tasks
      type: object
      required:
      - queued_tasks
      - completed_tasks
      title: TaskStatisticsResponse
    TaskStatus:
      type: string
      enum:
      - pending
      - assigned
      - in_progress
      - completed
      - failed
      - cancelled
      title: TaskStatus
      description: Task status enumeration
    TaskType:
      type: string
      enum:
      - text_completion
      - chat
      - code_generation
      - summarization
      - translation
      - classification
      - extraction
      - embeddings
      - image_analysis
      - speech_to_text
      - text_to_speech
      title: TaskType
      description: AI task types
    TemplateInfo:
      properties:
        name:
          type: string
          title: Name
        description:
          type: string
          title: Description
        agent_type:
          type: string
          title: Agent Type
        language:
          type: string
          title: Language
        capabilities:
          items:
            type: string
          type: array
          title: Capabilities
        config_schema:
          type: object
          title: Config Schema
        examples:
          items:
            type: object
          type: array
          title: Examples
      type: object
      required:
      - name
      - description
      - agent_type
      - language
      - capabilities
      - config_schema
      - examples
      title: TemplateInfo
    TextGenerationRequest:
      properties:
        prompt:
          type: string
          maxLength: 32000
          minLength: 1
          title: Prompt
          description: Generation prompt
        model:
          type: string
          title: Model
          description: Model to use
          default: gemini-pro
        temperature:
          anyOf:
          - type: number
            maximum: 1.0
            minimum: 0.0
          - type: 'null'
          title: Temperature
          description: Temperature
        max_output_tokens:
          anyOf:
          - type: integer
            maximum: 32768.0
            minimum: 1.0
          - type: 'null'
          title: Max Output Tokens
          description: Max tokens
        top_p:
          anyOf:
          - type: number
            maximum: 1.0
            minimum: 0.0
          - type: 'null'
          title: Top P
          description: Top P
        top_k:
          anyOf:
          - type: integer
            maximum: 100.0
            minimum: 1.0
          - type: 'null'
          title: Top K
          description: Top K
        agent_context:
          anyOf:
          - type: object
          - type: 'null'
          title: Agent Context
          description: Agent context
      type: object
      required:
      - prompt
      title: TextGenerationRequest
    TextGenerationResponse:
      properties:
        success:
          type: boolean
          title: Success
        text:
          type: string
          title: Text
        model:
          type: string
          title: Model
        tokens_used:
          anyOf:
          - type: integer
          - type: 'null'
          title: Tokens Used
        metadata:
          type: object
          title: Metadata
      type: object
      required:
      - success
      - text
      - model
      title: TextGenerationResponse
    Token:
      properties:
        access_token:
          type: string
          title: Access Token
        refresh_token:
          type: string
          title: Refresh Token
        token_type:
          type: string
          title: Token Type
      type: object
      required:
      - access_token
      - refresh_token
      - token_type
      title: Token
    UsagePattern:
      properties:
        pattern_id:
          type: string
          title: Pattern Id
        name:
          type: string
          title: Name
        description:
          type: string
          title: Description
        frequency:
          type: integer
          title: Frequency
        trend:
          type: string
          title: Trend
        impact_areas:
          items:
            type: string
          type: array
          title: Impact Areas
        improvement_opportunities:
          items:
            type: string
          type: array
          title: Improvement Opportunities
        identified_at:
          type: string
          title: Identified At
      type: object
      required:
      - pattern_id
      - name
      - description
      - frequency
      - trend
      - impact_areas
      - improvement_opportunities
      - identified_at
      title: UsagePattern
    UserCreate:
      properties:
        username:
          type: string
          maxLength: 255
          minLength: 3
          title: Username
        email:
          type: string
          pattern: ^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$
          title: Email
        full_name:
          anyOf:
          - type: string
            maxLength: 255
          - type: 'null'
          title: Full Name
        password:
          type: string
          minLength: 8
          title: Password
      type: object
      required:
      - username
      - email
      - password
      title: UserCreate
    UserPermissionsResponse:
      properties:
        permissions:
          items:
            type: string
          type: array
          title: Permissions
        roles:
          items:
            type: object
          type: array
          title: Roles
      type: object
      required:
      - permissions
      - roles
      title: UserPermissionsResponse
    UserResponse:
      properties:
        username:
          type: string
          maxLength: 255
          minLength: 3
          title: Username
        email:
          type: string
          pattern: ^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$
          title: Email
        full_name:
          anyOf:
          - type: string
            maxLength: 255
          - type: 'null'
          title: Full Name
        id:
          type: string
          format: uuid
          title: Id
        is_active:
          type: boolean
          title: Is Active
        is_superuser:
          type: boolean
          title: Is Superuser
        created_at:
          type: string
          format: date-time
          title: Created At
        last_login:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Last Login
      type: object
      required:
      - username
      - email
      - id
      - is_active
      - is_superuser
      - created_at
      - last_login
      title: UserResponse
    UserUpdate:
      properties:
        full_name:
          anyOf:
          - type: string
            maxLength: 255
          - type: 'null'
          title: Full Name
        email:
          anyOf:
          - type: string
            pattern: ^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$
          - type: 'null'
          title: Email
      type: object
      title: UserUpdate
    ValidationError:
      properties:
        loc:
          items:
            anyOf:
            - type: string
            - type: integer
          type: array
          title: Location
        msg:
          type: string
          title: Message
        type:
          type: string
          title: Error Type
      type: object
      required:
      - loc
      - msg
      - type
      title: ValidationError
    ValidationResponse:
      properties:
        valid:
          type: boolean
          title: Valid
        errors:
          items:
            type: string
          type: array
          title: Errors
          default: []
        warnings:
          items:
            type: string
          type: array
          title: Warnings
          default: []
      type: object
      required:
      - valid
      title: ValidationResponse
    WorkflowConnection:
      properties:
        source:
          type: string
          title: Source
        target:
          type: string
          title: Target
        source_output:
          type: string
          title: Source Output
          default: main
        target_input:
          type: string
          title: Target Input
          default: main
      type: object
      required:
      - source
      - target
      title: WorkflowConnection
    WorkflowDefinition:
      properties:
        name:
          type: string
          maxLength: 100
          minLength: 1
          title: Name
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
        nodes:
          items:
            $ref: '#/components/schemas/WorkflowNode'
          type: array
          title: Nodes
        connections:
          items:
            $ref: '#/components/schemas/WorkflowConnection'
          type: array
          title: Connections
        settings:
          type: object
          title: Settings
        tags:
          items:
            type: string
          type: array
          title: Tags
      type: object
      required:
      - name
      - nodes
      - connections
      title: WorkflowDefinition
    WorkflowExecutionResponse:
      properties:
        execution_id:
          type: string
          title: Execution Id
        workflow_id:
          type: string
          title: Workflow Id
        status:
          type: string
          title: Status
        started_at:
          type: string
          title: Started At
        completed_at:
          anyOf:
          - type: string
          - type: 'null'
          title: Completed At
        duration:
          anyOf:
          - type: number
          - type: 'null'
          title: Duration
        trigger_data:
          type: object
          title: Trigger Data
        results:
          type: object
          title: Results
        error:
          anyOf:
          - type: string
          - type: 'null'
          title: Error
      type: object
      required:
      - execution_id
      - workflow_id
      - status
      - started_at
      - completed_at
      - duration
      - trigger_data
      - results
      - error
      title: WorkflowExecutionResponse
    WorkflowNode:
      properties:
        id:
          type: string
          title: Id
        type:
          type: string
          title: Type
        name:
          type: string
          title: Name
        position:
          additionalProperties:
            type: number
          type: object
          title: Position
        parameters:
          type: object
          title: Parameters
        credentials:
          anyOf:
          - type: object
          - type: 'null'
          title: Credentials
      type: object
      required:
      - id
      - type
      - name
      - position
      title: WorkflowNode
    WorkflowResponse:
      properties:
        id:
          type: string
          title: Id
        name:
          type: string
          title: Name
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
        status:
          type: string
          title: Status
        created_at:
          type: string
          title: Created At
        updated_at:
          type: string
          title: Updated At
        created_by:
          type: string
          title: Created By
        node_count:
          type: integer
          title: Node Count
        execution_count:
          type: integer
          title: Execution Count
        last_execution:
          anyOf:
          - type: string
          - type: 'null'
          title: Last Execution
      type: object
      required:
      - id
      - name
      - description
      - status
      - created_at
      - updated_at
      - created_by
      - node_count
      - execution_count
      - last_execution
      title: WorkflowResponse
    api__ai__ChatMessage:
      properties:
        role:
          type: string
          title: Role
          description: 'Message role: system, user, or assistant'
        content:
          type: string
          title: Content
          description: Message content
      type: object
      required:
      - role
      - content
      title: ChatMessage
      description: Chat message schema
    api__ai__ChatRequest:
      properties:
        messages:
          items:
            $ref: '#/components/schemas/api__ai__ChatMessage'
          type: array
          title: Messages
        model:
          anyOf:
          - type: string
          - type: 'null'
          title: Model
          default: gpt-3.5-turbo
        provider:
          anyOf:
          - $ref: '#/components/schemas/AIProvider'
          - type: 'null'
        temperature:
          anyOf:
          - type: number
          - type: 'null'
          title: Temperature
          default: 0.7
        max_tokens:
          anyOf:
          - type: integer
          - type: 'null'
          title: Max Tokens
          default: 1000
        top_p:
          anyOf:
          - type: number
          - type: 'null'
          title: Top P
          default: 1.0
        frequency_penalty:
          anyOf:
          - type: number
          - type: 'null'
          title: Frequency Penalty
          default: 0.0
        presence_penalty:
          anyOf:
          - type: number
          - type: 'null'
          title: Presence Penalty
          default: 0.0
      type: object
      required:
      - messages
      title: ChatRequest
      description: Chat completion request
    api__ai__CodeGenerationRequest:
      properties:
        prompt:
          type: string
          title: Prompt
        language:
          anyOf:
          - type: string
          - type: 'null'
          title: Language
          default: python
        model:
          anyOf:
          - type: string
          - type: 'null'
          title: Model
          default: gpt-3.5-turbo
        provider:
          anyOf:
          - $ref: '#/components/schemas/AIProvider'
          - type: 'null'
        max_tokens:
          anyOf:
          - type: integer
          - type: 'null'
          title: Max Tokens
          default: 2000
      type: object
      required:
      - prompt
      title: CodeGenerationRequest
      description: Code generation request
    api__ai__EmbeddingRequest:
      properties:
        text:
          type: string
          title: Text
        model:
          anyOf:
          - type: string
          - type: 'null'
          title: Model
          default: text-embedding-ada-002
        provider:
          anyOf:
          - $ref: '#/components/schemas/AIProvider'
          - type: 'null'
          default: openai
      type: object
      required:
      - text
      title: EmbeddingRequest
      description: Embeddings request
    api__google_adk__ChatMessage:
      properties:
        role:
          type: string
          title: Role
          description: Message role (user/assistant)
        content:
          type: string
          title: Content
          description: Message content
      type: object
      required:
      - role
      - content
      title: ChatMessage
    api__google_adk__ChatRequest:
      properties:
        messages:
          items:
            $ref: '#/components/schemas/api__google_adk__ChatMessage'
          type: array
          minItems: 1
          title: Messages
          description: Chat messages
        model:
          type: string
          title: Model
          description: Model to use
          default: gemini-pro
        temperature:
          anyOf:
          - type: number
            maximum: 1.0
            minimum: 0.0
          - type: 'null'
          title: Temperature
          description: Temperature
        max_output_tokens:
          anyOf:
          - type: integer
            maximum: 32768.0
            minimum: 1.0
          - type: 'null'
          title: Max Output Tokens
          description: Max tokens
        stream:
          type: boolean
          title: Stream
          description: Stream response
          default: false
      type: object
      required:
      - messages
      title: ChatRequest
    api__google_adk__CodeGenerationRequest:
      properties:
        prompt:
          type: string
          maxLength: 8000
          minLength: 1
          title: Prompt
          description: Code generation prompt
        language:
          type: string
          title: Language
          description: Programming language
          default: python
        model:
          type: string
          title: Model
          description: Model to use
          default: code-bison
        temperature:
          anyOf:
          - type: number
            maximum: 1.0
            minimum: 0.0
          - type: 'null'
          title: Temperature
          description: Temperature
        max_output_tokens:
          anyOf:
          - type: integer
            maximum: 2048.0
            minimum: 1.0
          - type: 'null'
          title: Max Output Tokens
          description: Max tokens
      type: object
      required:
      - prompt
      title: CodeGenerationRequest
    api__google_adk__EmbeddingRequest:
      properties:
        texts:
          items:
            type: string
          type: array
          maxItems: 100
          minItems: 1
          title: Texts
          description: Texts to embed
        model:
          type: string
          title: Model
          description: Embedding model
          default: textembedding-gecko
        task_type:
          type: string
          title: Task Type
          description: Task type
          default: RETRIEVAL_DOCUMENT
      type: object
      required:
      - texts
      title: EmbeddingRequest
  securitySchemes:
    HTTPBearer:
      type: http
      scheme: bearer
