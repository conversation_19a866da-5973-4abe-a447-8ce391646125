# AI Agent Platform - Nginx Production Configuration

worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 2048;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    access_log /var/log/nginx/access.log main;

    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 1000;
    types_hash_max_size 2048;
    client_max_body_size 100M;
    server_tokens off;

    # Buffer settings
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_min_length 1000;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml
        application/x-font-ttf
        application/vnd.ms-fontobject
        font/opentype;

    # Brotli compression (if module available)
    # brotli on;
    # brotli_comp_level 6;
    # brotli_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=30r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=ai:10m rate=5r/s;
    
    # Connection limiting
    limit_conn_zone $binary_remote_addr zone=perip:10m;
    limit_conn_zone $server_name zone=perserver:10m;

    # SSL/TLS Configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_stapling on;
    ssl_stapling_verify on;

    # Upstream servers with health checks and load balancing
    upstream backend_api {
        least_conn;
        server backend:8000 max_fails=3 fail_timeout=30s weight=1;
        # Add more backend servers for scaling:
        # server backend2:8000 max_fails=3 fail_timeout=30s weight=1;
        keepalive 64;
        keepalive_requests 100;
        keepalive_timeout 60s;
    }

    upstream frontend_app {
        least_conn;
        server frontend:3000 max_fails=3 fail_timeout=30s weight=1;
        # Add more frontend servers for scaling:
        # server frontend2:3000 max_fails=3 fail_timeout=30s weight=1;
        keepalive 32;
    }

    # Cache configuration
    proxy_cache_path /tmp/nginx_cache levels=1:2 keys_zone=app_cache:10m max_size=1g 
                     inactive=60m use_temp_path=off;

    # HTTPS redirect server
    server {
        listen 80;
        server_name _;
        return 301 https://$host$request_uri;
    }

    # Main HTTPS server
    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        # SSL certificates
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_trusted_certificate /etc/nginx/ssl/ca.pem;

        # Security headers
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' ws: wss:;" always;
        add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;

        # Connection limits
        limit_conn perip 10;
        limit_conn perserver 1000;

        # API routes
        location /api/ {
            limit_req zone=api burst=50 nodelay;
            
            # Caching for GET requests
            proxy_cache app_cache;
            proxy_cache_methods GET HEAD;
            proxy_cache_valid 200 302 5m;
            proxy_cache_valid 404 1m;
            proxy_cache_key "$scheme$request_method$host$request_uri";
            proxy_cache_bypass $http_cache_control;
            add_header X-Cache-Status $upstream_cache_status;

            proxy_pass http://backend_api;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;
            
            # Timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # Buffering
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
            proxy_busy_buffers_size 8k;
        }

        # Authentication routes (more restrictive)
        location /api/v1/auth/ {
            limit_req zone=auth burst=20 nodelay;
            
            proxy_pass http://backend_api;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
            
            # No caching for auth routes
            proxy_no_cache 1;
            proxy_cache_bypass 1;
        }

        # AI routes (rate limited for expensive operations)
        location /api/v1/ai/ {
            limit_req zone=ai burst=10 nodelay;
            
            proxy_pass http://backend_api;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Longer timeouts for AI operations
            proxy_connect_timeout 120s;
            proxy_send_timeout 120s;
            proxy_read_timeout 300s;
            
            # Larger buffers for AI responses
            proxy_buffering on;
            proxy_buffer_size 8k;
            proxy_buffers 16 8k;
            proxy_busy_buffers_size 16k;
        }

        # Health check (no rate limiting)
        location /health {
            proxy_pass http://backend_api/health;
            proxy_set_header Host $host;
            access_log off;
            proxy_cache app_cache;
            proxy_cache_valid 200 30s;
        }

        # Metrics endpoint (secured)
        location /metrics {
            allow 127.0.0.1;
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;
            
            proxy_pass http://backend_api/metrics;
            proxy_set_header Host $host;
            access_log off;
        }

        # WebSocket support
        location /ws {
            proxy_pass http://backend_api;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 3600s;
            proxy_send_timeout 3600s;
            proxy_connect_timeout 60s;
        }

        # Frontend application
        location / {
            proxy_pass http://frontend_app;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Caching for static HTML
            proxy_cache app_cache;
            proxy_cache_valid 200 1h;
            proxy_cache_key "$scheme$request_method$host$request_uri";
            
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # Static files with aggressive caching
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp|avif)$ {
            proxy_pass http://frontend_app;
            
            # Long-term caching
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Content-Type-Options nosniff;
            
            # Proxy caching
            proxy_cache app_cache;
            proxy_cache_valid 200 1y;
            proxy_ignore_headers "Set-Cookie";
            
            # Compression
            gzip_static on;
        }

        # Font files
        location ~* \.(woff|woff2|ttf|eot)$ {
            proxy_pass http://frontend_app;
            add_header Access-Control-Allow-Origin "*";
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Security.txt
        location /.well-known/security.txt {
            return 200 "Contact: <EMAIL>\nExpires: 2024-12-31T23:59:59.000Z\n";
            add_header Content-Type text/plain;
        }

        # Block common attack patterns
        location ~* \.(php|asp|aspx|jsp|cgi)$ {
            return 444;
        }

        location ~* /(wp-admin|wp-login|wp-config|phpmyadmin|admin|administrator) {
            return 444;
        }

        # Error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /404.html {
            internal;
            proxy_pass http://frontend_app/404;
        }
        
        location = /50x.html {
            internal;
            proxy_pass http://frontend_app/500;
        }
    }

    # Status page for monitoring
    server {
        listen 127.0.0.1:8080;
        server_name localhost;
        
        location /nginx_status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            deny all;
        }
    }
}